2025-07-29 21:27:10,615 INFO  [main] o.a.d.s.b.c.e.WelcomeLogoApplicationListener - WelcomeLogoApplicationListener.java:62 -  [DUBBO] 

 :: Dubbo (v3.2.10) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>|EE556F110878F29190D18E16684784AB
, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:10,729 INFO  [main] c.a.n.c.env.SearchableProperties - SearchableProperties.java:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-29 21:27:12,255 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:12,263 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:12,825 INFO  [main] o.a.d.s.b.c.e.WelcomeLogoApplicationListener - WelcomeLogoApplicationListener.java:62 -  [DUBBO] 

 :: Dubbo (v3.2.10) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>|EE556F110878F29190D18E16684784AB
, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:12,913 INFO  [main] c.a.n.c.env.SearchableProperties - SearchableProperties.java:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-29 21:27:13,882 INFO  [main] o.a.d.s.b.c.e.WelcomeLogoApplicationListener - WelcomeLogoApplicationListener.java:62 -  [DUBBO] 

 :: Dubbo (v3.2.10) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>|EE556F110878F29190D18E16684784AB
, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:13,959 INFO  [main] c.a.n.c.env.SearchableProperties - SearchableProperties.java:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-29 21:27:14,239 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:14,239 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:14,927 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:14,927 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:16,538 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-admin] & group[DEFAULT_GROUP]
2025-07-29 21:27:16,570 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-admin.properties] & group[DEFAULT_GROUP]
2025-07-29 21:27:16,570 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - PropertySourceBootstrapConfiguration.java:109 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hxsy-admin.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-hxsy-admin,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-config.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-rpc.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-cache.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource-sharding.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-base.yaml,DEFAULT_GROUP'}]
2025-07-29 21:27:16,613 INFO  [main] cn.hxsy.HxsyAdminApplication - SpringApplication.java:631 - No active profile set, falling back to 1 default profile: "default"
2025-07-29 21:27:17,911 INFO  [main] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:86 -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:17,939 INFO  [main] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:96 -  [DUBBO] Creating global shared handler ..., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,103 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,106 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,170 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,170 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,198 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:107 -  [DUBBO] Serialize check serializable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,199 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,209 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,209 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,229 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,379 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-business] & group[DEFAULT_GROUP]
2025-07-29 21:27:18,405 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-business.properties] & group[DEFAULT_GROUP]
2025-07-29 21:27:18,416 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - PropertySourceBootstrapConfiguration.java:109 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hxsy-business.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-hxsy-business,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-config.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-rpc.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-cache.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource-sharding.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-base.yaml,DEFAULT_GROUP'}]
2025-07-29 21:27:18,426 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,426 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,437 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,437 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,460 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,460 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,460 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,460 INFO  [main] cn.hxsy.HxsyBusinessApplication - SpringApplication.java:631 - No active profile set, falling back to 1 default profile: "default"
2025-07-29 21:27:18,460 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,482 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:116 -  [DUBBO] Use default application: Dubbo Application[1.1](unknown), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,482 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,490 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,499 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,501 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,501 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,501 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,506 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:126 -  [DUBBO] Use default module model of target application: Dubbo Module[1.1.1], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:18,506 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:130 -  [DUBBO] Bind Dubbo Module[1.1.1] to spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@4b14b8, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,013 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 21:27:19,016 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 21:27:19,169 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:201 - Finished Spring Data repository scanning in 119 ms. Found 0 Redis repository interfaces.
2025-07-29 21:27:19,461 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:309 -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,461 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:311 -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,519 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:257 -  [DUBBO] Found 5 classes annotated by Dubbo @Service under package [cn.hxsy]: [cn.hxsy.rpc.company.CompWxCodeRpcServiceImpl, cn.hxsy.rpc.user.CompanyQyRelationRpcServiceImpl, cn.hxsy.rpc.user.CustomerRpcServiceImpl, cn.hxsy.rpc.user.SystemUserQyRelationRpcServiceImpl, cn.hxsy.rpc.user.UserInfoRpcServiceImpl], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,529 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,535 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,535 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.CustomerRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,535 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,538 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,713 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-auth] & group[DEFAULT_GROUP]
2025-07-29 21:27:19,746 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-auth.properties] & group[DEFAULT_GROUP]
2025-07-29 21:27:19,749 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - PropertySourceBootstrapConfiguration.java:109 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hxsy-auth.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-hxsy-auth,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-config.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-rpc.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-cache.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource-sharding.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-base.yaml,DEFAULT_GROUP'}]
2025-07-29 21:27:19,792 INFO  [main] cn.hxsy.auth.HxsyAuthApplication - SpringApplication.java:631 - No active profile set, falling back to 1 default profile: "default"
2025-07-29 21:27:19,817 INFO  [main] o.s.cloud.context.scope.GenericScope - GenericScope.java:283 - BeanFactory id=26bc9a8f-4717-31f3-b064-e2b5aa03a5b4
2025-07-29 21:27:19,914 INFO  [main] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:86 -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,949 INFO  [main] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:96 -  [DUBBO] Creating global shared handler ..., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:19,969 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: tencentWxInfoRpcService = ReferenceBean:cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0() at cn.hxsy.api.app.service.TencentWxInfoRpcService cn.hxsy.controller.UserController.tencentWxInfoRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,019 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: campPeriodRpcService = ReferenceBean:cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0() at private cn.hxsy.api.user.service.CampPeriodRpcService cn.hxsy.service.impl.CustomerCourseRelationServiceImpl.campPeriodRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,149 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,154 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,218 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,218 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,250 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:107 -  [DUBBO] Serialize check serializable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,250 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,263 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,263 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,289 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,531 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,531 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,549 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,549 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,559 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,559 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,559 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,563 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,586 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:116 -  [DUBBO] Use default application: Dubbo Application[1.1](unknown), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,586 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,599 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,602 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,602 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,602 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,609 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,613 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:126 -  [DUBBO] Use default module model of target application: Dubbo Module[1.1.1], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,613 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:130 -  [DUBBO] Bind Dubbo Module[1.1.1] to spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@72669f, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,658 INFO  [main] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:86 -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,679 INFO  [main] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:96 -  [DUBBO] Creating global shared handler ..., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,849 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,858 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,975 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:20,975 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,007 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:107 -  [DUBBO] Serialize check serializable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,007 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,018 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,018 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,049 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,071 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 21:27:21,083 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 21:27:21,194 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:201 - Finished Spring Data repository scanning in 90 ms. Found 0 Redis repository interfaces.
2025-07-29 21:27:21,253 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,253 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,269 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,269 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,279 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,280 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,281 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,282 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,290 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - AbstractAnnotationBeanPostProcessor.java:265 -  [DUBBO] class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,305 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:116 -  [DUBBO] Use default application: Dubbo Application[1.1](unknown), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,306 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,309 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,321 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,321 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,321 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,321 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,328 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:126 -  [DUBBO] Use default module model of target application: Dubbo Module[1.1.1], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,328 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:130 -  [DUBBO] Bind Dubbo Module[1.1.1] to spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@1ad1178, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,338 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:21,349 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:21,349 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$624/0x1d9caa18] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:21,349 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:21,353 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:21,359 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:21,552 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:309 -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,552 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:311 -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,615 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:257 -  [DUBBO] Found 2 classes annotated by Dubbo @Service under package [cn.hxsy]: [cn.hxsy.rpc.TencentWxInfoRpcServiceImpl, cn.hxsy.rpc.user.CampPeriodRpcServiceImpl], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,637 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,637 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,719 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 21:27:21,722 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 21:27:21,754 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:201 - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-07-29 21:27:21,909 INFO  [main] o.s.cloud.context.scope.GenericScope - GenericScope.java:283 - BeanFactory id=3b778475-c1bd-317a-9292-78692debbfa1
2025-07-29 21:27:21,946 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:309 -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,946 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:311 -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:21,946 WARN  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ?:? -  [DUBBO] No class annotated by Dubbo @DubboService or @Service was found under package [cn.hxsy.auth], ignore re-scanned classes: 0, dubbo version: 3.2.10, current host: **************, error code: 5-28. This may be caused by No annotations were found on the class, go to https://dubbo.apache.org/faq/5/28 to find instructions. 
2025-07-29 21:27:22,063 INFO  [main] o.s.cloud.context.scope.GenericScope - GenericScope.java:283 - BeanFactory id=2a3b6764-0cd7-34b6-9b7f-82da05fef37b
2025-07-29 21:27:22,106 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: userInfoRpcService = ReferenceBean:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0() at private cn.hxsy.api.user.service.UserInfoRpcService cn.hxsy.service.impl.CourseVideoServiceImpl.userInfoRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:22,109 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: compWxCodeRpcService = ReferenceBean:cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0() at private cn.hxsy.api.user.service.CompWxCodeRpcService cn.hxsy.service.impl.MiniProgramServiceImpl.compWxCodeRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:22,109 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: userInfoRpcService = ReferenceBean:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0() at cn.hxsy.api.user.service.UserInfoRpcService cn.hxsy.auth.service.impl.LoginServiceImpl.userInfoRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:22,117 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: tencentWxInfoRpcService = ReferenceBean:cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0() at cn.hxsy.api.app.service.TencentWxInfoRpcService cn.hxsy.auth.service.impl.LoginServiceImpl.tencentWxInfoRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:22,117 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: companyQyRelationRpcService = ReferenceBean:cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0() at cn.hxsy.api.user.service.CompanyQyRelationRpcService cn.hxsy.auth.service.impl.LoginServiceImpl.companyQyRelationRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:22,117 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: systemUserQyRelationRpcService = ReferenceBean:cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0() at cn.hxsy.api.user.service.SystemUserQyRelationRpcService cn.hxsy.auth.service.impl.LoginServiceImpl.systemUserQyRelationRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:22,119 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$4fddbf37] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:22,129 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: customerRpcService = ReferenceBean:cn.hxsy.api.user.service.CustomerRpcService:1.0.0() at private cn.hxsy.api.user.service.CustomerRpcService cn.hxsy.service.impl.WxPayServiceImpl.customerRpcService, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:22,669 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - AbstractAnnotationBeanPostProcessor.java:265 -  [DUBBO] class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,114 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:108 - Tomcat initialized with port(s): 13301 (http)
2025-07-29 21:27:23,134 INFO  [main] o.a.catalina.core.StandardService - DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-29 21:27:23,134 INFO  [main] o.a.catalina.core.StandardEngine - DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-29 21:27:23,292 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - AbstractAnnotationBeanPostProcessor.java:265 -  [DUBBO] class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,302 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/] - DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-29 21:27:23,302 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - ServletWebServerApplicationContext.java:292 - Root WebApplicationContext: initialization completed in 3485 ms
2025-07-29 21:27:23,359 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:23,365 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:23,367 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$626/0x1ede0418] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:23,367 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:23,369 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:23,375 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:23,409 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:101 -  [DUBBO] loading dubbo config beans ..., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,409 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:127 -  [DUBBO] dubbo config beans are loaded., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,499 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : nacos] supports as the config center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,499 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the config center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,503 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:366 -  [DUBBO] use registry as config-center: <dubbo:config-center highestPriority="false" id="config-center-nacos-*************-8848" address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, password=nacos, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,572 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:108 - Tomcat initialized with port(s): 10100 (http)
2025-07-29 21:27:23,598 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:23,599 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:23,599 INFO  [main] o.a.catalina.core.StandardService - DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-29 21:27:23,599 INFO  [main] o.a.catalina.core.StandardEngine - DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-29 21:27:23,792 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/] - DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-29 21:27:23,792 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - ServletWebServerApplicationContext.java:292 - Root WebApplicationContext: initialization completed in 7158 ms
2025-07-29 21:27:23,941 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:101 -  [DUBBO] loading dubbo config beans ..., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:23,944 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:127 -  [DUBBO] dubbo config beans are loaded., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,016 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,016 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,034 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : nacos] supports as the config center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,037 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the config center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,040 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:321 -  [DUBBO] The current configurations or effective configurations are as follows:, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,045 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:366 -  [DUBBO] use registry as config-center: <dubbo:config-center highestPriority="false" id="config-center-nacos-*************-8848" address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, password=nacos, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,045 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:application enableFileCache="true" executorManagementMode="isolation" serializeCheckStatus="WARN" parameters="{}" name="hxsy-auth" qosAcceptForeignIp="false" qosEnable="false" protocol="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,045 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:protocol preferSerialization="fastjson2,hessian2" port="-1" name="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,045 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,045 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:ssl />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,079 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:24,080 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:24,084 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.0] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,084 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.1] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,175 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$3f56c84d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-29 21:27:24,299 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : nacos] supports as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,299 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,303 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:523 -  [DUBBO] use registry as metadata-center: <dubbo:metadata-report address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{password=nacos, namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,335 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:24,335 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:24,552 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,552 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,584 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:321 -  [DUBBO] The current configurations or effective configurations are as follows:, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,584 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:application enableFileCache="true" executorManagementMode="isolation" serializeCheckStatus="WARN" parameters="{}" name="hxsy-admin" qosAcceptForeignIp="false" qosEnable="false" protocol="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,584 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:protocol preferSerialization="fastjson2,hessian2" port="-1" name="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,584 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,584 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:ssl />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,624 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.0] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,629 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.1] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,730 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:236 -  [DUBBO] Dubbo Application[1.1](hxsy-auth) has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,784 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.UserInfoRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,841 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : nacos] supports as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,841 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,847 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:523 -  [DUBBO] use registry as metadata-center: <dubbo:metadata-report address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{password=nacos, namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,886 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:24,886 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:24,891 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.app.service.TencentWxInfoRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,895 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.CompanyQyRelationRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:24,898 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.SystemUserQyRelationRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:25,253 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:236 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:25,338 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:108 - Tomcat initialized with port(s): 10200 (http)
2025-07-29 21:27:25,363 INFO  [main] o.a.catalina.core.StandardService - DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-29 21:27:25,363 INFO  [main] o.a.catalina.core.StandardEngine - DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-29 21:27:25,562 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/] - DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-29 21:27:25,562 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - ServletWebServerApplicationContext.java:292 - Root WebApplicationContext: initialization completed in 7072 ms
2025-07-29 21:27:25,569 INFO  [main] org.redisson.Version - Version.java:41 - Redisson 3.22.1
2025-07-29 21:27:25,909 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:101 -  [DUBBO] loading dubbo config beans ..., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:25,914 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:127 -  [DUBBO] dubbo config beans are loaded., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,032 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : nacos] supports as the config center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,034 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the config center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,042 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:366 -  [DUBBO] use registry as config-center: <dubbo:config-center highestPriority="false" id="config-center-nacos-*************-8848" address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, password=nacos, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,078 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:26,079 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:26,266 ERROR [main] c.a.d.pool.DruidAbstractDataSource - DruidAbstractDataSource.java:1094 - maxIdle is deprecated
2025-07-29 21:27:26,277 INFO  [pool-7-thread-1] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2043 - {dataSource-0} closing ...
2025-07-29 21:27:26,458 INFO  [main] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:990 - {dataSource-1,DataSource-30947243} inited
2025-07-29 21:27:26,501 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,501 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,544 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:321 -  [DUBBO] The current configurations or effective configurations are as follows:, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,545 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:application enableFileCache="true" executorManagementMode="isolation" serializeCheckStatus="WARN" parameters="{}" name="hxsy-business" qosAcceptForeignIp="false" qosEnable="false" protocol="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,546 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:protocol preferSerialization="fastjson2,hessian2" port="-1" name="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,546 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,547 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:ssl />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,580 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.0] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,586 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.1] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,840 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : nacos] supports as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,842 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,847 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:523 -  [DUBBO] use registry as metadata-center: <dubbo:metadata-report address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{password=nacos, namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:26,889 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:26,896 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:27,270 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:236 -  [DUBBO] Dubbo Application[1.1](hxsy-business) has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:27,738 INFO  [redisson-netty-2-6] o.r.c.p.MasterPubSubConnectionPool - ConnectionPool.java:140 - 1 connections initialized for *************/*************:6379
2025-07-29 21:27:27,879 INFO  [main] org.redisson.Version - Version.java:41 - Redisson 3.22.1
2025-07-29 21:27:29,217 INFO  [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool - ConnectionPool.java:140 - 24 connections initialized for *************/*************:6379
2025-07-29 21:27:29,925 INFO  [redisson-netty-2-6] o.r.c.p.MasterPubSubConnectionPool - ConnectionPool.java:140 - 1 connections initialized for *************/*************:6379
2025-07-29 21:27:30,164 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:77 - 
当前服务实例工作区数据中心CentID：2
2025-07-29 21:27:30,724 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:106 - 
当前服务实例工作区ID：2
2025-07-29 21:27:30,736 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.ColumnMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 21:27:30,760 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.ColumnMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 21:27:30,811 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.CompanyMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 21:27:30,827 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.CompanyMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 21:27:31,393 INFO  [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool - ConnectionPool.java:140 - 24 connections initialized for *************/*************:6379
2025-07-29 21:27:31,579 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.HeadquartersMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 21:27:31,584 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.HeadquartersMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-29 21:27:31,589 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.HeadquartersMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 21:27:31,626 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.SalesGroupMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-29 21:27:31,639 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.SalesGroupMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-29 21:27:32,594 ERROR [main] c.a.d.pool.DruidAbstractDataSource - DruidAbstractDataSource.java:1094 - maxIdle is deprecated
2025-07-29 21:27:32,608 INFO  [pool-6-thread-1] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2043 - {dataSource-0} closing ...
2025-07-29 21:27:32,837 INFO  [main] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:990 - {dataSource-1,DataSource-30947243} inited
2025-07-29 21:27:33,642 INFO  [main] org.redisson.Version - Version.java:41 - Redisson 3.22.1
2025-07-29 21:27:34,415 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:220 - Tomcat started on port(s): 13301 (http) with context path ''
2025-07-29 21:27:34,449 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:34,451 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:34,826 INFO  [main] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:76 - nacos registry, DEFAULT_GROUP hxsy-auth **************:13301 register finished
2025-07-29 21:27:36,060 INFO  [redisson-netty-2-6] o.r.c.p.MasterPubSubConnectionPool - ConnectionPool.java:140 - 1 connections initialized for *************/*************:6379
2025-07-29 21:27:36,122 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,123 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1285 -  [DUBBO] Dubbo Application[1.1](hxsy-auth) is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,123 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.0] is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,125 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.0] has started., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,165 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:83 -  [DUBBO] Serialize check level: WARN, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,172 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,221 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:36,221 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:36,632 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,634 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-auth-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,682 INFO  [main] o.a.d.r.c.m.MigrationRuleListener - MigrationRuleListener.java:106 -  [DUBBO] Listening for migration rules on dataId hxsy-auth.migration, group DUBBO_SERVICEDISCOVERY_MIGRATION, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:36,963 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:37,287 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795656153&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:37,340 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:37,341 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795656153&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:37,353 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:37,394 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:37,395 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795656153&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:37,395 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:37,543 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-auth-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:37,725 INFO  [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool - ConnectionPool.java:140 - 24 connections initialized for *************/*************:6379
2025-07-29 21:27:37,939 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:37,940 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:38,327 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,455 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin for service key cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,518 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,518 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-1] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.SystemUserQyRelationRpcService, apps: [hxsy-admin]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,518 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,520 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.SystemUserQyRelationRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,524 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-auth', serviceInterface='cn.hxsy.api.user.service.SystemUserQyRelationRpcService', version='1.0.0', group='', side='consumer'}; definition: {release=3.2.10, side=consumer, version=1.0.0, dubbo=2.0.2, application=hxsy-auth, interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService, pid=17492, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=querySystemQyUserInner, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795656153}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,548 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,640 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795658537&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,685 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:38,686 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795658537&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,686 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,734 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:38,734 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795658537&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,736 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:38,985 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin,hxsy-business for service key cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,027 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-2] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.UserInfoRpcService, apps: [hxsy-admin, hxsy-business]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,028 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,028 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,028 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.UserInfoRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,028 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-auth', serviceInterface='cn.hxsy.api.user.service.UserInfoRpcService', version='1.0.0', group='', side='consumer'}; definition: {release=3.2.10, side=consumer, version=1.0.0, dubbo=2.0.2, application=hxsy-auth, interface=cn.hxsy.api.user.service.UserInfoRpcService, pid=17492, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=PcLogin,query,register, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795658537}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,036 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,125 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659031&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,170 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:39,171 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659031&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,172 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,212 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:39,212 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659031&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,213 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,446 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin,hxsy-business for service key cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,447 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,447 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,447 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.app.service.TencentWxInfoRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,448 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-auth', serviceInterface='cn.hxsy.api.app.service.TencentWxInfoRpcService', version='1.0.0', group='', side='consumer'}; definition: {release=3.2.10, side=consumer, version=1.0.0, dubbo=2.0.2, application=hxsy-auth, interface=cn.hxsy.api.app.service.TencentWxInfoRpcService, pid=17492, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=queryWxConfig, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795659031}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,458 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,488 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-3] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.app.service.TencentWxInfoRpcService, apps: [hxsy-admin, hxsy-business]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,521 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659452&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,561 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:39,562 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659452&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,563 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,600 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:39,601 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659452&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,601 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:77 - 
当前服务实例工作区数据中心CentID：7
2025-07-29 21:27:39,602 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,836 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin for service key cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,837 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,838 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,838 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.CompanyQyRelationRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,838 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.1] has started., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,838 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-auth', serviceInterface='cn.hxsy.api.user.service.CompanyQyRelationRpcService', version='1.0.0', group='', side='consumer'}; definition: {release=3.2.10, side=consumer, version=1.0.0, dubbo=2.0.2, application=hxsy-auth, interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService, pid=17492, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=query, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795659452}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,840 INFO  [main] o.a.d.c.d.DefaultMetricsServiceExporter - DefaultMetricsServiceExporter.java:116 -  [DUBBO] The MetricsConfig not exist, will not export metrics service., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,849 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:209 -  [DUBBO] org.apache.dubbo.metadata.MetadataServiceService Port hasn't been set will use default protocol defined in protocols., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,857 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:266 -  [DUBBO] Using dubbo protocol to export org.apache.dubbo.metadata.MetadataService service on port -1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,857 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.UserInfoRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:39,872 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-4] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.CompanyQyRelationRpcService, apps: [hxsy-admin]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:40,117 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.CompWxCodeRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:40,218 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:106 - 
当前服务实例工作区ID：1
2025-07-29 21:27:40,325 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.CampPeriodRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,116 WARN  [main] o.apache.dubbo.config.ServiceConfig - ?:? -  [DUBBO] Use random available port(20880) for protocol dubbo, dubbo version: 3.2.10, current host: **************, error code: 5-8. This may be caused by , go to https://dubbo.apache.org/faq/5/8 to find instructions. 
2025-07-29 21:27:41,176 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,185 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to local registry url : injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-auth&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-auth&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=17492&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795659908&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,185 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:920 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to url dubbo://**************:20880/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-auth&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-auth&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=17492&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795659908&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,189 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,370 INFO  [main] o.a.d.r.transport.AbstractServer - AbstractServer.java:71 -  [DUBBO] Start NettyServer bind /0.0.0.0:20880, export /**************:20880, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,380 INFO  [main] o.a.d.c.m.ConfigurableMetadataServiceExporter - ConfigurableMetadataServiceExporter.java:80 -  [DUBBO] The MetadataService exports urls : [dubbo://**************:20880/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-auth&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-auth&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=17492&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795659908&version=1.0.0], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,383 INFO  [main] o.a.d.r.c.m.ServiceInstanceMetadataUtils - ServiceInstanceMetadataUtils.java:207 -  [DUBBO] Start registering instance address to registry., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,401 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1298 -  [DUBBO] Dubbo Application[1.1](hxsy-auth) is ready., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:41,406 INFO  [main] cn.hxsy.auth.HxsyAuthApplication - StartupInfoLogger.java:61 - Started HxsyAuthApplication in 30.225 seconds (JVM running for 30.812)
2025-07-29 21:27:41,412 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-auth, group=DEFAULT_GROUP
2025-07-29 21:27:41,414 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-auth.properties, group=DEFAULT_GROUP
2025-07-29 21:27:41,710 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.CustomerRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:42,189 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.app.service.TencentWxInfoRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:42,558 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:77 - 
当前服务实例工作区数据中心CentID：11
2025-07-29 21:27:43,175 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:106 - 
当前服务实例工作区ID：1
2025-07-29 21:27:46,535 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1220 - Using default implementation for ThreadExecutor
2025-07-29 21:27:46,571 INFO  [main] o.quartz.core.SchedulerSignalerImpl - SchedulerSignalerImpl.java:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 21:27:46,571 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:229 - Quartz Scheduler v.2.3.2 created.
2025-07-29 21:27:46,573 INFO  [main] org.quartz.simpl.RAMJobStore - RAMJobStore.java:155 - RAMJobStore initialized.
2025-07-29 21:27:46,574 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 21:27:46,575 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 21:27:46,575 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1378 - Quartz scheduler version: 2.3.2
2025-07-29 21:27:46,577 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@89931e
2025-07-29 21:27:47,763 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:220 - Tomcat started on port(s): 10100 (http) with context path ''
2025-07-29 21:27:47,783 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:47,784 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1220 - Using default implementation for ThreadExecutor
2025-07-29 21:27:47,784 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:47,811 INFO  [main] o.quartz.core.SchedulerSignalerImpl - SchedulerSignalerImpl.java:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-29 21:27:47,811 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:229 - Quartz Scheduler v.2.3.2 created.
2025-07-29 21:27:47,813 INFO  [main] org.quartz.simpl.RAMJobStore - RAMJobStore.java:155 - RAMJobStore initialized.
2025-07-29 21:27:47,815 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-29 21:27:47,816 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-29 21:27:47,816 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1378 - Quartz scheduler version: 2.3.2
2025-07-29 21:27:47,817 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@18a0024
2025-07-29 21:27:48,157 INFO  [main] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:76 - nacos registry, DEFAULT_GROUP hxsy-admin **************:10100 register finished
2025-07-29 21:27:49,428 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:220 - Tomcat started on port(s): 10200 (http) with context path ''
2025-07-29 21:27:49,458 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:49,458 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:49,862 INFO  [main] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:76 - nacos registry, DEFAULT_GROUP hxsy-business **************:10200 register finished
2025-07-29 21:27:50,721 INFO  [main] o.s.s.quartz.SchedulerFactoryBean - SchedulerFactoryBean.java:729 - Starting Quartz Scheduler now
2025-07-29 21:27:50,722 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 21:27:50,732 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:50,733 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1285 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,431 INFO  [main] o.s.s.quartz.SchedulerFactoryBean - SchedulerFactoryBean.java:729 - Starting Quartz Scheduler now
2025-07-29 21:27:52,432 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-29 21:27:52,450 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,452 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1285 -  [DUBBO] Dubbo Application[1.1](hxsy-business) is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,605 WARN  [main] o.apache.dubbo.config.ServiceConfig - ?:? -  [DUBBO] Use random available port(20881) for protocol dubbo, dubbo version: 3.2.10, current host: **************, error code: 5-8. This may be caused by , go to https://dubbo.apache.org/faq/5/8 to find instructions. 
2025-07-29 21:27:52,790 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:83 -  [DUBBO] Serialize check level: WARN, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,807 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,809 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753795671285&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,811 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,820 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:52,919 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:53,135 INFO  [main] o.a.d.r.transport.AbstractServer - AbstractServer.java:71 -  [DUBBO] Start NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:53,246 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-admin-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:53,350 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:53,350 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:53,722 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:53,774 INFO  [main] o.a.d.r.c.m.MigrationRuleListener - MigrationRuleListener.java:106 -  [DUBBO] Listening for migration rules on dataId hxsy-admin.migration, group DUBBO_SERVICEDISCOVERY_MIGRATION, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:53,832 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:53,833 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:53,872 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:53,872 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:54,265 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,267 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-admin-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,269 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,292 WARN  [main] o.apache.dubbo.config.ServiceConfig - ?:? -  [DUBBO] Use random available port(20882) for protocol dubbo, dubbo version: 3.2.10, current host: **************, error code: 5-8. This may be caused by , go to https://dubbo.apache.org/faq/5/8 to find instructions. 
2025-07-29 21:27:54,324 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:54,324 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,365 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:54,365 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,394 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.SystemUserQyRelationRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, version=1.0.0, interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService, release=3.2.10, application=hxsy-admin, pid=4704, dubbo=2.0.2, side=provider, executor-management-mode=isolation, file-cache=true, methods=querySystemQyUserInner, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20881, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753795671285}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.SystemUserQyRelationRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=querySystemQyUserInner, parameterTypes=[cn.hxsy.base.request.SystemUserQyRelationRequest], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,402 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,450 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,481 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753795674459&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,481 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,523 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,527 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:83 -  [DUBBO] Serialize check level: WARN, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,544 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,550 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.app.service.TencentWxInfoRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753795673057&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,552 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.app.service.TencentWxInfoRpcService url dubbo://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,555 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,700 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,747 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,790 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:54,797 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,830 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:54,830 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,833 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,869 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,882 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.CustomerRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753795674869&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,882 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CustomerRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,928 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CustomerRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,935 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CompWxCodeRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, version=1.0.0, interface=cn.hxsy.api.user.service.CompWxCodeRpcService, release=3.2.10, application=hxsy-admin, pid=4704, dubbo=2.0.2, side=provider, executor-management-mode=isolation, file-cache=true, methods=saveCompWxCode, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20881, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753795674459}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.CompWxCodeRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=saveCompWxCode, parameterTypes=[java.lang.Integer, java.lang.String, java.lang.String], returnType=boolean]]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:54,939 INFO  [main] o.a.d.r.transport.AbstractServer - AbstractServer.java:71 -  [DUBBO] Start NettyServer bind /0.0.0.0:20882, export /**************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,050 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-business-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,135 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,170 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:55,170 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,180 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:55,180 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:55,209 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:55,209 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,209 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.CustomerRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,247 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.CustomerRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,268 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753795675250&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,269 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,307 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CustomerRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, version=1.0.0, interface=cn.hxsy.api.user.service.CustomerRpcService, release=3.2.10, application=hxsy-admin, pid=4704, dubbo=2.0.2, side=provider, executor-management-mode=isolation, file-cache=true, methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20881, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753795674869}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.CustomerRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=getRedPacketStatus, parameterTypes=[cn.hxsy.base.request.wxPayRequest], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=saveReceiveRedPacket, parameterTypes=[java.lang.Long, java.lang.Long, java.lang.Long, java.lang.Long, java.lang.String, java.lang.Integer], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=updateRedPacketAndUseStatus, parameterTypes=[cn.hxsy.base.request.wxPayRequest], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,307 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,512 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,549 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:55,551 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,585 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:55,585 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,585 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,601 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,626 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,643 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.UserInfoRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753795675626&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,643 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.UserInfoRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,643 INFO  [main] o.a.d.r.c.m.MigrationRuleListener - MigrationRuleListener.java:106 -  [DUBBO] Listening for migration rules on dataId hxsy-business.migration, group DUBBO_SERVICEDISCOVERY_MIGRATION, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,680 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.UserInfoRpcService url dubbo://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,680 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CompanyQyRelationRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, version=1.0.0, interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService, release=3.2.10, application=hxsy-admin, pid=4704, dubbo=2.0.2, side=provider, executor-management-mode=isolation, file-cache=true, methods=query, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20881, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753795675250}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.CompanyQyRelationRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=query, parameterTypes=[cn.hxsy.api.qy.request.QyAppReq], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,686 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.app.service.TencentWxInfoRpcService url dubbo://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,686 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,729 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-29 21:27:55,729 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-29 21:27:55,892 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,933 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:55,933 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,984 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:55,990 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:55,990 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,033 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,033 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.0] is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,033 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.0] has started., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,048 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.UserInfoRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, version=1.0.0, interface=cn.hxsy.api.user.service.UserInfoRpcService, release=3.2.10, application=hxsy-admin, pid=4704, dubbo=2.0.2, side=provider, executor-management-mode=isolation, file-cache=true, methods=PcLogin,query,register, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20881, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753795675626}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.UserInfoRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=register, parameterTypes=[cn.hxsy.api.user.model.request.UserRegisterRequest], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=query, parameterTypes=[cn.hxsy.api.user.model.request.UserInfoRequest], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=PcLogin, parameterTypes=[cn.hxsy.api.user.model.request.PcLoginRequest], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,115 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,115 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-business-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,115 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,160 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:56,160 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,200 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:56,200 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,230 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-business', serviceInterface='cn.hxsy.api.app.service.TencentWxInfoRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={side=provider, release=3.2.10, dubbo=2.0.2, pid=20900, interface=cn.hxsy.api.app.service.TencentWxInfoRpcService, application=hxsy-business, version=1.0.0, anyhost=true, executor-management-mode=isolation, file-cache=true, methods=queryWxConfig, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20882, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753795673057}} ServiceDefinition [canonicalName=cn.hxsy.api.app.service.TencentWxInfoRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=queryWxConfig, parameterTypes=[cn.hxsy.api.app.model.request.TencentWxInfoRequest], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,245 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,285 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,330 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,330 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.CampPeriodRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=***********05&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,337 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CampPeriodRpcService url dubbo://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,371 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CampPeriodRpcService url dubbo://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,560 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,595 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:56,600 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,640 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:56,640 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,642 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,667 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676048&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,672 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-business', serviceInterface='cn.hxsy.api.user.service.CampPeriodRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={side=provider, release=3.2.10, dubbo=2.0.2, pid=20900, interface=cn.hxsy.api.user.service.CampPeriodRpcService, application=hxsy-business, version=1.0.0, anyhost=true, executor-management-mode=isolation, file-cache=true, methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20882, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=***********05}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.CampPeriodRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=getCampPeriodsByIds, parameterTypes=[java.util.List<java.lang.Long>], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=getCampTodayCourse, parameterTypes=[java.lang.Long, java.time.LocalDate], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=getCampGroupAndCourses, parameterTypes=[java.lang.Long], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=countCampPeriodBySalesGroupId, parameterTypes=[java.lang.Long, java.lang.Long], returnType=int], MethodDefinition [name=queryCampPeriod, parameterTypes=[java.lang.Long, java.lang.Long], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,695 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,696 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.0] is starting., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,697 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.0] has started., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,726 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:56,726 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676048&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,738 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,781 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:56,781 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676048&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,781 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:56,871 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-admin-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,032 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,032 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin,hxsy-business for service key cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,105 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-1] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.app.service.TencentWxInfoRpcService, apps: [hxsy-admin, hxsy-business]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,158 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,158 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,158 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.app.service.TencentWxInfoRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,163 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.app.service.TencentWxInfoRpcService', version='1.0.0', group='', side='consumer'}; definition: {version=1.0.0, interface=cn.hxsy.api.app.service.TencentWxInfoRpcService, release=3.2.10, application=hxsy-admin, pid=4704, dubbo=2.0.2, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=queryWxConfig, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795676048}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,190 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,259 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677174&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,296 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:57,296 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677174&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,296 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,340 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:57,340 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677174&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,342 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,400 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676734&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,440 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:57,442 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676734&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,460 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CustomerRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,493 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:57,497 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676734&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,497 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CustomerRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,585 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin,hxsy-business for service key cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,589 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,590 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,590 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.CampPeriodRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,590 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.1] has started., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,590 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CampPeriodRpcService', version='1.0.0', group='', side='consumer'}; definition: {version=1.0.0, interface=cn.hxsy.api.user.service.CampPeriodRpcService, release=3.2.10, application=hxsy-admin, pid=4704, dubbo=2.0.2, side=consumer, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795677174}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,590 INFO  [main] o.a.d.c.d.DefaultMetricsServiceExporter - DefaultMetricsServiceExporter.java:116 -  [DUBBO] The MetricsConfig not exist, will not export metrics service., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,595 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:209 -  [DUBBO] org.apache.dubbo.metadata.MetadataServiceService Port hasn't been set will use default protocol defined in protocols., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,604 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:266 -  [DUBBO] Using dubbo protocol to export org.apache.dubbo.metadata.MetadataService service on port -1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,606 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-business-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,623 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-2] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.CampPeriodRpcService, apps: [hxsy-admin, hxsy-business]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,678 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to local registry url : injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795677638&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,678 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:920 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to url dubbo://**************:20881/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795677638&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,684 INFO  [main] o.a.d.c.m.ConfigurableMetadataServiceExporter - ConfigurableMetadataServiceExporter.java:80 -  [DUBBO] The MetadataService exports urls : [dubbo://**************:20881/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795677638&version=1.0.0], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,684 INFO  [main] o.a.d.r.c.m.ServiceInstanceMetadataUtils - ServiceInstanceMetadataUtils.java:207 -  [DUBBO] Start registering instance address to registry., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,716 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1298 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) is ready., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,725 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753795670822, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,730 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,734 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin for service key cn.hxsy.api.user.service.CustomerRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,772 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753795670822, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,772 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753795674459, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,772 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,796 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-1] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.CustomerRpcService, apps: [hxsy-admin]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,812 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,812 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.CustomerRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,812 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753795674459, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,815 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.CustomerRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,815 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CustomerRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753795674869, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,815 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-business', serviceInterface='cn.hxsy.api.user.service.CustomerRpcService', version='1.0.0', group='', side='consumer'}; definition: {side=consumer, release=3.2.10, dubbo=2.0.2, pid=20900, interface=cn.hxsy.api.user.service.CustomerRpcService, application=hxsy-business, version=1.0.0, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795676734}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,815 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,843 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,843 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CustomerRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753795674869, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,843 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753795675250, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,843 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,890 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753795675250, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,890 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753795675626, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,894 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,928 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 url dubbo://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753795675626, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,930 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677819&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,941 INFO  [main] o.apache.dubbo.metadata.MetadataInfo - MetadataInfo.java:203 -  [DUBBO] metadata revision changed: null -> 847467605409d37fbcd26478e1591921, app: hxsy-admin, services: 5, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,969 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677819&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,983 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676734&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,985 WARN  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:57,987 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:57,990 INFO  [main] cn.hxsy.HxsyAdminApplication - StartupInfoLogger.java:61 - Started HxsyAdminApplication in 50.543 seconds (JVM running for 51.35)
2025-07-29 21:27:57,990 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-admin.properties, group=DEFAULT_GROUP
2025-07-29 21:27:58,011 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-admin, group=DEFAULT_GROUP
2025-07-29 21:27:58,050 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:60 -  [DUBBO] The connection of /**************:53820 -> /**************:20881 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,051 INFO  [main] o.a.d.r.transport.AbstractClient - AbstractClient.java:254 -  [DUBBO] Successfully connect to server /**************:20881 from NettyClient ************** using dubbo version 3.2.10, channel is NettyChannel [channel=[id: 0x5fc6d2c4, L:/**************:53820 - R:/**************:20881]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,054 INFO  [main] o.a.d.r.transport.AbstractClient - AbstractClient.java:81 -  [DUBBO] Start NettyClient /************** connect to the server /**************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,065 INFO  [NettyServerWorker-8-1] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:76 -  [DUBBO] The connection of /**************:53820 -> /**************:20881 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,266 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,267 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CustomerRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,278 WARN  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:58,278 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,288 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795656153&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,377 INFO  [NettyServerWorker-8-2] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:76 -  [DUBBO] The connection of /**************:53855 -> /**************:20881 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,390 WARN  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:58,390 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:60 -  [DUBBO] The connection of /**************:53855 -> /**************:20881 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,390 INFO  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,392 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-admin, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,395 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-admin, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,395 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.r.transport.AbstractClient - AbstractClient.java:254 -  [DUBBO] Successfully connect to server /**************:20881 from NettyClient ************** using dubbo version 3.2.10, channel is NettyChannel [channel=[id: 0x6fe5304b, L:/**************:53855 - R:/**************:20881]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,395 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-admin, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,395 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.r.transport.AbstractClient - AbstractClient.java:81 -  [DUBBO] Start NettyClient /************** connect to the server /**************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,400 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,402 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,402 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,406 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,406 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,414 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.CustomerRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,417 WARN  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ?:? -  [DUBBO] Received url with EMPTY protocol from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), will clear all available addresses., dubbo version: 3.2.10, current host: **************, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-29 21:27:58,417 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,420 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,422 WARN  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ?:? -  [DUBBO] Received url with EMPTY protocol from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), will clear all available addresses., dubbo version: 3.2.10, current host: **************, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-29 21:27:58,422 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,427 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,427 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,427 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,427 WARN  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ?:? -  [DUBBO] Received url with EMPTY protocol from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), will clear all available addresses., dubbo version: 3.2.10, current host: **************, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-29 21:27:58,427 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,427 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,435 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,472 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.CustomerRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,472 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,472 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.CustomerRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,478 WARN  [Dubbo-framework-registry-notification-6-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:58,478 INFO  [Dubbo-framework-registry-notification-6-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,482 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,488 WARN  [Dubbo-framework-registry-notification-8-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:27:58,488 INFO  [Dubbo-framework-registry-notification-8-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,506 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677819&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,512 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,631 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:62 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,631 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,631 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-admin, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,631 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,631 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,631 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,792 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin,hxsy-business for service key cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,794 INFO  [main] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-admin, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,796 INFO  [main] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,833 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-2] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.UserInfoRpcService, apps: [hxsy-admin, hxsy-business]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,833 INFO  [main] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:266 -  [DUBBO] Notify serviceKey: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:null, listener: ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,833 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,839 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,839 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,842 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,842 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.UserInfoRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,843 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-business', serviceInterface='cn.hxsy.api.user.service.UserInfoRpcService', version='1.0.0', group='', side='consumer'}; definition: {side=consumer, release=3.2.10, dubbo=2.0.2, pid=20900, interface=cn.hxsy.api.user.service.UserInfoRpcService, application=hxsy-business, version=1.0.0, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=PcLogin,query,register, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795677819}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,843 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,922 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,922 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,922 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,922 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,922 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,925 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,953 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795678843&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:58,998 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795678843&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,025 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:62 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,025 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,025 INFO  [Dubbo-framework-registry-notification-6-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659452&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,025 INFO  [Dubbo-framework-registry-notification-6-thread-1] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,025 INFO  [Dubbo-framework-registry-notification-6-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,036 INFO  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795658537&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,040 INFO  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,040 INFO  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,110 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,164 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795678843&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,164 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,409 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin for service key cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,410 INFO  [main] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:266 -  [DUBBO] Notify serviceKey: cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0:null, listener: ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,411 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,413 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,414 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,416 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,416 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.CompWxCodeRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,417 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.1] has started., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,417 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-business', serviceInterface='cn.hxsy.api.user.service.CompWxCodeRpcService', version='1.0.0', group='', side='consumer'}; definition: {side=consumer, release=3.2.10, dubbo=2.0.2, pid=20900, interface=cn.hxsy.api.user.service.CompWxCodeRpcService, application=hxsy-business, version=1.0.0, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=saveCompWxCode, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753795678843}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,418 INFO  [main] o.a.d.c.d.DefaultMetricsServiceExporter - DefaultMetricsServiceExporter.java:116 -  [DUBBO] The MetricsConfig not exist, will not export metrics service., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,426 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:209 -  [DUBBO] org.apache.dubbo.metadata.MetadataServiceService Port hasn't been set will use default protocol defined in protocols., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,427 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:266 -  [DUBBO] Using dubbo protocol to export org.apache.dubbo.metadata.MetadataService service on port -1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,451 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-3] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.CompWxCodeRpcService, apps: [hxsy-admin]}, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,495 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to local registry url : injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-business&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795679453&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,496 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:920 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to url dubbo://**************:20882/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-business&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795679453&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,501 INFO  [main] o.a.d.c.m.ConfigurableMetadataServiceExporter - ConfigurableMetadataServiceExporter.java:80 -  [DUBBO] The MetadataService exports urls : [dubbo://**************:20882/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-business&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795679453&version=1.0.0], dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,501 INFO  [main] o.a.d.r.c.m.ServiceInstanceMetadataUtils - ServiceInstanceMetadataUtils.java:207 -  [DUBBO] Start registering instance address to registry., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,528 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1298 -  [DUBBO] Dubbo Application[1.1](hxsy-business) is ready., dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,542 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0 url dubbo://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753795672543, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,543 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,578 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0 url dubbo://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753795672543, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,580 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0 url dubbo://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=***********05, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,581 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,613 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0 url dubbo://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=***********05, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,628 INFO  [main] o.apache.dubbo.metadata.MetadataInfo - MetadataInfo.java:203 -  [DUBBO] metadata revision changed: null -> 81e4d937ce4b626e00e9904e99c77dae, app: hxsy-business, services: 2, dubbo version: 3.2.10, current host: **************
2025-07-29 21:27:59,682 INFO  [main] cn.hxsy.HxsyBusinessApplication - StartupInfoLogger.java:61 - Started HxsyBusinessApplication in 50.078 seconds (JVM running for 51.129)
2025-07-29 21:27:59,689 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-business, group=DEFAULT_GROUP
2025-07-29 21:27:59,690 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-business.properties, group=DEFAULT_GROUP
2025-07-29 21:28:00,179 WARN  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:28:00,181 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-business, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,180 INFO  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,183 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-business, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,183 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 2 unique working revisions: 81e4d937ce4b626e00e9904e99c77dae 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,184 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,184 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 2 unique working revisions: 81e4d937ce4b626e00e9904e99c77dae 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,185 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,185 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 1[**************:20881], validInvokers: 1[**************:20881], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,189 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,189 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,191 WARN  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 21:28:00,192 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,192 INFO  [NettyClientWorker-9-2] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:60 -  [DUBBO] The connection of /**************:53857 -> /**************:20882 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,192 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.transport.AbstractClient - AbstractClient.java:254 -  [DUBBO] Successfully connect to server /**************:20882 from NettyClient ************** using dubbo version 3.2.10, channel is NettyChannel [channel=[id: 0x43352ba5, L:/**************:53857 - R:/**************:20882]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,193 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.transport.AbstractClient - AbstractClient.java:81 -  [DUBBO] Start NettyClient /************** connect to the server /**************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,194 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-business, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,194 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,195 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 2 unique working revisions: 81e4d937ce4b626e00e9904e99c77dae 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,199 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,203 INFO  [NettyServerWorker-8-1] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:76 -  [DUBBO] The connection of /**************:53857 -> /**************:20882 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,260 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:60 -  [DUBBO] The connection of /**************:53893 -> /**************:20882 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,260 INFO  [NettyServerWorker-8-2] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:76 -  [DUBBO] The connection of /**************:53893 -> /**************:20882 is established., dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,267 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.transport.AbstractClient - AbstractClient.java:254 -  [DUBBO] Successfully connect to server /**************:20882 from NettyClient ************** using dubbo version 3.2.10, channel is NettyChannel [channel=[id: 0x793f214f, L:/**************:53893 - R:/**************:20882]], dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,268 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.transport.AbstractClient - AbstractClient.java:81 -  [DUBBO] Start NettyClient /************** connect to the server /**************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,294 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,325 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:62 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,326 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,327 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,328 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 1[**************:20881], validInvokers: 1[**************:20881], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,328 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,329 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.UserInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,330 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659031&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,335 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,335 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,460 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:62 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,462 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,463 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,466 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:357 -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,667 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:62 -  [DUBBO] No interface address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,668 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,669 INFO  [Dubbo-framework-registry-notification-12-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677174&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,682 INFO  [Dubbo-framework-registry-notification-10-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676048&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,709 INFO  [Dubbo-framework-registry-notification-12-thread-1] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,709 INFO  [Dubbo-framework-registry-notification-12-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,924 INFO  [Dubbo-framework-registry-notification-10-thread-1] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:89 -  [DUBBO] serviceKey:cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0 Instance address size 1, interface address size 1, threshold 0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:00,924 INFO  [Dubbo-framework-registry-notification-10-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : **************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 21:28:55,549 ERROR [redisson-netty-2-10] o.r.c.handler.ErrorsLoggingHandler - ErrorsLoggingHandler.java:47 - Exception occured. Channel: [id: 0xa0d685e0, L:/**************:53603 - R:*************/*************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 21:28:55,633 ERROR [redisson-netty-2-12] o.r.c.handler.ErrorsLoggingHandler - ErrorsLoggingHandler.java:47 - Exception occured. Channel: [id: 0xcd59c987, L:/**************:53604 - R:*************/*************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 21:28:55,745 ERROR [redisson-netty-2-14] o.r.c.handler.ErrorsLoggingHandler - ErrorsLoggingHandler.java:47 - Exception occured. Channel: [id: 0xb72d3585, L:/**************:53605 - R:*************/*************:6379]
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:378)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)
2025-07-29 22:01:00,118 DEBUG [scheduling-1] c.h.d.S.countNewMembers - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM customer WHERE created_at BETWEEN ? AND ?
2025-07-29 22:01:01,174 DEBUG [scheduling-1] c.h.d.S.countNewMembers - BaseJdbcLogger.java:137 - ==> Parameters: 2025-07-29T21:00(LocalDateTime), 2025-07-29T22:00(LocalDateTime)
2025-07-29 22:01:01,270 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM customer WHERE created_at BETWEEN ? AND ?
2025-07-29 22:01:01,270 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:01,271 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM customer_0000 WHERE created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_0001 WHERE created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_0002 WHERE created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_0003 WHERE created_at BETWEEN ? AND ? ::: [2025-07-29T21:00, 2025-07-29T22:00, 2025-07-29T21:00, 2025-07-29T22:00, 2025-07-29T21:00, 2025-07-29T22:00, 2025-07-29T21:00, 2025-07-29T22:00]
2025-07-29 22:01:01,782 DEBUG [scheduling-1] c.h.d.S.countNewMembers - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:01:01,789 DEBUG [scheduling-1] c.h.d.S.countTotalCompanies - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM company WHERE status = 1
2025-07-29 22:01:01,806 DEBUG [scheduling-1] c.h.d.S.countTotalCompanies - BaseJdbcLogger.java:137 - ==> Parameters: 
2025-07-29 22:01:01,807 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM company WHERE status = 1
2025-07-29 22:01:01,807 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:01,808 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM company WHERE status = 1
2025-07-29 22:01:01,843 DEBUG [scheduling-1] c.h.d.S.countTotalCompanies - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:01:01,845 DEBUG [scheduling-1] c.h.d.S.countTotalSalesAccounts - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM system_user WHERE role_id = 12 AND status = 1
2025-07-29 22:01:01,851 DEBUG [scheduling-1] c.h.d.S.countTotalSalesAccounts - BaseJdbcLogger.java:137 - ==> Parameters: 
2025-07-29 22:01:01,852 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM system_user WHERE role_id = 12 AND status = 1
2025-07-29 22:01:01,852 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:01,853 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM system_user WHERE role_id = 12 AND status = 1
2025-07-29 22:01:01,888 DEBUG [scheduling-1] c.h.d.S.countTotalSalesAccounts - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:01:01,890 DEBUG [scheduling-1] c.h.d.S.countMonthNewCompanies - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM company WHERE status = 1 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:01,893 DEBUG [scheduling-1] c.h.d.S.countMonthNewCompanies - BaseJdbcLogger.java:137 - ==> Parameters: 2025-07-01T00:00(LocalDateTime), 2025-07-31T23:59:59.*********(LocalDateTime)
2025-07-29 22:01:01,894 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM company WHERE status = 1 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:01,894 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:01,894 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM company WHERE status = 1 AND created_at BETWEEN ? AND ? ::: [2025-07-01T00:00, 2025-07-31T23:59:59.*********]
2025-07-29 22:01:01,928 DEBUG [scheduling-1] c.h.d.S.countMonthNewCompanies - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:01:01,929 DEBUG [scheduling-1] c.h.d.S.countMonthNewSalesAccounts - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM system_user WHERE role_id = 12 AND status = 1 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:01,932 DEBUG [scheduling-1] c.h.d.S.countMonthNewSalesAccounts - BaseJdbcLogger.java:137 - ==> Parameters: 2025-07-01T00:00(LocalDateTime), 2025-07-31T23:59:59.*********(LocalDateTime)
2025-07-29 22:01:01,932 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM system_user WHERE role_id = 12 AND status = 1 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:01,932 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:01,932 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM system_user WHERE role_id = 12 AND status = 1 AND created_at BETWEEN ? AND ? ::: [2025-07-01T00:00, 2025-07-31T23:59:59.*********]
2025-07-29 22:01:01,968 DEBUG [scheduling-1] c.h.d.S.countMonthNewSalesAccounts - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:01:01,969 DEBUG [scheduling-1] c.h.d.S.countTodayViews - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM customer_behavior WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:01,972 DEBUG [scheduling-1] c.h.d.S.countTodayViews - BaseJdbcLogger.java:137 - ==> Parameters: 2025-07-29T00:00(LocalDateTime), 2025-07-29T23:59:59(LocalDateTime)
2025-07-29 22:01:01,973 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM customer_behavior WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:01,974 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:01,974 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM customer_behavior_0000 WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_behavior_0001 WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_behavior_0002 WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_behavior_0003 WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1 AND created_at BETWEEN ? AND ? ::: [2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59]
2025-07-29 22:01:02,058 DEBUG [scheduling-1] c.h.d.S.countTodayViews - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:01:02,059 DEBUG [scheduling-1] c.h.d.S.countTodayComplete - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM customer_course_relation WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:02,061 DEBUG [scheduling-1] c.h.d.S.countTodayComplete - BaseJdbcLogger.java:137 - ==> Parameters: 2025-07-29T00:00(LocalDateTime), 2025-07-29T23:59:59(LocalDateTime)
2025-07-29 22:01:02,062 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM customer_course_relation WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:02,062 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:02,062 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM customer_course_relation_0000 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_course_relation_0001 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_course_relation_0002 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_course_relation_0003 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_course_relation_0004 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_course_relation_0005 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_course_relation_0006 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? UNION ALL SELECT COUNT(*) FROM customer_course_relation_0007 WHERE status = 1 AND arrival_status = 2 AND created_at BETWEEN ? AND ? ::: [2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59, 2025-07-29T00:00, 2025-07-29T23:59:59]
2025-07-29 22:01:02,110 DEBUG [scheduling-1] c.h.d.S.countTodayComplete - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:01:02,113 DEBUG [scheduling-1] c.h.d.S.countTodayRedPackets - BaseJdbcLogger.java:137 - ==>  Preparing: SELECT COUNT(*) FROM course_red_packet WHERE status = 1 AND state = 5 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:02,114 DEBUG [scheduling-1] c.h.d.S.countTodayRedPackets - BaseJdbcLogger.java:137 - ==> Parameters: 2025-07-29T00:00(LocalDateTime), 2025-07-29T23:59:59(LocalDateTime)
2025-07-29 22:01:02,114 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Logic SQL: SELECT COUNT(*) FROM course_red_packet WHERE status = 1 AND state = 5 AND created_at BETWEEN ? AND ?
2025-07-29 22:01:02,115 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - SQLStatement: MySQLSelectStatement(table=Optional.empty, limit=Optional.empty, lock=Optional.empty, window=Optional.empty)
2025-07-29 22:01:02,115 INFO  [scheduling-1] ShardingSphere-SQL - SQLLogger.java:74 - Actual SQL: ds ::: SELECT COUNT(*) FROM course_red_packet WHERE status = 1 AND state = 5 AND created_at BETWEEN ? AND ? ::: [2025-07-29T00:00, 2025-07-29T23:59:59]
2025-07-29 22:01:02,155 DEBUG [scheduling-1] c.h.d.S.countTodayRedPackets - BaseJdbcLogger.java:137 - <==      Total: 1
2025-07-29 22:56:02,126 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:102 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-29 22:56:02,126 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:136 - [NotifyCenter] Start destroying Publisher
2025-07-29 22:56:02,126 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:79 -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,126 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:153 - [NotifyCenter] Destruction of the end
2025-07-29 22:56:02,126 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:136 - [NotifyCenter] Start destroying Publisher
2025-07-29 22:56:02,126 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:79 -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,126 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:102 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-29 22:56:02,126 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:111 - [HttpClientBeanHolder] Destruction of the end
2025-07-29 22:56:02,142 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:153 - [NotifyCenter] Destruction of the end
2025-07-29 22:56:02,142 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:111 - [HttpClientBeanHolder] Destruction of the end
2025-07-29 22:56:02,142 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,142 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,142 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:141 -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](hxsy-business), dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,142 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:141 -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](hxsy-admin), dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,142 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,142 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:136 - [NotifyCenter] Start destroying Publisher
2025-07-29 22:56:02,142 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:79 -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,142 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:102 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-29 22:56:02,142 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:153 - [NotifyCenter] Destruction of the end
2025-07-29 22:56:02,151 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:112 -  [DUBBO] Waiting for modules(Dubbo Application[1.1](hxsy-auth)) managed by Spring to be shutdown., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,151 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:111 - [HttpClientBeanHolder] Destruction of the end
2025-07-29 22:56:02,151 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,151 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,166 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795656153&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,166 INFO  [DubboClientHandler-thread-2] o.a.d.r.e.s.h.HeaderExchangeHandler - HeaderExchangeHandler.java:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x43352ba5, L:/**************:53857 - R:/**************:20882]], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,166 INFO  [DubboClientHandler-thread-3] o.a.d.r.e.s.h.HeaderExchangeHandler - HeaderExchangeHandler.java:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x6fe5304b, L:/**************:53855 - R:/**************:20881]], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,168 INFO  [DubboClientHandler-thread-2] o.a.d.r.e.s.h.HeaderExchangeHandler - HeaderExchangeHandler.java:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x793f214f, L:/**************:53893 - R:/**************:20882]], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,168 INFO  [DubboClientHandler-thread-2] o.a.d.r.e.s.h.HeaderExchangeHandler - HeaderExchangeHandler.java:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x5fc6d2c4, L:/**************:53820 - R:/**************:20881]], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,173 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:135 -  [DUBBO] Dubbo wait for application(Dubbo Application[1.1](hxsy-auth)) managed by Spring to be shutdown failed, time usage: 22ms, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,173 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:141 -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](hxsy-auth), dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,189 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-business&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,189 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,221 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20882/cn.hxsy.api.app.service.TencentWxInfoRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795673057&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,221 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,253 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,284 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,284 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20882/cn.hxsy.api.user.service.CampPeriodRpcService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=***********05&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,300 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659031&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,316 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20881/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795671285&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,348 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676734&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,380 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20881/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674459&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,428 INFO  [SpringApplicationShutdownHook] o.a.d.r.t.netty4.NettyChannel - NettyChannel.java:261 -  [DUBBO] Close netty channel [id: 0x43352ba5, L:/**************:53857 - R:/**************:20882], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,438 INFO  [NettyClientWorker-9-2] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:75 -  [DUBBO] The connection of /**************:53857 -> /**************:20882 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,438 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795658537&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,438 INFO  [NettyServerWorker-8-1] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:93 -  [DUBBO] The connection of /**************:53857 -> /**************:20882 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,444 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20881/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795674869&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,460 INFO  [Dubbo-framework-shared-scheduler-thread-2] o.a.d.r.c.AbstractServiceDiscovery - AbstractServiceDiscovery.java:188 -  [DUBBO] Metadata of instance changed, updating instance with revision 0., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,476 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677819&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,507 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20881/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675250&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,555 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-auth&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795659452&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,571 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20881/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753795675626&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,586 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-business&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795678843&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,603 INFO  [Dubbo-framework-shared-scheduler-thread-1] o.a.d.r.c.AbstractServiceDiscovery - AbstractServiceDiscovery.java:188 -  [DUBBO] Metadata of instance changed, updating instance with revision 0., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,634 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795677174&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,682 INFO  [SpringApplicationShutdownHook] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:499 -  [DUBBO] Destroying instance listener of  [hxsy-admin], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,682 INFO  [SpringApplicationShutdownHook] o.a.d.r.t.netty4.NettyChannel - NettyChannel.java:261 -  [DUBBO] Close netty channel [id: 0x6fe5304b, L:/**************:53855 - R:/**************:20881], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,682 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:75 -  [DUBBO] The connection of /**************:53855 -> /**************:20881 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,689 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,689 INFO  [NettyServerWorker-8-2] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:93 -  [DUBBO] The connection of /**************:53855 -> /**************:20881 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,690 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:312 -  [DUBBO] Reset global default application from Dubbo Application[1.1](hxsy-auth) to null, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,690 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.1](hxsy-auth) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,691 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:594 -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,692 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:607 -  [DUBBO] Closing dubbo server: /**************:20880, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,693 INFO  [SpringApplicationShutdownHook] o.a.d.r.transport.AbstractServer - AbstractServer.java:129 -  [DUBBO] Close NettyServer bind /0.0.0.0:20880, export /**************:20880, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,699 INFO  [SpringApplicationShutdownHook] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:499 -  [DUBBO] Destroying instance listener of  [hxsy-admin], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,714 INFO  [SpringApplicationShutdownHook] o.a.d.r.t.netty4.NettyChannel - NettyChannel.java:261 -  [DUBBO] Close netty channel [id: 0x5fc6d2c4, L:/**************:53820 - R:/**************:20881], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,714 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:75 -  [DUBBO] The connection of /**************:53820 -> /**************:20881 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,714 WARN  [NettyServerWorker-8-1] o.a.d.r.transport.AbstractServer - ?:? -  [DUBBO] All clients has disconnected from /**************:20881. You can graceful shutdown now., dubbo version: 3.2.10, current host: **************, error code: 99-0. This may be caused by unknown error in remoting module, go to https://dubbo.apache.org/faq/99/0 to find instructions. 
2025-07-29 22:56:02,718 INFO  [NettyServerWorker-8-1] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:93 -  [DUBBO] The connection of /**************:53820 -> /**************:20881 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,718 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,718 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:312 -  [DUBBO] Reset global default application from Dubbo Application[1.1](hxsy-business) to null, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,718 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.1](hxsy-business) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,718 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:594 -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,718 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:607 -  [DUBBO] Closing dubbo server: /**************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,718 INFO  [DubboClientHandler-thread-2] o.a.d.r.e.s.h.HeaderExchangeHandler - HeaderExchangeHandler.java:80 -  [DUBBO] ChannelReadOnly set true for channel: NettyChannel [channel=[id: 0x793f214f, L:/**************:53893 - R:/**************:20882]], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,761 WARN  [Dubbo-framework-registry-notification-10-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-29 22:56:02,761 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676048&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,761 INFO  [Dubbo-framework-registry-notification-10-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753795676048&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,761 INFO  [Dubbo-framework-registry-notification-10-thread-1] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,889 INFO  [SpringApplicationShutdownHook] o.a.d.r.t.netty4.NettyChannel - NettyChannel.java:261 -  [DUBBO] Close netty channel [id: 0x793f214f, L:/**************:53893 - R:/**************:20882], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,889 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:75 -  [DUBBO] The connection of /**************:53893 -> /**************:20882 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,897 WARN  [NettyServerWorker-8-2] o.a.d.r.transport.AbstractServer - ?:? -  [DUBBO] All clients has disconnected from /**************:20882. You can graceful shutdown now., dubbo version: 3.2.10, current host: **************, error code: 99-0. This may be caused by unknown error in remoting module, go to https://dubbo.apache.org/faq/99/0 to find instructions. 
2025-07-29 22:56:02,898 INFO  [NettyServerWorker-8-2] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:93 -  [DUBBO] The connection of /**************:53893 -> /**************:20882 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,898 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,898 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:312 -  [DUBBO] Reset global default application from Dubbo Application[1.1](hxsy-admin) to null, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,898 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,898 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:594 -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,905 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:607 -  [DUBBO] Closing dubbo server: /**************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,905 INFO  [SpringApplicationShutdownHook] o.a.d.r.transport.AbstractServer - AbstractServer.java:129 -  [DUBBO] Close NettyServer bind /0.0.0.0:20882, export /**************:20882, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:02,905 INFO  [SpringApplicationShutdownHook] o.a.d.r.transport.AbstractServer - AbstractServer.java:129 -  [DUBBO] Close NettyServer bind /0.0.0.0:20881, export /**************:20881, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:03,063 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-business, instances: 0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:03,063 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:03,063 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-business, instances: 0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:03,063 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:03,063 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 847467605409d37fbcd26478e1591921 , dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:03,063 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:03,063 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,712 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: dubbo://**************:20880/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-auth&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-auth&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=17492&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795659908&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,714 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.injvm.InjvmProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-auth&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-auth&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=17492&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795659908&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,716 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,717 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,718 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&serialize.check.status=WARN, nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&serialize.check.status=WARN], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,718 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:619 -  [DUBBO] Destroy registry:nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-auth&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=17492&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&serialize.check.status=WARN, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,734 INFO  [SpringApplicationShutdownHook] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:499 -  [DUBBO] Destroying instance listener of  [hxsy-admin, hxsy-business], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,735 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,737 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.1](hxsy-auth) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,740 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:119 -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,741 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:124 -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,741 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,742 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,742 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,742 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,743 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,743 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,744 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:139 -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,744 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:332 -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,744 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:108 -  [DUBBO] Destroying global resources ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,746 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:145 -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,746 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.FrameworkExecutorRepository - FrameworkExecutorRepository.java:210 -  [DUBBO] destroying framework executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:04,748 INFO  [SpringApplicationShutdownHook] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:78 -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@1ad1178, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,019 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: dubbo://**************:20881/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795677638&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,019 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: dubbo://**************:20882/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-business&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795679453&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,020 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.injvm.InjvmProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20881&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4704&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795677638&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,020 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.injvm.InjvmProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-business&background=false&bind.ip=**************&bind.port=20882&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-business&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=20900&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753795679453&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,021 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,021 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,023 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,023 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,025 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&serialize.check.status=WARN], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,025 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&serialize.check.status=WARN], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,025 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:619 -  [DUBBO] Destroy registry:nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-business&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=20900&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,041 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:619 -  [DUBBO] Destroy registry:nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4704&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,044 INFO  [SpringApplicationShutdownHook] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:499 -  [DUBBO] Destroying instance listener of  [hxsy-admin, hxsy-business], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,046 INFO  [SpringApplicationShutdownHook] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:499 -  [DUBBO] Destroying instance listener of  [hxsy-admin, hxsy-business], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,047 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,048 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,048 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,050 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.1](hxsy-business) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,053 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:119 -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,053 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:124 -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,054 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,054 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:119 -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,054 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,055 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:124 -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,055 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,055 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,056 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,056 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,056 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,056 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,057 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,057 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [], dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,058 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,058 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:139 -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,058 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,058 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:332 -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,059 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:108 -  [DUBBO] Destroying global resources ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,059 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:139 -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,060 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:332 -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,061 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:108 -  [DUBBO] Destroying global resources ..., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,062 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:145 -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,063 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.FrameworkExecutorRepository - FrameworkExecutorRepository.java:210 -  [DUBBO] destroying framework executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,064 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:145 -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,065 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.FrameworkExecutorRepository - FrameworkExecutorRepository.java:210 -  [DUBBO] destroying framework executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,067 INFO  [SpringApplicationShutdownHook] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:78 -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@4b14b8, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,067 INFO  [SpringApplicationShutdownHook] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:78 -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@72669f, dubbo version: 3.2.10, current host: **************
2025-07-29 22:56:05,076 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 22:56:05,076 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 22:56:05,243 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:95 - De-registering from Nacos Server now...
2025-07-29 22:56:05,272 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:115 - De-registration finished.
2025-07-29 22:56:05,610 INFO  [SpringApplicationShutdownHook] o.s.s.quartz.SchedulerFactoryBean - SchedulerFactoryBean.java:847 - Shutting down Quartz Scheduler
2025-07-29 22:56:05,611 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 22:56:05,611 INFO  [SpringApplicationShutdownHook] o.s.s.quartz.SchedulerFactoryBean - SchedulerFactoryBean.java:847 - Shutting down Quartz Scheduler
2025-07-29 22:56:05,611 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 22:56:05,611 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-29 22:56:05,611 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-29 22:56:05,612 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 22:56:05,612 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-29 22:56:05,626 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:95 - De-registering from Nacos Server now...
2025-07-29 22:56:05,627 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:95 - De-registering from Nacos Server now...
2025-07-29 22:56:05,656 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:115 - De-registration finished.
2025-07-29 22:56:05,657 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:115 - De-registration finished.
2025-07-29 22:56:05,663 INFO  [SpringApplicationShutdownHook] c.q.cos.http.DefaultCosHttpClient - DefaultCosHttpClient.java:158 - shutdown stackTrace:
Class: com.qcloud.cos.http.DefaultCosHttpClient, Method: shutdown, Line: 147
Class: com.qcloud.cos.COSClient, Method: shutdown, Line: 183
Class: sun.reflect.NativeMethodAccessorImpl, Method: invoke0, Line: -2
Class: sun.reflect.NativeMethodAccessorImpl, Method: invoke, Line: 62
Class: sun.reflect.DelegatingMethodAccessorImpl, Method: invoke, Line: 43
Class: java.lang.reflect.Method, Method: invoke, Line: 498
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: invokeCustomDestroyMethod, Line: 319
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: destroy, Line: 253
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroyBean, Line: 587
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingleton, Line: 559
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingleton, Line: 1163
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingletons, Line: 520
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingletons, Line: 1156
Class: org.springframework.context.support.AbstractApplicationContext, Method: destroyBeans, Line: 1106
Class: org.springframework.context.support.AbstractApplicationContext, Method: doClose, Line: 1075
Class: org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext, Method: doClose, Line: 174
Class: org.springframework.context.support.AbstractApplicationContext, Method: close, Line: 1021
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: closeAndWait, Line: 145
Class: java.lang.Iterable, Method: forEach, Line: 75
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: run, Line: 114
Class: java.lang.Thread, Method: run, Line: 750
2025-07-29 22:56:05,663 INFO  [SpringApplicationShutdownHook] c.q.cos.http.DefaultCosHttpClient - DefaultCosHttpClient.java:158 - shutdown stackTrace:
Class: com.qcloud.cos.http.DefaultCosHttpClient, Method: shutdown, Line: 147
Class: com.qcloud.cos.COSClient, Method: shutdown, Line: 183
Class: sun.reflect.NativeMethodAccessorImpl, Method: invoke0, Line: -2
Class: sun.reflect.NativeMethodAccessorImpl, Method: invoke, Line: 62
Class: sun.reflect.DelegatingMethodAccessorImpl, Method: invoke, Line: 43
Class: java.lang.reflect.Method, Method: invoke, Line: 498
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: invokeCustomDestroyMethod, Line: 319
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: destroy, Line: 253
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroyBean, Line: 587
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingleton, Line: 559
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingleton, Line: 1163
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingletons, Line: 520
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingletons, Line: 1156
Class: org.springframework.context.support.AbstractApplicationContext, Method: destroyBeans, Line: 1106
Class: org.springframework.context.support.AbstractApplicationContext, Method: doClose, Line: 1075
Class: org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext, Method: doClose, Line: 174
Class: org.springframework.context.support.AbstractApplicationContext, Method: close, Line: 1021
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: closeAndWait, Line: 145
Class: java.lang.Iterable, Method: forEach, Line: 75
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: run, Line: 114
Class: java.lang.Thread, Method: run, Line: 750
2025-07-29 22:56:05,667 INFO  [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2043 - {dataSource-1} closing ...
2025-07-29 22:56:05,669 INFO  [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2116 - {dataSource-1} closed
2025-07-29 22:56:05,737 INFO  [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2043 - {dataSource-1} closing ...
2025-07-29 22:56:05,740 INFO  [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2116 - {dataSource-1} closed
