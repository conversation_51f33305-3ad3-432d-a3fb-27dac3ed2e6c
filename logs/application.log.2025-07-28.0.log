2025-07-28 22:55:26,632 INFO  [main] o.a.d.s.b.c.e.WelcomeLogoApplicationListener - WelcomeLogoApplicationListener.java:62 -  [DUBBO] 

 :: Dubbo (v3.2.10) : https://github.com/apache/dubbo
 :: Discuss group : <EMAIL>|EE556F110878F29190D18E16684784AB
, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:26,753 INFO  [main] c.a.n.c.env.SearchableProperties - SearchableProperties.java:197 - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-07-28 22:55:27,990 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 22:55:27,991 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 22:55:31,901 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-admin] & group[DEFAULT_GROUP]
2025-07-28 22:55:31,932 WARN  [main] c.a.c.n.c.NacosPropertySourceBuilder - NacosPropertySourceBuilder.java:87 - Ignore the empty nacos configuration and get it based on dataId[hxsy-admin.properties] & group[DEFAULT_GROUP]
2025-07-28 22:55:31,935 INFO  [main] o.s.c.b.c.PropertySourceBootstrapConfiguration - PropertySourceBootstrapConfiguration.java:109 - Located property source: [BootstrapPropertySource {name='bootstrapProperties-hxsy-admin.properties,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-hxsy-admin,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-config.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-rpc.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-cache.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource-sharding.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-datasource.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-base.yaml,DEFAULT_GROUP'}]
2025-07-28 22:55:31,981 INFO  [main] cn.hxsy.HxsyAdminApplication - SpringApplication.java:631 - No active profile set, falling back to 1 default profile: "default"
2025-07-28 22:55:33,550 INFO  [main] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:86 -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,585 INFO  [main] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:96 -  [DUBBO] Creating global shared handler ..., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,778 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,785 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,857 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,858 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,887 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:107 -  [DUBBO] Serialize check serializable: true, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,888 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,898 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,899 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:33,924 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,149 INFO  [main] o.a.dubbo.rpc.model.ApplicationModel - ApplicationModel.java:107 -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,150 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,167 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,168 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,176 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,177 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,177 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,178 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,198 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:116 -  [DUBBO] Use default application: Dubbo Application[1.1](unknown), dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,199 INFO  [main] o.apache.dubbo.rpc.model.ScopeModel - ModuleModel.java:63 -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,209 INFO  [main] o.a.d.c.c.AbstractConfigManager - AbstractConfigManager.java:143 -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,215 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,216 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:135 -  [DUBBO] Read serialize allow list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.allowlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,216 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo/3.2.10/dubbo-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,217 INFO  [main] o.a.d.c.u.SerializeSecurityConfigurator - SerializeSecurityConfigurator.java:159 -  [DUBBO] Read serialize blocked list from jar:file:/D:/Work/App/Maven/warehouse-3.3.9/org/apache/dubbo/dubbo-common/3.2.10/dubbo-common-3.2.10.jar!/security/serialize.blockedlist, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,219 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:126 -  [DUBBO] Use default module model of target application: Dubbo Module[1.1.1], dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,221 INFO  [main] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:130 -  [DUBBO] Bind Dubbo Module[1.1.1] to spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@4b14b8, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:34,681 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-28 22:55:34,688 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-28 22:55:34,823 INFO  [main] o.s.d.r.c.RepositoryConfigurationDelegate - RepositoryConfigurationDelegate.java:201 - Finished Spring Data repository scanning in 104 ms. Found 0 Redis repository interfaces.
2025-07-28 22:55:35,115 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:309 -  [DUBBO] BeanNameGenerator bean can't be found in BeanFactory with name [org.springframework.context.annotation.internalConfigurationBeanNameGenerator], dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,115 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:311 -  [DUBBO] BeanNameGenerator will be a instance of org.springframework.context.annotation.AnnotationBeanNameGenerator , it maybe a potential problem on bean name generation., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,185 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:257 -  [DUBBO] Found 5 classes annotated by Dubbo @Service under package [cn.hxsy]: [cn.hxsy.rpc.company.CompWxCodeRpcServiceImpl, cn.hxsy.rpc.user.CompanyQyRelationRpcServiceImpl, cn.hxsy.rpc.user.CustomerRpcServiceImpl, cn.hxsy.rpc.user.SystemUserQyRelationRpcServiceImpl, cn.hxsy.rpc.user.UserInfoRpcServiceImpl], dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,203 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,204 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,205 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.CustomerRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,205 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,206 INFO  [main] o.a.d.c.s.b.f.a.ServiceAnnotationPostProcessor - ServiceAnnotationPostProcessor.java:650 -  [DUBBO] Register ServiceBean[ServiceBean:cn.hxsy.api.user.service.UserInfoRpcService:1.0.0:]: Root bean: class [org.apache.dubbo.config.spring.ServiceBean]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,508 INFO  [main] o.s.cloud.context.scope.GenericScope - GenericScope.java:283 - BeanFactory id=266e4f9a-6b51-346a-a1d8-6d82fdce843d
2025-07-28 22:55:35,658 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: tencentWxInfoRpcService = ReferenceBean:cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0() at cn.hxsy.api.app.service.TencentWxInfoRpcService cn.hxsy.controller.UserController.tencentWxInfoRpcService, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:35,701 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - ReferenceAnnotationBeanPostProcessor.java:556 -  [DUBBO] Register dubbo reference bean: campPeriodRpcService = ReferenceBean:cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0() at private cn.hxsy.api.user.service.CampPeriodRpcService cn.hxsy.service.impl.CustomerCourseRelationServiceImpl.campPeriodRpcService, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:36,955 INFO  [main] o.a.d.c.s.b.f.a.ReferenceAnnotationBeanPostProcessor - AbstractAnnotationBeanPostProcessor.java:265 -  [DUBBO] class org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor was destroying!, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:37,002 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 22:55:37,007 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 22:55:37,008 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$624/0x1d55aa18] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 22:55:37,008 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 22:55:37,011 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'stringToNoneShardingStrategyConfigurationConverter' of type [org.apache.shardingsphere.spring.boot.converter.StringToNoneShardingStrategyConfigurationConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 22:55:37,018 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'spring.shardingsphere-org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration' of type [org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 22:55:37,791 INFO  [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - PostProcessorRegistrationDelegate.java:376 - Bean 'org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration' of type [org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration$$EnhancerBySpringCGLIB$$29d33a7c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-28 22:55:39,003 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:108 - Tomcat initialized with port(s): 10100 (http)
2025-07-28 22:55:39,032 INFO  [main] o.a.catalina.core.StandardService - DirectJDKLog.java:173 - Starting service [Tomcat]
2025-07-28 22:55:39,032 INFO  [main] o.a.catalina.core.StandardEngine - DirectJDKLog.java:173 - Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-07-28 22:55:39,238 INFO  [main] o.a.c.c.C.[Tomcat].[localhost].[/] - DirectJDKLog.java:173 - Initializing Spring embedded WebApplicationContext
2025-07-28 22:55:39,238 INFO  [main] o.s.b.w.s.c.ServletWebServerApplicationContext - ServletWebServerApplicationContext.java:292 - Root WebApplicationContext: initialization completed in 7231 ms
2025-07-28 22:55:39,389 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:101 -  [DUBBO] loading dubbo config beans ..., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,394 INFO  [main] o.a.d.c.s.c.DubboConfigBeanInitializer - DubboConfigBeanInitializer.java:127 -  [DUBBO] dubbo config beans are loaded., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,488 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : nacos] supports as the config center, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,493 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the config center, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,501 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:366 -  [DUBBO] use registry as config-center: <dubbo:config-center highestPriority="false" id="config-center-nacos-*************-8848" address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, password=nacos, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,529 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 22:55:39,529 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 22:55:39,947 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,947 INFO  [main] o.a.d.c.config.ConfigurationUtils - ConfigurationUtils.java:210 -  [DUBBO] Config center was specified, but no config item found., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,990 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:321 -  [DUBBO] The current configurations or effective configurations are as follows:, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,991 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:application enableFileCache="true" executorManagementMode="isolation" serializeCheckStatus="WARN" parameters="{}" name="hxsy-admin" qosAcceptForeignIp="false" qosEnable="false" protocol="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,992 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:protocol preferSerialization="fastjson2,hessian2" port="-1" name="dubbo" />, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,992 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:39,993 INFO  [main] o.a.d.config.context.ConfigManager - ConfigManager.java:323 -  [DUBBO] <dubbo:ssl />, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:40,028 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.0] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:40,032 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:146 -  [DUBBO] Dubbo Module[1.1.1] has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:40,288 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:555 -  [DUBBO] No value is configured in the registry, the MetadataReportFactory extension[name : nacos] supports as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:40,288 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:565 -  [DUBBO] The registry[<dubbo:registry port="8848" parameters="{namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, password=nacos, username=nacos, group=dubbo}" address="nacos://*************:8848" protocol="nacos" />] will be used as the metadata center, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:40,293 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:523 -  [DUBBO] use registry as metadata-center: <dubbo:metadata-report address="nacos://*************:8848" protocol="nacos" port="8848" parameters="{password=nacos, namespace=bc83bf40-3304-4071-a0dc-3dfea4516216, client=null, username=nacos, group=dubbo}" />, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:40,335 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 22:55:40,335 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 22:55:40,673 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:236 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) has been initialized!, dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:41,507 ERROR [main] c.a.d.pool.DruidAbstractDataSource - DruidAbstractDataSource.java:1094 - maxIdle is deprecated
2025-07-28 22:55:41,517 INFO  [pool-7-thread-1] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2043 - {dataSource-0} closing ...
2025-07-28 22:55:41,735 INFO  [main] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:990 - {dataSource-1,DataSource-30947243} inited
2025-07-28 22:55:45,825 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.ColumnMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-28 22:55:45,848 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.ColumnMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-28 22:55:45,898 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.CompanyMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-28 22:55:45,916 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.CompanyMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-28 22:55:46,561 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.HeadquartersMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-28 22:55:46,567 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.HeadquartersMapper.deleteById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.DeleteById]
2025-07-28 22:55:46,574 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.HeadquartersMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-28 22:55:46,607 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.SalesGroupMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-07-28 22:55:46,621 WARN  [main] c.b.m.core.injector.AbstractMethod - AbstractMethod.java:343 - [cn.hxsy.dao.SalesGroupMapper.selectById] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectById]
2025-07-28 22:55:48,347 INFO  [main] org.redisson.Version - Version.java:41 - Redisson 3.22.1
2025-07-28 22:55:50,214 INFO  [redisson-netty-2-8] o.r.c.p.MasterPubSubConnectionPool - ConnectionPool.java:140 - 1 connections initialized for *************/*************:6379
2025-07-28 22:55:51,551 INFO  [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool - ConnectionPool.java:140 - 24 connections initialized for *************/*************:6379
2025-07-28 22:55:52,870 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:77 - 
当前服务实例工作区数据中心CentID：7
2025-07-28 22:55:53,325 INFO  [main] c.h.c.config.snowId.IdWorkerConfig - IdWorkerConfig.java:106 - 
当前服务实例工作区ID：1
2025-07-28 22:55:53,407 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.user.service.CampPeriodRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:54,816 INFO  [main] o.a.d.c.s.reference.ReferenceCreator - ReferenceCreator.java:98 -  [DUBBO] The configBean[type:ReferenceConfig<cn.hxsy.api.app.service.TencentWxInfoRpcService>] has been built., dubbo version: 3.2.10, current host: **************
2025-07-28 22:55:57,904 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1220 - Using default implementation for ThreadExecutor
2025-07-28 22:55:57,929 INFO  [main] o.quartz.core.SchedulerSignalerImpl - SchedulerSignalerImpl.java:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-28 22:55:57,929 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:229 - Quartz Scheduler v.2.3.2 created.
2025-07-28 22:55:57,930 INFO  [main] org.quartz.simpl.RAMJobStore - RAMJobStore.java:155 - RAMJobStore initialized.
2025-07-28 22:55:57,932 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-28 22:55:57,932 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1374 - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-28 22:55:57,932 INFO  [main] org.quartz.impl.StdSchedulerFactory - StdSchedulerFactory.java:1378 - Quartz scheduler version: 2.3.2
2025-07-28 22:55:57,934 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4a5741
2025-07-28 22:55:58,856 INFO  [main] o.s.b.w.e.tomcat.TomcatWebServer - TomcatWebServer.java:220 - Tomcat started on port(s): 10100 (http) with context path ''
2025-07-28 22:55:58,878 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 22:55:58,878 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 22:55:59,233 INFO  [main] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:76 - nacos registry, DEFAULT_GROUP hxsy-admin **************:10100 register finished
2025-07-28 22:56:00,993 INFO  [main] o.s.s.quartz.SchedulerFactoryBean - SchedulerFactoryBean.java:729 - Starting Quartz Scheduler now
2025-07-28 22:56:00,994 INFO  [main] org.quartz.core.QuartzScheduler - QuartzScheduler.java:547 - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-28 22:56:01,001 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:01,002 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1285 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) is starting., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,269 WARN  [main] o.apache.dubbo.config.ServiceConfig - ?:? -  [DUBBO] Use random available port(20880) for protocol dubbo, dubbo version: 3.2.10, current host: **************, error code: 5-8. This may be caused by , go to https://dubbo.apache.org/faq/5/8 to find instructions. 
2025-07-28 22:56:02,431 INFO  [main] o.a.d.c.u.SerializeSecurityManager - SerializeSecurityManager.java:83 -  [DUBBO] Serialize check level: WARN, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,439 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,448 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753714561415&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,450 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,456 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,553 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,724 INFO  [main] o.a.d.r.transport.AbstractServer - AbstractServer.java:71 -  [DUBBO] Start NettyServer bind /0.0.0.0:20880, export /**************:20880, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,800 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-admin-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:02,878 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 22:56:02,879 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 22:56:03,232 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,263 INFO  [main] o.a.d.r.c.m.MigrationRuleListener - MigrationRuleListener.java:106 -  [DUBBO] Listening for migration rules on dataId hxsy-admin.migration, group DUBBO_SERVICEDISCOVERY_MIGRATION, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,302 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,303 INFO  [main] o.a.d.q.protocol.QosProtocolWrapper - QosProtocolWrapper.java:109 -  [DUBBO] qos won't be started because it is disabled. Please check dubbo.application.qos.enable is configured either in system property, dubbo.properties or XML/spring-boot configuration., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,342 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-28 22:56:03,350 INFO  [main] c.a.n.p.a.s.c.ClientAuthPluginManager - ClientAuthPluginManager.java:56 - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-28 22:56:03,707 INFO  [main] o.a.d.r.n.NacosNamingServiceWrapper - NacosNamingServiceWrapper.java:71 -  [DUBBO] Nacos batch register enable: true, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,709 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-admin-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,709 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,756 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:03,756 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,792 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:03,793 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,813 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.SystemUserQyRelationRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService, dubbo=2.0.2, application=hxsy-admin, side=provider, version=1.0.0, pid=4688, release=3.2.10, anyhost=true, executor-management-mode=isolation, file-cache=true, methods=querySystemQyUserInner, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20880, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753714561415}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.SystemUserQyRelationRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=querySystemQyUserInner, parameterTypes=[cn.hxsy.base.request.SystemUserQyRelationRequest], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,817 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,853 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,876 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753714563862&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,877 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:03,910 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,110 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,148 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:04,149 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,183 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:04,184 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,185 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,219 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,232 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.CustomerRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753714564223&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,233 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CustomerRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,267 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CustomerRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,306 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CompWxCodeRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={interface=cn.hxsy.api.user.service.CompWxCodeRpcService, dubbo=2.0.2, application=hxsy-admin, side=provider, version=1.0.0, pid=4688, release=3.2.10, anyhost=true, executor-management-mode=isolation, file-cache=true, methods=saveCompWxCode, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20880, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753714563862}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.CompWxCodeRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=saveCompWxCode, parameterTypes=[java.lang.Integer, java.lang.String, java.lang.String], returnType=boolean]]], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,467 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,504 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:04,504 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,540 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:04,540 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,541 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.CustomerRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,576 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.CustomerRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,592 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753714564583&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,593 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,626 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,739 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CustomerRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={interface=cn.hxsy.api.user.service.CustomerRpcService, dubbo=2.0.2, application=hxsy-admin, side=provider, version=1.0.0, pid=4688, release=3.2.10, anyhost=true, executor-management-mode=isolation, file-cache=true, methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20880, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753714564223}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.CustomerRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=updateRedPacketAndUseStatus, parameterTypes=[cn.hxsy.base.request.wxPayRequest], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=getRedPacketStatus, parameterTypes=[cn.hxsy.base.request.wxPayRequest], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=saveReceiveRedPacket, parameterTypes=[java.lang.Long, java.lang.Long, java.lang.Long, java.lang.Long, java.lang.String, java.lang.Integer], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,826 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,860 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:04,861 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,896 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:04,897 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,897 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,933 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,948 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service cn.hxsy.api.user.service.UserInfoRpcService to local registry url : injvm://127.0.0.1/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=provider&timestamp=1753714564936&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,948 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.UserInfoRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:04,981 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:907 -  [DUBBO] Register dubbo service cn.hxsy.api.user.service.UserInfoRpcService url dubbo://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0 to registry *************:8848, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,105 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CompanyQyRelationRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService, dubbo=2.0.2, application=hxsy-admin, side=provider, version=1.0.0, pid=4688, release=3.2.10, anyhost=true, executor-management-mode=isolation, file-cache=true, methods=query, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20880, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753714564583}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.CompanyQyRelationRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=query, parameterTypes=[cn.hxsy.api.qy.request.QyAppReq], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,179 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: provider://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,218 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:05,219 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,255 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:05,256 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,257 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:412 -  [DUBBO] Try to register interface application mapping for service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,289 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:417 -  [DUBBO] Successfully registered interface application mapping for service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,290 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:355 -  [DUBBO] Dubbo Module[1.1.0] is starting., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,292 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.0] has started., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,482 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:307 -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.UserInfoRpcService', version='1.0.0', group='', side='provider'}; definition: FullServiceDefinition{parameters={interface=cn.hxsy.api.user.service.UserInfoRpcService, dubbo=2.0.2, application=hxsy-admin, side=provider, version=1.0.0, pid=4688, release=3.2.10, anyhost=true, executor-management-mode=isolation, file-cache=true, methods=PcLogin,query,register, deprecated=false, service-name-mapping=true, qos.enable=false, generic=false, bind.port=20880, revision=1.0.0, serialize.check.status=WARN, bind.ip=**************, prefer.serialization=fastjson2,hessian2, background=false, dynamic=true, qos.accept.foreign.ip=false, timestamp=1753714564936}} ServiceDefinition [canonicalName=cn.hxsy.api.user.service.UserInfoRpcService, codeSource=file:/D:/Work/HXSY/hxsy-parent/hxsy-common/hxsy-api/target/classes/, methods=[MethodDefinition [name=register, parameterTypes=[cn.hxsy.api.user.model.request.UserRegisterRequest], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=query, parameterTypes=[cn.hxsy.api.user.model.request.UserInfoRequest], returnType=cn.hxsy.base.response.Result], MethodDefinition [name=PcLogin, parameterTypes=[cn.hxsy.api.user.model.request.PcLoginRequest], returnType=cn.hxsy.base.response.Result]]], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,520 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,825 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714565305&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,860 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:05,861 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714565305&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,872 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,909 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:05,909 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714565305&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,910 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:05,979 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - AbstractRegistry.java:344 -  [DUBBO] Loaded registry cache file C:\Users\<USER>\.dubbo\dubbo-registry-hxsy-admin-*************-8848.cache, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,147 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin,hxsy-business for service key cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,200 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-1] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.app.service.TencentWxInfoRpcService, apps: [hxsy-admin, hxsy-business]}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,246 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,246 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,247 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.app.service.TencentWxInfoRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,248 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.app.service.TencentWxInfoRpcService', version='1.0.0', group='', side='consumer'}; definition: {interface=cn.hxsy.api.app.service.TencentWxInfoRpcService, dubbo=2.0.2, application=hxsy-admin, side=consumer, version=1.0.0, pid=4688, release=3.2.10, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=queryWxConfig, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753714565305}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,273 INFO  [main] o.a.d.registry.nacos.NacosRegistry - NacosRegistry.java:187 -  [DUBBO] Please set 'dubbo.registry.parameters.register-consumer-url=true' to turn on consumer url registration., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,334 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:452 -  [DUBBO] Subscribe: consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714566264&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,371 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:06,371 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714566264&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,371 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,408 WARN  [main] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:06,409 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714566264&unloadClusterRelated=false&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,410 INFO  [main] o.a.d.r.i.RegistryDirectory - RegistryDirectory.java:363 -  [DUBBO] Received invokers changed event from registry. Registry type: interface. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,641 INFO  [main] o.a.d.r.c.ServiceDiscoveryRegistry - ServiceDiscoveryRegistry.java:340 -  [DUBBO] Trying to subscribe from apps hxsy-admin,hxsy-business for service key cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0, , dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,644 INFO  [main] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,644 INFO  [main] o.a.d.r.c.m.MigrationRuleHandler - MigrationRuleHandler.java:90 -  [DUBBO] Succeed Migrated to APPLICATION_FIRST mode. Service Name: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,644 INFO  [main] o.a.dubbo.config.ReferenceConfig - ReferenceConfig.java:489 -  [DUBBO] Referred dubbo service: [cn.hxsy.api.user.service.CampPeriodRpcService]. it's not GenericService reference, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,644 INFO  [main] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:362 -  [DUBBO] Dubbo Module[1.1.1] has started., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,644 INFO  [DubboSaveMetadataReport-thread-1] o.a.d.m.s.nacos.NacosMetadataReport - AbstractMetadataReport.java:348 -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='hxsy-admin', serviceInterface='cn.hxsy.api.user.service.CampPeriodRpcService', version='1.0.0', group='', side='consumer'}; definition: {interface=cn.hxsy.api.user.service.CampPeriodRpcService, dubbo=2.0.2, application=hxsy-admin, side=consumer, version=1.0.0, pid=4688, release=3.2.10, executor-management-mode=isolation, file-cache=true, register.ip=**************, methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod, check=false, qos.enable=false, timeout=3000, unloadClusterRelated=false, revision=1.0.0, serialize.check.status=WARN, background=false, sticky=false, qos.accept.foreign.ip=false, timestamp=1753714566264}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,645 INFO  [main] o.a.d.c.d.DefaultMetricsServiceExporter - DefaultMetricsServiceExporter.java:116 -  [DUBBO] The MetricsConfig not exist, will not export metrics service., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,652 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:209 -  [DUBBO] org.apache.dubbo.metadata.MetadataServiceService Port hasn't been set will use default protocol defined in protocols., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,653 INFO  [main] o.a.d.c.b.b.InternalServiceConfigBuilder - InternalServiceConfigBuilder.java:266 -  [DUBBO] Using dubbo protocol to export org.apache.dubbo.metadata.MetadataService service on port -1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,678 INFO  [Dubbo-framework-mapping-refreshing-scheduler-thread-2] o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener - ServiceDiscoveryRegistry.java:413 -  [DUBBO] Received mapping notification from meta server, {serviceKey: cn.hxsy.api.user.service.CampPeriodRpcService, apps: [hxsy-admin, hxsy-business]}, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,700 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:962 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to local registry url : injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753714566671&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,701 INFO  [main] o.apache.dubbo.config.ServiceConfig - ServiceConfig.java:920 -  [DUBBO] Export dubbo service org.apache.dubbo.metadata.MetadataService to url dubbo://**************:20880/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753714566671&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,703 INFO  [main] o.a.d.c.m.ConfigurableMetadataServiceExporter - ConfigurableMetadataServiceExporter.java:80 -  [DUBBO] The MetadataService exports urls : [dubbo://**************:20880/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753714566671&version=1.0.0], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,705 INFO  [main] o.a.d.r.c.m.ServiceInstanceMetadataUtils - ServiceInstanceMetadataUtils.java:207 -  [DUBBO] Start registering instance address to registry., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,728 INFO  [main] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1298 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) is ready., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,740 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753714561041, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,741 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,779 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.SystemUserQyRelationRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753714561041, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,780 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753714563862, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,780 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,814 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompWxCodeRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753714563862, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,816 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CustomerRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753714564223, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,816 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,849 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CustomerRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753714564223, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,850 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753714564583, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,851 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,883 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.CompanyQyRelationRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753714564583, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,884 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0 to registry service-discovery-registry://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&registry=nacos&release=3.2.10&serialize.check.status=WARN&timestamp=1753714564936, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,885 INFO  [main] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:424 -  [DUBBO] Register: dubbo://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,917 INFO  [main] o.a.d.r.integration.RegistryProtocol - RegistryProtocol.java:1118 -  [DUBBO] Registered dubbo service cn.hxsy.api.user.service.UserInfoRpcService:1.0.0 url dubbo://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0 to registry nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN&timestamp=1753714564936, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,928 INFO  [main] o.apache.dubbo.metadata.MetadataInfo - MetadataInfo.java:203 -  [DUBBO] metadata revision changed: null -> 36cb05d55632269bed006dcf878172ad, app: hxsy-admin, services: 5, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:06,972 INFO  [main] cn.hxsy.HxsyAdminApplication - StartupInfoLogger.java:61 - Started HxsyAdminApplication in 42.877 seconds (JVM running for 43.796)
2025-07-28 22:56:06,977 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-admin.properties, group=DEFAULT_GROUP
2025-07-28 22:56:06,979 INFO  [main] c.a.c.n.r.NacosContextRefresher - NacosContextRefresher.java:129 - [Nacos Config] Listening config: dataId=hxsy-admin, group=DEFAULT_GROUP
2025-07-28 22:56:07,330 WARN  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:07,330 WARN  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:07,331 INFO  [Dubbo-framework-registry-notification-0-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,331 INFO  [Dubbo-framework-registry-notification-2-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,428 WARN  [Dubbo-framework-registry-notification-6-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:07,428 INFO  [Dubbo-framework-registry-notification-6-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,430 WARN  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:07,431 INFO  [Dubbo-framework-registry-notification-4-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,434 WARN  [Dubbo-framework-registry-notification-8-thread-1] o.a.d.registry.nacos.NacosRegistry - ?:? -  [DUBBO] Received empty url address list and empty protection is disabled, will clear current available addresses, dubbo version: 3.2.10, current host: **************, error code: 1-37. This may be caused by , go to https://dubbo.apache.org/faq/1/37 to find instructions. 
2025-07-28 22:56:07,435 INFO  [Dubbo-framework-registry-notification-8-thread-1] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:555 -  [DUBBO] Notify urls for subscribe url provider://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0, url size: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,526 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-admin, instances: 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,541 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.apache.dubbo.rpc.model.ModuleModel - ModuleModel.java:210 -  [DUBBO] Dynamically registering consumer model hxsy-admin/org.apache.dubbo.metadata.MetadataService:1.0.0 into model Dubbo Module[1.1.0], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,599 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:60 -  [DUBBO] The connection of /**************:63669 -> /**************:20880 is established., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,599 INFO  [NettyServerWorker-8-1] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:76 -  [DUBBO] The connection of /**************:63669 -> /**************:20880 is established., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,602 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.transport.AbstractClient - AbstractClient.java:254 -  [DUBBO] Successfully connect to server /**************:20880 from NettyClient ************** using dubbo version 3.2.10, channel is NettyChannel [channel=[id: 0x9e06ec7c, L:/**************:63669 - R:/**************:20880]], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,603 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.transport.AbstractClient - AbstractClient.java:81 -  [DUBBO] Start NettyClient /************** connect to the server /**************:20880, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,660 INFO  [NettyServerWorker-8-1] o.a.d.rpc.protocol.dubbo.DubboCodec - DubboCodec.java:247 -  [DUBBO] Because thread pool isolation is enabled on the dubbo protocol, the body can only be decoded on the io thread, and the parameter[decode.in.io.thread] will be ignored, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,716 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.t.netty4.NettyChannel - NettyChannel.java:261 -  [DUBBO] Close netty channel [id: 0x9e06ec7c, L:/**************:63669 - R:/**************:20880], dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,719 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 1 unique working revisions: 36cb05d55632269bed006dcf878172ad , dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,720 INFO  [NettyClientWorker-9-1] o.a.d.r.t.netty4.NettyClientHandler - NettyClientHandler.java:75 -  [DUBBO] The connection of /**************:63669 -> /**************:20880 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,720 WARN  [NettyServerWorker-8-1] o.a.d.r.transport.AbstractServer - ?:? -  [DUBBO] All clients has disconnected from /**************:20880. You can graceful shutdown now., dubbo version: 3.2.10, current host: **************, error code: 99-0. This may be caused by unknown error in remoting module, go to https://dubbo.apache.org/faq/99/0 to find instructions. 
2025-07-28 22:56:07,721 INFO  [NettyServerWorker-8-1] o.a.d.r.t.netty4.NettyServerHandler - NettyServerHandler.java:93 -  [DUBBO] The connection of /**************:63669 -> /**************:20880 is disconnected., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,723 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,726 WARN  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ?:? -  [DUBBO] Received url with EMPTY protocol from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), will clear all available addresses., dubbo version: 3.2.10, current host: **************, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-28 22:56:07,728 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,728 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,728 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,729 WARN  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ?:? -  [DUBBO] Received url with EMPTY protocol from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [hxsy-admin, hxsy-business])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), will clear all available addresses., dubbo version: 3.2.10, current host: **************, error code: 4-1. This may be caused by , go to https://dubbo.apache.org/faq/4/1 to find instructions. 
2025-07-28 22:56:07,729 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.m.DefaultMigrationAddressComparator - DefaultMigrationAddressComparator.java:56 -  [DUBBO] No instance address available, stop compare., dubbo version: 3.2.10, current host: **************
2025-07-28 22:56:07,729 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.ServiceDiscoveryRegistryDirectory - ServiceDiscoveryRegistryDirectory.java:388 -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0. Urls Size : 1. Invokers Size : 0. Available Size: 0. Available Invokers : empty, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,596 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:136 - [NotifyCenter] Start destroying Publisher
2025-07-28 22:57:31,596 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:102 - [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-28 22:57:31,596 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:79 -  [DUBBO] Run shutdown hook now., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,597 WARN  [Thread-8] c.a.nacos.common.notify.NotifyCenter - NotifyCenter.java:153 - [NotifyCenter] Destruction of the end
2025-07-28 22:57:31,598 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:112 -  [DUBBO] Waiting for modules(Dubbo Application[1.1](hxsy-admin)) managed by Spring to be shutdown., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,599 WARN  [Thread-2] c.a.n.c.http.HttpClientBeanHolder - HttpClientBeanHolder.java:111 - [HttpClientBeanHolder] Destruction of the end
2025-07-28 22:57:31,599 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,600 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,612 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:135 -  [DUBBO] Dubbo wait for application(Dubbo Application[1.1](hxsy-admin)) managed by Spring to be shutdown failed, time usage: 14ms, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,613 INFO  [DubboShutdownHook] o.a.dubbo.config.DubboShutdownHook - DubboShutdownHook.java:141 -  [DUBBO] Dubbo shutdown hooks execute now. Dubbo Application[1.1](hxsy-admin), dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,638 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,671 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,704 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,738 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:437 -  [DUBBO] Unregister: dubbo://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?application=hxsy-admin&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&prefer.serialization=fastjson2,hessian2&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,774 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20880/cn.hxsy.api.user.service.SystemUserQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.SystemUserQyRelationRpcService&methods=querySystemQyUserInner&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714561415&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,842 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20880/cn.hxsy.api.user.service.CompWxCodeRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompWxCodeRpcService&methods=saveCompWxCode&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714563862&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,908 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20880/cn.hxsy.api.user.service.CustomerRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CustomerRpcService&methods=getRedPacketStatus,saveReceiveRedPacket,updateRedPacketAndUseStatus&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564223&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:31,975 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20880/cn.hxsy.api.user.service.CompanyQyRelationRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.CompanyQyRelationRpcService&methods=query&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564583&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,040 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: provider://**************:20880/cn.hxsy.api.user.service.UserInfoRpcService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&category=configurators&check=false&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&interface=cn.hxsy.api.user.service.UserInfoRpcService&methods=PcLogin,query,register&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&service-name-mapping=true&side=provider&timestamp=1753714564936&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,107 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.user.service.CampPeriodRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.user.service.CampPeriodRpcService&methods=countCampPeriodBySalesGroupId,getCampGroupAndCourses,getCampPeriodsByIds,getCampTodayCourse,queryCampPeriod&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714566264&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,238 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:467 -  [DUBBO] Unsubscribe: consumer://**************/cn.hxsy.api.app.service.TencentWxInfoRpcService?application=hxsy-admin&background=false&category=providers,configurators,routers&check=false&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&interface=cn.hxsy.api.app.service.TencentWxInfoRpcService&methods=queryWxConfig&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&revision=1.0.0&serialize.check.status=WARN&side=consumer&sticky=false&timeout=3000&timestamp=1753714565305&unloadClusterRelated=false&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,362 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,363 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:312 -  [DUBBO] Reset global default application from Dubbo Application[1.1](hxsy-admin) to null, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,363 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,394 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:594 -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,395 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - DubboProtocol.java:607 -  [DUBBO] Closing dubbo server: /**************:20880, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,397 INFO  [SpringApplicationShutdownHook] o.a.d.r.transport.AbstractServer - AbstractServer.java:129 -  [DUBBO] Close NettyServer bind /0.0.0.0:20880, export /**************:20880, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,992 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:343 -  [DUBBO] Received instance notification, serviceName: hxsy-admin, instances: 0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,992 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:384 -  [DUBBO] 0 unique working revisions: , dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,992 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.user.service.CampPeriodRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:32,993 INFO  [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:465 -  [DUBBO] Notify service cn.hxsy.api.app.service.TencentWxInfoRpcService:1.0.0:null with urls 1, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,446 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.dubbo.DubboProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: dubbo://**************:20880/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753714566671&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,447 INFO  [SpringApplicationShutdownHook] o.a.d.r.protocol.injvm.InjvmProtocol - AbstractProtocol.java:124 -  [DUBBO] Unexport service: injvm://127.0.0.1/org.apache.dubbo.metadata.MetadataService?anyhost=true&application=hxsy-admin&background=false&bind.ip=**************&bind.port=20880&delay=0&deprecated=false&dubbo=2.0.2&dynamic=true&executor-management-mode=isolation&exporter.listener=injvm&file-cache=true&generic=false&getAndListenInstanceMetadata.1.callback=true&getAndListenInstanceMetadata.return=true&getAndListenInstanceMetadata.sent=true&group=hxsy-admin&interface=org.apache.dubbo.metadata.MetadataService&methods=exportInstanceMetadata,getAndListenInstanceMetadata,getExportedServiceURLs,getExportedURLs,getInstanceMetadataChangedListenerMap,getMetadataInfo,getMetadataInfos,getMetadataURL,getServiceDefinition,getSubscribedURLs,isMetadataService,serviceName,toSortedStrings,version&pid=4688&prefer.serialization=fastjson2,hessian2&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&revision=3.2.10&serialize.check.status=WARN&service.filter=-default&side=provider&timestamp=1753714566671&version=1.0.0, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,448 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,449 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,450 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&release=3.2.10&serialize.check.status=WARN], dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,451 INFO  [SpringApplicationShutdownHook] o.a.d.registry.nacos.NacosRegistry - AbstractRegistry.java:619 -  [DUBBO] Destroy registry:nacos://*************:8848/org.apache.dubbo.registry.RegistryService?application=hxsy-admin&dubbo=2.0.2&executor-management-mode=isolation&file-cache=true&group=dubbo&interface=org.apache.dubbo.registry.RegistryService&namespace=bc83bf40-3304-4071-a0dc-3dfea4516216&pid=4688&qos.accept.foreign.ip=false&qos.enable=false&register=false&release=3.2.10&serialize.check.status=WARN, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,466 INFO  [SpringApplicationShutdownHook] o.a.d.r.c.e.l.ServiceInstancesChangedListener - ServiceInstancesChangedListener.java:499 -  [DUBBO] Destroying instance listener of  [hxsy-admin, hxsy-business], dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,467 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,469 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.1](hxsy-admin) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,473 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:119 -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,474 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:124 -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,474 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1334 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,474 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:399 -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,474 INFO  [SpringApplicationShutdownHook] o.a.d.c.deploy.DefaultModuleDeployer - DefaultModuleDeployer.java:409 -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,475 INFO  [SpringApplicationShutdownHook] o.a.d.r.support.RegistryManager - RegistryManager.java:100 -  [DUBBO] Close all registries [], dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,475 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.DefaultExecutorRepository - DefaultExecutorRepository.java:419 -  [DUBBO] destroying application executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,475 INFO  [SpringApplicationShutdownHook] o.a.d.c.d.DefaultApplicationDeployer - DefaultApplicationDeployer.java:1348 -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,476 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:139 -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,476 INFO  [SpringApplicationShutdownHook] o.a.dubbo.rpc.model.FrameworkModel - FrameworkModel.java:332 -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,477 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:108 -  [DUBBO] Destroying global resources ..., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,481 INFO  [SpringApplicationShutdownHook] o.a.d.c.r.GlobalResourcesRepository - GlobalResourcesRepository.java:145 -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,481 INFO  [SpringApplicationShutdownHook] o.a.d.c.t.m.FrameworkExecutorRepository - FrameworkExecutorRepository.java:210 -  [DUBBO] destroying framework executor repository .., dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,484 INFO  [SpringApplicationShutdownHook] o.a.d.c.s.c.DubboSpringInitializer - DubboSpringInitializer.java:78 -  [DUBBO] Unbind Dubbo Module[1.1.1] from spring container: org.springframework.beans.factory.support.DefaultListableBeanFactory@4b14b8, dubbo version: 3.2.10, current host: **************
2025-07-28 22:57:34,488 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 22:57:34,980 INFO  [SpringApplicationShutdownHook] o.s.s.quartz.SchedulerFactoryBean - SchedulerFactoryBean.java:847 - Shutting down Quartz Scheduler
2025-07-28 22:57:34,980 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:666 - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-28 22:57:34,980 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:585 - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-28 22:57:34,981 INFO  [SpringApplicationShutdownHook] org.quartz.core.QuartzScheduler - QuartzScheduler.java:740 - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-28 22:57:34,993 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:95 - De-registering from Nacos Server now...
2025-07-28 22:57:35,023 INFO  [SpringApplicationShutdownHook] c.a.c.n.r.NacosServiceRegistry - NacosServiceRegistry.java:115 - De-registration finished.
2025-07-28 22:57:35,027 INFO  [SpringApplicationShutdownHook] c.q.cos.http.DefaultCosHttpClient - DefaultCosHttpClient.java:158 - shutdown stackTrace:
Class: com.qcloud.cos.http.DefaultCosHttpClient, Method: shutdown, Line: 147
Class: com.qcloud.cos.COSClient, Method: shutdown, Line: 183
Class: sun.reflect.NativeMethodAccessorImpl, Method: invoke0, Line: -2
Class: sun.reflect.NativeMethodAccessorImpl, Method: invoke, Line: 62
Class: sun.reflect.DelegatingMethodAccessorImpl, Method: invoke, Line: 43
Class: java.lang.reflect.Method, Method: invoke, Line: 498
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: invokeCustomDestroyMethod, Line: 319
Class: org.springframework.beans.factory.support.DisposableBeanAdapter, Method: destroy, Line: 253
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroyBean, Line: 587
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingleton, Line: 559
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingleton, Line: 1163
Class: org.springframework.beans.factory.support.DefaultSingletonBeanRegistry, Method: destroySingletons, Line: 520
Class: org.springframework.beans.factory.support.DefaultListableBeanFactory, Method: destroySingletons, Line: 1156
Class: org.springframework.context.support.AbstractApplicationContext, Method: destroyBeans, Line: 1106
Class: org.springframework.context.support.AbstractApplicationContext, Method: doClose, Line: 1075
Class: org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext, Method: doClose, Line: 174
Class: org.springframework.context.support.AbstractApplicationContext, Method: close, Line: 1021
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: closeAndWait, Line: 145
Class: java.lang.Iterable, Method: forEach, Line: 75
Class: org.springframework.boot.SpringApplicationShutdownHook, Method: run, Line: 114
Class: java.lang.Thread, Method: run, Line: 750
2025-07-28 22:57:35,087 INFO  [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2043 - {dataSource-1} closing ...
2025-07-28 22:57:35,095 INFO  [SpringApplicationShutdownHook] c.alibaba.druid.pool.DruidDataSource - DruidDataSource.java:2116 - {dataSource-1} closed
