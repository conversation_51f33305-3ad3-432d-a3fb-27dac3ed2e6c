# 客户群变更事件回调与行为轨迹记录实现

## 功能概述

在企微回调事件处理中实现了客户加群和退群的行为轨迹自动记录功能，当企微群发生成员变更时，系统会自动记录相关的客户行为轨迹。

## 主要功能

1. **自动监听群变更事件** - 通过企微回调接口监听群成员变更
2. **解析XML回调数据** - 正确解析企微发送的XML格式回调数据
3. **记录加群行为轨迹** - 自动记录客户加入群组的行为和入群方式
4. **记录退群行为轨迹** - 自动记录客户退出群组的行为和退群方式
5. **关联客户信息** - 通过external_userid关联客户绑定信息

## 新增字段

### CustomerBehavior表新增字段

| 字段名 | 类型 | 说明 | 取值范围 |
|--------|------|------|----------|
| join_scene | INT | 入群方式 | 0-由成员邀请入群, 3-通过扫描群二维码入群 |
| quit_scene | INT | 退群方式 | 0-自己退群, 1-群主/群管理员移出 |

## 企微回调数据结构

### 完整的XML回调示例

```xml
<xml>
    <ToUserName><![CDATA[ww55ca070cb9b7eb22]]></ToUserName>
    <FromUserName><![CDATA[sys]]></FromUserName>
    <CreateTime>1403610513</CreateTime>
    <MsgType><![CDATA[event]]></MsgType>
    <Event><![CDATA[change_external_chat]]></Event>
    <ChatId><![CDATA[wrx7HUARsKwGRaQBVKPBTcEyzdHA4HrQ]]></ChatId>
    <ChangeType><![CDATA[update]]></ChangeType>
    <UpdateDetail><![CDATA[add_member]]></UpdateDetail>
    <JoinScene>1</JoinScene>
    <QuitScene>0</QuitScene>
    <MemChangeCnt>10</MemChangeCnt>
    <MemChangeList>
        <Item>Jack</Item>
        <Item>Rose</Item>
    </MemChangeList>
    <LastMemVer>9c3f97c2ada667dfb5f6d03308d963e1</LastMemVer>
    <CurMemVer>71217227bbd112ecfe3a49c482195cb4</CurMemVer>
</xml>
```

### 关键字段说明

| 字段名 | 说明 | 示例值 |
|--------|------|--------|
| ToUserName | 企业微信CorpID | ww55ca070cb9b7eb22 |
| Event | 事件类型 | change_external_chat |
| ChatId | 群ID | wrx7HUARsKwGRaQBVKPBTcEyzdHA4HrQ |
| UpdateDetail | 变更详情 | add_member / del_member |
| JoinScene | 入群方式 | 0-邀请入群, 3-扫码入群 |
| QuitScene | 退群方式 | 0-自己退群, 1-被移出 |
| MemChangeList | 成员变更列表 | XML格式的Item列表 |

## 实现逻辑

### 1. 事件分发处理

```java
private String updateGroup(QyCallBackRequest qyCallBackRequest) {
    // 保留原有注释信息
    //ToUserName	企业微信CorpID
    //FromUserName	此事件该值固定为sys，表示该消息由系统生成
    //CreateTime	消息创建时间 （unix时间戳）
    //MsgType	消息的类型，此时固定为event
    //Event	事件的类型，此时固定为change_external_chat
    //ChatId	群ID
    //ChangeType	此时固定为update
    //UpdateDetail	变更详情。目前有以下几种： add_member : 成员入群 del_member : 成员退群 change_owner : 群主变更 change_name : 群名变更 change_notice : 群公告变更
    //JoinScene	当是成员入群时有值。表示成员的入群方式
    //0 - 由成员邀请入群（包括直接邀请入群和通过邀请链接入群）
    //3 - 通过扫描群二维码入群
    //QuitScene	当是成员退群时有值。表示成员的退群方式
    //0 - 自己退群
    //1 - 群主/群管理员移出
    //MemChangeCnt	当是成员入群或退群时有值。表示成员变更数量
    //MemChangeList	当是成员入群或退群时有值。变更的成员列表
    //LastMemVer	当是成员入群或退群时有值。 变更前的群成员版本号
    //CurMemVer	当是成员入群或退群时有值。变更后的群成员版本号
    
    try {
        Element element = qyCallBackRequest.getElement();
        String corpId = WecomXmlUtil.getNodeText(element, "ToUserName");
        String chatId = WecomXmlUtil.getNodeText(element, "ChatId");
        String updateDetail = WecomXmlUtil.getNodeText(element, "UpdateDetail");
        
        // 只处理成员入群和退群事件
        if ("add_member".equals(updateDetail)) {
            handleMemberJoinGroup(element, corpId, chatId);
        } else if ("del_member".equals(updateDetail)) {
            handleMemberExitGroup(element, corpId, chatId);
        }
        
    } catch (Exception e) {
        log.error("处理客户群变更事件异常: {}", e.getMessage(), e);
    }
    
    return "success";
}
```

### 2. XML数据解析

```java
// 解析MemChangeList中的Item元素
NodeList memChangeList = element.getElementsByTagName("MemChangeList");
if (memChangeList.getLength() > 0) {
    Element memChangeListElement = (Element) memChangeList.item(0);
    NodeList itemList = memChangeListElement.getElementsByTagName("Item");
    
    for (int i = 0; i < itemList.getLength(); i++) {
        Node itemNode = itemList.item(i);
        String externalUserId = itemNode.getTextContent();
        
        if (StringUtils.isNotBlank(externalUserId)) {
            // 记录行为轨迹
            recordJoinGroupBehavior(corpId, chatId, externalUserId, joinScene);
        }
    }
}
```

### 3. 客户信息关联

```java
private void recordJoinGroupBehavior(String corpId, String chatId, String externalUserId, Integer joinScene) {
    try {
        // 1. 根据external_userid查找客户绑定信息
        LambdaQueryWrapper<CustomerWecomBinding> bindingWrapper = Wrappers.lambdaQuery(CustomerWecomBinding.class);
        bindingWrapper.eq(CustomerWecomBinding::getCorpId, corpId)
                     .eq(CustomerWecomBinding::getExternalUserid, externalUserId);
        CustomerWecomBinding binding = customerWecomBindingService.getOne(bindingWrapper);
        
        if (ObjectUtils.isNotEmpty(binding)) {
            // 2. 获取企业信息
            LambdaQueryWrapper<CompanyQyRelation> companyWrapper = Wrappers.lambdaQuery(CompanyQyRelation.class);
            companyWrapper.eq(CompanyQyRelation::getCorpId, corpId);
            CompanyQyRelation companyQyRelation = companyQyRelationService.getOne(companyWrapper);
            
            if (ObjectUtils.isNotEmpty(companyQyRelation)) {
                // 3. 获取销售用户信息
                LambdaQueryWrapper<SystemUserQyRelation> userWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
                userWrapper.eq(SystemUserQyRelation::getSystemUserId, String.valueOf(binding.getSalesId()))
                          .eq(SystemUserQyRelation::getCorpId, corpId);
                SystemUserQyRelation userQyRelation = systemUserQyRelationService.getOne(userWrapper);
                
                String employeeName = ObjectUtils.isNotEmpty(userQyRelation) ? userQyRelation.getQyName() : "未知员工";
                String employeeWeworkName = ObjectUtils.isNotEmpty(userQyRelation) ? userQyRelation.getQyUserId() : "未知";
                
                // 4. 记录加入群组行为
                boolean success = customerBehaviorService.saveJoinGroupChat(
                    binding.getCustomerId(),
                    Long.valueOf(companyQyRelation.getCompanyId()),
                    corpId,
                    chatId,
                    "群聊", // 群名称，可以后续通过API获取
                    employeeName,
                    employeeWeworkName,
                    joinScene
                );
                
                log.info("记录客户加入群组行为: customerId={}, chatId={}, joinScene={}, success={}", 
                        binding.getCustomerId(), chatId, joinScene, success);
            }
        } else {
            log.warn("未找到客户绑定信息: corpId={}, externalUserId={}", corpId, externalUserId);
        }
        
    } catch (Exception e) {
        log.error("记录加入群组行为异常: corpId={}, chatId={}, externalUserId={}, error={}", 
                corpId, chatId, externalUserId, e.getMessage(), e);
    }
}
```

## 数据库变更

### SQL迁移脚本

```sql
-- 添加入群方式字段
ALTER TABLE customer_behavior 
ADD COLUMN join_scene INT COMMENT '入群方式 0-由成员邀请入群, 3-通过扫描群二维码入群' AFTER corp_id;

-- 添加退群方式字段
ALTER TABLE customer_behavior 
ADD COLUMN quit_scene INT COMMENT '退群方式 0-自己退群, 1-群主/群管理员移出' AFTER join_scene;

-- 添加索引
CREATE INDEX idx_customer_behavior_join_scene ON customer_behavior(join_scene);
CREATE INDEX idx_customer_behavior_quit_scene ON customer_behavior(quit_scene);
```

## 业务流程

### 加群流程

1. **企微回调** → 系统接收到`change_external_chat`事件
2. **事件解析** → 解析XML获取`UpdateDetail=add_member`
3. **成员解析** → 解析`MemChangeList`中的`Item`元素
4. **客户关联** → 通过`external_userid`查找客户绑定信息
5. **信息收集** → 获取企业信息、销售用户信息
6. **行为记录** → 调用`saveJoinGroupChat`记录行为轨迹

### 退群流程

1. **企微回调** → 系统接收到`change_external_chat`事件
2. **事件解析** → 解析XML获取`UpdateDetail=del_member`
3. **成员解析** → 解析`MemChangeList`中的`Item`元素
4. **客户关联** → 通过`external_userid`查找客户绑定信息
5. **信息收集** → 获取企业信息、销售用户信息
6. **行为记录** → 调用`saveExitGroupChat`记录行为轨迹

## 关键改进

### 1. XML解析修复

**问题**: 原来错误地将`MemChangeList`当作JSON格式解析
**解决**: 正确解析XML结构中的`<Item>`元素

### 2. 使用ObjectUtils工具类

**改进**: 统一使用`ObjectUtils.isNotEmpty()`替换`!= null`判断
**优势**: 代码更规范，空值判断更安全

### 3. 保留原有注释

**保持**: 完整保留了企微回调字段的详细注释说明
**价值**: 便于后续维护和理解业务逻辑

## 监控和日志

### 关键日志点

1. **事件接收**: 记录完整的回调请求信息
2. **解析结果**: 记录解析出的关键字段值
3. **成员处理**: 记录每个成员的处理情况
4. **行为记录**: 记录行为轨迹保存结果
5. **异常处理**: 详细记录各种异常情况

### 异常处理

1. **XML解析异常**: 捕获并记录XML解析错误
2. **数据库异常**: 捕获并记录数据查询/保存错误
3. **业务异常**: 捕获并记录业务逻辑处理错误
4. **容错机制**: 单个成员处理失败不影响其他成员

## 扩展建议

1. **群名称获取**: 通过企微API获取真实的群名称
2. **批量优化**: 对于大量成员变更的情况进行批量处理优化
3. **重复处理**: 添加幂等性处理，避免重复记录
4. **实时通知**: 可以添加实时通知机制，及时告知相关人员

## 测试建议

1. **单元测试**: 测试XML解析逻辑
2. **集成测试**: 测试完整的回调处理流程
3. **异常测试**: 测试各种异常情况的处理
4. **性能测试**: 测试大量成员变更时的处理性能
