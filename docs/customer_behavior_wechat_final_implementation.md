# 客户行为轨迹企微功能 - 最终实现方案

## 功能概述

实现了客户行为轨迹系统中的四个企微相关功能，使用专门的字段而不复用现有字段：

1. **添加企微用户** - 记录客户添加企微销售人员的行为
2. **删除企微用户** - 记录客户删除企微关系的行为
3. **加入群组** - 记录客户加入企微群组的行为
4. **退出群组** - 记录客户退出企微群组的行为

## 数据库字段设计

### CustomerBehavior表新增字段

| 字段名 | 类型 | 说明 | 用途 |
|--------|------|------|------|
| corp_name | VARCHAR(255) | 企业名称 | 替换原enterprise_wechat_name字段 |
| group_name | VARCHAR(255) | 群组名称 | 记录企微群组的显示名称 |
| group_id | VARCHAR(100) | 群组ID | 记录企微群组的唯一标识 |
| corp_id | VARCHAR(100) | 企微企业ID | 记录企微企业的唯一标识 |

### 字段使用说明

#### 添加/删除企微行为
- `corp_id` - 企微企业ID
- `corp_name` - 企业名称
- `employee_name` - 员工姓名
- `employee_wework_name` - 员工企微昵称

#### 加入/退出群组行为
- `corp_id` - 企微企业ID
- `group_id` - 群组ID
- `group_name` - 群组名称
- `employee_name` - 员工姓名
- `employee_wework_name` - 员工企微昵称

## API接口

### 1. 添加企微行为

**接口**: `POST /api/v1/customer-behavior/add-wechat-enterprise`

**参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "corpId": "wx123456789",
  "corpName": "华夏盛业科技",
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售"
}
```

### 2. 删除企微行为

**接口**: `POST /api/v1/customer-behavior/delete-wechat-enterprise`

**参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "corpId": "wx123456789",
  "corpName": "华夏盛业科技",
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售"
}
```

### 3. 加入群组行为

**接口**: `POST /api/v1/customer-behavior/join-group-chat`

**参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "corpId": "wx123456789",
  "groupId": "wrOgQhDgAAMYQiS5ol9G7gK9JVAAAA",
  "groupName": "VIP客户交流群",
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售"
}
```

### 4. 退出群组行为

**接口**: `POST /api/v1/customer-behavior/exit-group-chat`

**参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "corpId": "wx123456789",
  "groupId": "wrOgQhDgAAMYQiS5ol9G7gK9JVAAAA",
  "groupName": "VIP客户交流群",
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售"
}
```

## 服务方法

### CustomerBehaviorService接口

```java
// 添加企微行为
boolean saveAddWechatEnterprise(Long customerId, Long companyId, String corpId, String corpName, String employeeName, String employeeWeworkName);

// 删除企微行为
boolean saveDeleteWechatEnterprise(Long customerId, Long companyId, String corpId, String corpName, String employeeName, String employeeWeworkName);

// 加入群组行为
boolean saveJoinGroupChat(Long customerId, Long companyId, String corpId, String groupId, String groupName, String employeeName, String employeeWeworkName);

// 退出群组行为
boolean saveExitGroupChat(Long customerId, Long companyId, String corpId, String groupId, String groupName, String employeeName, String employeeWeworkName);
```

## 数据库迁移脚本

```sql
-- 修改企业微信名称字段为企业名称
ALTER TABLE customer_behavior 
CHANGE COLUMN enterprise_wechat_name corp_name VARCHAR(255) COMMENT '企业名称';

-- 添加群组名称字段
ALTER TABLE customer_behavior 
ADD COLUMN group_name VARCHAR(255) COMMENT '群组名称' AFTER corp_name;

-- 添加群组ID字段
ALTER TABLE customer_behavior 
ADD COLUMN group_id VARCHAR(100) COMMENT '群组ID' AFTER group_name;

-- 添加企微企业ID字段
ALTER TABLE customer_behavior 
ADD COLUMN corp_id VARCHAR(100) COMMENT '企微企业ID' AFTER group_id;

-- 添加索引
CREATE INDEX idx_customer_behavior_corp_id ON customer_behavior(corp_id);
CREATE INDEX idx_customer_behavior_group_id ON customer_behavior(group_id);
CREATE INDEX idx_customer_behavior_customer_corp ON customer_behavior(customer_id, corp_id);
CREATE INDEX idx_customer_behavior_customer_group ON customer_behavior(customer_id, group_id);
```

## 使用示例

### Java调用示例

```java
@Autowired
private CustomerBehaviorService customerBehaviorService;

// 记录添加企微行为
public void recordAddWechatEnterprise() {
    customerBehaviorService.saveAddWechatEnterprise(
        1001L,                    // customerId
        2001L,                    // companyId
        "wx123456789",           // corpId
        "华夏盛业科技",           // corpName
        "张三",                   // employeeName
        "张三-销售"               // employeeWeworkName
    );
}

// 记录加入群组行为
public void recordJoinGroupChat() {
    customerBehaviorService.saveJoinGroupChat(
        1001L,                              // customerId
        2001L,                              // companyId
        "wx123456789",                     // corpId
        "wrOgQhDgAAMYQiS5ol9G7gK9JVAAAA", // groupId
        "VIP客户交流群",                    // groupName
        "张三",                             // employeeName
        "张三-销售"                         // employeeWeworkName
    );
}
```

### 前端调用示例

```javascript
// 记录添加企微行为
async function recordAddWechatEnterprise(customerId, companyId, corpId, corpName, employeeName, employeeWeworkName) {
    const params = new URLSearchParams({
        customerId: customerId,
        companyId: companyId,
        corpId: corpId,
        corpName: corpName,
        employeeName: employeeName,
        employeeWeworkName: employeeWeworkName
    });
    
    try {
        const response = await fetch('/api/v1/customer-behavior/add-wechat-enterprise', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: params
        });
        
        const result = await response.json();
        if (result.code === 200) {
            console.log('添加企微行为记录成功');
        }
    } catch (error) {
        console.error('记录失败:', error);
    }
}
```

## 设计优势

### 1. 字段语义清晰
- 每个字段都有明确的用途，不存在复用情况
- `corp_name` vs `group_name` 语义明确
- `corp_id` vs `group_id` 便于区分和查询

### 2. 数据完整性
- 企微相关的所有关键信息都有专门字段存储
- 支持复杂的数据分析和统计查询

### 3. 扩展性强
- 新增字段不影响现有功能
- 便于后续添加更多企微相关字段

### 4. 查询性能优化
- 添加了合适的索引
- 支持高效的复合查询

## 业务价值

### 1. 客户生命周期跟踪
- 完整记录客户在企微渠道的行为轨迹
- 分析客户从添加到流失的完整过程

### 2. 群组运营分析
- 跟踪群组的加入/退出情况
- 分析群组活跃度和价值

### 3. 销售效果评估
- 统计各销售人员的客户获取和维护情况
- 评估不同销售策略的效果

### 4. 数据驱动决策
- 基于行为数据优化营销策略
- 指导资源配置和人员安排

## 注意事项

1. **数据一致性**: 确保行为记录与实际企微操作同步
2. **性能考虑**: 建议异步记录行为，避免影响主业务
3. **数据隐私**: 遵守相关法规，保护客户隐私
4. **监控告警**: 建立异常行为检测机制

## 后续扩展建议

1. **批量操作**: 支持批量记录多个客户的行为
2. **实时统计**: 提供实时的行为分析dashboard
3. **自动化集成**: 与企微webhook集成，自动记录行为
4. **数据分析**: 基于行为数据提供更深入的分析报告
