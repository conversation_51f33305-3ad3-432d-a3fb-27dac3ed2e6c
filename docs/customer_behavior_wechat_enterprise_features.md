# 客户行为轨迹 - 企微功能实现文档

## 功能概述

在客户行为轨迹系统中实现了四个企微相关的行为记录功能：
1. **添加企微用户**
2. **删除企微用户**
3. **加入群组**
4. **退出群组**

## 行为类型枚举

在`BehaviorTypeEnum`中定义了以下行为类型：

```java
ADD_WECHAT_ENTERPRISE(6, "添加企微"),
DELETE_WECHAT_ENTERPRISE(7, "删除企微"),
JOIN_GROUP_CHAT(8, "加入群聊"),
EXIT_GROUP_CHAT(9, "退出群聊")
```

## 数据库表结构

### CustomerBehavior表字段说明

| 字段名 | 类型 | 说明 | 企微功能用途 |
|--------|------|------|-------------|
| customer_id | Long | 客户ID | 记录哪个客户的行为 |
| behavior_type | Integer | 行为类型 | 6-添加企微, 7-删除企微, 8-加入群聊, 9-退出群聊 |
| company_id | Long | 企业ID | 记录所属企业 |
| corp_id | String | 企微企业ID | 记录企微企业的唯一标识 |
| employee_name | String | 员工姓名 | 记录操作的员工姓名 |
| employee_wework_name | String | 员工企微昵称 | 记录员工在企微中的昵称 |
| enterprise_wechat_name | String | 企业微信名称 | 记录企业微信的显示名称 |
| group_id | String | 群组ID | 记录企微群组的唯一标识 |
| group_name | String | 群组名称 | 记录企微群组的显示名称 |
| external_user_id | String | 外部联系人ID | 记录客户在企微中的外部联系人ID |

## 服务接口

### CustomerBehaviorService接口

```java
/**
 * 保存添加企微行为
 * @param customerId 客户ID
 * @param companyId 企业ID
 * @param corpId 企微企业ID
 * @param employeeName 员工姓名
 * @param employeeWeworkName 员工企微昵称
 * @param enterpriseWeChatName 企业微信名称
 * @param externalUserId 外部联系人ID
 * @return 是否保存成功
 */
boolean saveAddWechatEnterprise(Long customerId, Long companyId, String corpId, String employeeName, String employeeWeworkName, String enterpriseWeChatName, String externalUserId);

/**
 * 保存删除企微行为
 * @param customerId 客户ID
 * @param companyId 企业ID
 * @param employeeName 员工姓名
 * @param employeeWeworkName 员工企微昵称
 * @param enterpriseWeChatName 企业微信名称
 * @return 是否保存成功
 */
boolean saveDeleteWechatEnterprise(Long customerId, Long companyId, String employeeName, String employeeWeworkName, String enterpriseWeChatName);

/**
 * 保存加入群组行为
 * @param customerId 客户ID
 * @param companyId 企业ID
 * @param groupName 群组名称
 * @param employeeName 员工姓名
 * @param employeeWeworkName 员工企微昵称
 * @return 是否保存成功
 */
boolean saveJoinGroupChat(Long customerId, Long companyId, String groupName, String employeeName, String employeeWeworkName);

/**
 * 保存退出群组行为
 * @param customerId 客户ID
 * @param companyId 企业ID
 * @param groupName 群组名称
 * @param employeeName 员工姓名
 * @param employeeWeworkName 员工企微昵称
 * @return 是否保存成功
 */
boolean saveExitGroupChat(Long customerId, Long companyId, String groupName, String employeeName, String employeeWeworkName);
```

## API接口

### 1. 记录添加企微行为

**接口路径**: `POST /api/v1/customer-behavior/add-wechat-enterprise`

**请求参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售",
  "enterpriseWeChatName": "华夏盛业科技"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "success",
  "data": true
}
```

### 2. 记录删除企微行为

**接口路径**: `POST /api/v1/customer-behavior/delete-wechat-enterprise`

**请求参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售",
  "enterpriseWeChatName": "华夏盛业科技"
}
```

### 3. 记录加入群组行为

**接口路径**: `POST /api/v1/customer-behavior/join-group-chat`

**请求参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "groupName": "VIP客户交流群",
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售"
}
```

### 4. 记录退出群组行为

**接口路径**: `POST /api/v1/customer-behavior/exit-group-chat`

**请求参数**:
```json
{
  "customerId": 1001,
  "companyId": 2001,
  "groupName": "VIP客户交流群",
  "employeeName": "张三",
  "employeeWeworkName": "张三-销售"
}
```

## 使用示例

### Java调用示例

```java
@Autowired
private CustomerBehaviorService customerBehaviorService;

// 记录添加企微行为
public void recordAddWechatEnterprise(Long customerId, Long companyId) {
    customerBehaviorService.saveAddWechatEnterprise(
        customerId, 
        companyId, 
        "张三", 
        "张三-销售", 
        "华夏盛业科技"
    );
}

// 记录加入群组行为
public void recordJoinGroupChat(Long customerId, Long companyId) {
    customerBehaviorService.saveJoinGroupChat(
        customerId, 
        companyId, 
        "VIP客户交流群", 
        "张三", 
        "张三-销售"
    );
}
```

### 前端调用示例

```javascript
// 记录添加企微行为
async function recordAddWechatEnterprise(customerId, companyId, employeeName, employeeWeworkName, enterpriseWeChatName) {
    try {
        const response = await fetch('/api/v1/customer-behavior/add-wechat-enterprise', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                customerId: customerId,
                companyId: companyId,
                employeeName: employeeName,
                employeeWeworkName: employeeWeworkName,
                enterpriseWeChatName: enterpriseWeChatName
            })
        });
        
        const result = await response.json();
        if (result.code === 200) {
            console.log('添加企微行为记录成功');
        }
    } catch (error) {
        console.error('记录失败:', error);
    }
}

// 记录加入群组行为
async function recordJoinGroupChat(customerId, companyId, groupName, employeeName, employeeWeworkName) {
    try {
        const response = await fetch('/api/v1/customer-behavior/join-group-chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                customerId: customerId,
                companyId: companyId,
                groupName: groupName,
                employeeName: employeeName,
                employeeWeworkName: employeeWeworkName
            })
        });
        
        const result = await response.json();
        if (result.code === 200) {
            console.log('加入群组行为记录成功');
        }
    } catch (error) {
        console.error('记录失败:', error);
    }
}
```

## 业务场景

### 1. 添加企微用户场景
- **触发时机**: 客户通过扫码或其他方式添加企微销售人员
- **记录信息**: 客户ID、企业ID、销售员工信息、企业微信名称
- **用途**: 跟踪客户获取渠道，分析销售人员效果

### 2. 删除企微用户场景
- **触发时机**: 客户删除企微销售人员或被销售人员删除
- **记录信息**: 客户ID、企业ID、销售员工信息、企业微信名称
- **用途**: 分析客户流失原因，优化客户维护策略

### 3. 加入群组场景
- **触发时机**: 客户被邀请加入企微群组
- **记录信息**: 客户ID、企业ID、群组名称、邀请人信息
- **用途**: 跟踪群组活跃度，分析群组运营效果

### 4. 退出群组场景
- **触发时机**: 客户主动退出或被移出企微群组
- **记录信息**: 客户ID、企业ID、群组名称、相关员工信息
- **用途**: 分析群组内容质量，优化群组管理策略

## 数据分析价值

### 1. 客户生命周期分析
- 通过添加/删除企微行为，分析客户在企微渠道的生命周期
- 识别高价值客户和流失风险客户

### 2. 销售人员效果评估
- 统计各销售人员的客户添加/删除情况
- 评估销售人员的客户维护能力

### 3. 群组运营分析
- 分析不同群组的加入/退出率
- 优化群组内容和运营策略

### 4. 渠道效果分析
- 对比不同企微渠道的客户获取和留存效果
- 指导营销资源投入决策

## 注意事项

1. **数据一致性**: 确保行为记录与实际企微操作保持一致
2. **性能考虑**: 行为记录应该异步处理，不影响主业务流程
3. **数据隐私**: 遵守相关法律法规，保护客户隐私信息
4. **监控告警**: 建立异常行为监控，及时发现系统问题

## 扩展建议

1. **批量操作支持**: 支持批量记录多个客户的行为
2. **行为关联分析**: 建立行为之间的关联关系分析
3. **实时统计**: 提供实时的行为统计和分析功能
4. **自动化触发**: 与企微webhook集成，自动记录行为
