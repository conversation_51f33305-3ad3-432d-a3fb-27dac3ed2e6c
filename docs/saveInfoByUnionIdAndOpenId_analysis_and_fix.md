# saveInfoByUnionIdAndOpenId方法逻辑分析与修复建议

## 当前逻辑分析

### 方法流程
1. 根据`companyId`获取`corpId`
2. 查询是否已存在绑定关系
3. 如果已绑定且有`externalUserid`，直接返回有权限
4. 调用企微接口获取`externalUserid`或`pendingId`
5. 根据结果保存或更新绑定信息

## 发现的问题

### 1. 返回类型不合理
```java
public CampCourseVideoResponse saveInfoByUnionIdAndOpenId(...)
```
**问题**: 方法名暗示保存关联信息，但返回课程视频响应类型
**影响**: 语义不清晰，容易误解

### 2. 查询条件过于严格
```java
// 第149-154行的查询条件
queryWrapper.eq(CustomerWecomBinding::getUnionId, unionid)
            .eq(CustomerWecomBinding::getOpenid, openid)
            .eq(CustomerWecomBinding::getCustomerId, customerId)
            .eq(CustomerWecomBinding::getSalesId, salesId)  // 问题所在
            .eq(CustomerWecomBinding::getCorpId, corpId);
```
**问题**: 如果客户之前绑定了其他销售，这个查询会失败
**风险**: 可能创建重复的绑定记录

### 3. 关键逻辑未实现
```java
// TODO 根据 salesId 查询 salesUserid 并校验 salesUserid
```
**问题**: 销售用户ID校验逻辑缺失
**风险**: 数据一致性问题

### 4. 重复查询逻辑混乱
```java
// 第183-187行重新查询
queryWrapper.clear();
queryWrapper.eq(CustomerWecomBinding::getExternalUserid, externalUserid)
        .eq(CustomerWecomBinding::getSalesId, salesId)
        .eq(CustomerWecomBinding::getCorpId, corpId);
```
**问题**: 查询条件不一致，逻辑复杂

### 5. 异常处理不规范
```java
throw new RuntimeException("未找到对应公司企微关系");
```
**问题**: 使用通用异常，不利于错误分类处理

## 修复建议

### 1. 改进查询逻辑
```java
// 第一步：查询是否已存在该客户的绑定（不限制salesId）
LambdaQueryWrapper<CustomerWecomBinding> queryWrapper = Wrappers.lambdaQuery(CustomerWecomBinding.class);
queryWrapper.eq(CustomerWecomBinding::getUnionId, unionid)
            .eq(CustomerWecomBinding::getOpenid, openid)
            .eq(CustomerWecomBinding::getCustomerId, customerId)
            .eq(CustomerWecomBinding::getCorpId, corpId);

List<CustomerWecomBinding> existingBindings = list(queryWrapper);

// 第二步：检查是否已绑定当前销售
CustomerWecomBinding currentSalesBinding = existingBindings.stream()
    .filter(binding -> Objects.equals(binding.getSalesId(), salesId))
    .findFirst()
    .orElse(null);

// 第三步：检查是否已绑定其他销售
CustomerWecomBinding otherSalesBinding = existingBindings.stream()
    .filter(binding -> !Objects.equals(binding.getSalesId(), salesId))
    .findFirst()
    .orElse(null);
```

### 2. 实现销售用户ID校验
```java
// 根据salesId查询SystemUserQyRelation获取salesUserid
LambdaQueryWrapper<SystemUserQyRelation> userQyWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
userQyWrapper.eq(SystemUserQyRelation::getSystemUserId, salesId)
            .eq(SystemUserQyRelation::getCorpId, corpId);
SystemUserQyRelation systemUserQyRelation = systemUserQyRelationService.getOne(userQyWrapper);

if (systemUserQyRelation == null) {
    throw new BusinessException("销售用户在该企业中没有企微账号");
}

String salesUserid = systemUserQyRelation.getQyUserId();
```

### 3. 优化返回类型
```java
@Data
public class CustomerWecomBindingResult {
    private boolean hasCoursePermission;
    private String salesQrCode;
    private String bindingStatus; // ALREADY_BOUND, PENDING, FAILED
    private String message;
}

public CustomerWecomBindingResult saveInfoByUnionIdAndOpenId(...)
```

### 4. 改进异常处理
```java
public class CustomerWecomBindingException extends RuntimeException {
    public CustomerWecomBindingException(String message) {
        super(message);
    }
}

// 使用自定义异常
if (ObjectUtils.isEmpty(companyQyRelated)) {
    throw new CustomerWecomBindingException("未找到企业ID为" + companyId + "的企微配置");
}
```

### 5. 完整的修复后逻辑
```java
public CustomerWecomBindingResult saveInfoByUnionIdAndOpenId(String unionid, String openid, 
                                                           Long companyId, Long customerId, Long salesId) {
    log.info("保存客户企微绑定信息: unionid={}, openid={}, companyId={}, customerId={}, salesId={}", 
             unionid, openid, companyId, customerId, salesId);
    
    // 1. 参数验证
    if (StringUtils.isAnyBlank(unionid, openid) || companyId == null || customerId == null || salesId == null) {
        throw new CustomerWecomBindingException("参数不能为空");
    }
    
    // 2. 获取企业企微配置
    CompanyQyRelated companyQyRelated = getCompanyQyRelated(companyId);
    String corpId = companyQyRelated.getCorpId();
    
    // 3. 验证销售用户
    SystemUserQyRelation salesUserQyRelation = validateSalesUser(salesId, corpId);
    String salesUserid = salesUserQyRelation.getQyUserId();
    
    // 4. 检查现有绑定
    CustomerWecomBinding existingBinding = checkExistingBinding(unionid, openid, customerId, corpId, salesId);
    if (existingBinding != null && StringUtils.isNotBlank(existingBinding.getExternalUserid())) {
        return CustomerWecomBindingResult.success(true, null, "已绑定");
    }
    
    // 5. 调用企微接口
    ExternalUserInfo externalUserInfo = getExternalUserInfo(unionid, openid, corpId);
    
    // 6. 保存或更新绑定信息
    return saveOrUpdateBinding(externalUserInfo, customerId, salesId, salesUserid, corpId, unionid, openid);
}
```

## 总结

当前的`saveInfoByUnionIdAndOpenId`方法存在多个逻辑问题，主要集中在：
1. 查询条件设计不合理
2. 关键校验逻辑缺失
3. 异常处理不规范
4. 返回类型语义不清

建议按照上述方案进行重构，提高代码的健壮性和可维护性。
