-- 为CustomerBehavior表添加企微相关字段的SQL脚本
-- 执行前请备份数据库

-- 修改企业微信名称字段为企业名称
ALTER TABLE customer_behavior
CHANGE COLUMN enterprise_wechat_name corp_name VARCHAR(255) COMMENT '企业名称';
-- 添加群组名称字段
ALTER TABLE customer_behavior
ADD COLUMN group_name VARCHAR(255) COMMENT '群组名称' AFTER corp_name;
-- 添加群组ID字段
ALTER TABLE customer_behavior
ADD COLUMN group_id VARCHAR(100) COMMENT '群组ID' AFTER group_name;
-- 添加企微企业ID字段
ALTER TABLE customer_behavior
ADD COLUMN corp_id VARCHAR(100) COMMENT '企微企业ID' AFTER group_id;
-- 添加入群方式字段
ALTER TABLE customer_behavior
ADD COLUMN join_scene INT COMMENT '入群方式 0-由成员邀请入群, 3-通过扫描群二维码入群' AFTER corp_id;
-- 添加退群方式字段
ALTER TABLE customer_behavior
ADD COLUMN quit_scene INT COMMENT '退群方式 0-自己退群, 1-群主/群管理员移出' AFTER join_scene;

-- 添加索引以提高查询性能
CREATE INDEX idx_customer_behavior_corp_id ON customer_behavior(corp_id);
CREATE INDEX idx_customer_behavior_group_id ON customer_behavior(group_id);
CREATE INDEX idx_customer_behavior_join_scene ON customer_behavior(join_scene);
CREATE INDEX idx_customer_behavior_quit_scene ON customer_behavior(quit_scene);
