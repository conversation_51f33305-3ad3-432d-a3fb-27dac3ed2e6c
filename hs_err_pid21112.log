#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 528482304 bytes for G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=21112, tid=27984
#
# JRE version:  (17.0.10+1) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.10+1-b1087.23, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://github.com': 

Host: AMD Ryzen 7 4800H with Radeon Graphics         , 16 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
Time: Sun Apr 20 21:31:44 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.3775) elapsed time: 2.042183 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x0000015e5f30d320):  JavaThread "Unknown thread" [_thread_in_vm, id=27984, stack(0x0000001b7a500000,0x0000001b7a600000)]

Stack: [0x0000001b7a500000,0x0000001b7a600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x688a29]
V  [jvm.dll+0x84216a]
V  [jvm.dll+0x843dae]
V  [jvm.dll+0x844413]
V  [jvm.dll+0x24ba8f]
V  [jvm.dll+0x6857f9]
V  [jvm.dll+0x67a0aa]
V  [jvm.dll+0x30c7eb]
V  [jvm.dll+0x313c96]
V  [jvm.dll+0x363a2e]
V  [jvm.dll+0x363c5f]
V  [jvm.dll+0x2e25b8]
V  [jvm.dll+0x2e3524]
V  [jvm.dll+0x812cd1]
V  [jvm.dll+0x3718e1]
V  [jvm.dll+0x7f1836]
V  [jvm.dll+0x3f553f]
V  [jvm.dll+0x3f7171]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0xb14fc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffa82deef58, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x0000015e5f3bd3a0 GCTaskThread "GC Thread#0" [stack: 0x0000001b7a600000,0x0000001b7a700000] [id=30524]
  0x0000015e5f3be2c0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000001b7a700000,0x0000001b7a800000] [id=33928]
  0x0000015e5f3beb80 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000001b7a800000,0x0000001b7a900000] [id=37564]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffa825a3187]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000015e5f3085e0] Heap_lock - owner thread: 0x0000015e5f30d320

Heap address: 0x000000060a000000, size: 8032 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x000000060a000000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x0000015e751d0000,0x0000015e76180000] _byte_map_base: 0x0000015e72180000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000015e5f3bd9c0, (CMBitMap*) 0x0000015e5f3bda00
 Prev Bits: [0x0000015e77130000, 0x0000015e7eeb0000)
 Next Bits: [0x0000015e00000000, 0x0000015e07d80000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.009 Loaded shared library D:\App\IDEA_2023.3.6_Protable\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff7a8e10000 - 0x00007ff7a8e1a000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\java.exe
0x00007ffae3860000 - 0x00007ffae3ac0000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffae1f50000 - 0x00007ffae2017000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffae1260000 - 0x00007ffae162a000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffae0d90000 - 0x00007ffae0edb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffadb800000 - 0x00007ffadb81b000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\VCRUNTIME140.dll
0x00007ffadb0f0000 - 0x00007ffadb107000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\jli.dll
0x00007ffae18f0000 - 0x00007ffae1abc000 	C:\WINDOWS\System32\USER32.dll
0x00007ffae1630000 - 0x00007ffae1657000 	C:\WINDOWS\System32\win32u.dll
0x00007ffae18c0000 - 0x00007ffae18ea000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffae0bb0000 - 0x00007ffae0ce1000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffacbb70000 - 0x00007ffacbe07000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24\COMCTL32.dll
0x00007ffae0fa0000 - 0x00007ffae1043000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffae3610000 - 0x00007ffae36b9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffae2210000 - 0x00007ffae223f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffadb880000 - 0x00007ffadb88c000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\vcruntime140_1.dll
0x00007ffaa2780000 - 0x00007ffaa280d000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\msvcp140.dll
0x00007ffa822b0000 - 0x00007ffa82f33000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\server\jvm.dll
0x00007ffae36c0000 - 0x00007ffae3772000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffae2c30000 - 0x00007ffae2cd6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffae3390000 - 0x00007ffae34a6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffad8ee0000 - 0x00007ffad8f16000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffad8f20000 - 0x00007ffad8f2b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffac71e0000 - 0x00007ffac71ea000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffadf680000 - 0x00007ffadf6de000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffae1660000 - 0x00007ffae16d4000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffadf5c0000 - 0x00007ffadf5d4000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffadf950000 - 0x00007ffadf96a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffadb860000 - 0x00007ffadb86a000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\jimage.dll
0x00007ffade110000 - 0x00007ffade351000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffae2ce0000 - 0x00007ffae3062000 	C:\WINDOWS\System32\combase.dll
0x00007ffae3190000 - 0x00007ffae3266000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa738c0000 - 0x00007ffa738f9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffae0cf0000 - 0x00007ffae0d89000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffad40b0000 - 0x00007ffad40d5000 	D:\App\IDEA_2023.3.6_Protable\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\App\IDEA_2023.3.6_Protable\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3624_none_3e086962e3345f24;D:\App\IDEA_2023.3.6_Protable\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://github.com': 
java_class_path (initial): D:/App/IDEA_2023.3.6_Protable/plugins/vcs-git/lib/git4idea-rt.jar;D:/App/IDEA_2023.3.6_Protable/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 13                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 528482304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8422162432                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8422162432                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_131
PATH=D:\App\Git\mingw64\libexec\git-core;D:\App\Git\mingw64\libexec\git-core;D:\App\Git\mingw64\bin;D:\App\Git\usr\bin;C:\Users\<USER>\bin;D:\App\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\ProgramData\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0;C:\windows\System32\OpenSSH;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\App\nvm;D:\App\nvm\nodejs;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\Docker\Docker\resources\bin;D:\Work\App\Maven\apache-maven-3.6.3\bin;C:\Program Files\Git\cmd;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\App\nvm;D:\App\nvm\nodejs;D:\App\cursor\resources\app\bin;D:\App\cursor\resources\app\bin;C:\Program Files\Java\jdk1.8.0_131\bin;D:\App\΢��web�����߹���\dll;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\App\nvm;D:\App\nvm\nodejs;D:\App\cursor\resources\app\bin
USERNAME=Lenovo
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=cygwin
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 96 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.3775)
OS uptime: 5 days 7:44 hours
Hyper-V role detected

CPU: total 16 (initial active 16) (16 cores per cpu, 2 threads per core) family 23 model 96 stepping 1 microcode 0x8600106, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for all 16 processors :
  Max Mhz: 2900, Current Mhz: 2900, Mhz Limit: 2900

Memory: 4k page, system-wide physical 32125M (4601M free)
TotalPageFile size 44925M (AvailPageFile size 50M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 71M, peak: 575M

vm_info: OpenJDK 64-Bit Server VM (17.0.10+1-b1087.23) for windows-amd64 JRE (17.0.10+1-b1087.23), built on 2024-03-18 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
