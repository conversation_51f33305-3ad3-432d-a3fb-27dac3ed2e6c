package cn.hxsy.gateway.auth;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import cn.hxsy.base.constant.user.UserPermission;
import cn.hxsy.base.constant.user.UserRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * sa-token的全局配置
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class SaTokenConfigure {

    @Bean
    public SaReactorFilter getSaReactorFilter() {
        return new SaReactorFilter()
                // 拦截地址
                .addInclude("/**")
                // 开放地址
                .addExclude("/favicon.ico")
                // 鉴权方法：每次访问进入
                .setAuth(obj -> {
                    // 登录校验 -- 拦截所有路由，并排除auth服务。此处排序为-100，比网关正常配置的filter执行更快，所以要全路径匹配
                    SaRouter.match("/**").notMatch("/gateway/hxsy-auth/**",
                            "/gateway/hxsy-business/api/v1/mini-program/wx-pay-callback",
                            "/gateway/hxsy-admin/api/v1/wecom/callback/**",
                            "/gateway/hxsy-business/api/v1/course-group/publicCourseVideoList").check(r -> StpUtil.checkLogin());
                    // 权限认证 -- 不同模块, 校验不同权限
//                    SaRouter.match("/hxsy-admin/**", r -> StpUtil.checkRole(UserRole.ADMIN.name()));
//                    SaRouter.match("/hxsy-business/**", r -> StpUtil.checkPermission(UserPermission.AUTH.name()));
//                    SaRouter.match("/user/**", r -> StpUtil.checkPermissionOr(UserPermission.BASIC.name(), UserPermission.FROZEN.name()));
                })
                // 异常处理方法：每次setAuth函数出现异常时进入
                .setError(this::getSaResult);
    }

    private SaResult getSaResult(Throwable throwable) {
        if (throwable instanceof NotLoginException) {
            return SaResult.error("请先登录");
        } else if (throwable instanceof NotRoleException) {
            NotRoleException notRoleException = (NotRoleException) throwable;
            if (UserRole.ADMIN.name().equals(notRoleException.getRole())) {
                return SaResult.error("请勿越权使用");
            }
            return SaResult.error("您无权限进行此操作！");
        } else if (throwable instanceof NotPermissionException) {
            NotPermissionException notPermissionException = (NotPermissionException) throwable;
            if (UserPermission.AUTH.name().equals(notPermissionException.getPermission())) {
                return SaResult.error("请先完成实名认证！");
            }
            return SaResult.error("您无权限进行此操作！");
        } else {
            return SaResult.error(throwable.getMessage());
        }
    }
}
