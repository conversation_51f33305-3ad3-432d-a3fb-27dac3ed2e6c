//package cn.hxsy.gateway.auth;
//
//import cn.dev33.satoken.stp.StpInterface;
//import cn.dev33.satoken.stp.StpUtil;
//import cn.hxsy.api.user.model.response.UserInfoResponse;
//import cn.hxsy.base.constant.user.UserPermission;
//import cn.hxsy.base.constant.user.UserRole;
//import cn.hxsy.base.constant.UserStateEnum;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//
///**
// * 自定义权限验证接口
// *
// * <AUTHOR>
// */
//@Component
//public class StpInterfaceImpl implements StpInterface {
//    @Override
//    public List<String> getPermissionList(Object loginId, String loginType) {
//        UserInfoResponse userInfoResponse = (UserInfoResponse) StpUtil.getSessionByLoginId(loginId).get((String) loginId);
//
//        if (userInfoResponse.getUserRole() == UserRole.ADMIN || userInfoResponse.getState().equals(UserStateEnum.ACTIVE.name()) || userInfoResponse.getState().equals(UserStateEnum.AUTH.name()) ) {
//            return Arrays.asList(UserPermission.BASIC.name(), UserPermission.AUTH.name());
//        }
//
//        if (userInfoResponse.getState().equals(UserStateEnum.INIT.name())) {
//            return Collections.singletonList(UserPermission.BASIC.name());
//        }
//
//        if (userInfoResponse.getState().equals(UserStateEnum.FROZEN.name())) {
//            return Collections.singletonList(UserPermission.FROZEN.name());
//        }
//
//        return Collections.singletonList(UserPermission.NONE.name());
//    }
//
//    @Override
//    public List<String> getRoleList(Object loginId, String loginType) {
//        UserInfoResponse userInfoResponse = (UserInfoResponse) StpUtil.getSessionByLoginId(loginId).get((String) loginId);
//        if (userInfoResponse.getUserRole() == UserRole.ADMIN) {
//            return Collections.singletonList(UserRole.ADMIN.name());
//        }
//        return Collections.singletonList(UserRole.CUSTOMER.name());
//    }
//}
