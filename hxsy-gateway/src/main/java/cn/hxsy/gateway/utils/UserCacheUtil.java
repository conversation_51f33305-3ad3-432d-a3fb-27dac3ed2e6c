package cn.hxsy.gateway.utils;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.session.SaTerminalInfo;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.cache.config.RedisJsonUtils;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hxsy.base.constant.user.UserRole.ADMIN;
import static cn.hxsy.base.constant.user.UserRole.COMMON_ADMIN;
import static cn.hxsy.base.exception.system.code.SystemUserErrorCode.OPERATION_NOT_PERMISSION;
import static cn.hxsy.cache.constant.user.CacheConstant.CUSTOM_LOGIN_TOKEN;
import static cn.hxsy.cache.constant.user.CacheConstant.SYS_USER_LOGIN_TOKEN;

/**
 * <AUTHOR> XiaQL
 * @description : 用户信息缓存获取工具类
 * @ClassName : GetUserCacheUtils
 * @date: 2025-05-11 17:01
 */
@Component
public class UserCacheUtil {

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    /**
    * @description: 根据传入用户请求头获取用户信息
    * @author: xiaQL
    * @date: 2025/5/11 17:02
    */
    public SystemUserResponse getSystemUserInfo(String key) {
        JSONObject systemUserInfo = (JSONObject)redisJsonUtils.getSystemUserInfo(key);
        return systemUserInfo.toJavaObject(SystemUserResponse.class);
    }

    /**
     * @description: 获取业务人员自身缓存信息
     * 1、当前token为空，表明正在登录、或者缓存丢失，返回空
     * 2、如果当前登录用户不能转换成对应业务人员信息（是客户，或者业务人员之前的缓存已过期），返回空
     * @author: xiaQL
     * @date: 2025/5/11 17:02
     */
    public SystemUserResponse getSystemUserSelfInfo() {
        String token = StpUtil.getTokenValue();
        if(StringUtils.isEmpty(token)){
            // 1、正在登录，或者缓存丢失，返回空
            return null;
        }
        JSONObject systemUserInfo = null;
        try {
            systemUserInfo = (JSONObject)redisJsonUtils.getSystemUserInfo(token);
        } catch (Exception e) {
            // 如果当前登录用户没有对应信息（是客户，或者业务人员之前的缓存已过期），返回空
            return null;
        }
        return systemUserInfo.toJavaObject(SystemUserResponse.class);
    }

    /**
     * @description: 校验用户是否为超管、普管权限
     * @author: xiaQL
     * @date: 2025/5/11 17:02
     */
    public void checkUserAdmin(SystemUserResponse systemUserResponse) {
        if (systemUserResponse == null
                || (ObjectUtils.notEqual(systemUserResponse.getRoleType(), ADMIN.getCode()) && ObjectUtils.notEqual(systemUserResponse.getRoleType(), COMMON_ADMIN.getCode()))
        ){
            throw new BizException(OPERATION_NOT_PERMISSION);
        }
    }

    /**
     * @description: 删除传入用户id对应用户缓存
     * @author: xiaQL
     * @date: 2025/5/11 17:02
     */
    public void deleteUserCache(String loginId) {
        // 首先获取此用户id对应的token集合
        SaSession userSession = StpUtil.getSessionByLoginId(loginId);
        List<SaTerminalInfo> terminalList = userSession.getTerminalList();
        List<String> tokenLists = terminalList.stream().map(saTerminalInfo -> SYS_USER_LOGIN_TOKEN + saTerminalInfo.getTokenValue()).collect(Collectors.toList());
        redisJsonUtils.deleteBatch(tokenLists);
        StpUtil.logout(loginId);
    }

    /**
     * description : 删除客户缓存
     * @title: deleteCustomerCache
     * @param: loginId
     * <AUTHOR>
     * @date 2025/6/8 19:04
     * @return void
     */
    public void deleteCustomerCache(Long loginId) {
        SaSession saSession = StpUtil.getSessionByLoginId(loginId, false);
        if (ObjectUtil.isNotEmpty(saSession)) {
            List<SaTerminalInfo> terminalList = saSession.getTerminalList();
            List<String> tokenLists = terminalList.stream().map(saTerminalInfo -> CUSTOM_LOGIN_TOKEN + saTerminalInfo.getTokenValue()).collect(Collectors.toList());
            redisJsonUtils.deleteBatch(tokenLists);
        }
        StpUtil.logout(loginId);
    }

}
