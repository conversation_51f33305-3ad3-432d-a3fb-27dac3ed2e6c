package cn.hxsy.gateway.filter;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.gateway.utils.UserCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;

/**
* @description: 业务人员操作请求日志
* @author: xiaQL
* @date: 2025/6/14 21:50
*/
@Component
@Slf4j
public class SystemUserRequestFilter implements GlobalFilter, Ordered {

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 这里把需要处理的异常情况都交给缓存工具类，只要返回不为空，那就打印
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        if(systemUserSelfInfo != null){
            // 1、获取请求体相关
            ServerHttpRequest request = exchange.getRequest();
            String method = request.getMethodValue();
            String requestPath = request.getPath().pathWithinApplication().value();
            HttpHeaders headers = request.getHeaders();
            // 2、获取请求体相关(由于请求体流是一次性的，读取后需要重新设置回去)
            if(headers.containsKey(HttpHeaders.CONTENT_TYPE)) {
                if (headers.getContentType() != null && headers.getContentType().toString().contains("application/json")) {
                    // 2.1、读取请求体参数
                    return DataBufferUtils.join(exchange.getRequest().getBody())
                            .defaultIfEmpty(exchange.getResponse().bufferFactory().wrap(new byte[0]))
                            .flatMap(dataBuffer -> {
                                byte[] bytes = new byte[dataBuffer.readableByteCount()];
                                dataBuffer.read(bytes);
                                DataBufferUtils.release(dataBuffer);
                                // 将请求体数据转换为string字符方便打印
                                String rootData = new String(bytes);
                                log.info("业务人员：{}，当前访问路径：{}，操作方法：{}，请求体：{}",
                                        systemUserSelfInfo.getAccountId(),
                                        requestPath,
                                        method,
                                        rootData);
                                // 2.2、重新构建请求，转发到对应的后端服务
                                DataBufferFactory dataBufferFactory = exchange.getResponse().bufferFactory();
                                Flux<DataBuffer> bodyFlux = Flux.just(dataBufferFactory.wrap(bytes));
                                ServerHttpRequest newRequest = request.mutate().uri(request.getURI()).build();
                                // 构建新的请求体信息
                                newRequest = new ServerHttpRequestDecorator(newRequest) {
                                    @Override
                                    public Flux<DataBuffer> getBody() {
                                        return bodyFlux;
                                    }
                                };
                                // 构建新的请求头信息
                                HttpHeaders newHeaders = new HttpHeaders();
                                newHeaders.putAll(exchange.getRequest().getHeaders());
                                newRequest = new ServerHttpRequestDecorator(newRequest) {
                                    @Override
                                    public HttpHeaders getHeaders() {
                                        return newHeaders;
                                    }
                                };
                                // 继续处理请求
                                return chain.filter(exchange.mutate().request(newRequest).build());
                            });
                }
            }
            log.info("业务人员：{}，当前访问路径：{}，操作方法：{}，未获取到请求体内容",
                    systemUserSelfInfo.getAccountId(),
                    requestPath,
                    method);
        }
        // 请求放行
        return chain.filter(exchange.mutate().build());
    }

    @Override
    public int getOrder() {
        // 设置过滤器的优先级
        return -101;
    }


}

