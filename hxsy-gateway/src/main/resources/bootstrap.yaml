spring:
  application:
    name: @application.name@
#  config:
#    import: classpath:base.yml,classpath:config.yml #报错可忽略、idea不能识别这种写法
  cloud: # 使用线上配置
    nacos:
      username: nacos
      password: nacos
      config:
        server-addr: 43.137.62.211:8848
        namespace: 09d6456c-e5a0-4d68-8120-bf646f2e3929
        group: DEFAULT_GROUP
        name: ${spring.application.name}
        shared-configs:
          - data-id: base.yaml
          - data-id: cache.yaml
          - data-id: config.yaml