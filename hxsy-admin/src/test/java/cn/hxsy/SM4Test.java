package cn.hxsy;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import cn.hxsy.base.util.Sm4EncryptPo;

import java.security.SecureRandom;

import static cn.hutool.crypto.Mode.CBC;
import static cn.hutool.crypto.Padding.ZeroPadding;

public class SM4Test {

    //初始秘钥
    private static String initKey = "SgFeV7Hj34TKiEzT";

    //初始向量
    private static String initIv = "GDoP8oPshOsMZh2Q";

    /**
    * @description: 自定义秘钥与向量，进行加密后解密测试，对比前端加解密方法数据
    * @author: xiaQL
    * @date: 2025/4/19 16:37
    */
    public static void main(String[] argc){
        String originTxt = "pw@32!";
        //自定义秘钥与向量，进行加密后解密测试，对比数据
        Sm4EncryptPo encrypt = encrypt(originTxt);
//        //前端传入秘钥与向量，进行加密测试
//        Sm4EncryptPo encrypt = new Sm4EncryptPo();
//        encrypt.setEncryptKey("M10PuTza4aPtFWvM");
//        encrypt.setEncryptInitIv("6Pg17Fs9zDmaQXdb");
//        encrypt.setEncryptData("{SM4}4b4fcc5c62395c27227817e4f3f4c6b0");
        String cipherTxt = encrypt.getEncryptData();
        System.out.println("密文: " + cipherTxt);
        String plainTxt = decrypt(cipherTxt, encrypt.getEncryptKey(), encrypt.getEncryptInitIv());
        System.out.println("解密结果: " + plainTxt);
    }

    /**
     * sm4对象构造
     * 加密时自己生成秘钥与向量
     * 解密时由传入的秘钥与向量来生成sm4
     * @param encryptKey 秘钥
     * @param initIv 初始向量
     * @return
     */
    public static Sm4EncryptPo sm4Build(String encryptKey, String initIv){
        System.out.println("初始秘钥: " + encryptKey);
        System.out.println("初始向量: " + initIv);
        SymmetricCrypto sm4 = new SM4(
                CBC, ZeroPadding,
                encryptKey.getBytes(),
                initIv.getBytes()
        );
        //构造返回对象
        Sm4EncryptPo sm4EncryptPo = new Sm4EncryptPo(sm4, encryptKey, initIv);
        return sm4EncryptPo;
    }

    /**
     * sm4加密过程，需要自己生成秘钥与向量
     * @param plainTxt 待加密数据
     * @return
     */
    public static Sm4EncryptPo encrypt(String plainTxt){
        //秘钥生成
        byte[] key = new byte[8];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(key);
        StringBuilder sbKey = new StringBuilder(16);
        for (int i = 0; i < key.length; i++) {
            sbKey.append(String.format("%02x", key[i]));
        }
        //初始向量生成
        byte[] iv = new byte[8];
        secureRandom.nextBytes(iv);
        StringBuilder sbIv = new StringBuilder(16);
        for (int i = 0; i < key.length; i++) {
            sbIv.append(String.format("%02x", iv[i]));
        }
        Sm4EncryptPo sm4EncryptPo = sm4Build(sbKey.toString(), sbIv.toString());
//        Sm4EncryptPo sm4EncryptPo = sm4Build(initKey, initIv);
        SymmetricCrypto sm4 = sm4EncryptPo.getSm4();
        String cipherTxt = sm4.encryptHex(plainTxt);
        sm4EncryptPo.setEncryptData("{SM4}" + cipherTxt);
        return sm4EncryptPo;
    }

    /**
     * sm4解密，需要获取到加密方法时使用的秘钥与向量
     * @param cipherTxt 待解密数据
     * @param encryptKey 加密时使用的秘钥
     * @param initIv 加密时使用的初始向量
     * @return
     */
    public static String decrypt(String cipherTxt, String encryptKey, String initIv){
        if(!cipherTxt.startsWith("{SM4}")){
            return cipherTxt;
        }
        cipherTxt = cipherTxt.substring(5);
        Sm4EncryptPo sm4EncryptPo = sm4Build(encryptKey, initIv);
        SymmetricCrypto sm4 = sm4EncryptPo.getSm4();
        String plainTxt = sm4.decryptStr(HexUtil.decodeHex(cipherTxt), CharsetUtil.CHARSET_UTF_8);
        // 移除所有非打印控制字符
        plainTxt = plainTxt.replaceAll("[\\p{Cntrl}\\p{Cc}]","");
        plainTxt = plainTxt.replaceAll("\\s+$", "");
        return plainTxt;
    }
}
