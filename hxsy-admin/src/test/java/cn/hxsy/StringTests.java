package cn.hxsy;

import java.security.SecureRandom;

public class StringTests {
    private static final String CHAR_SET = "abcdefghijklmnopqrstuvwxyz0123456789";
    private SecureRandom random = new SecureRandom();

    public String generateRandomString(int length) {
        if (length != 32) {
            throw new IllegalArgumentException("Length must be 32");
        }
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(CHAR_SET.length());
            sb.append(CHAR_SET.charAt(randomIndex));
        }
        return sb.toString();
    }

//    public static void main(String[] args) {
//        StringTests generator = new StringTests();
//        String randomString = generator.generateRandomString(32);
//        System.out.println("Generated Random String: " + randomString);
//    }

        public static void main(String[] args) {
        String path = "/gate/application/api/v1/survey-task/getList";
        int startIndex = path.indexOf("/", 1);
        int endIndex = path.indexOf("/", startIndex + 1);
        String apiPath = path.substring(endIndex);
        // 判断截取后的路径是否包含渠道标识，包含则需要再截取一个"/"
        if (apiPath.contains("/channel")) {
            int index = apiPath.indexOf("/", 1);
            apiPath = apiPath.substring(index);
            System.out.println(apiPath);
        }else {
            System.out.println(apiPath);
        }
    }
}
