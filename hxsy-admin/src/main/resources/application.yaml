server:
  port: 10100   #  配置启动端口号
spring:
  application:
    name: @application.name@
#  config:
#    import: classpath:base.yml,classpath:datasource.yml,classpath:datasource-sharding.yml,classpath:rpc.yml,classpath:cache.yml
  main:
    allow-bean-definition-overriding: true # 允许bean重名
    allow-circular-references: true # 允许循环依赖
  shardingsphere:
    rules:
      sharding:
        bindingTables:
          customer, customer_sales_relation, customer_tags, customer_behavior
        tables:
          customer: # 需进行分表处理的表 此处为客户表
            actual-data-nodes: ds.customer_000${0..3} # 实际数据节点，ds对应配置的数据库，${}表示不确定的分表名
            keyGenerateStrategy: # 主键生成策略
              column: id # 主键列字段为id
              keyGeneratorName: snowflake # 使用 snowflake 算法生成主键
            table-strategy: # 分表策略
              standard: # 分表模式
                shardingColumn: id # 分片列字段为id
                shardingAlgorithmName: customer-sharding # 使用 customer-sharding 算法，在下面配置
          customer_sales_relation: # 客户-销售（营期id，即课程大类）关联表
            actual-data-nodes: ds.customer_sales_relation_000${0..3}
            keyGenerateStrategy:
              column: id
              keyGeneratorName: snowflake
            table-strategy:
              standard:
                shardingColumn: customer_id
                shardingAlgorithmName: customer-sales-relation-sharding
          customer_course_relation: # 客户-课程小节关联表
            actual-data-nodes: ds.customer_course_relation_000${0..7}
            keyGenerateStrategy:
              column: id
              keyGeneratorName: snowflake
            table-strategy:
              standard:
                shardingColumn: camp_period_id
                shardingAlgorithmName: customer-course-relation-sharding
          customer_behavior: # 客户-行为轨迹
            actual-data-nodes: ds.customer_behavior_000${0..3}
            keyGenerateStrategy:
              column: id
              keyGeneratorName: snowflake
            table-strategy:
              standard:
                shardingColumn: customer_id
                shardingAlgorithmName: customer-behavior-sharding
          customer_tags: # 客户-标签
            actual-data-nodes: ds.customer_tags_000${0..3}
            keyGenerateStrategy:
              column: id
              keyGeneratorName: snowflake
            table-strategy:
              standard:
                shardingColumn: customer_id
                shardingAlgorithmName: customer-tags-sharding
        shardingAlgorithms: # 分表算法配置 此处为客户表
          customer-sharding: # 分表算法名称
            type: INLINE # 算法类型
            props: # 分表规则
              algorithm-expression: customer_000${id % 4} # 按id分
          customer-course-relation-sharding: # 客户-课程小节关联表
            type: INLINE
            props:
              algorithm-expression: customer_course_relation_000${camp_period_id % 8} # 按营期id，即课程大类分，方便业务人员与客户同时查询自身关联大类课程下的小节
          customer-sales-relation-sharding: # 客户-销售（营期id，即课程大类）关联表
            type: INLINE
            props:
              algorithm-expression: customer_sales_relation_000${customer_id % 4} # 按客户id分，为了客户观看视频时快速定位大类id，查询小节内容
          customer-behavior-sharding:
            type: INLINE
            props:
              algorithm-expression: customer_behavior_000${customer_id % 4}
          customer-tags-sharding:
            type: INLINE
            props:
              algorithm-expression: customer_tags_000${customer_id % 4}
        keyGenerators:
          snowflake: # 主键生成算法具体配置参数
            type: SNOWFLAKE # 雪花可配置不同机器上的work_id，不同服务的data_id，用于分布式服务生成不重复
            props:
              worker-id: 123
        auditors: # 审计策略配置
          sharding_key_required_auditor:
            type: DML_SHARDING_CONDITIONS  # 对 DML 操作（INSERT/UPDATE/DELETE），必须使用分片策略校验
      # 启用审计策略
      audit:
        defaultAuditStrategy:
          auditorNames: sharding_key_required_auditor
          allowHintDisable: false  # 禁止通过 Hint 绕过分片键检查
async: # 异步注册线程池配置
  pool:
    customer:
      registry:
        core-pool-size: 10
        max-pool-size: 20
        queue-capacity: 1000
        keep-alive-time: 60
      update:
        core-pool-size: 10
        max-pool-size: 20
        queue-capacity: 1000
        keep-alive-time: 60
customer:
  sharding:
    table-count: 4 # customer, customer_sales_relation, customer_tags, customer_behavior 等表分片数量
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 是否读取cookie
  is-read-cookie: false
  # 是否向响应写入cookie
  is-write-header: false
  # 是否读取header
  is-read-header: true