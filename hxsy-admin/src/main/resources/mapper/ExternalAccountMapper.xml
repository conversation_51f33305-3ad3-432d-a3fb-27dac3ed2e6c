<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.ExternalAccountMapper">

    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.ExternalAccount">
        <id column="id" property="id" />
        <result column="account_order_id" property="accountOrderId" />
        <result column="order_id" property="orderId" />
        <result column="corp_id" property="corpId" />
        <result column="active_code" property="activeCode" />
        <result column="userid" property="userid" />
        <result column="account_type" property="accountType" />
        <result column="account_status" property="accountStatus" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <sql id="Base_Column_List">
        id, account_order_id, order_id, corp_id, active_code, userid, 
        account_type, account_status, remark, status, created_by, 
        created_at, updated_by, updated_at
    </sql>

    <select id="queryPage" resultType="cn.hxsy.datasource.model.entity.ExternalAccount">
        SELECT
            ea.id AS id,
            ea.order_id AS orderId,
            ea.corp_id AS corpId,
            ea.active_code AS activeCode,
            ea.userid AS userid,
            ea.account_type AS accountType,
            ea.account_status AS accountStatus,
            ea.remark AS remark,
            ea.status AS status,
            suqr.qy_name AS qyName,
            suqr.qy_user_id AS qyUserId,
            suqr.active_time AS activeTime,
            eao.service_start_time AS serviceStartTime,
            eao.service_expire_time AS serviceEndTime
        FROM EXTERNAL_ACCOUNT ea
            LEFT JOIN SYSTEM_USER_QY_RELATION suqr ON ea.ACTIVE_CODE = suqr.ACTIVE_CODE
            LEFT JOIN EXTERNAL_ACCOUNT_ORDER eao ON ea.ACCOUNT_ORDER_ID = eao.ID
        <where>
            <if test="qyUserRequest.corpId != null and qyUserRequest.corpId != ''">
                and ea.corp_id = #{qyUserRequest.corpId}
            </if>
            <if test="qyUserRequest.qyName != null and qyUserRequest.qyName != ''">
                and suqr.qy_name like concat('%',#{qyUserRequest.qyName},'%')
            </if>
            <if test="qyUserRequest.accountStatus != null and qyUserRequest.accountStatus != ''">
                and ea.account_status = #{qyUserRequest.accountStatus}
            </if>
        </where>
    </select>
</mapper>