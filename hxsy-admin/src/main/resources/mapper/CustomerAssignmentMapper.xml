<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CustomerAssignmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CustomerAssignment">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="original_employee_id" property="originalEmployeeId" />
        <result column="original_employee_name" property="originalEmployeeName" />
        <result column="new_employee_id" property="newEmployeeId" />
        <result column="new_employee_name" property="newEmployeeName" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="change_time" property="changeTime" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />

        <result column="original_column_id" property="originalColumnId" />
        <result column="original_column_name" property="originalColumnName" />
        <result column="original_company_id" property="originalCompanyId" />
        <result column="original_company_name" property="originalCompanyName" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, original_employee_id, original_employee_name, new_employee_id, new_employee_name, operator_id, operator_name, change_time, remark, status, created_by, created_at, updated_by, updated_at,
        original_column_id, original_column_name, original_company_id, original_company_name
    </sql>

</mapper>