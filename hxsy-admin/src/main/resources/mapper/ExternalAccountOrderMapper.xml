<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.ExternalAccountOrderMapper">

    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.ExternalAccountOrder">
        <id column="id" property="id" />
        <result column="corp_id" property="corpId" />
        <result column="buyer_userid" property="buyerUserid" />
        <result column="order_id" property="orderId" />
        <result column="base_count" property="baseCount" />
        <result column="external_contact_count" property="externalContactCount" />
        <result column="account_duration" property="accountDuration" />
        <result column="order_type" property="orderType" />
        <result column="pay_status" property="payStatus" />
        <result column="price" property="price" />
        <result column="service_start_time" property="serviceStartTime" />
        <result column="service_expire_time" property="serviceExpireTime" />
        <result column="pay_time" property="payTime" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <sql id="Base_Column_List">
        id, corp_id, buyer_userid, order_id, base_count, external_contact_count,
        account_duration, order_type, pay_status, price, service_start_time, 
        service_expire_time, pay_time, remark, status, created_by, 
        created_at, updated_by, updated_at
    </sql>
</mapper>