<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CustomerWecomBindingMapper">

    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CustomerWecomBinding">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="union_id" property="unionId" />
        <result column="openid" property="openid" />
        <result column="external_userid" property="externalUserid" />
        <result column="pending_id" property="pendingId" />
        <result column="corp_id" property="corpId" />
        <result column="corp_name" property="corpName" />
        <result column="sales_userid" property="salesUserid" />
        <result column="sales_user_name" property="salesUserName" />
        <result column="sales_id" property="salesId" />
        <result column="sales_name" property="salesName" />
        <result column="state" property="state" />
        <result column="welcome_code" property="welcomeCode" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <sql id="Base_Column_List">
        id, customer_id, union_id, openid, external_userid, pending_id, corp_id, corp_name, sales_userid, sales_user_name, sales_id, sales_name, state, welcome_code,
        remark, status, created_by, created_at, updated_by, updated_at
    </sql>
    <delete id="deleteByCustomerId">
        DELETE FROM customer_wecom_binding WHERE customer_id = #{customerId}
    </delete>

</mapper> 