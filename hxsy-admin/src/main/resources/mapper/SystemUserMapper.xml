<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.SystemUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.SystemUserPO">
        <id column="id" property="id" />
        <result column="account_id" property="accountId" />
        <result column="union_id" property="unionId" />
        <result column="account_type" property="accountType" />
        <result column="user_id" property="userId" />
        <result column="headquarters_id" property="headquartersId" />
        <result column="column_id" property="columnId" />
        <result column="company_id" property="companyId" />
        <result column="sales_group_id" property="salesGroupId" />
        <result column="role_name" property="roleName" />
        <result column="role_id" property="roleId" />
        <result column="phone" property="phone" />
        <result column="birth_date" property="birthDate" />
        <result column="wechat_bind_id" property="wechatBindId" />
        <result column="audit_status" property="auditStatus" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="password" property="password" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account_id, union_id, account_type, user_id, headquarters_id, section_id, company_id, password, sales_group_id, role_name, role_id, phone, birth_date, wechat_bind_id, audit_status, remark, status, created_by, created_at, updated_by, updated_at
    </sql>

</mapper>
