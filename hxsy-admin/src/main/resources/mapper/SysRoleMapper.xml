<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.SysRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.SysRole">
        <id column="id" property="id" />
        <result column="role_name" property="roleName" />
        <result column="role_code" property="roleCode" />
        <result column="role_type" property="roleType" />
        <result column="user_from" property="userFrom" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_name, role_code, role_type, user_from, status, remark, created_by, created_at, updated_by, updated_at
    </sql>

</mapper>
