<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.HeadquartersMapper">

    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.HeadquartersPO">
        <id property="id" column="id"/>
        <result property="headquartersName" column="headquarters_name"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, headquarters_name, remark, status, created_by, created_at, updated_by, updated_at
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM headquarters WHERE id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO headquarters (headquarters_name, remark, status, created_by)
        VALUES (#{headquartersName}, #{remark}, #{status}, #{createdBy})
    </insert>

    <delete id="deleteById">
        DELETE FROM headquarters WHERE id = #{id}
    </delete>

</mapper>