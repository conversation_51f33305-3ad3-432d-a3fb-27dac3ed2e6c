<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CustomerSalesRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CustomerSalesRelation">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="company_id" property="companyId" />
        <result column="column_id" property="columnId" />
        <result column="sales_group_id" property="salesGroupId" />
        <result column="camp_period_id" property="campPeriodId" />
        <result column="sales_id" property="salesId" />
        <result column="sales_name" property="salesName" />
        <result column="wechat_remark" property="wechatRemark" />
        <result column="source" property="source" />
        <result column="assign_time" property="assignTime" />
        <result column="assign_type" property="assignType" />
        <result column="assign_by" property="assignBy" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        customer_id, company_id, column_id, sales_group_id, camp_period_id, sales_id, sales_name,
        wechat_remark, source, assign_time, assign_type, assign_by, remark, status,
        created_by, created_at, updated_by, updated_at
    </sql>

</mapper> 