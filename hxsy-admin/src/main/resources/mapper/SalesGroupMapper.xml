<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.SalesGroupMapper">

    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.SalesGroupPO">
        <id property="id" column="id"/>
        <result property="salesGroupName" column="sales_group_name"/>
        <result property="companyId" column="company_id"/>
        <result property="relPerson" column="rel_person"/>
        <result property="phone" column="phone"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, sales_group_name, company_id, rel_person, phone, remark, status, created_by, created_at, updated_by, updated_at
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM sales_group WHERE id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sales_group (sales_group_name, company_id, rel_person, phone, remark, status, created_by)
        VALUES (#{salesGroupName}, #{companyId}, #{relPerson}, #{phone}, #{remark}, #{status}, #{createdBy})
    </insert>
</mapper>