<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.TagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.Tag">
        <result column="id" property="id" />
        <result column="group_id" property="groupId" />
        <result column="tag_name" property="tagName" />
        <result column="sort_order" property="sortOrder" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, group_id, tag_name, sort_order, remark, status,
        created_by, created_at, updated_by, updated_at
    </sql>

    <!-- 标签组及其标签的嵌套结果映射 -->
    <resultMap id="TagGroupResultMap" type="cn.hxsy.api.user.model.response.TagGroupTreeResponse">
        <id property="id" column="group_id"/>
        <result property="groupName" column="group_name"/>
        <result property="sortOrder" column="group_sort_order"/>
        <result property="parentId" column="parent_id"/>
        <result property="level" column="level"/>
        <collection property="tags" ofType="cn.hxsy.datasource.model.entity.Tag">
            <id property="id" column="tag_id"/>
            <result property="groupId" column="tag_group_id"/>
            <result property="tagName" column="tag_name"/>
            <result property="sortOrder" column="tag_sort_order"/>
            <result property="remark" column="tag_remark"/>
            <result property="status" column="tag_status"/>
            <result property="createdBy" column="tag_created_by"/>
            <result property="createdAt" column="tag_created_at"/>
            <result property="updatedBy" column="tag_updated_by"/>
            <result property="updatedAt" column="tag_updated_at"/>
        </collection>
    </resultMap>

    <!-- 查询所有标签组及其标签（两级结构） -->
    <select id="getAllTagGroupsWithTags" resultMap="TagGroupResultMap">
        WITH RECURSIVE tag_groups AS (
            -- 查询一级标签组
            SELECT 
                id as group_id,
                group_name,
                sort_order as group_sort_order,
                parent_id,
                level,
                CAST(id AS CHAR(50)) as path
            FROM tag_group
            WHERE level = 1
            
            UNION ALL
            
            -- 查询二级标签组
            SELECT 
                tg.id,
                tg.group_name,
                tg.sort_order,
                tg.parent_id,
                tg.level,
                CONCAT(tgt.path, ',', tg.id)
            FROM tag_group tg
            INNER JOIN tag_groups tgt ON tg.parent_id = tgt.group_id
            WHERE tg.level = 2
        ),
        tag_data AS (
            -- 预处理标签数据
            SELECT 
                t.id as tag_id,
                t.group_id as tag_group_id,
                t.tag_name,
                t.sort_order as tag_sort_order,
                t.remark as tag_remark,
                t.status as tag_status,
                t.created_by as tag_created_by,
                t.created_at as tag_created_at,
                t.updated_by as tag_updated_by,
                t.updated_at as tag_updated_at
            FROM tag t
            WHERE EXISTS (
                SELECT 1 FROM tag_groups tg WHERE tg.group_id = t.group_id
            )
        )
        SELECT 
            tg.*,
            td.*
        FROM tag_groups tg
        LEFT JOIN tag_data td ON td.tag_group_id = tg.group_id
        ORDER BY tg.path, tg.group_sort_order, td.tag_sort_order
    </select>

    <!--根据客户ID获取客户标签 -->
    <select id="getTagsByCustomerId" resultMap="BaseResultMap">
        SELECT
            t.id,
            t.group_id,
            t.tag_name,
            t.sort_order,
            t.remark,
            t.status,
            t.created_by,
            t.created_at,
            t.updated_by,
            t.updated_at
        FROM
            customer_tag_relation ctr
        LEFT JOIN tag t ON ctr.tag_id = t.id
        WHERE
            ctr.customer_id = #{customerId}
    </select>

</mapper> 