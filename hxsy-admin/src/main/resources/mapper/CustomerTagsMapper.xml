<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CustomerTagsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CustomerTags">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="camp_period_id" property="campPeriodId" />
        <result column="camp_period_name" property="campPeriodName" />
        <result column="tags_name" property="tagsName" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />

        <result column="manual_tags_name" property="manualTagsName"/>
    </resultMap>

    <resultMap id="JoinResultMap" type="cn.hxsy.datasource.model.entity.CustomerTags">
        <result column="ct_id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="ct_camp_period_id" property="campPeriodId" />
        <result column="ct_camp_period_name" property="campPeriodName" />
        <result column="tags_name" property="tagsName" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="manual_tags_name" property="manualTagsName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, camp_period_id, camp_period_name, tags_name, remark, status, created_by, created_at, updated_by, updated_at,
        manual_tags_name
    </sql>

    <!-- 连接查询结果列 -->
    <sql id="Alias_Column_List">
        ${alias}.id as ct_id, ${alias}.customer_id, ${alias}.camp_period_id as ct_camp_period_id, ${alias}.camp_period_name as ct_camp_period_name, ${alias}.tags_name, ${alias}.remark,
        ${alias}.status, ${alias}.created_by, ${alias}.created_at, ${alias}.updated_by, ${alias}.updated_at,
        ${alias}.manual_tags_name
    </sql>

</mapper>