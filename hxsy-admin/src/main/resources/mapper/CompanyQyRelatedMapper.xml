<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CompanyQyRelatedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CompanyQyRelated">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="corp_id" property="corpId" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, corp_id, remark, status, created_by, created_at, updated_by, updated_at
    </sql>

</mapper>
