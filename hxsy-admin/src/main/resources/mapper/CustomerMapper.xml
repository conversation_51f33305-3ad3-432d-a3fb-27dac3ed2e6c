<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CustomerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.Customer">
        <result column="id" property="id" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="nickname" property="nickname" />
        <result column="last_active_time" property="lastActiveTime" />
        <result column="mobile" property="mobile" />
        <result column="mobile_status" property="mobileStatus" />
        <result column="gender" property="gender" />
        <result column="wework_status" property="weworkStatus" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="union_id" property="unionId" />
        <result column="openid" property="openid" />
        <result column="forbidden_status" property="forbiddenStatus"/>
        <result column="red_packet_status" property="redPacketStatus"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        avatar_url, nickname, last_active_time, mobile, mobile_status, gender,
        wework_status, remark, status,
        created_by, created_at, updated_by, updated_at,
        union_id, openid, forbidden_status, red_packet_status
    </sql>

    <!-- 连接查询结果列 -->
    <sql id="Alias_Column_List">
        ${alias}.id as customer_id,
        ${alias}.avatar_url, ${alias}.nickname, ${alias}.last_active_time, ${alias}.mobile, ${alias}.mobile_status, ${alias}.gender,
        ${alias}.wework_status, ${alias}.remark, ${alias}.status,
        ${alias}.created_by, ${alias}.created_at as customer_created_at, ${alias}.updated_by, ${alias}.updated_at,
        ${alias}.union_id, ${alias}.openid, ${alias}.forbidden_status, ${alias}.red_packet_status
    </sql>

    <!-- 客户查询结果映射 -->
    <resultMap id="CustomerQueryResultMap" type="cn.hxsy.api.user.model.response.CustomerQueryResponse">
        <result column="customer_id" property="customerId"/>
        <result column="union_id" property="unionId"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar_url" property="avatarUrl"/>
        <result column="mobile" property="mobile"/>
        <result column="mobile_status" property="mobileStatus"/>
        <result column="wework_status" property="weworkStatus"/>
        <result column="wechat_remark" property="wechatRemark"/>
        <result column="source" property="source"/>
        <result column="last_active_time" property="lastActiveTime"/>
        <result column="customer_created_at" property="customerCreatedAt"/>
        <result column="forbidden_status" property="forbiddenStatus"/>
        <result column="red_packet_status" property="redPacketStatus"/>
        <collection property="tags" resultMap="cn.hxsy.dao.CustomerTagsMapper.JoinResultMap"/>
        <collection property="behaviors" resultMap="cn.hxsy.dao.CustomerBehaviorMapper.JoinResultMap"/>
    </resultMap>

    <!-- 客户查询结果映射 -->
    <resultMap id="CustomerRelateInfoMap" type="cn.hxsy.api.user.model.response.CustomerQueryResponse">
        <result column="customer_id" property="customerId"/>
        <result column="source" property="source"/>
        <collection property="tags" resultMap="cn.hxsy.dao.CustomerTagsMapper.BaseResultMap"/>
        <collection property="behaviors" resultMap="cn.hxsy.dao.CustomerBehaviorMapper.BaseResultMap"/>
    </resultMap>

    <!-- 分页查询客户列表 -->
    <select id="queryCustomerPage" resultMap="CustomerQueryResultMap">
        select
            <include refid="Alias_Column_List">
                <property name="alias" value="c"/>
            </include>
            ,
            <include refid="cn.hxsy.dao.CustomerTagsMapper.Alias_Column_List">
                <property name="alias" value="ct"/>
            </include>
--             ,
<!--            <include refid="cn.hxsy.dao.CustomerBehaviorMapper.Alias_Column_List">-->
<!--                <property name="alias" value="cb"/>-->
<!--            </include>-->
        from (
            select customer.* from customer_000${shardingNum} customer
            <where>
                <if test="request.forbiddenStatus != null">
                    AND customer.forbidden_status = #{request.forbiddenStatus}
                </if>
                <if test="request.redPacketStatus != null">
                    AND customer.red_packet_status = #{request.redPacketStatus}
                </if>

                <if test="request.weworkStatus != null and request.weworkStatus != ''">
                    AND customer.wework_status = #{request.weworkStatus}
                </if>
                <if test="request.mobileStatus != null and request.mobileStatus != ''">
                    AND customer.mobile_status = #{request.mobileStatus}
                </if>
                <if test="request.wechatRemark != null and request.wechatRemark != ''">
                    AND customer.wechat_remark LIKE CONCAT('%', #{request.wechatRemark}, '%')
                </if>
                <if test="request.nickname != null and request.nickname != ''">
                    <choose>
                        <when test="request.nicknameQueryType == 1">
                            AND customer.nickname LIKE CONCAT('%', #{request.nickname}, '%')
                        </when>
                        <when test="request.nicknameQueryType == 2">
                            AND customer.nickname = #{request.nickname}
                        </when>
                        <otherwise>
                            AND customer.nickname LIKE CONCAT('%', #{request.nickname}, '%')
                        </otherwise>
                    </choose>
                </if>
                <choose>
                    <when test="request.customerId != null and request.customerId != ''">
                        and customer.id = #{request.customerId}
                    </when>
                    <when test="request.unionId != null and request.unionId != ''">
                        and customer.union_id = #{request.unionId}
                    </when>
                    <!-- 有request.customerId和request.unionId时不执行时间查询 -->
                    <otherwise>
                        <if test="request.createStartTime != null">
                            AND customer.created_at >= #{request.createStartTime}
                        </if>
                        <if test="request.createEndTime != null">
                            AND customer.created_at &lt;= #{request.createEndTime}
                        </if>

                        <if test="request.activeStartTime != null">
                            AND customer.last_active_time >= #{request.activeStartTime}
                        </if>
                        <if test="request.activeEndTime != null">
                            AND customer.last_active_time &lt;= #{request.activeEndTime}
                        </if>
                    </otherwise>
                </choose>

                <if test="request.mobile != null and request.mobile != ''">
                    AND customer.mobile = #{request.mobile}
                </if>
                <if test="request.needJoinSales != null and request.needJoinSales != ''">
                    AND EXISTS (
                    select 1 from customer_sales_relation_000${shardingNum} csr
                    <where>
                        csr.customer_id = customer.id
                        <choose>
                            <when test="request.perColumnId != null and request.perColumnId.size() > 0">
                                AND csr.column_id IN
                                <foreach item="item" collection="request.perColumnId" separator="," close=")" index="index" open="(">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                <if test="request.columnId != null and request.columnId != ''">
                                    AND csr.column_id = #{request.columnId}
                                </if>
                            </otherwise>
                        </choose>
                        <choose>
                            <when test="request.perCompanyId != null and request.perCompanyId.size() > 0">
                                AND csr.company_id IN
                                <foreach item="item" collection="request.perCompanyId" separator="," close=")" index="index" open="(">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                <if test="request.companyId != null and request.companyId != ''">
                                    AND csr.company_id = #{request.companyId}
                                </if>
                            </otherwise>
                        </choose>
                        <choose>
                            <when test="request.perSalesGroupId != null and request.perSalesGroupId.size() > 0">
                                AND csr.sales_group_id IN
                                <foreach item="item" collection="request.perSalesGroupId" separator="," close=")" index="index" open="(">
                                    #{item}
                                </foreach>
                            </when>
                            <otherwise>
                                <if test="request.salesGroupId != null and request.salesGroupId != ''">
                                    AND csr.sales_group_id = #{request.salesGroupId}
                                </if>
                            </otherwise>
                        </choose>
                        <if test="request.campPeriodId != null and request.campPeriodId != ''">
                            AND csr.camp_period_id = #{request.campPeriodId}
                        </if>
                        <if test="request.salesId != null and request.salesId != ''">
                            AND csr.sales_id = #{request.salesId}
                        </if>
                    </where>
                    )
                </if>
            </where>
            order by customer.created_at desc
            limit #{beginIndex}, #{endIndex}
        ) as c
        left join customer_tags_000${shardingNum} ct on
            ct.customer_id = c.id
<!--        left join customer_behavior_000${shardingNum} cb on-->
<!--            cb.customer_id = c.id-->
<!--        <where>-->
<!--            <if test="request.behaviorType != null and request.behaviorType != ''">-->
<!--                AND cb.behavior_type = #{request.behaviorType}-->
<!--            </if>-->
<!--        </where>-->
    </select>

    <select id="queryCustomerRelateInfo" resultMap="CustomerRelateInfoMap">
        select
        csr.customer_id,
        csr.source,
        ct.*,
        cb.*
        from
        customer_sales_relation csr
        left join customer_tags ct on csr.customer_id = ct.customer_id
        left join customer_behavior cb on csr.customer_id = cb.customer_id
        <where>
            <if test="request.customerIds != null and request.customerIds.size() != 0">
                <foreach collection="request.customerIds" item="item" index="index" open="csr.customer_id IN (" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="request.behaviorType != null and request.behaviorType != ''">
                AND cb.behavior_type = #{request.behaviorType}
            </if>
            <if test="request.activeStartTime != null">
                AND cb.created_at >= #{request.activeStartTime}
            </if>
            <if test="request.activeEndTime != null">
                AND cb.created_at &lt;= #{request.activeEndTime}
            </if>
            <if test="request.campPeriodId != null and request.campPeriodId != ''">
                AND csr.camp_period_id = #{request.campPeriodId}
            </if>
            <if test="request.companyId != null and request.companyId != ''">
                AND csr.company_id = #{request.companyId}
            </if>
            <if test="request.columnId != null and request.columnId != ''">
                AND csr.column_id = #{request.columnId}
            </if>
        </where>
    </select>

    <select id="queryCustomerCount" resultType="java.lang.Integer">
        select count(DISTINCT customer.id) from customer_000${shardingNum} customer
        <where>
            <if test="request.forbiddenStatus != null">
                AND customer.forbidden_status = #{request.forbiddenStatus}
            </if>
            <if test="request.redPacketStatus != null">
                AND customer.red_packet_status = #{request.redPacketStatus}
            </if>
            <if test="request.weworkStatus != null and request.weworkStatus != ''">
                AND customer.wework_status = #{request.weworkStatus}
            </if>
            <if test="request.mobileStatus != null and request.mobileStatus != ''">
                AND customer.mobile_status = #{request.mobileStatus}
            </if>
            <if test="request.wechatRemark != null and request.wechatRemark != ''">
                AND customer.wechat_remark LIKE CONCAT('%', #{request.wechatRemark}, '%')
            </if>
            <if test="request.nickname != null and request.nickname != ''">
                <choose>
                    <when test="request.nicknameQueryType == 1">
                        AND customer.nickname LIKE CONCAT('%', #{request.nickname}, '%')
                    </when>
                    <when test="request.nicknameQueryType == 2">
                        AND customer.nickname = #{request.nickname}
                    </when>
                    <otherwise>
                        AND customer.nickname LIKE CONCAT('%', #{request.nickname}, '%')
                    </otherwise>
                </choose>
            </if>
            <choose>
                <when test="request.customerId != null and request.customerId != ''">
                    and customer.id = #{request.customerId}
                </when>
                <when test="request.unionId != null and request.unionId != ''">
                    and customer.union_id = #{request.unionId}
                </when>
                <!-- 有request.customerId和request.unionId时不执行时间查询 -->
                <otherwise>
                    <if test="request.createStartTime != null">
                        AND customer.created_at >= #{request.createStartTime}
                    </if>
                    <if test="request.createEndTime != null">
                        AND customer.created_at &lt;= #{request.createEndTime}
                    </if>

                    <if test="request.activeStartTime != null">
                        AND customer.last_active_time >= #{request.activeStartTime}
                    </if>
                    <if test="request.activeEndTime != null">
                        AND customer.last_active_time &lt;= #{request.activeEndTime}
                    </if>
                </otherwise>
            </choose>

            <if test="request.mobile != null and request.mobile != ''">
                AND customer.mobile = #{request.mobile}
            </if>

            <if test="request.needJoinSales != null and request.needJoinSales != ''">
                AND EXISTS (
                select 1 from customer_sales_relation_000${shardingNum} csr
                <where>
                    csr.customer_id = customer.id
                    <choose>
                        <when test="request.perColumnId != null and request.perColumnId.size() > 0">
                            AND csr.column_id IN
                            <foreach item="item" collection="request.perColumnId" separator="," close=")" index="index" open="(">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            <if test="request.columnId != null and request.columnId != ''">
                                AND csr.column_id = #{request.columnId}
                            </if>
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="request.perCompanyId != null and request.perCompanyId.size() > 0">
                            AND csr.company_id IN
                            <foreach item="item" collection="request.perCompanyId" separator="," close=")" index="index" open="(">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            <if test="request.companyId != null and request.companyId != ''">
                                AND csr.company_id = #{request.companyId}
                            </if>
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="request.perSalesGroupId != null and request.perSalesGroupId.size() > 0">
                            AND csr.sales_group_id IN
                            <foreach item="item" collection="request.perSalesGroupId" separator="," close=")" index="index" open="(">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            <if test="request.salesGroupId != null and request.salesGroupId != ''">
                                AND csr.sales_group_id = #{request.salesGroupId}
                            </if>
                        </otherwise>
                    </choose>
                    <if test="request.campPeriodId != null and request.campPeriodId != ''">
                        AND csr.camp_period_id = #{request.campPeriodId}
                    </if>
                    <if test="request.salesId != null and request.salesId != ''">
                        AND csr.sales_id = #{request.salesId}
                    </if>
                </where>
                )
            </if>
        </where>
    </select>

</mapper>
