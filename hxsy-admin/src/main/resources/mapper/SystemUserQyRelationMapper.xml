<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.SystemUserQyRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.SystemUserQyRelation">
        <id column="id" property="id" />
        <result column="system_user_id" property="systemUserId" />
        <result column="qy_user_id" property="qyUserId" />
        <result column="corp_id" property="corpId" />
        <result column="active_code" property="activeCode" />
        <result column="account_type" property="accountType" />
        <result column="active_time" property="activeTime" />
        <result column="config_id" property="configId" />
        <result column="qr_code" property="qrCode" />
        <result column="qy_name" property="qyName" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, system_user_id, qy_user_id, corp_id, qy_name, active_code, account_type, active_time, config_id, qr_code, remark, status, created_by, created_at, updated_by, updated_at
    </sql>
    <sql id="Alias_Column_List">
        ${alias}.id, ${alias}.system_user_id, ${alias}.qy_user_id, ${alias}.corp_id, ${alias}.qy_name, ${alias}.active_code, ${alias}.account_type, ${alias}.active_time, ${alias}.config_id, ${alias}.qr_code, ${alias}.remark, ${alias}.status, ${alias}.created_by, ${alias}.created_at, ${alias}.updated_by, ${alias}.updated_at
    </sql>

    <select id="queryQyUserPage" resultType="cn.hxsy.datasource.model.entity.SystemUserQyRelation">
        SELECT
            suqr.id AS id,
            suqr.system_user_id AS systemUserId,
            suqr.qy_user_id AS qyUserId,
            suqr.qy_name AS qyName,
            suqr.corp_id AS corpId,
            suqr.active_code AS activeCode,
            suqr.account_type AS accountType,
            suqr.active_time AS activeTime,
            suqr.config_id AS configId,
            suqr.qr_code AS qrCode,
            suqr.status AS status,
            su.user_name AS systemUserName,
            su.status AS systemUserStatus,
            cqr.corp_name AS corpName,
            IFNULL(ea.account_status, 0) AS accountStatus
        FROM
            SYSTEM_USER_QY_RELATION suqr
                LEFT JOIN `SYSTEM_USER` su ON
                    suqr.SYSTEM_USER_ID = su.ID
                LEFT JOIN EXTERNAL_ACCOUNT ea ON
                    ea.ACTIVE_CODE = suqr.ACTIVE_CODE
                LEFT JOIN EXTERNAL_ACCOUNT_ORDER eao ON
                    ea.account_order_id = eao.id
                LEFT JOIN COMPANY_QY_RELATION cqr ON
                    cqr.corp_id = suqr.corp_id
        <where>
            <if test="qyUserRequest.systemUserName != null and qyUserRequest.systemUserName != ''">
                and su.user_name like concat('%', #{qyUserRequest.systemUserName} ,'%')
            </if>
            <if test="qyUserRequest.qyName != null and qyUserRequest.qyName != ''">
                and suqr.qy_name like concat('%', #{qyUserRequest.qyName} ,'%')
            </if>

            <if test="qyUserRequest.corpId != null and qyUserRequest.corpId != ''">
                and suqr.corp_id = #{qyUserRequest.corpId}
            </if>

            <if test="qyUserRequest.systemUserStatus != null and qyUserRequest.systemUserStatus != ''">
                and su.status = #{qyUserRequest.systemUserStatus}
            </if>

            <if test="qyUserRequest.qyStatus != null and qyUserRequest.qyStatus != ''">
                and suqr.status = #{qyUserRequest.qyStatus}
            </if>

            <if test="qyUserRequest.accountStatus != null">
                <if test="qyUserRequest.accountStatus == 0">
                    and ea.account_status is null or ea.account_status = 0
                </if>
                <if test="qyUserRequest.accountStatus != ''">
                    and ea.account_status = #{qyUserRequest.accountStatus}
                </if>
            </if>

            <if test="qyUserRequest.expireTimeStart != null">
                and eao.service_expire_time >= #{qyUserRequest.expireTimeStart}
            </if>
            <if test="qyUserRequest.expireTimeEnd != null">
                and eao.service_expire_time &lt;= #{qyUserRequest.expireTimeEnd}
            </if>

        </where>
    </select>

</mapper>
