<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CustomerBehaviorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CustomerBehavior">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="behavior_type" property="behaviorType" />
        <result column="course_id" property="courseId" />
        <result column="course_name" property="courseName" />
        <result column="company_id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="camp_period_id" property="campPeriodId" />
        <result column="camp_period_name" property="campPeriodName" />
        <result column="reward_amount" property="rewardAmount" />
        <result column="reward_type" property="rewardType" />
        <result column="reward_rule" property="rewardRule" />
        <result column="employee_name" property="employeeName" />
        <result column="employee_wework_name" property="employeeWeworkName" />
        <result column="enterprise_wechat_name" property="enterpriseWeChatName" />
        <result column="access_url" property="accessUrl" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <resultMap id="JoinResultMap" type="cn.hxsy.datasource.model.entity.CustomerBehavior">
        <result column="cb_id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="behavior_type" property="behaviorType" />
        <result column="course_id" property="courseId" />
        <result column="course_name" property="courseName" />
        <result column="company_id" property="companyId" />
        <result column="company_name" property="companyName" />
        <result column="cb_camp_period_id" property="campPeriodId" />
        <result column="cb_camp_period_name" property="campPeriodName" />
        <result column="reward_amount" property="rewardAmount" />
        <result column="reward_type" property="rewardType" />
        <result column="reward_rule" property="rewardRule" />
        <result column="employee_name" property="employeeName" />
        <result column="employee_wework_name" property="employeeWeworkName" />
        <result column="enterprise_wechat_name" property="enterpriseWeChatName" />
        <result column="access_url" property="accessUrl" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, behavior_type, course_id, course_name, company_id,
        company_name, camp_period_id, camp_period_name, reward_amount,
        reward_type, reward_rule, employee_name, employee_wework_name, company_name,
        access_url, remark, status, created_by, created_at, updated_by, updated_at
    </sql>

    <!-- 连接查询结果列 -->
    <sql id="Alias_Column_List">
        ${alias}.id as cb_id, ${alias}.customer_id, ${alias}.behavior_type, ${alias}.course_id, ${alias}.course_name, ${alias}.company_id,
        ${alias}.company_name, ${alias}.camp_period_id as cb_camp_period_id, ${alias}.camp_period_name as cb_camp_period_name, ${alias}.reward_amount,
        ${alias}.reward_type, ${alias}.reward_rule, ${alias}.employee_name, ${alias}.employee_wework_name, ${alias}.company_name,
        ${alias}.access_url, ${alias}.remark, ${alias}.status, ${alias}.created_by, ${alias}.created_at, ${alias}.updated_by, ${alias}.updated_at
    </sql>

    <select id="queryByType" resultMap="BaseResultMap">
        SELECT
            cb.id,
            cb.customer_id,
            cb.behavior_type,
            cb.course_id,
            cb.company_id,
            cb.camp_period_id,
            cb.reward_amount,
            cb.reward_type,
            cb.reward_rule,
            cb.employee_name,
            cb.employee_wework_name,
            cb.access_url,
            cb.remark,
            cb.status,
            cb.created_by,
            cb.created_at,
            cb.updated_by,
            cb.updated_at,
            cv.course_name,
            c.company_name,
            cp.campperiod_name as camp_period_name
        FROM customer_behavior cb
            LEFT JOIN company c ON cb.company_id = c.id
            LEFT JOIN camp_period cp ON cb.camp_period_id = cp.id
            LEFT JOIN course_video cv ON cb.course_id = cv.id
        <where>
            customer_id = #{request.customerId}
            <!-- 支持多个行为类型查询 -->
            <if test="request.behaviorTypes != null and !request.behaviorTypes.isEmpty()">
                AND behavior_type IN
                <foreach collection="request.behaviorTypes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.columnId != null">
                AND c.column_id = #{request.columnId}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

</mapper> 