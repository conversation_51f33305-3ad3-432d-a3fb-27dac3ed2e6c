<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CompanyQyRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CompanyQyRelation">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="corp_id" property="corpId" />
        <result column="corp_secret" property="corpSecret" />
        <result column="contact_secret" property="contactSecret" />
        <result column="customer_secret" property="customerSecret" />
        <result column="suite_id" property="suiteId" />
        <result column="suite_secret" property="suiteSecret" />
        <result column="suite_ticket" property="suiteTicket" />
        <result column="token" property="token" />
        <result column="encoding_aes_key" property="encodingAESKey" />
        <result column="open_corp_id" property="openCorpId" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, corp_id, corp_secret, contact_secret, customer_secret, suite_id, suite_secret, suite_ticket, token, encoding_aes_key, open_corp_id, remark, status, created_by, created_at, updated_by, updated_at
    </sql>

</mapper>
