<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.ColumnMapper">

    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.ColumnPO">
        <id property="id" column="id"/>
        <result property="columnName" column="column_name"/>
        <result property="headquartersId" column="headquarters_id"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="relPerson" column="rel_person"/>
        <result property="phone" column="phone"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedBy" column="updated_by"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, column_name, headquarters_id, remark, status, rel_person, phone, created_by, created_at, updated_by, updated_at
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> FROM `column` WHERE id = #{id}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `column` (column_name, headquarters_id, remark, status, rel_person, phone, created_by)
        VALUES (#{columnName}, #{headquartersId}, #{remark}, #{status}, #{relPerson}, #{phone}, #{createdBy})
    </insert>

</mapper>