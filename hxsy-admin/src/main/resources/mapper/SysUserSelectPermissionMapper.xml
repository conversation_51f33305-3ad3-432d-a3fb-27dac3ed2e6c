<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.datasource.mapper.SysUserSelectPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.SysUserSelectPermission">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="per_column_id" property="perColumnId" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="per_company_id" property="perCompanyId" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="per_sales_group_id" property="perSalesGroupId" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, per_column_id, per_company_id, per_sales_group_id, created_by, created_at, updated_by, updated_at
    </sql>

</mapper>
