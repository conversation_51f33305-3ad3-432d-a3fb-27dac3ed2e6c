<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CustomerCourseRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CustomerCourseRelation">
        <result column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="camp_period_id" property="campPeriodId" />
        <result column="course_id" property="courseId" />
        <result column="arrival_status" property="arrivalStatus" />
        <result column="remark" property="remark" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
        <result column="arrival_time" property="arrivalTime"/>
        <result column="complete_time" property="completeTime"/>
        <result column="play_progress" property="playProgress"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, camp_period_id, course_id,
        arrival_status, remark, status, created_by, created_at, updated_by, updated_at,
        arrival_time, complete_time, play_progress
    </sql>
    <sql id="Alias_Column_List">
        ${alias}.id, ${alias}.customer_id, ${alias}.camp_period_id, ${alias}.course_id,
        ${alias}.arrival_status, ${alias}.remark, ${alias}.status, ${alias}.created_by, ${alias}.created_at, ${alias}.updated_by, ${alias}.updated_at,
        ${alias}.arrival_time, ${alias}.complete_time, ${alias}.play_progress
    </sql>

    <!-- 根据客户id和栏目id查询课程 -->
    <select id="getByCustomerIdAndColumnId" resultMap="BaseResultMap">
        select
            <include refid="Alias_Column_List" >
                <property name="alias" value="ccr" />
            </include>
        from
            customer_course_relation ccr
                left  join camp_period cp on ccr.camp_period_id = cp.id
                left join company c on cp.company_id = c.id
        <where>
            customer_id = #{customerId}
            and c.column_id = #{columnId}
        </where>
    </select>

</mapper>
