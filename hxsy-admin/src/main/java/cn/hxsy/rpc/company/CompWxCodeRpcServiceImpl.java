package cn.hxsy.rpc.company;

import cn.hxsy.api.user.service.CompWxCodeRpcService;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CompWxCode;
import cn.hxsy.service.CompWxCodeService;
import cn.hxsy.service.CompanyService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


/**
 * 类名称：$CLASSNAME$
 * <p>
 * 描述：
 * 创建人: jinseyeon
 * 创建时间: 2025/04/19
 */

@DubboService(version = "1.0.0")
@Component
@Slf4j
public class CompWxCodeRpcServiceImpl implements CompWxCodeRpcService {


    @Autowired
    private CompWxCodeService compWxCodeService;

    @Override
    public boolean saveCompWxCode(Integer companyId, String appid, String sellUrl) {
        CompWxCode existingCode = compWxCodeService.lambdaQuery()
                .eq(CompWxCode::getCompanyId, companyId)
                .eq(CompWxCode::getAppid, appid)
                .one();

        CompWxCode compWxCode = existingCode != null ? existingCode : new CompWxCode();
        compWxCode.setCompanyId(companyId);
        compWxCode.setAppid(appid);
        compWxCode.setSellUrl(sellUrl);
        compWxCode.setCreatedAt(existingCode != null ? existingCode.getCreatedAt() : LocalDateTime.now());
        return compWxCodeService.saveOrUpdate(compWxCode);

    }
}
