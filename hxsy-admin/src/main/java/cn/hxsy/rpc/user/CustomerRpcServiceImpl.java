package cn.hxsy.rpc.user;

import cn.hxsy.api.user.service.CustomerRpcService;
import cn.hxsy.base.exception.system.code.WePayErrorCode;
import cn.hxsy.base.request.wxPayRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.Customer;
import cn.hxsy.service.CustomerBehaviorService;
import cn.hxsy.service.CustomerCourseRelationService;
import cn.hxsy.service.CustomerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@DubboService(version = "1.0.0")
@Component
@Slf4j
public class CustomerRpcServiceImpl implements CustomerRpcService {

    @Autowired
    private CustomerBehaviorService customerBehaviorService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerCourseRelationService customerCourseRelationService;

    /**
     * description : 记录行为轨迹-领取红包
     * @title: saveReceiveRedPacket
     * @param: customerId
     * @param: companyId
     * @param: campPeriodId
     * @param: courseId
     * @param: amount
     * @param: type
     * <AUTHOR>
     * @date 2025/5/10 16:31
     * @return Result
     */
    @Override
    public Result saveReceiveRedPacket(Long customerId, Long companyId, Long campPeriodId, Long courseId, String amount, Integer type) {
        boolean b = customerBehaviorService.saveReceiveRedPacket(customerId, companyId, campPeriodId, courseId, amount, type);
        if (b) {
            return Result.ok();
        }
        return Result.error("记录行为轨迹-领取红包 失败");
    }

    @Override
    public Result<Integer> getRedPacketStatus(wxPayRequest wxPayRequest) {
        Long customerId = wxPayRequest.getCustomerId();
        Long campPeriodId = wxPayRequest.getCampPeriodId();
        Long courseId = wxPayRequest.getCourseId();
        
        // 1. 检查用户红包状态
        Customer customer = customerService.getById(customerId);
        Integer redPacketStatus = customer.getRedPacketStatus();
        
        // 2. 如果红包状态允许领取，再检查课程完成状态
        if (redPacketStatus != null && redPacketStatus == 0) {
            // 检查用户是否参加这节课并且已完成课程
            Boolean courseCompleted = customerCourseRelationService.isCourseArrival(customerId, campPeriodId, courseId);
            if (!courseCompleted) {
                log.info("用户[{}]未完成课程[{}]，不允许领取红包", customerId, courseId);
                return Result.error(WePayErrorCode.PAY_ERROR.getMessage());
            }
        }
        
        return Result.ok(redPacketStatus);
    }

    @Override
    public Result<Boolean> updateRedPacketAndUseStatus(wxPayRequest wxPayRequest) {
        return Result.ok(customerService.updateRedPacketAndUseStatus(wxPayRequest));
    }

}
