package cn.hxsy.rpc.user;

import cn.hxsy.api.user.model.response.SystemUserQyRelationResponse;
import cn.hxsy.api.user.service.SystemUserQyRelationRpcService;
import cn.hxsy.base.request.SystemUserQyRelationRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.service.CompanyQyRelationService;
import cn.hxsy.service.SystemUserQyRelationService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.List;

@DubboService(version = "1.0.0")
@Component
@Slf4j
public class SystemUserQyRelationRpcServiceImpl implements SystemUserQyRelationRpcService {

    @Resource
    private SystemUserQyRelationService systemUserQyRelationService;

    @Resource
    private CompanyQyRelationService companyQyRelationService;

    @Override
    public Result<Object> querySystemQyUserInner(SystemUserQyRelationRequest request) {
        LambdaQueryWrapper<SystemUserQyRelation> queryWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
        queryWrapper.eq(SystemUserQyRelation::getSystemUserId, request.getSystemUserId());
        List<SystemUserQyRelation> list = systemUserQyRelationService.list(queryWrapper);

        if (list.isEmpty()) {
            return Result.ok(new ArrayList<>());
        }
        // 收集所有的corpId
        Set<String> corpIds = list.stream()
                .map(SystemUserQyRelation::getCorpId)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());
        // 批量查询企业信息
        Map<String, String> corpIdToNameMap = new HashMap<>();
        if (!corpIds.isEmpty()) {
            LambdaQueryWrapper<CompanyQyRelation> companyWrapper = Wrappers.lambdaQuery(CompanyQyRelation.class);
            companyWrapper.in(CompanyQyRelation::getCorpId, corpIds);
            List<CompanyQyRelation> companyQyRelations = companyQyRelationService.list(companyWrapper);
            corpIdToNameMap = companyQyRelations.stream()
                    .collect(Collectors.toMap(
                            CompanyQyRelation::getCorpId,
                            CompanyQyRelation::getCorpName,
                            (existing, replacement) -> existing
                    ));
        }
        // 转换为响应对象
        List<SystemUserQyRelationResponse> responseList = new ArrayList<>();
        for (SystemUserQyRelation relation : list) {
            SystemUserQyRelationResponse response = new SystemUserQyRelationResponse();
            response.setId(String.valueOf(relation.getId()));
            response.setSystemUserId(relation.getSystemUserId());
            response.setQyUserId(relation.getQyUserId());
            response.setQyName(relation.getQyName());
            response.setCorpId(relation.getCorpId());
            // 从Map中获取企业名称
            if (StrUtil.isNotEmpty(relation.getCorpId())) {
                response.setCorpName(corpIdToNameMap.get(relation.getCorpId()));
            }
            responseList.add(response);
        }
        return Result.ok(responseList);
    }

}
