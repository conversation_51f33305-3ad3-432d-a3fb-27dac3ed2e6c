package cn.hxsy.rpc.user;

import cn.hxsy.api.user.model.request.*;
import cn.hxsy.api.user.model.response.CustomerResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.api.user.model.response.UserInfoResponse;
import cn.hxsy.api.user.service.UserInfoRpcService;
import cn.hxsy.base.enums.AccountTypeEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.datasource.model.entity.CustomerCourseRelation;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.datasource.model.entity.User;
import cn.hxsy.datasource.model.entity.UserAuth;
import cn.hxsy.service.CustomerService;
import cn.hxsy.service.SystemUserService;
import cn.hxsy.service.UserAuthService;
import cn.hxsy.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Xxxxx
 * @ClassName : UserInfoRpcServiceImpl
 * @description : UserInfoRpcServiceImpl
 * @date: 2025-03-31 22:08
 */

@DubboService(version = "1.0.0")
@Component
@Slf4j
public class UserInfoRpcServiceImpl implements UserInfoRpcService {

    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private CustomerService customerService;

    @Override
    public Result<Object> query(UserInfoRequest userQueryRequest) {
        //区分业务人员与客户，将获取id转为业务、客户对应id进行主键查询
        if(AccountTypeEnum.SYSTEM_USER.getCodeToString().equals(userQueryRequest.getUserType())){
            SystemUserResponse systemUserResponse = systemUserService.registerWxUser(userQueryRequest.getUnionId(), userQueryRequest.getId());
            return Result.ok(systemUserResponse);
        }else {
            CustomerLoginRequest customerLoginRequest = new CustomerLoginRequest();
            customerLoginRequest.setCustomerId(userQueryRequest.getId());
            customerLoginRequest.setUnionId(userQueryRequest.getUnionId());
            customerLoginRequest.setOpenid(userQueryRequest.getOpenid());
            CustomerResponse customerResponse = customerService.customerLogin(customerLoginRequest);
            return Result.ok(customerResponse);
        }
    }

    @Override
    public Result<Object> register(UserRegisterRequest userRegisterRequest) {
        //区分业务人员与客户
        if(AccountTypeEnum.SYSTEM_USER.getCodeToString().equals(userRegisterRequest.getUserType())){
            SystemUserResponse systemUserResponse = systemUserService.registerWxUser(userRegisterRequest.getUnionId(), null);
            systemUserResponse.setUserType(AccountTypeEnum.SYSTEM_USER.getCodeToString());
            UserAuth userAuth = new UserAuth();
            userAuth.setUnionId(userRegisterRequest.getUnionId());
            userAuth.setUserType(AccountTypeEnum.SYSTEM_USER.getCode());
            userAuth.setId(systemUserResponse.getId());
            if(StringUtils.isEmpty(systemUserResponse.getIsRegister())){
                userAuthService.getBaseMapper().insert(userAuth);
            }
            return Result.ok(systemUserResponse);
        } else {
            CustomerRegisterRequest customerRegisterRequest = new CustomerRegisterRequest();
            BeanUtils.copyProperties(userRegisterRequest, customerRegisterRequest);
            CustomerResponse customerResponse = customerService.customerRegister(customerRegisterRequest);
            customerResponse.setUserType(AccountTypeEnum.CUSTER.getCodeToString());
            return Result.ok(customerResponse);
        }
    }

    @Override
    public Result<SystemUserResponse> PcLogin(PcLoginRequest pcLoginRequest) {
        SystemUserResponse systemUserResponse = systemUserService.PcLogin(pcLoginRequest);
        if(systemUserResponse == null){
            return Result.error("账号或密码错误");
        }
        return Result.ok(systemUserResponse);
    }
}
