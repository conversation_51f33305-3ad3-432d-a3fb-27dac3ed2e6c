package cn.hxsy.rpc.user;

import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.user.service.CompanyQyRelationRpcService;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.CompanyQyRelationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
* @description:
* @author: xiaQL
* @date: 2025/6/17 22:01
*/
@DubboService(version = "1.0.0")
@Component
@Slf4j
public class CompanyQyRelationRpcServiceImpl implements CompanyQyRelationRpcService {


    @Autowired
    private CompanyQyRelationService companyQyRelationService;

    @Override
    public Result<Object> query(QyAppReq qyAppReq) {
        return companyQyRelationService.queryCompanyQyToken(qyAppReq);
    }
}
