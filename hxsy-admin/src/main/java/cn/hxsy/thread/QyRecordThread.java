package cn.hxsy.thread;

import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.request.CustomerTransferSysRequest;
import cn.hxsy.service.CompanyQyRelationService;
import cn.hxsy.service.CustomerWecomBindingService;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.utils.WeComTokenGenerator;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
* @description: 企业回调异步更新线程池
* @author: xiaQL
* @date: 2025/6/29 18:11
*/
@Component
public class QyRecordThread {

    @Resource
    private QyAuthService qyAuthService;

    @Resource
    private CompanyQyRelationService companyQyRelationService;

    @Resource
    private CustomerWecomBindingService customerWecomBindingService;

    @Async("threadPoolTaskExecutor")
    public void getCorpInfoAndSave(String authCode, String authType) {
        // 生成TOKEN以及EncodingAESKey
        String token = WeComTokenGenerator.generateToken(32);
        String encodingAESKey = WeComTokenGenerator.generateEncodingAESKey();
        qyAuthService.getPermanentCode(authCode, authType, token, encodingAESKey);
    }

    // companyQyRelationService.updateById(companyQyRelation);
    @Async("threadPoolTaskExecutor")
    public void updateCompanyQyRelation(CompanyQyRelation companyQyRelation) {
        companyQyRelationService.updateById(companyQyRelation);
    }

    /**
    * @description: 异步转接客户企微关系
    * @author: xiaQL
    * @date: 2025/8/2 13:28
    */
    @Async("threadPoolTaskExecutor")
    public void transferCustomerAsync(CustomerTransferSysRequest request) {
        customerWecomBindingService.transferCustomer(request);
    }

}
