package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.WechatUserOpenid;
import cn.hxsy.service.WechatUserOpenidService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 微信用户openid关联控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "微信用户openid关联管理")
@RestController
@RequestMapping("/api/v1/wechat-user-openid")
@RequiredArgsConstructor
public class WechatUserOpenidController {

    @Autowired
    private WechatUserOpenidService wechatUserOpenidService;

    /**
     * 新增微信用户openid关联信息
     *
     * @param wechatUserOpenid 微信用户openid关联信息对象
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody WechatUserOpenid wechatUserOpenid) {
        return Result.ok(wechatUserOpenidService.add(wechatUserOpenid));
    }

    /**
     * 根据ID删除微信用户openid关联信息
     *
     * @param id 微信用户openid关联ID
     * @return 操作结果
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(wechatUserOpenidService.deleteById(id));
    }

    /**
     * 更新微信用户openid关联信息
     *
     * @param wechatUserOpenid 微信用户openid关联信息对象
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody WechatUserOpenid wechatUserOpenid) {
        return Result.ok(wechatUserOpenidService.update(wechatUserOpenid));
    }

    /**
     * 根据ID查询微信用户openid关联信息
     *
     * @param id 微信用户openid关联ID
     * @return 返回微信用户openid关联信息，如果不存在返回null
     */
    @ApiOperation("根据ID查询微信用户openid关联")
    @GetMapping("/{id}")
    public Result<WechatUserOpenid> getById(@ApiParam("微信用户openid关联ID") @PathVariable Long id) {
        return Result.ok(wechatUserOpenidService.getById(id));
    }

    /**
     * 根据appid和openid查询微信用户openid关联信息
     *
     * @param appId 小程序appid
     * @param openid 微信openid
     * @return 微信用户openid关联信息
     */
    @GetMapping("/getByAppIdAndOpenid")
    public Result<WechatUserOpenid> getByAppIdAndOpenid(
            @RequestParam String appId,
            @RequestParam String openid) {
        return Result.ok(wechatUserOpenidService.getByAppIdAndOpenid(appId, openid));
    }

} 