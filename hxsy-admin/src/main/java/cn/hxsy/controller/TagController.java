package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.TagGroupTreeResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.Tag;
import cn.hxsy.service.TagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "标签管理")
@RestController
@RequestMapping("/api/v1/tag")
@RequiredArgsConstructor
public class TagController {

    @Autowired
    private TagService tagService;

    /**
     * 新增标签
     *
     * @param tag 标签信息
     * @return 保存后的标签信息
     * <AUTHOR>
     * @date 2024-04-01
     */
    @ApiOperation("新增标签")
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody Tag tag) {
        return Result.ok(tagService.add(tag));
    }

    /**
     * 删除标签
     *
     * @param id 标签ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @ApiOperation("删除标签")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(tagService.deleteById(id));
    }

    /**
     * 更新标签
     *
     * @param tag 标签信息
     * @return 更新后的标签信息
     * <AUTHOR>
     * @date 2024-04-01
     */
    @ApiOperation("更新标签")
    @PostMapping("/update")
    public Result<Boolean> update(@RequestBody Tag tag) {
        return Result.ok(tagService.update(tag));
    }

    /**
     * 根据ID查询标签
     *
     * @param id 标签ID
     * @return 标签信息
     * <AUTHOR>
     * @date 2024-04-01
     */
    @ApiOperation("根据ID查询标签")
    @GetMapping("/{id}")
    public Tag getById(@PathVariable Long id) {
        return tagService.getById(id);
    }

    /**
     * 根据标签组ID查询标签列表
     *
     * @param groupId 标签组ID
     * @return 标签列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    @ApiOperation("根据标签组ID查询标签列表")
    @GetMapping("/list-by-groupid")
    public Result<List<Tag>> listByGroupId(@RequestParam Long groupId) {
        return Result.ok(tagService.getByGroupId(groupId));
    }

    /**
     * 查询所有标签组及其标签（两级结构）
     *
     * @return 标签组及其标签列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    @ApiOperation("查询所有标签组及其标签（两级结构）")
    @GetMapping("/list-allgroups-with-tags")
    public Result<List<TagGroupTreeResponse>> listAllGroupsWithTags() {
        return Result.ok(tagService.getAllTagGroupsWithTags());
    }

    @ApiOperation("根据客户ID查询标签列表")
    @GetMapping("/list-tags-by-customerid")
    public Result<List<Tag>> listTagsByCustomerId(@RequestParam Long customerId) {
        return Result.ok(tagService.getTagsByCustomerId(customerId));
    }
} 