package cn.hxsy.controller;

import cn.hxsy.api.qy.QyBaseResponse;
import cn.hxsy.api.qy.request.tag.TagAddRequest;
import cn.hxsy.api.qy.request.tag.TagListRequest;
import cn.hxsy.api.qy.response.tag.TagAddResponse;
import cn.hxsy.api.qy.response.tag.TagCorpResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.WecomTagSyncRecord;
import cn.hxsy.dto.BatchCustomerMarkTagDTO;
import cn.hxsy.dto.CustomerMarkTagDTO;
import cn.hxsy.dto.DeleteCorpTagRequest;
import cn.hxsy.dto.WecomTagGroupDTO;
import cn.hxsy.service.CustomerWecomTagService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 企业微信客户标签控制器
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@RestController
@RequestMapping("/api/v1/customer-wecom-tag")
@Api(tags = "企业微信客户标签管理", description = "提供企业微信客户标签的增删改查功能")
@Slf4j
public class CustomerWecomTagController {

    @Autowired
    private CustomerWecomTagService customerWecomTagService;

    /**
     * 获取企业标签库
     * 获取企业设置的所有客户标签，可以通过标签ID或标签组ID进行筛选
     *
     * @param corpId 企业微信ID
     * @return 企业标签库信息，包含标签组和标签列表
     */
    @PostMapping("/list")
    @ApiOperation(value = "获取企业标签库", notes = "获取企业设置的所有客户标签，可以通过标签ID或标签组ID进行筛选")
    public Result<TagCorpResponse> getCorpTagList(
            @ApiParam(value = "企业微信ID", required = true, example = "ww12345678") @RequestParam String corpId,
            @ApiParam(value = "标签组ID") @RequestBody(required = false) TagListRequest request) {
        log.info("获取企业标签库，企业ID：{}，标签请求信息：{}", corpId, request);
        
        TagCorpResponse response = customerWecomTagService.getCorpTagList(corpId, request);
        
        if (response != null && "0".equals(response.getErrcode())) {
            return Result.ok(response);
        } else {
            return Result.error(response != null ? response.getErrmsg() : "获取企业标签库失败");
        }
    }

    /**
     * 添加企业客户标签
     * 可以创建新的标签组或在已有的标签组中添加标签
     *
     * @param corpId 企业微信ID
     * @param groupId 标签组ID，如果创建的是标签，则需要指定此参数
     * @param groupName 标签组名称，如果创建的是标签组，则需要指定此参数
     * @param order 标签组次序值，非必填，默认为0
     * @param tagList 标签列表，包含标签名称和次序值
     * @return 添加标签结果，包含创建的标签组和标签信息
     */
    @PostMapping("/add")
    @ApiOperation(value = "添加企业客户标签", notes = "可以创建新的标签组或在已有的标签组中添加标签")
    public Result<TagAddResponse> addCorpTag(
            @ApiParam(value = "企业微信ID", required = true, example = "ww12345678") @RequestParam String corpId,
            @ApiParam(value = "标签组ID，创建标签时必填", example = "group123456") @RequestParam(required = false) String groupId,
            @ApiParam(value = "标签组名称，创建标签组时必填", example = "客户等级") @RequestParam(required = false) String groupName,
            @ApiParam(value = "标签组次序值，数字越大排序越靠前", example = "1") @RequestParam(required = false) Integer order,
            @ApiParam(value = "标签列表，包含标签名称和次序值") @RequestBody(required = false) List<TagAddRequest.TagInfo> tagList) {
        log.info("添加企业客户标签，企业ID：{}，标签组ID：{}，标签组名称：{}", corpId, groupId, groupName);
        
        TagAddResponse response = customerWecomTagService.addCorpTag(corpId, groupId, groupName, order, tagList);
        
        if (response != null && "0".equals(response.getErrcode())) {
            return Result.ok(response);
        } else {
            return Result.error(response != null ? response.getErrmsg() : "添加企业客户标签失败");
        }
    }

    /**
     * 编辑企业客户标签
     *
     * @param corpId 企业微信ID
     * @param id 标签或标签组的ID
     * @param name 新的标签或标签组名称
     * @param order 标签/标签组的次序值，非必填，默认为0
     * @param groupId 标签组ID，如果要修改标签所属的标签组，需要填写此参数
     * @param isGroup 是否是标签组，默认false
     * @return 编辑标签结果
     */
    @PutMapping("/edit")
    @ApiOperation("编辑企业客户标签")
    public Result<Void> editCorpTag(
            @ApiParam(value = "企业微信ID", required = true) @RequestParam String corpId,
            @ApiParam(value = "标签或标签组的ID", required = true) @RequestParam String id,
            @ApiParam(value = "新的标签或标签组名称", required = true) @RequestParam String name,
            @ApiParam(value = "标签/标签组的次序值") @RequestParam(required = false) Integer order,
            @ApiParam(value = "标签组ID") @RequestParam(required = false) String groupId,
            @ApiParam(value = "是否是标签组") @RequestParam(required = false, defaultValue = "false") Boolean isGroup) {
        log.info("编辑企业客户标签，企业ID：{}，标签/标签组ID：{}，新名称：{}", corpId, id, name);
        
        QyBaseResponse response = customerWecomTagService.editCorpTag(corpId, id, name, order, groupId, isGroup);
        
        if (response != null && "0".equals(response.getErrcode())) {
            return Result.ok();
        } else {
            return Result.error(response != null ? response.getErrmsg() : "编辑企业客户标签失败");
        }
    }

    /**
     * 删除企业客户标签
     *
     * @param request 删除标签请求参数
     * @return 删除标签结果
     */
        @PostMapping("/delete")
        @ApiOperation("删除企业客户标签")
        public Result<Void> deleteCorpTag(@RequestBody DeleteCorpTagRequest request) {
        log.info("删除企业客户标签，企业ID：{}，标签ID数量：{}，标签组ID数量：{}", 
                request.getCorpId(), 
                request.getTagIds() != null ? request.getTagIds().size() : 0, 
                request.getGroupIds() != null ? request.getGroupIds().size() : 0);
        
        QyBaseResponse response = customerWecomTagService.deleteCorpTag(
                request.getCorpId(), 
                request.getTagIds(), 
                request.getGroupIds(), 
                request.getDeleteTagWithGroup());
        
        if (response != null && "0".equals(response.getErrcode())) {
            return Result.ok();
        } else {
            return Result.error(response != null ? response.getErrmsg() : "删除企业客户标签失败");
        }
    }
    
    /**
     * 获取本地数据库中的企微标签组及标签
     * 
     * @param corpId 企业微信ID
     * @return 标签组及标签列表
     */
    @GetMapping("/local")
    @ApiOperation(value = "获取本地标签组及标签", notes = "从本地数据库获取企业微信标签组及标签数据，不调用企业微信API")
    public Result<List<WecomTagGroupDTO>> getLocalTagGroups(
            @ApiParam(value = "企业微信ID", required = true, example = "ww12345678") @RequestParam String corpId) {
        log.info("获取本地数据库中的企微标签组及标签，企业ID：{}", corpId);
        
        try {
            List<WecomTagGroupDTO> tagGroups = customerWecomTagService.getLocalTagGroups(corpId);
            return Result.ok(tagGroups);
        } catch (Exception e) {
            log.error("获取本地数据库中的企微标签组及标签失败", e);
            return Result.error("获取本地标签数据失败：" + e.getMessage());
        }
    }
    
    /**
     * 为客户打标签
     * 
     * @param dto 客户打标签请求参数
     * @return 操作结果
     */
    @PostMapping("/mark")
    @ApiOperation(value = "为客户打标签", notes = "为指定的客户添加或移除标签")
    public Result<Void> markCustomerTag(@RequestBody CustomerMarkTagDTO dto) {
        log.info("为客户打标签，企业ID：{}，成员ID：{}，客户ID：{}，添加标签数：{}，移除标签数：{}", 
                dto.getCorpId(), dto.getUserId(), dto.getExternalUserId(), 
                dto.getAddTagIds() != null ? dto.getAddTagIds().size() : 0, 
                dto.getRemoveTagIds() != null ? dto.getRemoveTagIds().size() : 0);
        
        if ((dto.getAddTagIds() == null || dto.getAddTagIds().isEmpty()) && 
            (dto.getRemoveTagIds() == null || dto.getRemoveTagIds().isEmpty())) {
            return Result.error("添加标签和移除标签不能同时为空");
        }
        
        QyBaseResponse response = customerWecomTagService.markCustomerTag(dto);
        
        if (response != null && "0".equals(response.getErrcode())) {
            return Result.ok();
        } else {
            return Result.error(response != null ? response.getErrmsg() : "为客户打标签失败");
        }
    }
    
    /**
     * 批量为客户打标签
     * 
     * @param dto 批量客户打标签请求参数
     * @return 操作结果
     */
    @PostMapping("/batch-mark")
    @ApiOperation(value = "批量为客户打标签", notes = "批量为多个客户添加或移除标签")
    public Result<Void> batchMarkCustomerTag(@RequestBody BatchCustomerMarkTagDTO dto) {
        log.info("批量为客户打标签，企业ID：{}，标签请求数量：{}", 
                dto.getCorpId(), dto.getTagList() != null ? dto.getTagList().size() : 0);
        
        if (dto.getTagList() == null || dto.getTagList().isEmpty()) {
            return Result.error("标签请求列表不能为空");
        }
        
        QyBaseResponse response = customerWecomTagService.batchMarkCustomerTag(dto.getCorpId(), dto.getTagList());
        
        if (response != null && "0".equals(response.getErrcode())) {
            return Result.ok();
        } else {
            return Result.error(response != null ? response.getErrmsg() : "批量为客户打标签失败");
        }
    }
    
    /**
     * 分页查询企业微信标签同步记录
     * 
     * @param current 当前页码
     * @param size 每页大小
     * @param corpId 企业微信ID
     * @param syncType 同步类型：1-全量同步，2-指定标签组同步
     * @param status 同步状态：0-进行中，1-成功，2-失败
     * @return 分页结果
     */
    @GetMapping("/sync-records")
    @ApiOperation(value = "查询标签同步记录", notes = "分页查询企业微信标签同步记录")
    public Result<IPage<WecomTagSyncRecord>> getSyncRecords(
            @ApiParam(value = "当前页码", example = "1") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam(value = "企业微信ID", required = true, example = "ww12345678") @RequestParam String corpId,
            @ApiParam(value = "同步类型：1-全量同步，2-指定标签组同步", example = "1") @RequestParam(required = false) Integer syncType,
            @ApiParam(value = "同步状态：0-进行中，1-成功，2-失败", example = "1") @RequestParam(required = false) Integer status) {
        log.info("分页查询企业微信标签同步记录，企业ID：{}，同步类型：{}，同步状态：{}", corpId, syncType, status);
        
        try {
            // 创建分页参数
            Page<WecomTagSyncRecord> page = new Page<>(current, size);
            
            // 调用服务查询同步记录
            IPage<WecomTagSyncRecord> result = customerWecomTagService.getSyncRecords(page, corpId, syncType, status);
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("分页查询企业微信标签同步记录失败", e);
            return Result.error("查询同步记录失败：" + e.getMessage());
        }
    }
}
