package cn.hxsy.controller;

import cn.hxsy.api.system.request.SysMenuRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.SysMenuService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 系统菜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:04:30
 */
@RestController
@RequestMapping("/api/v1/sys-menu")
public class SysMenuController {

    @Autowired
    private SysMenuService sysMenuService;

    @ApiOperation("获取当前系统具有菜单列表")
    @PostMapping("/get-system-menu-page")
    public Result<Object> getSystemMenu(@RequestBody SysMenuRequest request) {
        return Result.ok(sysMenuService.querySystemMenu(request));
    }

    @ApiOperation("保存或更新系统菜单")
    @PostMapping("/save-update-system-menu")
    public Result<Object> saveOrUpdateSystemMenu(@RequestBody SysMenuRequest request) {
        return Result.ok(sysMenuService.saveOrUpdateSystemMenu(request));
    }
}
