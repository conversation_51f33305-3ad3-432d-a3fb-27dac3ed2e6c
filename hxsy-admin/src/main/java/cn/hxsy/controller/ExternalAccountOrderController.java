package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.ExternalAccountOrder;
import cn.hxsy.service.ExternalAccountOrderService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "互通账号订单管理")
@RestController
@RequestMapping("/api/v1/external-account-order")
@RequiredArgsConstructor
public class ExternalAccountOrderController {

    @Autowired
    private ExternalAccountOrderService externalAccountOrderService;

    @ApiOperation("删除订单")
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(externalAccountOrderService.removeById(id));
    }

    @ApiOperation("根据ID查询订单")
    @GetMapping("/{id}")
    public Result<Object> getById(@PathVariable Long id) {
        return Result.ok(externalAccountOrderService.getById(id));
    }

    @ApiOperation("创建订单")
    @PostMapping("/create")
    public Result<Boolean> create(@RequestBody ExternalAccountOrder order) {
        return Result.ok(externalAccountOrderService.createOrder(order));
    }

    @ApiOperation("创建续费订单")
    @PostMapping("/createRenewal")
    public Result<Boolean> createRenewal(@RequestBody ExternalAccountOrder order) {
        return Result.ok(externalAccountOrderService.createRenewalOrder(order));
    }

    @ApiOperation("分页查询互通账号订单列表")
    @GetMapping("/query/page")
    public Result<Page<ExternalAccountOrder>> queryPage(
            @ApiParam("当前页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("企业ID") @RequestParam(required = true) String corpId,
            @ApiParam("订单ID") @RequestParam(required = false) String orderId,
            @ApiParam("订单类型") @RequestParam(required = false) Integer orderType,
            @ApiParam("支付状态") @RequestParam(required = false) Integer payStatus
            ) {
        ExternalAccountOrder externalAccountOrder = new ExternalAccountOrder();
        externalAccountOrder.setCorpId(corpId);
        externalAccountOrder.setOrderId(orderId);
        externalAccountOrder.setOrderType(orderType);
        externalAccountOrder.setPayStatus(payStatus);
        return Result.ok(externalAccountOrderService.queryPage(pageNum, pageSize, externalAccountOrder));
    }

    @ApiOperation("手动获取订单中激活码")
    @GetMapping("/getActivationCode")
    public Result<Boolean> getActivationCode(@RequestParam("orderId") String orderId) {
        return Result.ok(externalAccountOrderService.getActivationCode(orderId));
    }
}