package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.CustomerBehaviorPageRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CustomerBehavior;
import cn.hxsy.service.CustomerBehaviorService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 客户行为轨迹控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户行为轨迹管理")
@RestController
@RequestMapping("/api/v1/customer-behavior")
@RequiredArgsConstructor
@Slf4j
public class CustomerBehaviorController {

    private final CustomerBehaviorService customerBehaviorService;

    /**
     * 根据客户ID查询行为轨迹列表
     *
     * @param customerId 客户ID
     * @return 行为轨迹列表
     */
    @ApiOperation("根据客户ID查询行为轨迹列表")
    @GetMapping("/list-by-customerid")
    public Result<List<CustomerBehavior>> listByCustomerId(@RequestParam Long customerId) {
        return Result.ok(customerBehaviorService.getByCustomerId(customerId));
    }

    /**
     * 分页查询客户行为轨迹信息
     *
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param customerBehaviorPageRequest
     * @return 分页结果
     */
    @ApiOperation("分页查询客户行为轨迹信息")
    @PostMapping("/page")
    public Result<Page<CustomerBehavior>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestBody @Valid CustomerBehaviorPageRequest customerBehaviorPageRequest) {
        return Result.ok(customerBehaviorService.page(pageNum, pageSize, customerBehaviorPageRequest));
    }
}