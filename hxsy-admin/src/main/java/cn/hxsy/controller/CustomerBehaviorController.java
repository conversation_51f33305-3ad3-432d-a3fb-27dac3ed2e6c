package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.CustomerBehaviorPageRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CustomerBehavior;
import cn.hxsy.service.CustomerBehaviorService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 客户行为轨迹控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户行为轨迹管理")
@RestController
@RequestMapping("/api/v1/customer-behavior")
@RequiredArgsConstructor
@Slf4j
public class CustomerBehaviorController {

    private final CustomerBehaviorService customerBehaviorService;

    /**
     * 根据客户ID查询行为轨迹列表
     *
     * @param customerId 客户ID
     * @return 行为轨迹列表
     */
    @ApiOperation("根据客户ID查询行为轨迹列表")
    @GetMapping("/list-by-customerid")
    public Result<List<CustomerBehavior>> listByCustomerId(@RequestParam Long customerId) {
        return Result.ok(customerBehaviorService.getByCustomerId(customerId));
    }

    /**
     * 分页查询客户行为轨迹信息
     *
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param customerBehaviorPageRequest
     * @return 分页结果
     */
    @ApiOperation("分页查询客户行为轨迹信息")
    @PostMapping("/page")
    public Result<Page<CustomerBehavior>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestBody @Valid CustomerBehaviorPageRequest customerBehaviorPageRequest) {
        return Result.ok(customerBehaviorService.page(pageNum, pageSize, customerBehaviorPageRequest));
    }

    @ApiOperation("记录添加企微行为")
    @PostMapping("/add-wechat-enterprise")
    public Result<Boolean> addWechatEnterprise(
            @ApiParam("客户ID") @RequestParam Long customerId,
            @ApiParam("企业ID") @RequestParam Long companyId,
            @ApiParam("企微企业ID") @RequestParam String corpId,
            @ApiParam("企业名称") @RequestParam String corpName,
            @ApiParam("员工姓名") @RequestParam String employeeName,
            @ApiParam("员工企微昵称") @RequestParam String employeeWeworkName
    ) {
        log.info("记录添加企微行为: customerId={}, companyId={}, corpId={}, corpName={}, employeeName={}, employeeWeworkName={}",
                customerId, companyId, corpId, corpName, employeeName, employeeWeworkName);

        boolean result = customerBehaviorService.saveAddWechatEnterprise(customerId, companyId, corpId, corpName, employeeName, employeeWeworkName);
        return Result.ok(result);
    }

    @ApiOperation("记录删除企微行为")
    @PostMapping("/delete-wechat-enterprise")
    public Result<Boolean> deleteWechatEnterprise(
            @ApiParam("客户ID") @RequestParam Long customerId,
            @ApiParam("企业ID") @RequestParam Long companyId,
            @ApiParam("企微企业ID") @RequestParam String corpId,
            @ApiParam("企业名称") @RequestParam String corpName,
            @ApiParam("员工姓名") @RequestParam String employeeName,
            @ApiParam("员工企微昵称") @RequestParam String employeeWeworkName
    ) {
        log.info("记录删除企微行为: customerId={}, companyId={}, corpId={}, corpName={}, employeeName={}, employeeWeworkName={}",
                customerId, companyId, corpId, corpName, employeeName, employeeWeworkName);

        boolean result = customerBehaviorService.saveDeleteWechatEnterprise(customerId, companyId, corpId, corpName, employeeName, employeeWeworkName);
        return Result.ok(result);
    }

    @ApiOperation("记录加入群组行为")
    @PostMapping("/join-group-chat")
    public Result<Boolean> joinGroupChat(
            @ApiParam("客户ID") @RequestParam Long customerId,
            @ApiParam("企业ID") @RequestParam Long companyId,
            @ApiParam("企微企业ID") @RequestParam String corpId,
            @ApiParam("群组ID") @RequestParam String groupId,
            @ApiParam("群组名称") @RequestParam String groupName,
            @ApiParam("员工姓名") @RequestParam String employeeName,
            @ApiParam("员工企微昵称") @RequestParam String employeeWeworkName,
            @ApiParam("入群方式 0-由成员邀请入群, 3-通过扫描群二维码入群") @RequestParam(required = false) Integer joinScene
    ) {
        log.info("记录加入群组行为: customerId={}, companyId={}, corpId={}, groupId={}, groupName={}, employeeName={}, employeeWeworkName={}, joinScene={}",
                customerId, companyId, corpId, groupId, groupName, employeeName, employeeWeworkName, joinScene);

        boolean result = customerBehaviorService.saveJoinGroupChat(customerId, companyId, corpId, groupId, groupName, employeeName, employeeWeworkName, joinScene);
        return Result.ok(result);
    }

    @ApiOperation("记录退出群组行为")
    @PostMapping("/exit-group-chat")
    public Result<Boolean> exitGroupChat(
            @ApiParam("客户ID") @RequestParam Long customerId,
            @ApiParam("企业ID") @RequestParam Long companyId,
            @ApiParam("企微企业ID") @RequestParam String corpId,
            @ApiParam("群组ID") @RequestParam String groupId,
            @ApiParam("群组名称") @RequestParam String groupName,
            @ApiParam("员工姓名") @RequestParam String employeeName,
            @ApiParam("员工企微昵称") @RequestParam String employeeWeworkName,
            @ApiParam("退群方式 0-自己退群, 1-群主/群管理员移出") @RequestParam(required = false) Integer quitScene
    ) {
        log.info("记录退出群组行为: customerId={}, companyId={}, corpId={}, groupId={}, groupName={}, employeeName={}, employeeWeworkName={}, quitScene={}",
                customerId, companyId, corpId, groupId, groupName, employeeName, employeeWeworkName, quitScene);

        boolean result = customerBehaviorService.saveExitGroupChat(customerId, companyId, corpId, groupId, groupName, employeeName, employeeWeworkName, quitScene);
        return Result.ok(result);
    }
}