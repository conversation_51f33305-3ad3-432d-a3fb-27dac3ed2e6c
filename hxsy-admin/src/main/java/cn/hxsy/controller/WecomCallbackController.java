package cn.hxsy.controller;

import cn.hxsy.service.WecomCallbackService;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@RestController
@RequestMapping("/api/v1/wecom")
public class WecomCallbackController {

    @Resource
    private WecomCallbackService wecomCallbackService;

    @ApiOperation("企业微信回调统一入口(应用服务商)")
    @RequestMapping(value = "/callback/provider/{corpId}/{corpType}", method = {RequestMethod.GET, RequestMethod.POST})
    public String callbackProvider(HttpServletRequest request,
                           @PathVariable("corpId") String corpId,
                           @PathVariable("corpType") String corpType,
                           @RequestParam("msg_signature") String msgSignature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce,
                           @RequestParam(name = "echostr", required = false) String echoStr
    ) {

        String xml = null;
        if ("POST".equalsIgnoreCase(request.getMethod())) {
            try {
                xml = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return wecomCallbackService.handleCallbackProvider(corpId, corpType, msgSignature, timestamp, nonce, xml);
        } else if ("GET".equalsIgnoreCase(request.getMethod())) {
            return wecomCallbackService.handleGetCallbackProvider(corpId, corpType, msgSignature, timestamp, nonce, echoStr);
        }
        return "error";
    }

    @ApiOperation("企业微信回调统一入口")
    @RequestMapping(value = "/callback/{corpId}", method = {RequestMethod.GET, RequestMethod.POST})
    public String callback(HttpServletRequest request,
                           @PathVariable("corpId") String corpId,
                           @RequestParam("msg_signature") String msgSignature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce,
                           @RequestParam(name = "echostr", required = false) String echoStr
    ) {
        String xml = null;
        if ("POST".equalsIgnoreCase(request.getMethod())) {
            try {
                xml = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return wecomCallbackService.handleCallback(corpId, msgSignature, timestamp, nonce, xml);
        } else if ("GET".equalsIgnoreCase(request.getMethod())) {
            return wecomCallbackService.handleGetCallback(corpId, msgSignature, timestamp, nonce, echoStr);
        }
        return "error";
    }

    @RequestMapping(value = "/unionid_to_external_userid/{corpId}", method = {RequestMethod.GET, RequestMethod.POST})
    public String handleUnionidToExternalUserid(
            @PathVariable("corpId") String corpId,
            @RequestParam("unionid") String unionid,
            @RequestParam("openid") String openid,
            @RequestParam("subject_type") String subject_type) {
        return wecomCallbackService.handleUnionidToExternalUserid(corpId, unionid, openid, subject_type);
    }

    // 测试接口
    @RequestMapping(value = "/license/test/{method}", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject test(
            @PathVariable("method") String method,
            @RequestBody JSONObject jsonObject
    ) throws Exception {
        return wecomCallbackService.licenseTest(method, jsonObject);
    }

} 