package cn.hxsy.controller;

import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.base.response.Result;
import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.service.CompanyQyRelationService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 公司与企微账号关联配置-企微官方数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 11:42:49
 */
@RestController
@RequestMapping("/api/v1/companyQyRelation")
public class CompanyQyRelationController {

    @Resource
    private CompanyQyRelationService companyQyRelationService;

    @ApiOperation("查询公司下关联企微配置")
    @PostMapping("/query-company-qy-token")
    public Result<Object> queryCompanyQyToken(@RequestBody QyAppReq qyAppReq) {
        return companyQyRelationService.queryCompanyQyToken(qyAppReq);
    }

    @ApiOperation("代开发企微登录流程")
    @PostMapping("/query-qy-login-token")
    public Result<Object> queryQyLoginToken(@RequestBody QyAppReq qyAppReq) {
        return companyQyRelationService.queryQyLoginToken(qyAppReq);
    }

    @ApiOperation("新增单个企微账号信息")
    @PostMapping("/save-company-qy")
    public Result<Object> saveQyCompany(@RequestBody CompanyQyRelationRequest request) {
        return companyQyRelationService.saveQyCompany(request);
    }

//    @ApiOperation("公司批量绑定企微账号信息")
//    @PostMapping("/save-company-bind-qy-batch")
//    public Result<Object> saveCompanyBindQy(@RequestBody CompanyQyRelationRequest request) {
//        return companyQyRelationService.saveCompanyBindQy(request);
//    }

//    @ApiOperation("查询公司下关联单个企微对应的部门列表")
//    @PostMapping("/query-company-qy-dept-list")
//    public Result<Object> queryCompanyQyDeptList(@RequestBody QyAppReq qyAppReq) {
//        return companyQyRelationService.queryCompanyQyDept(qyAppReq);
//    }
//
//    @ApiOperation("查询公司下关联单个企微下所有成员信息")
//    @PostMapping("/query-company-qy-user-list")
//    public Result<Object> queryCompanyQyUserList(@RequestBody QyAppReq qyAppReq) {
//        return companyQyRelationService.queryCompanyQyUser(qyAppReq);
//    }

//    @ApiOperation("同步一个企微下员工账号信息")
//    @PostMapping("/save-qy-user")
//    public Result<Object> saveQyCompanyUser(@RequestBody QyAppReq qyAppReq) {
//        return companyQyRelationService.saveQyCompanyUser(qyAppReq);
//    }

    /**
     * description : 获取所有的企微信息
     * @title: queryQyList
     * @param:
     * <AUTHOR>
     * @date 2025/7/21 23:34
     * @return List<CompanyQyRelation>
     */
    @ApiOperation("获取企微企业列表")
    @GetMapping("/query-qy-list")
    public Result<Object> queryQyInfo() {
        return Result.ok(companyQyRelationService.queryQyList());
    }

    @ApiOperation("获取企微企业列表-权限校验")
    @PostMapping("/query-qy-list-auth")
    public Result<Object> queryQyListAuth(@RequestBody CompanyQyRelationRequest request) {
        return Result.ok(companyQyRelationService.queryQyListAuth(request));
    }

}
