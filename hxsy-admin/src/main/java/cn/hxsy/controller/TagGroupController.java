package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.TagGroup;
import cn.hxsy.service.TagGroupService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签组控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "标签组管理")
@RestController
@RequestMapping("/api/v1/tag-group")
@RequiredArgsConstructor
public class TagGroupController {

    @Autowired
    private TagGroupService tagGroupService;

    /**
     * 新增标签组信息
     *
     * @param tagGroup 标签组信息对象
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody TagGroup tagGroup) {
        return Result.ok(tagGroupService.add(tagGroup));
    }

    /**
     * 根据ID删除标签组信息
     *
     * @param id 标签组ID
     * @return 操作结果
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(tagGroupService.deleteById(id));
    }

    /**
     * 更新标签组信息
     *
     * @param tagGroup 标签组信息对象
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody TagGroup tagGroup) {
        return Result.ok(tagGroupService.update(tagGroup));
    }

    /**
     * 根据父级ID查询子标签组列表
     *
     * @param parentId 父级标签组ID
     * @return 子标签组列表
     */
    @GetMapping("/listByParentId")
    public Result<List<TagGroup>> listByParentId(@RequestParam Long parentId) {
        return Result.ok(tagGroupService.getByParentId(parentId));
    }

    /**
     * 根据层级查询标签组列表
     *
     * @param level 层级（1-一级标签组，2-二级标签组）
     * @return 标签组列表
     */
    @GetMapping("/listByLevel")
    public Result<List<TagGroup>> listByLevel(@RequestParam Integer level) {
        return Result.ok(tagGroupService.getByLevel(level));
    }

} 