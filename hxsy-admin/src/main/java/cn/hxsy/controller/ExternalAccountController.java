package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.BatchActivationVO;
import cn.hxsy.datasource.model.entity.ExternalAccount;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.service.ExternalAccountService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "互通账号池管理")
@RestController
@RequestMapping("/api/v1/external-account")
@RequiredArgsConstructor
public class ExternalAccountController {

    @Autowired
    private ExternalAccountService externalAccountService;

    @ApiOperation("根据ID查询账号")
    @GetMapping("/{id}")
    public Result<Object> getById(@PathVariable Long id) {
        return Result.ok(externalAccountService.getById(id));
    }

    @ApiOperation("批量激活账号（不可跨企业批量激活）")
    @PostMapping("/batchActivation")
    public Result<Boolean> batchActivation(@Valid @RequestBody BatchActivationVO batchActivationVO) {
        String corpId = batchActivationVO.getCorpId();
        List<SystemUserQyRelation> systemUserQyRelations = batchActivationVO.getUsers();
        return externalAccountService.batchActivation(corpId, systemUserQyRelations) ?
                Result.ok(true) : Result.error("批量激活账号失败");
    }

    @ApiOperation("分页查询互通账号列表")
    @GetMapping("/query/page")
    public Result<Page<ExternalAccount>> queryPage(
            @ApiParam("当前页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("企业ID") @RequestParam(required = true) String corpId,
            @ApiParam("企微名称") @RequestParam(required = false) String qyName,
            @ApiParam("账号状态 0-未绑定 1-已绑定") @RequestParam(required = false) Integer accountStatus) {
        QyUserRequest qyUserRequest = new QyUserRequest();
        qyUserRequest.setCorpId(corpId);
        qyUserRequest.setQyName(qyName);
        qyUserRequest.setAccountStatus(accountStatus);
        return Result.ok(externalAccountService.queryPage(pageNum, pageSize, qyUserRequest));
    }

    @ApiOperation("删除激活码")
    @PostMapping("/deleteById")
    public Result<Boolean> deleteById(@RequestBody Long id) {
        return externalAccountService.removeById(id) ?
                Result.ok(true) : Result.error("删除失败");
    }

    @ApiOperation("根据订单号查询激活码")
    @GetMapping("/queryActivationCode/{orderId}")
    public Result<List<ExternalAccount>> queryActivationCode(@PathVariable String orderId) {
        return Result.ok(externalAccountService.queryActivationCode(orderId));
    }
}