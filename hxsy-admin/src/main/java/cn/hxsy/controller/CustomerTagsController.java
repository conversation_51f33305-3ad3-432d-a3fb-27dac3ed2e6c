package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CustomerTags;
import cn.hxsy.service.CustomerTagsService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户标签控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户标签管理")
@RestController
@RequestMapping("/api/v1/customer-tags")
@RequiredArgsConstructor
public class CustomerTagsController {

    private final CustomerTagsService customerTagsService;

    /**
     * 根据客户ID查询标签列表
     *
     * @param customerId 客户ID
     * @return 标签列表
     */
    @ApiOperation("根据客户ID查询标签列表")
    @GetMapping("/list-by-customerid")
    public Result<List<CustomerTags>> listByCustomerId(@RequestParam Long customerId) {
        return Result.ok(customerTagsService.getByCustomerId(customerId));
    }

    /**
     * 分页查询客户标签信息
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param customerId 客户ID
     * @param campPeriodId 营期ID
     * @param tagsName 标签名称
     * @param status 使用状态
     * @return 分页结果
     */
    @ApiOperation("分页查询客户标签信息")
    @GetMapping("/page")
    public Result<Page<CustomerTags>> page(
            @RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long customerId,
            @RequestParam(required = false) Long campPeriodId,
            @RequestParam(required = false) String tagsName,
            @RequestParam(required = false) Integer status) {
        return Result.ok(customerTagsService.page(current, size, customerId, campPeriodId, tagsName, status));
    }

    /**
     * description : 保存手动标签
     * @title: saveManualTag
     * @param: customerTags
     * <AUTHOR>
     * @date 2025/5/25 20:40
     * @return Result
     */
    @ApiOperation("保存手动标签")
    @PostMapping("/save-manual-tag")
    public Result saveManualTag(@RequestBody CustomerTags customerTags) {
        return Result.ok(customerTagsService.saveManualTag(customerTags));
    }
}