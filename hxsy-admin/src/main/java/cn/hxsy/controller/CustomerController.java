package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.CustomerBatchRequest;
import cn.hxsy.api.user.model.request.CustomerCourseVideoRequest;
import cn.hxsy.api.user.model.request.CustomerLoginRequest;
import cn.hxsy.api.user.model.request.CustomerQueryRequest;
import cn.hxsy.api.user.model.response.CampCourseVideoResponse;
import cn.hxsy.api.user.model.response.CustomerQueryResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.Customer;
import cn.hxsy.datasource.model.entity.CustomerCourseRelation;
import cn.hxsy.base.request.BatchRemarkCustomerRequest;
import cn.hxsy.service.CustomerService;
import cn.hxsy.datasource.model.entity.BatchRemarkCustomerVO;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;

/**
 * 客户控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户管理")
@RestController
@RequestMapping("/api/v1/customer")
public class CustomerController {

    @Autowired
    private CustomerService customerService;
    
    /**
     * 分页查询客户列表
     *
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param request 查询条件
     * @return 客户信息分页结果
     */
    @ApiOperation("分页查询客户列表")
    @PostMapping("/query/page")
    public Result<Page<CustomerQueryResponse>> queryPage(
            @ApiParam("当前页码") @RequestParam(defaultValue = "1") long pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") long pageSize,
            @RequestBody CustomerQueryRequest request) {
        return Result.ok(customerService.queryCustomerPage(pageNum, pageSize, request));
    }

    /**
     * 分页查询客户列表
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param request 查询条件
     * @return 客户信息分页结果
     */
    @ApiOperation("分页查询客户列表")
    @PostMapping("/query/page-new")
    public Result<Page<CustomerQueryResponse>> queryPageNew(
            @ApiParam("当前页码") @RequestParam(defaultValue = "1") long current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") long size,
            @RequestBody CustomerQueryRequest request) {
        return Result.ok(customerService.queryCustomerPageNew(current, size, request));
    }

    /**
     * 用户分页查询客户列表
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param request 查询条件
     * @return 客户信息分页结果
     */
    @ApiOperation("用户分页查询客户列表")
    @PostMapping("/user/query/page")
    public Result<Page<CustomerQueryResponse>> userQueryPage(
            @ApiParam("当前页码") @RequestParam(defaultValue = "1") long current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") long size,
            @Valid @RequestBody CustomerQueryRequest request) {
        // 查询用户自己的客户
        return Result.ok(customerService.queryCustomerPage(current, size, request));
    }

    @ApiOperation("更新客户课程关联")
    @PostMapping("/upd-course-customer-rel")
    public Result updCourseCustomerRel(@RequestBody CustomerCourseRelation customerCourseRelation) {
        return Result.ok(customerService.updCourseCustomerRel(customerCourseRelation));
    }

    @ApiOperation("更新客户课程关联(PC)")
    @PostMapping("/upd-course-customer-rel-status")
    public Result updCourseCustomerRelStatus(@RequestBody CustomerCourseRelation customerCourseRelation) {
        return Result.ok(customerService.updCourseCustomerRelStatus(customerCourseRelation));
    }

    // 更新客户课程时长
    @ApiOperation("更新客户课程时长")
    @PostMapping("/upd-course-duration")
    public Result updCourseDuration(@RequestBody CustomerCourseRelation customerCourseRelation) {
        return Result.ok(customerService.updCourseDuration(customerCourseRelation));
    }

    @ApiOperation("获取客户课程视频")
    @PostMapping("/get-course-video")
    public Result<CampCourseVideoResponse> getCourseVideo(@RequestBody CustomerCourseVideoRequest customerCourseVideoRequest) {
        return Result.ok(customerService.getCourseVideo(customerCourseVideoRequest));
    }

    @ApiOperation("提交答题")
    @PostMapping("/submit-answer")
    public Result submitAnswer(@RequestBody CustomerCourseVideoRequest customerCourseVideoRequest) {
        return Result.ok(customerService.submitAnswer(customerCourseVideoRequest));
    }

    @ApiOperation("更新手机号")
    @PostMapping("/update-mobile")
    public Result updateMobile(@RequestBody Customer customer) {
        return Result.ok(customerService.updateMobile(customer));
    }

    @ApiOperation("修改账号状态")
    @PostMapping("/upd-status")
    public Result updStatus(@RequestBody CustomerBatchRequest customerBatchRequest) {
        return Result.ok(customerService.updStatus(customerBatchRequest));
    }

    @ApiOperation("删除客户账号")
    @PostMapping("/del-customer")
    public Result<Object> delCustom(@RequestBody CustomerLoginRequest request) {
        return customerService.delCustom(request);
    }

    @ApiOperation("获取客户详情")
    @GetMapping("/get-customer-detail")
    public Result<JSONArray> getCustomerDetail(
            @ApiParam("客户ID主键") @RequestParam("customerId") Long customerId
    ) {
        return Result.ok(customerService.getCustomerDetail(customerId));
    }

    @ApiOperation("批量设置客户企微备注")
    @PostMapping("/batch-remark")
    public Result<BatchRemarkCustomerVO> batchRemarkCustomer(
            @ApiParam("批量设置备注参数") @Valid @RequestBody BatchRemarkCustomerRequest param
    ) {
        return Result.ok(customerService.batchRemarkCustomer(param));
    }

}