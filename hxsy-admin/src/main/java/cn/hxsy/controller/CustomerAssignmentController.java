package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CustomerAssignment;
import cn.hxsy.service.CustomerAssignmentService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 客户分配记录控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户分配记录管理")
@RestController
@RequestMapping("/api/v1/customer-assignment")
@RequiredArgsConstructor
public class CustomerAssignmentController {

    private final CustomerAssignmentService customerAssignmentService;

    /**
     * 分页查询客户分配记录
     *
     * @param pageNum 当前页码
     * @param pageSize 每页大小
     * @param customerId 客户ID
     * @return 分页结果
     */
    @ApiOperation("分页查询客户分配记录")
    @GetMapping("/page")
    public Result<Page<CustomerAssignment>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false)Long customerId) {
        return Result.ok(customerAssignmentService.page(pageNum, pageSize, customerId));
    }
}