package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CompWxCode;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.service.CompWxCodeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公司与小程序关联管理接口
 *
 * <AUTHOR>
 */
@Api(tags = "公司小程序关联管理")
@RestController
@RequestMapping("/api/v1/company-mini-program")
@RequiredArgsConstructor
public class CompWxCodeController {

    private final CompWxCodeService compWxCodeService;

    @ApiOperation("分页查询公司小程序关联列表")
    @GetMapping("/page")
    public Result<Page<CompWxCode>> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("公司ID") @RequestParam(required = false) Long companyId,
            @ApiParam("小程序ID") @RequestParam(required = false) Long miniProgramId,
            @ApiParam("状态") @RequestParam(required = false) Integer status
    ) {
        Page<CompWxCode> page = new Page<>(current, size);
        LambdaQueryWrapper<CompWxCode> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (companyId != null) {
            wrapper.eq(CompWxCode::getCompanyId, companyId);
        }
        if (miniProgramId != null) {
            wrapper.eq(CompWxCode::getAppid, miniProgramId);
        }
        if (status != null) {
            wrapper.eq(CompWxCode::getStatus, status);
        }

        // 执行分页查询
        Page<CompWxCode> resultPage = compWxCodeService.page(page, wrapper);
        return Result.ok(resultPage);
    }

    @ApiOperation("获取公司关联的小程序信息列表")
    @GetMapping("/mini-programs/{companyId}")
    public Result<List<CompWxCode>> getMiniProgramsByCompanyId(
            @ApiParam("公司ID") @PathVariable Long companyId
    ) {
        List<CompWxCode> miniPrograms = compWxCodeService.getMiniProgramsByCompanyId(companyId);
        return Result.ok(miniPrograms);
    }

    @ApiOperation("获取小程序关联的公司ID列表")
    @GetMapping("/companies/{miniProgramId}")
    public Result<List<CompanyPO>> getCompaniesByMiniProgramId(
            @ApiParam("小程序ID") @PathVariable String miniProgramId
    ) {
        List<CompanyPO> companyIds = compWxCodeService.getCompanyIdsByMiniProgramId(miniProgramId);
        return Result.ok(companyIds);
    }

    @ApiOperation("为小程序设置可访问的公司列表")
    @PostMapping("/assign-companies")
    public Result<Boolean> assignCompaniesToMiniProgram(
            @ApiParam(value = "小程序ID", required = true) @RequestParam String miniProgramId,
            @ApiParam(value = "小程序名称", required = true) @RequestParam String miniProgramName,
            @ApiParam(value = "公司ID列表", required = true) @RequestBody List<Long> companyIds
    ) {
        // 参数校验
        if (miniProgramId == null || miniProgramId.trim().isEmpty()) {
            throw new IllegalArgumentException("小程序ID不能为空");
        }
        if (companyIds == null || companyIds.isEmpty()) {
            throw new IllegalArgumentException("公司ID列表不能为空");
        }

        boolean success = compWxCodeService.assignCompaniesToMiniProgram(miniProgramId, miniProgramName, companyIds);
        return success ? Result.ok(true) : Result.error("设置小程序可访问公司失败");
    }

    @ApiOperation("移除公司的小程序权限")
    @PostMapping("/remove")
    public Result<Boolean> removeMiniPrograms(
            @ApiParam("公司ID") @RequestParam Long companyId,
            @ApiParam("小程序ID列表") @RequestBody List<String> miniProgramIds
    ) {
        boolean success = compWxCodeService.removeMiniProgramsFromCompany(companyId, miniProgramIds);
        return success ? Result.ok(true) : Result.error("移除小程序权限失败");
    }

    @ApiOperation("新增公司小程序关联")
    @PostMapping
    public Result<Boolean> save(@ApiParam("关联信息") @RequestBody CompWxCode companyMiniProgram) {
        return Result.ok(compWxCodeService.save(companyMiniProgram));
    }

    @ApiOperation("修改公司小程序关联")
    @PutMapping
    public Result<Boolean> update(@ApiParam("关联信息") @RequestBody CompWxCode companyMiniProgram) {
        return Result.ok(compWxCodeService.updateById(companyMiniProgram));
    }

    @ApiOperation("删除公司小程序关联")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@ApiParam("关联ID") @PathVariable Long id) {
        return Result.ok(compWxCodeService.removeById(id));
    }
}