package cn.hxsy.controller;

import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.datasource.model.entity.ExternalAccountOrder;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.request.SystemUserBindQyUserRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.response.SystemUserQyRelationResponse;
import cn.hxsy.service.SystemUserQyRelationService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 业务人员账号与企微信息关联-系统数据
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@RestController
@RequestMapping("/api/v1/systemUserQyRelation")
public class SystemUserQyRelationController {

    @Resource
    private SystemUserQyRelationService systemUserQyRelationService;

    @ApiOperation("同步一个企微下员工账号信息")
    @PostMapping("/save-qy-user")
    public Result<Object> saveQyCompanyUser(@RequestBody SystemUserBindQyUserRequest request) {
        return systemUserQyRelationService.saveQyCompanyUser(request);
    }

    @ApiOperation("给系统用户绑定企微下员工账号信息（可选择不同企微下多个员工账号）")
    @PostMapping("/bind-qy-user-to-system-user")
    public Result<Object> bindQyUserToSystemUser(@RequestBody SystemUserBindQyUserRequest request) {
        return systemUserQyRelationService.bindQyUserToSystemUser(request);
    }

    @ApiOperation("查询系统中已同步的一个企微下员工账号信息-同系统交互数据")
    @PostMapping("/query-qy-user-to-system-user")
    public Result<Object> querySystemQyUser(@RequestBody SystemUserBindQyUserRequest request) {
        return systemUserQyRelationService.querySystemQyUser(request);
    }

    @ApiOperation("生成企微联系人二维码（个人）")
    @PostMapping("/generate-qy-contact-qr-code")
    public Result<Object> generateQyContactQrCode(@RequestBody SystemUserQyRelation systemUserQyRelation) {
        return Result.ok(systemUserQyRelationService.generateQyContactQrCode(systemUserQyRelation));
    }

    @ApiOperation("分页查询企微员工信息列表")
    @GetMapping("/query-qy-user-page")
    public Result<Page<SystemUserQyRelation>> queryQyUserPage(
            @ApiParam("当前页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("企业ID") @RequestParam(required = true) String corpId,
            @ApiParam("业务名称") @RequestParam(required = false) String systemUserName,
            @ApiParam("企微名称") @RequestParam(required = false) String qyName,
            @ApiParam("业务账号状态") @RequestParam(required = false) Integer systemUserStatus,
            @ApiParam("企微账号状态") @RequestParam(required = false) Integer qyStatus,
            @ApiParam("互通账号状态：0-未分配 1-已分配") @RequestParam(required = false) Integer accountStatus,
            @ApiParam("互通账号过期时间-开始") @RequestParam(required = false) LocalDateTime expireTimeStart,
            @ApiParam("互通账号过期时间-结束") @RequestParam(required = false) LocalDateTime expireTimeEnd
    ) {
        QyUserRequest qyUserRequest = new QyUserRequest();
        qyUserRequest.setCorpId(corpId);
        qyUserRequest.setSystemUserName(systemUserName);
        qyUserRequest.setQyName(qyName);
        qyUserRequest.setSystemUserStatus(systemUserStatus);
        qyUserRequest.setQyStatus(qyStatus);
        qyUserRequest.setAccountStatus(accountStatus);
        qyUserRequest.setExpireTimeStart(expireTimeStart);
        qyUserRequest.setExpireTimeEnd(expireTimeEnd);
        return Result.ok(systemUserQyRelationService.queryQyUserPage(pageNum, pageSize, qyUserRequest));
    }

}
