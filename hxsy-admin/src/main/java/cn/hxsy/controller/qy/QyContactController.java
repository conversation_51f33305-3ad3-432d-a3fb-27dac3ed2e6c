package cn.hxsy.controller.qy;

import cn.hxsy.api.qy.request.QyUserReq;
import cn.hxsy.api.qy.request.contact.QyDeptReq;
import cn.hxsy.base.response.Result;
import cn.hxsy.request.QyProviderTokenRequest;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.service.qy.QyContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> XiaQL
 * @description : QyAuthController
 * @ClassName : QyAuthController
 * @date: 2025-06-29 16:57
 */
@Slf4j
@RestController
@RequestMapping(QyContactController.API_PREFIX)
public class QyContactController {

    @Resource
    private QyContactService qyContactService;

    public static final String API_PREFIX = "/api/v1/qy-contact";

    @PostMapping("/deptList")
    public Result<Object> deptList(@RequestBody QyProviderTokenRequest qyProviderTokenRequest) {
        return Result.ok(qyContactService.deptList(qyProviderTokenRequest));
    }

    @PostMapping("/userList")
    public Result<Object> userList(@RequestBody QyProviderTokenRequest qyProviderTokenRequest) {
        return Result.ok(qyContactService.userList(qyProviderTokenRequest));
    }

//    @PostMapping("/userListId")
//    public Result<Object> userListId(@RequestBody QyProviderTokenRequest qyProviderTokenRequest) {
//        return Result.ok(qyContactService.userListId(qyProviderTokenRequest));
//    }
//
//    @PostMapping("/openUserToUser")
//    public Result<Object> openUserToUser(@RequestBody QyProviderTokenRequest qyProviderTokenRequest) {
//        return Result.ok(qyContactService.openUserToUser(qyProviderTokenRequest));
//    }

    @PostMapping("/userToOpenUser")
    public Result<Object> userToOpenUser(@RequestBody QyProviderTokenRequest qyProviderTokenRequest) {
        return Result.ok(qyContactService.userToOpenUser(qyProviderTokenRequest));
    }

    @PostMapping("/all-user-list")
    public Result<Object> allUserList(@RequestBody QyProviderTokenRequest qyProviderTokenRequest) {
        return Result.ok(qyContactService.allUserList(qyProviderTokenRequest));
    }
}
