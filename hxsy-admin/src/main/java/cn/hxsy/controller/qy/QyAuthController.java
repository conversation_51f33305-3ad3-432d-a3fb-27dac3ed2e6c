package cn.hxsy.controller.qy;

import cn.hxsy.service.qy.QyAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> XiaQL
 * @description : QyAuthController
 * @ClassName : QyAuthController
 * @date: 2025-06-29 16:57
 */
@Slf4j
@RestController
@RequestMapping(QyAuthController.API_PREFIX)
public class QyAuthController {

    @Resource
    private QyAuthService qyAuthService;

    public static final String API_PREFIX = "/api/v1/qy-auth";

//    @PostMapping("/getSuiteToken")
//    public Result<Object> getSuiteToken(@RequestBody String corpId) {
//        return Result.ok(qyAuthService.getSuiteToken(corpId));
//    }

//    @PostMapping("/getPermanentCode")
//    public Result<Object> getPermanentCode(@RequestBody String corpId) {
//        qyAuthService.getPermanentCode("K33eRJxU4pqI06KlGIUXZjqcK2eMkXhZjMwy9TFlWcJCQePYJKwa1ig7u-iu3QEyyS1pXWi15Eigm5zSyBwLE-X0_3iDzRJEVe0jI2nxt0g",
//                "", "");
//        return Result.ok();
//    }
}
