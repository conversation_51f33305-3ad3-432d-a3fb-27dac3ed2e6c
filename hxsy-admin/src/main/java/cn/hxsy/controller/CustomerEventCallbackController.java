//package cn.hxsy.controller;
//
//import cn.hxsy.service.CustomerEventCallbackService;
//import io.swagger.annotations.ApiOperation;
//import org.apache.commons.io.IOUtils;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//
//@RestController
//@RequestMapping("/api/v1/wecom/customer")
//public class CustomerEventCallbackController {
//
//    @Resource
//    private CustomerEventCallbackService customerEventCallbackService;
//
//    @ApiOperation("企业客户事件回调入口")
//    @RequestMapping(value = "/event/{corpId}", method = {RequestMethod.GET, RequestMethod.POST})
//    public String customerEventCallback(HttpServletRequest request,
//                                        @PathVariable("corpId") String corpId,
//                                        @RequestParam("msg_signature") String msgSignature,
//                                        @RequestParam("timestamp") String timestamp,
//                                        @RequestParam("nonce") String nonce,
//                                        @RequestParam(name = "echostr", required = false) String echoStr
//    ) {
//        if ("POST".equalsIgnoreCase(request.getMethod())) {
//            String xml;
//            try {
//                xml = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//            return customerEventCallbackService.handleCustomerEvent(corpId, msgSignature, timestamp, nonce, xml);
//        } else if ("GET".equalsIgnoreCase(request.getMethod())) {
//            // GET请求用于URL校验，调用service解密
//            return customerEventCallbackService.handleCustomerEventGet(corpId, msgSignature, timestamp, nonce, echoStr);
//        }
//        return "error";
//    }
//}