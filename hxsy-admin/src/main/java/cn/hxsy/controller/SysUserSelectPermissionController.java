package cn.hxsy.controller;

import cn.hxsy.api.system.request.SysUserSelectPermissionRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.SysUserSelectPermissionService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 系统用户可见查询权限表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24 12:04:54
 */
@RestController
@RequestMapping("/api/v1/sysUserSelectPermission")
public class SysUserSelectPermissionController {

    @Autowired
    private SysUserSelectPermissionService sysUserSelectPermissionService;

    @ApiOperation("获取当前系统具有角色列表")
    @PostMapping("/get-system-user-permission")
    public Result<Object> getSystemSelectPermission(@RequestBody SysUserSelectPermissionRequest request) {
        return Result.ok(sysUserSelectPermissionService.getSysUserSelectPermission(request));
    }

    @ApiOperation("新增或更新业务人员具有角色列表")
    @PostMapping("/save-or-update-system-user-permission")
    public Result<Object> saveOrUpdateSystemSelectPermission(@RequestBody SysUserSelectPermissionRequest request) {
        Boolean b = sysUserSelectPermissionService.saveOrUpdateSystemSelectPermission(request);
        if (!b) {
            return Result.error("保存失败，请稍后重试");
        }
        return Result.ok();
    }

}
