package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.SystemUserRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.api.user.model.request.UpdateUnionIdRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.api.user.model.request.SystemUserPageRequest;
import cn.hxsy.api.user.model.request.WxUserRegisterRequest;
import cn.hxsy.service.SystemUserService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "系统用户管理")
@RestController
@RequestMapping("/api/v1/system-user")
@RequiredArgsConstructor
public class SystemUserController {

    private final SystemUserService systemUserService;

    @ApiOperation("分页查询用户列表")
    @PostMapping("/query-user-by-page")
    public Result<Object> page(
            @ApiParam("查询请求") @RequestBody SystemUserPageRequest request) {
        return Result.ok(systemUserService.getUserPage(request));
    }

    @ApiOperation("根据用户名查询用户")
    @GetMapping("/username/{username}")
    public Result<SystemUserPO> getByUsername(@ApiParam("用户名") @PathVariable String username) {
        return Result.ok(systemUserService.getUserByUsername(username));
    }

    @ApiOperation("获取用户详情")
    @GetMapping("/get-user")
    public Result<SystemUserPO> getById(@ApiParam("用户ID") @RequestParam String id) {
        return Result.ok(systemUserService.getById(id));
    }

    @ApiOperation("新增用户")
    @PostMapping("/save")
    public Result<Boolean> save(@ApiParam("用户信息") @RequestBody SystemUserPO user) {
        return Result.ok(systemUserService.createUser(user));
    }

    @ApiOperation("修改用户")
    @PutMapping("/update")
    public Result<Boolean> update(@ApiParam("用户信息") @RequestBody SystemUserRequest user) {
        return Result.ok(systemUserService.updateUser(user));
    }

    @ApiOperation("删除用户")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@ApiParam("用户ID") @PathVariable Long id,
                                 @ApiParam("unionId") @RequestParam String unionId) {
        return Result.ok(systemUserService.deleteUser(id, unionId));
    }

    @ApiOperation("更新用户状态")
    @PostMapping("/update-status")
    public Result<Boolean> updateStatus(
            @RequestBody UpdateStatusRequest updateStatusRequest) {
        return Result.ok(systemUserService.updateUserStatus(updateStatusRequest));
    }

    /**
     * @return {@link Result }<{@link Object }>
     * <AUTHOR>
     * @date 2025/04/07
     */

    @ApiOperation("更新用户审核状态")
    @PostMapping("/update-audit-status")
    public Result<Object> updateAuditStatus(
            @RequestBody UpdateStatusRequest updateStatusRequest) {
        return systemUserService.updateAuditStatusBatch(updateStatusRequest);
    }

    /**
     * @param request
     * @return {@link Result }
     */
    @ApiOperation("更新销售人员信息")
    @PostMapping("/wx-update-user")
    public Result<Object> updateWxUser(@ApiParam("更新销售人员信息") @RequestBody WxUserRegisterRequest request) {
        if (request == null) {
            return Result.error("参数不能为空");
        }
        return systemUserService.updateWxUser(request);
    }

    /**
     * @param unionId
     * @return {@link Object }
     */
    @ApiOperation("注册销售人员信息")
    @GetMapping("/wx/register-user")
    public SystemUserResponse registerWxUser(@RequestParam("unionId") String unionId, Long userId) {
        return systemUserService.registerWxUser(unionId, userId);
    }

    /**
     * 根据用户ID更新用户的unionId
     * 同时更新SystemUser表和UserAuth表的unionId
     *
     * @param request 更新unionId请求
     * @return 更新结果
     */
    @ApiOperation("更新用户unionId")
    @PostMapping("/update-union-id")
    public Result<Boolean> updateUserUnionId(@ApiParam("更新unionId请求") @RequestBody UpdateUnionIdRequest request) {
        try {
            boolean success = systemUserService.updateUserUnionId(request);
            return success ? Result.ok(true) : Result.error("更新失败");
        } catch (IllegalArgumentException e) {
            return Result.error(e.getMessage());
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        } catch (Exception e) {
            return Result.error("系统异常，请稍后重试");
        }
    }
}