package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CustomerTagRelation;
import cn.hxsy.service.CustomerTagRelationService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户标签关联控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户标签关联管理")
@RestController
@RequestMapping("/api/v1/customer-tag-relation")
@RequiredArgsConstructor
public class CustomerTagRelationController {

    @Autowired
    private CustomerTagRelationService customerTagRelationService;

    /**
     * 新增客户标签关联信息
     *
     * @param customerTagRelation 客户标签关联信息对象
     * @return 操作结果
     */
    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody CustomerTagRelation customerTagRelation) {
        return Result.ok(customerTagRelationService.add(customerTagRelation));
    }

    /**
     * 根据ID删除客户标签关联信息
     *
     * @param id 客户标签关联ID
     * @return 操作结果
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.ok(customerTagRelationService.deleteById(id));
    }

    /**
     * 更新客户标签关联信息
     *
     * @param customerTagRelation 客户标签关联信息对象
     * @return 操作结果
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody CustomerTagRelation customerTagRelation) {
        return Result.ok(customerTagRelationService.update(customerTagRelation));
    }

    /**
     * 根据客户ID查询标签关联列表
     *
     * @param customerId 客户ID
     * @return 标签关联列表
     */
    @GetMapping("/listByCustomerId")
    public Result<List<CustomerTagRelation>> listByCustomerId(@RequestParam Long customerId) {
        return Result.ok(customerTagRelationService.getByCustomerId(customerId));
    }

} 