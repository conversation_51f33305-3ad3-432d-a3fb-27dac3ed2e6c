package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.SalesGroupPO;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.SalesGroupService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "销售组管理")
@RestController
@RequestMapping("/api/v1/sales-group")
@RequiredArgsConstructor
public class SalesGroupController {

    private final SalesGroupService salesGroupService;

    @ApiOperation("分页查询销售组列表")
    @PostMapping("/page")
    public Result<Page<SalesGroupPO>> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @RequestBody OrganizationQueryRequest request) {
        return Result.ok(salesGroupService.querySaleGroupPage(current, size, request));
    }

    @ApiOperation("获取销售组详情")
    @GetMapping("/{id}")
    public Result<SalesGroupPO> getById(@ApiParam("销售组ID") @PathVariable Long id) {
        return Result.ok(salesGroupService.getById(id));
    }

    @ApiOperation("新增销售组")
    @PostMapping
    public Result<Boolean> save(@ApiParam("销售组信息") @RequestBody SalesGroupPO salesGroup) {
        return Result.ok(salesGroupService.save(salesGroup));
    }

    @ApiOperation("修改销售组")
    @PutMapping
    public Result<Boolean> update(@ApiParam("销售组信息") @RequestBody SalesGroupPO salesGroup) {
        return Result.ok(salesGroupService.updateById(salesGroup));
    }

    @ApiOperation("删除销售组")
    @PostMapping("/delete")
    public Result<Boolean> delete(@ApiParam("销售组信息") @RequestBody SalesGroupPO salesGroup)   {
        return Result.ok(salesGroupService.removeById(salesGroup.getId()));
    }

    @ApiOperation("更新销售组状态")
    @PostMapping("/update-status")
    public Result<Boolean> updateStatus(
            @RequestBody UpdateStatusRequest updateStatusRequest) {

        return Result.ok(salesGroupService.updateByIds(updateStatusRequest));
    }

    @ApiOperation("根据公司ID查询销售组列表")
    @GetMapping("/list-by-company-id")
    public Result<List<SalesGroupPO>> listByCompanyId(@ApiParam("公司ID") @RequestParam String companyId) {
        LambdaQueryWrapper<SalesGroupPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SalesGroupPO::getCompanyId, companyId)
               .orderByDesc(SalesGroupPO::getCreatedAt);
        return Result.ok(salesGroupService.list(wrapper));
    }
} 