package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.base.response.CompanyTreeResponse;
import cn.hxsy.base.response.HeadquartersTreeResponse;
import cn.hxsy.datasource.model.entity.ColumnPO;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.base.response.Result;
import cn.hxsy.dto.CompanyWithWxCodesDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import cn.hxsy.service.CompanyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

@Api(tags = "公司管理")
@RestController
@RequestMapping("/api/v1/company")
@RequiredArgsConstructor
public class CompanyController {

    private final CompanyService companyService;

    @ApiOperation("分页查询公司列表")
    @PostMapping("/page")
    public Result<IPage<CompanyWithWxCodesDTO>> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @RequestBody OrganizationQueryRequest request) {
        return Result.ok(companyService.queryCompanyPage(current, size, request));
    }

    @ApiOperation("获取公司详情")
    @GetMapping("/{id}")
    public Result<CompanyPO> getById(@ApiParam("公司ID") @PathVariable Long id) {
        return Result.ok(companyService.getById(id));
    }

    @ApiOperation("新增公司")
    @PostMapping
    public Result<Boolean> save(@ApiParam("公司信息") @RequestBody CompanyPO company) {
        return Result.ok(companyService.save(company));
    }

    @ApiOperation("修改公司")
    @PutMapping
    public Result<Boolean> update(@ApiParam("公司信息") @RequestBody CompanyPO company) {
        return Result.ok(companyService.updateById(company));
    }

    @ApiOperation("删除公司")
    @PostMapping("/delete")
    public Result<Boolean> delete(@ApiParam("公司信息") @RequestBody CompanyPO company)  {
        return Result.ok(companyService.removeById(company.getId()));
    }

    @ApiOperation("更新公司状态")
    @PostMapping("/update-status")
    public Result<Boolean> updateStatus(
            @RequestBody UpdateStatusRequest updateStatusRequest) {

        return Result.ok(companyService.updateByIds(updateStatusRequest));
    }

    @ApiOperation("根据公司id查询销售组")
    @GetMapping("/get-company-group")
    public Result<CompanyTreeResponse> getCompanyGroup(@ApiParam("公司ID") @RequestParam Integer id) {
        return Result.ok(companyService.getCompanyGroup(id));
    }

    @ApiOperation("根据公司id查询销售组")
    @GetMapping("/update-sell-url")
    public Result<Object> updateSellUrl(@ApiParam("公司ID") @RequestParam Integer id, @ApiParam("公司邀请URL") @RequestParam String url) {
        return Result.ok(companyService.updateSellUrl(id, url));
    }

}