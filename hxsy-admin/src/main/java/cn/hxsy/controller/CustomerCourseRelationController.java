package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.CampPeriodResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CustomerCourseRelation;
import cn.hxsy.service.CustomerCourseRelationService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户课程关联控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户课程关联管理")
@RestController
@RequestMapping("/api/v1/customer-course-relation")
@RequiredArgsConstructor
public class CustomerCourseRelationController {

    private final CustomerCourseRelationService customerCourseRelationService;

    /**
     * description : 根据客户id查询已参加营期信息
     * @title: getCampPeriodInfoByCustomerId
     * @param: customerId
     * <AUTHOR>
     * @date 2025/5/15 21:03
     * @return Result
     */
    @GetMapping("/get-camp-period-info-by-customer-id")
    public Result getCampPeriodInfoByCustomerId(@RequestParam Long customerId,
                                                @RequestParam Long columnId) {
        List<CampPeriodResponse> campPeriodResponseList = customerCourseRelationService.getCampPeriodInfoByCustomerId(customerId, columnId);
        return Result.ok(campPeriodResponseList);
    }

    /**
     * description : 根据客户id查询客户课程关联信息
     * @title: getCourseRelationByCustomerId
     * @param: customerId
     * <AUTHOR>
     * @date 2025/5/15 21:03
     * @return Result
     */
    @GetMapping("/get-course-relation-by-customer-id")
    public Result getCourseRelationByCustomerId( @RequestParam(defaultValue = "1") Integer pageNum,
                                                 @RequestParam(defaultValue = "10") Integer pageSize,
                                                 @RequestParam(required = false)Long customerId,
                                                 @RequestParam(required = false)Long campPeriodId) {
        return Result.ok(customerCourseRelationService.getCourseRelationByCustomerId(pageNum,  pageSize, customerId, campPeriodId));
    }
} 