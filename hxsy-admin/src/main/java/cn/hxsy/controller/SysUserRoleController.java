package cn.hxsy.controller;

import cn.hxsy.api.system.request.SysRoleRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.SysMenuService;
import cn.hxsy.service.SysUserRoleService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 系统用户-角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:04:30
 */
@RestController
@RequestMapping("/api/v1/sys-user-role")
public class SysUserRoleController {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @ApiOperation("获取当前用户具有的菜单权限")
    @PostMapping("/get-user-role-menu")
    public Result<Object> getUserRoleMenu(@RequestBody SysRoleRequest request) {
        return Result.ok();
    }
}
