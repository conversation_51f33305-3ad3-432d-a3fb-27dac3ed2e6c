package cn.hxsy.controller;

import cn.hxsy.service.CustomerWecomBindingService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 客户与企微external_userid及销售userid绑定关系 控制器
 */
@Api(tags = "客户-企微external_userid-销售userid绑定关系")
@RestController
@RequestMapping("/api/v1/customer-external-userid-relation")
public class CustomerWecomBindingController {

    @Autowired
    private CustomerWecomBindingService relationService;

    // 可扩展具体接口方法
} 