package cn.hxsy.controller;

import cn.hxsy.api.system.request.SysRoleRequest;
import cn.hxsy.api.system.response.SysRoleResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SysRole;
import cn.hxsy.service.SysRoleService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 系统角色表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:04:30
 */
@RestController
@RequestMapping("/api/v1/sysRole")
public class SysRoleController {

    @Autowired
    private SysRoleService systemUserService;

    @ApiOperation("获取当前系统具有角色列表")
    @PostMapping("/get-system-role-page")
    public Result<Page<SysRole>> getSystemRolePage(
            @ApiParam("查询请求") @RequestParam Integer pageNum, @RequestParam Integer pageSize, @RequestBody SysRoleRequest request) {
        return Result.ok(systemUserService.getSystemRolePage(pageNum, pageSize, request));
    }

    @ApiOperation("新增或修改系统角色")
    @PostMapping("/save-or-update-role")
    public Result<Object> saveOrUpdateRole(
            @ApiParam("查询请求") @RequestBody SysRoleRequest request) {
        return systemUserService.saveOrUpdateRole(request);
    }
}
