package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.datasource.model.entity.ColumnPO;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.ColumnService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "栏目管理")
@RestController
@RequestMapping("/api/v1/column")
@RequiredArgsConstructor
public class ColumnController {

    private final ColumnService columnService;

    @ApiOperation("分页查询栏目列表")
    @GetMapping("/page")
    public Result<Page<ColumnPO>> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("栏目名称") @RequestParam(required = false) String name,
            @ApiParam("总部ID") @RequestParam(required = false) Long headquartersId) {
        return Result.ok(columnService.queryPageById(current, size, name, headquartersId));
    }

    @ApiOperation("获取栏目详情")
    @GetMapping("/{id}")
    public Result<ColumnPO> getById(@ApiParam("栏目ID") @PathVariable Long id) {
        return Result.ok(columnService.getById(id));
    }

    @ApiOperation("新增栏目")
    @PostMapping
    public Result<Boolean> save(@ApiParam("栏目信息") @RequestBody ColumnPO column) {
        return Result.ok(columnService.save(column));
    }

    @ApiOperation("修改栏目")
    @PutMapping
    public Result<Boolean> update(@ApiParam("栏目信息") @RequestBody ColumnPO column) {
        return Result.ok(columnService.updateById(column));
    }

    @ApiOperation("更新栏目状态")
    @PostMapping("/update-status")
    public Result<Boolean> updateStatus(
            @RequestBody UpdateStatusRequest updateStatusRequest) {

        return Result.ok(columnService.updateByIds(updateStatusRequest));
    }
    @ApiOperation("删除栏目")
    @PostMapping("/delete")
    public Result<Boolean> delete(@ApiParam("栏目信息") @RequestBody ColumnPO column)  {
        return Result.ok(columnService.removeById(column.getId()));
    }
}