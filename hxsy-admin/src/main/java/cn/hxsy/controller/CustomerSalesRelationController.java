package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.CustomerAssignRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.CustomerSalesRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 客户销售关联控制器
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(tags = "客户销售关联管理")
@RestController
@RequestMapping("/api/v1/customer-sales-relation")
public class CustomerSalesRelationController {

    @Autowired
    private CustomerSalesRelationService customerSalesRelationService;

    /**
     * 分配客户给销售人员
     *
     * @param request 分配请求
     * @return 是否分配成功
     */
    @ApiOperation("分配客户给销售人员")
    @PostMapping("/assign")
    public Result<Boolean> assignCustomer(@Valid @RequestBody CustomerAssignRequest request) {
        return Result.ok(customerSalesRelationService.assignCustomer(request));
    }

    /**
     * description : 根据客户ID查询销售人员
     * @title: listByCustomerId
     * @param: customerId
     * <AUTHOR>
     * @date 2025/5/18 14:20
     * @return Result<Object>
     */
    @ApiOperation("根据客户ID查询销售人员")
    @GetMapping("/list-by-customer-id")
    public Result<Object> listByCustomerId(@RequestParam Long customerId) {
        return Result.ok(customerSalesRelationService.listByCustomerId(customerId));
    }

} 