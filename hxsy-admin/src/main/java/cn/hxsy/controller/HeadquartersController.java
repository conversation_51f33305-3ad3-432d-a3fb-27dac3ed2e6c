package cn.hxsy.controller;

import cn.hxsy.datasource.model.entity.HeadquartersPO;
import cn.hxsy.base.response.Result;
import cn.hxsy.base.response.HeadquartersTreeResponse;
import cn.hxsy.service.HeadquartersService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Api(tags = "总部管理")
@RestController
@RequestMapping("/api/v1/headquarters")
@RequiredArgsConstructor
public class HeadquartersController {

    private final HeadquartersService headquartersService;

    @ApiOperation("分页查询总部列表")
    @GetMapping("/page")
    public Result<Page<HeadquartersPO>> page(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页大小") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("总部名称") @RequestParam(required = false) String name) {
        
        Page<HeadquartersPO> page = new Page<>(current, size);
        LambdaQueryWrapper<HeadquartersPO> wrapper = new LambdaQueryWrapper<>();
        if (name != null) {
            wrapper.like(HeadquartersPO::getHeadquartersName, name);
        }
        wrapper.orderByDesc(HeadquartersPO::getCreatedAt);
        
        return Result.ok(headquartersService.page(page, wrapper));
    }

    @ApiOperation("获取总部详情")
    @GetMapping("/{id}")
    public Result<HeadquartersPO> getById(@ApiParam("总部ID") @PathVariable Long id) {
        return Result.ok(headquartersService.getById(id));
    }

    @ApiOperation("新增总部")
    @PostMapping
    public Result<Boolean> save(@ApiParam("总部信息") @RequestBody HeadquartersPO headquarters) {
        return Result.ok(headquartersService.save(headquarters));
    }

    @ApiOperation("修改总部")
    @PutMapping
    public Result<Boolean> update(@ApiParam("总部信息") @RequestBody HeadquartersPO headquarters) {
        return Result.ok(headquartersService.updateById(headquarters));
    }

    @ApiOperation("删除总部")
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@ApiParam("总部ID") @PathVariable Long id) {
        return Result.ok(headquartersService.removeById(id));
    }

    @ApiOperation("获取总部树形结构")
    @GetMapping("/tree")
    public Result<HeadquartersTreeResponse> getHeadquartersTree(@ApiParam("总部ID") @RequestParam Integer id, Integer level) {
        return Result.ok(headquartersService.getHeadquartersTree(id, level));
    }
} 