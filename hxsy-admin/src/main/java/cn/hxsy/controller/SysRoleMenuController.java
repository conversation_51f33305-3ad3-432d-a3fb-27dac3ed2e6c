package cn.hxsy.controller;

import cn.hxsy.api.system.request.SysRoleMenuRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.SysRoleMenuService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 系统角色-菜单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:04:30
 */
@RestController
@RequestMapping("/api/v1/sys-role-menu")
public class SysRoleMenuController {

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @ApiOperation("获取当前角色可分配的系统菜单列表")
    @PostMapping("/get-system-role-menu-page")
    public Result<Object> getRoleMenu(
            @ApiParam("查询请求") @RequestBody SysRoleMenuRequest request) {
        return Result.ok(sysRoleMenuService.getRoleHasMenu(request));
    }

    @ApiOperation("保存角色菜单关联关系")
    @PostMapping("/save-system-role-menu")
    public Result<Object> saveRoleMenu(
            @ApiParam("查询请求") @RequestBody SysRoleMenuRequest request) {
        return sysRoleMenuService.saveRoleMenu(request);
    }
}
