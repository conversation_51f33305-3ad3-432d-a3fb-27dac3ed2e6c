package cn.hxsy.controller;

import cn.hutool.json.JSONObject;
import cn.hxsy.api.app.model.request.TencentWxInfoRequest;
import cn.hxsy.api.app.model.response.TencentWxInfoResponse;
import cn.hxsy.api.app.service.TencentWxInfoRpcService;
import cn.hxsy.api.user.feign.vx.AppletClient;
import cn.hxsy.api.user.model.response.AppletInfoResponse;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.base.exception.rpc.code.WxErrorCode;
import cn.hxsy.base.util.AESUtils;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.datasource.model.entity.User;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.UserService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;

import static cn.hxsy.base.constant.ResponseType.Success;
import static cn.hxsy.cache.constant.user.CacheConstant.APP_CONFIG_KEY_PREFIX;

/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13 16:22:51
 */
@Slf4j
@RestController
@RequestMapping(UserController.API_PREFIX)
public class UserController {

    public static final String API_PREFIX = "/api/v1/user";

    @Resource
    private UserService userService;

    @Resource
    private AppletClient appletClient;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @DubboReference(version = "1.0.0")
    TencentWxInfoRpcService tencentWxInfoRpcService;


    @PostMapping("/login")
    public Result login(@RequestBody User user) {
        return userService.login(user);
    }

    @PostMapping("/login-auth")
    public Result loginAuth(@RequestBody User user) {
        return userService.loginAuth(user);
    }

    @ApiOperation(value = "获取微信绑定手机号", notes = "获取微信绑定手机号")
    @GetMapping(value = "/get-user-phone", consumes = MediaType.ALL_VALUE)
    public Result<Object> getUserPhone(@RequestParam String encryptedData, @RequestParam String iv,
            @RequestParam String vxCode, @RequestParam String appId) throws InvalidAlgorithmParameterException {

        // 从缓存中获取小程序配置
        String appCacheKey = APP_CONFIG_KEY_PREFIX + appId;
        TencentWxInfoResponse tencentWxInfoResponse = redisJsonUtils.get(appCacheKey, TencentWxInfoResponse.class);

        //此处需要判断系统获取配置与小程序分别请求是否成功
        if (tencentWxInfoResponse == null) {
            TencentWxInfoRequest tencentWxInfoRequest = new TencentWxInfoRequest();
            tencentWxInfoRequest.setAppid(appId);
            Result<TencentWxInfoResponse> tencentWxInfoQueryResponse = tencentWxInfoRpcService.queryWxConfig(tencentWxInfoRequest);
            if (tencentWxInfoQueryResponse.getCode() != Success.getCode()
                    || tencentWxInfoQueryResponse.getData() == null) {
                throw new BizException(WxErrorCode.request_failure);
            }
            tencentWxInfoResponse = tencentWxInfoQueryResponse.getData();
        }

        AppletInfoResponse authorizationCode = appletClient.jscode2session(tencentWxInfoResponse.getAppid(),
                tencentWxInfoResponse.getSecret(), vxCode, "authorization_code");
        // AESUtils微信获取手机号解密工具类
        AESUtils aes = new AESUtils();
        // 调用AESUtils工具类decrypt方法解密获取json串
        byte[] resultByte = aes.decrypt(Base64.decodeBase64(encryptedData), Base64.decodeBase64(authorizationCode.getSession_key()),
                Base64.decodeBase64(iv));
        if (resultByte == null || resultByte.length == 0) {
            return Result.error("session_key:失败");
        }
        String jsons = new String(resultByte, StandardCharsets.UTF_8);
        JSONObject json = new JSONObject(jsons);
        // json解析获取phoneNumber
        String phoneNumber = json.getStr("phoneNumber");
        return Result.ok(phoneNumber);
    }

}
