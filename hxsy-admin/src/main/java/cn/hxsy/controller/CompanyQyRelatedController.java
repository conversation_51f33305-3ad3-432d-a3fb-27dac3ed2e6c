package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.service.CompanyQyRelatedService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 公司与企微账号关联列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26 15:23:52
 */
@RestController
@RequestMapping("/api/v1/companyQyRelated")
public class CompanyQyRelatedController {

	@Resource
	private CompanyQyRelatedService companyQyRelatedService;

	@ApiOperation("公司批量绑定企微账号信息")
	@PostMapping("/save-company-qy-bind-batch")
	public Result<Object> saveCompanyBindQy(@RequestBody CompanyQyRelationRequest request) {
		return companyQyRelatedService.saveCompanyQyBind(request);
	}

	@ApiOperation("查询公司与企微关联关系")
	@PostMapping("/query-company-bind-qy")
	public Result<Object> queryCompanyBindQy(@RequestBody CompanyQyRelationRequest request) {
		return companyQyRelatedService.queryCompanyBindQy(request);
	}
}
