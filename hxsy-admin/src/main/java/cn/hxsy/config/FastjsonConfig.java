//package cn.hxsy.config;
//
//import com.alibaba.fastjson.serializer.SerializeConfig;
//import com.alibaba.fastjson.serializer.SerializerFeature;
//import com.alibaba.fastjson.support.config.FastJsonConfig;
//import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.http.MediaType;
//import org.springframework.http.converter.HttpMessageConverter;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//
///**
//* @description: Fastjson全局配置，主要用于处理时间类型的序列化
//* @author: xiaQL
//* @date: 2025/4/27 1:06
//*/
//@Configuration
//public class FastjsonConfig implements WebMvcConfigurer {
//
//    @Override
//    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
//        FastJsonHttpMessageConverter converter = new FastJsonHttpMessageConverter();
//        FastJsonConfig config = new FastJsonConfig();
//        // 配置序列化特性
//        config.setSerializerFeatures(
//            SerializerFeature.WriteMapNullValue,        // 输出空值字段
//            SerializerFeature.WriteNullListAsEmpty,     // List字段如果为null,输出为[]
//            SerializerFeature.WriteNullStringAsEmpty,   // 字符类型字段如果为null,输出为""
//            SerializerFeature.WriteDateUseDateFormat,   // 日期格式化
//            SerializerFeature.DisableCircularReferenceDetect // 禁用循环引用检测
//        );
//        // 配置日期格式
//        config.setDateFormat("yyyy-MM-dd HH:mm:ss");
//        // 全局配置序列化
//        SerializeConfig serializeConfig = SerializeConfig.globalInstance;
//        // 处理LocalDateTime，确保输出格式不带T
//        serializeConfig.put(LocalDateTime.class, new com.alibaba.fastjson.serializer.ToStringSerializer() {
//            @Override
//            public void write(com.alibaba.fastjson.serializer.JSONSerializer serializer, Object object,
//                            Object fieldName, java.lang.reflect.Type fieldType, int features) {
//                if (object == null) {
//                    serializer.out.writeNull();
//                    return;
//                }
//                LocalDateTime localDateTime = (LocalDateTime) object;
//                String format = localDateTime.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//                serializer.out.writeString(format);
//            }
//        });
//        // 将Long类型转换成String类型
//        serializeConfig.put(Long.class, com.alibaba.fastjson.serializer.ToStringSerializer.instance);
//        serializeConfig.put(Long.TYPE, com.alibaba.fastjson.serializer.ToStringSerializer.instance);
//        // 将Integer类型转换成String类型
//        serializeConfig.put(Integer.class, com.alibaba.fastjson.serializer.ToStringSerializer.instance);
//        serializeConfig.put(Integer.TYPE, com.alibaba.fastjson.serializer.ToStringSerializer.instance);
//        config.setSerializeConfig(serializeConfig);
//        // 设置支持的媒体类型
//        List<MediaType> mediaTypes = new ArrayList<>();
//        mediaTypes.add(MediaType.APPLICATION_JSON);
//        converter.setSupportedMediaTypes(mediaTypes);
//        converter.setFastJsonConfig(config);
//        converters.add(0, converter); // 添加到首位
//    }
//}
