package cn.hxsy;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {
        "cn.hxsy"})
@EnableDubbo
@EnableFeignClients(basePackages = {"cn.hxsy.api.user.feign", "cn.hxsy.api.qy.feign"})
@EnableAsync
public class HxsyAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(HxsyAdminApplication.class, args);
    }

}
