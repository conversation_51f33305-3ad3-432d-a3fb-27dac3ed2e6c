package cn.hxsy.utils;

import cn.hxsy.api.system.response.SysUserSelectPermissionResponse;
import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.constant.user.UserRole;
import cn.hxsy.base.constant.user.UserSelectType;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.cache.config.RedisJsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static cn.hxsy.base.constant.user.UserSelectType.*;
import static cn.hxsy.base.exception.system.code.SystemUserErrorCode.OPERATION_NOT_PERMISSION;

/**
 * <AUTHOR> XiaQL
 * @description : 角色查询权限工具类
 * 根据用户拥有角色类型查询
 * @ClassName : UserSelectUtil
 * @date: 2025-05-11 17:01
 */
@Component
public class UserSelectUtil {

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    /**
    * @description: 根据对应查询场景、用户拥有角色类型，返回应有查询条件
     * 1、角色权限的查询
     * 返回对象不为空，即表示需要做查询权限限制
     * 超管、普管-查询全部
     * 栏目管理员-获取自身对应查询权限，查询其下可查询的栏目；若没有则查询自身下栏目的全部公司，公司下的全部销售组信息
     * 公司管理员-获取自身对应查询权限，查询其下可查询公司（不限制在自身栏目下）；若没有则查询自身所属单栏目下单公司信息，此公司下的全部销售组信息
     * 销售组管理员-获取自身对应查询权限，查询其下可查询销售组（不限制在自身公司下）；若没有则查询自身所属销售组信息
     * 其他-查询自身归属栏目、公司下的单个销售组信息
     * 2、不同查询场景下，前端传入筛选条件，结合上一步返回的查询限制
     * 2.1 为空即代表当前有更大的查询权限，所以前端传入的筛选条件可以直接设置
     * 2.2 不为空，判断返回与当前查询值是否匹配，不匹配则当前筛选条件不能使用，只能用当前用户自身所属
     * 查询公司信息，需要判断自己是否拥有对应公司的查询权限
     * 查询销售组信息，需要判断自己是否拥有对应销售组的查询权限
    * @author: xiaQL
    * @date: 2025/5/11 17:02
    */
    public SelectPermissionRequest getSelectPermission(SystemUserResponse systemUserSelfInfo, UserSelectType userSelectType, OrganizationQueryRequest request) {
        // 1、判断用户角色类型，获取用户可查询范围（栏目、销售组、公司管理员可用）
        Integer roleType = systemUserSelfInfo.getRoleType();
        SysUserSelectPermissionResponse sysUserSelectPermissionResponse = systemUserSelfInfo.getSysUserSelectPermissionResponse();
        // 2、根据角色类型获取查询条件
        SelectPermissionRequest selectPermissionRequest = new SelectPermissionRequest();
//        if(roleType == null){
//            throw new BizException(OPERATION_NOT_PERMISSION);
//        }
        if (roleType == null){
            // 没分配角色，按归属下的那个销售组来查就行
            BeanUtils.copyProperties(systemUserSelfInfo, selectPermissionRequest);
        } else if (roleType == UserRole.ADMIN.getCode() || roleType == UserRole.COMMON_ADMIN.getCode()){
            // 超管和普管，可以查询所有组织，不设置查询条件
        }else if (roleType == UserRole.COL_ADMIN.getCode()){
            // 2.1、栏目管理员，首先需要查询下自身可查询范围，判断是否可查询多栏目
            if(sysUserSelectPermissionResponse != null && CollectionUtils.isNotEmpty(sysUserSelectPermissionResponse.getPerColumnId())){
                // 2.1.1、可查询多栏目，设置上
                selectPermissionRequest.setPerColumnId(sysUserSelectPermissionResponse.getPerColumnId());
                selectPermissionRequest.setPerCompanyId(sysUserSelectPermissionResponse.getPerCompanyId());
                selectPermissionRequest.setPerSalesGroupId(sysUserSelectPermissionResponse.getPerSalesGroupId());
            }
            // 若没有可见查询范围，则查询自身栏目下所有公司，但因为业务人员注册时就绑定了归属公司id、销售组id，所以需要置空公司、销售组查询条件，只设置归属栏目查询条件
            selectPermissionRequest.setHeadquartersId(systemUserSelfInfo.getHeadquartersId());
            selectPermissionRequest.setColumnId(systemUserSelfInfo.getColumnId());
        }else if (roleType == UserRole.COM_ADMIN.getCode()){
            // 2.2、公司管理员，首先需要查询下自身可查询范围，判断是否可查询多公司
            if(sysUserSelectPermissionResponse != null && CollectionUtils.isNotEmpty(sysUserSelectPermissionResponse.getPerCompanyId())){
                // 2.2.1、可查询多公司，设置上
                selectPermissionRequest.setPerColumnId(sysUserSelectPermissionResponse.getPerColumnId());
                selectPermissionRequest.setPerCompanyId(sysUserSelectPermissionResponse.getPerCompanyId());
                selectPermissionRequest.setPerSalesGroupId(sysUserSelectPermissionResponse.getPerSalesGroupId());
            }
            // 若没有可见查询范围，则查询自身公司下的全部销售组信息，但因为业务人员注册时就绑定了归属销售组id，所以手动置空销售组查询条件，设置归属公司查询条件
            BeanUtils.copyProperties(systemUserSelfInfo, selectPermissionRequest);
            selectPermissionRequest.setSalesGroupId(null);
        }else if (roleType == UserRole.SALE_ADMIN.getCode()){
            // 2.3、销售组管理员，首先需要查询下自身可查询范围，判断是否可查询多销售组
            if(sysUserSelectPermissionResponse != null && CollectionUtils.isNotEmpty(sysUserSelectPermissionResponse.getPerSalesGroupId())){
                // 2.3.1、可查询多销售组，设置上
                selectPermissionRequest.setPerColumnId(sysUserSelectPermissionResponse.getPerColumnId());
                selectPermissionRequest.setPerCompanyId(sysUserSelectPermissionResponse.getPerCompanyId());
                selectPermissionRequest.setPerSalesGroupId(sysUserSelectPermissionResponse.getPerSalesGroupId());
            }
            // 若没有可见查询范围，则查询自身销售组信息
            BeanUtils.copyProperties(systemUserSelfInfo, selectPermissionRequest);
        }else {
            // 2.4、其他角色，就正常按照自己归属下的那个销售组来查就行
            BeanUtils.copyProperties(systemUserSelfInfo, selectPermissionRequest);
        }
        // 3、根据查询场景，获取前端传入的筛选条件
        this.getPermissionBySelectType(selectPermissionRequest, userSelectType, request);
        return selectPermissionRequest;
    }

    /**
    * @description: 根据对应查询场景传入的筛选条件，返回对应角色应有查询条件
     * 不同查询场景下，前端传入筛选条件，结合上一步返回的查询限制
     * 1）传入权限参数为空即代表当前有更大的查询权限，所以前端传入的筛选条件可以直接设置
     * 2）不为空，判断返回的权限中，是否包含查询权限（栏目、公司、销售组管理员）
     * 2.1）包含查询权限，看是否包含当前查询值，包含则设置上，并置空查询权限
     * 2.2）不包含查询权限，则看自身所属组织是否匹配当前筛选条件，匹配则带上
     * userSelectType: 查询场景介绍（也可以用作筛选当前用户是否具有传入查询组织的查询权限，自己构造对应下一集的筛选参数，只要没有抛出异常，就是具有对应权限了）
     * column: 查询一个总部下的全部栏目
     * company: 查询一个栏目下的全部公司（用作筛选当前用户是否具有传入栏目的查询权限）
     * salesGroup: 查询一个公司下的全部销售组（用作筛选当前用户是否具有传入公司的查询权限）
    * @author: xiaQL
    * @date: 2025/5/18 22:13
    */
    public void getPermissionBySelectType(SelectPermissionRequest selectPermissionRequest, UserSelectType userSelectType, OrganizationQueryRequest request) {
        if(column == userSelectType){
            // 当前角色具备查询全部栏目信息，直接使用前端筛选查询条件
            if(selectPermissionRequest.getColumnId() == null){
                selectPermissionRequest.setColumnId(
                        StringUtils.isEmpty(request.getColumnId()) ? null : Integer.valueOf(request.getColumnId()));
            }else {
                // 1、需要判断当前角色具备查询的栏目id与当前筛选的栏目id不匹配，则不具备对应查询权限
                if(StringUtils.isNotEmpty(request.getColumnId())){
                    if (CollectionUtils.isNotEmpty(selectPermissionRequest.getPerColumnId())){
                        // 1.1、首先是栏目管理员，需要单独处理可见权限范围，如果在范围内就使用，并且置空可见范围用于具体类中查询
                        if(selectPermissionRequest.getPerColumnId().contains(request.getColumnId())){
                            selectPermissionRequest.setPerColumnId(null);
                            selectPermissionRequest.setColumnId(Integer.valueOf(request.getColumnId()));
                        }else {
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }else {
                        // 1.2、其他角色，需要判断当前角色具备查询的栏目id与当前筛选的栏目id不匹配，则不具备对应查询权限
                        if(!Objects.equals(selectPermissionRequest.getColumnId(), Integer.valueOf(request.getColumnId()))){
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }
                }
            }
        }else if(company == userSelectType){
            // 当前角色具备查询全部栏目下的公司信息，直接使用前端筛选查询条件
            if(selectPermissionRequest.getColumnId() == null){
                selectPermissionRequest.setColumnId(
                        StringUtils.isEmpty(request.getColumnId()) ? null : Integer.valueOf(request.getColumnId()));
            }else {
                // 1、需要判断当前角色具备查询的栏目id与当前筛选的栏目id不匹配，则不具备对应查询权限
                if(StringUtils.isNotEmpty(request.getColumnId())){
                    if (CollectionUtils.isNotEmpty(selectPermissionRequest.getPerColumnId())){
                        // 1.1、首先是栏目管理员，需要单独处理可见权限范围，如果在范围内就使用，并且置空可见范围用于具体类中查询
                        if(selectPermissionRequest.getPerColumnId().contains(request.getColumnId())){
                            selectPermissionRequest.setPerColumnId(null);
                            selectPermissionRequest.setColumnId(Integer.valueOf(request.getColumnId()));
                        }else {
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }else {
                        // 1.2、其他角色，需要判断当前角色具备查询的栏目id与当前筛选的栏目id不匹配，则不具备对应查询权限
                        if(!Objects.equals(selectPermissionRequest.getColumnId(), Integer.valueOf(request.getColumnId()))){
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }
                }
            }
        }else if(saleGroup == userSelectType){
            // 当前角色具备查询全部公司下的销售组信息，直接使用前端筛选查询条件
            if(selectPermissionRequest.getCompanyId() == null){
                selectPermissionRequest.setCompanyId(
                        StringUtils.isEmpty(request.getCompanyId()) ? null : Integer.valueOf(request.getCompanyId()));
                // 对于栏目管理员也不存在公司筛选条件，但是存在公司查询范围，需要置空
                selectPermissionRequest.setPerCompanyId(null);
            }else {
                // 1、需要判断当前角色具备查询的公司id与当前前端筛选的公司id是否匹配，则不具备对应查询权限
                if(StringUtils.isNotEmpty(request.getCompanyId())){
                    if (CollectionUtils.isNotEmpty(selectPermissionRequest.getPerCompanyId())){
                        // 1.1、首先是公司管理员，需要单独处理可见权限范围
                        if(selectPermissionRequest.getPerCompanyId().contains(request.getCompanyId())){
                            selectPermissionRequest.setPerCompanyId(null);
                            selectPermissionRequest.setCompanyId(Integer.valueOf(request.getCompanyId()));
                        }else {
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }else {
                        // 1.2、对于没有分配查询权限的角色（公司管理员或更小权限角色），需要判断当前角色具备查询的公司id
                        if(!Objects.equals(selectPermissionRequest.getCompanyId(), Integer.valueOf(request.getCompanyId()))){
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }
                }
            }
            // 小程序端还会使用销售组id作为反向查询营期的条件，所以这里还需要判断前端传入的销售组id条件是否使用，首先是更大权限的判断
            if(selectPermissionRequest.getSalesGroupId() == null){
                selectPermissionRequest.setSalesGroupId(
                        StringUtils.isEmpty(request.getSalesGroup()) ? null : Integer.valueOf(request.getSalesGroup()));
                // 对于栏目、公司管理员也不存在销售组筛选条件，但是存在销售组查询范围，需要置空销售组查询范围
                selectPermissionRequest.setPerSalesGroupId(null);
            }else {
                // 1、需要判断当前角色具备查询的销售组id是否与前端传入的销售组id条件匹配
                if(StringUtils.isNotEmpty(request.getSalesGroup())){
                    if (CollectionUtils.isNotEmpty(selectPermissionRequest.getPerSalesGroupId())){
                        // 1.1、首先是销售组管理员，需要单独处理可见权限范围
                        if(selectPermissionRequest.getPerSalesGroupId().contains(request.getSalesGroup())){
                            selectPermissionRequest.setPerSalesGroupId(null);
                            selectPermissionRequest.setSalesGroupId(Integer.valueOf(request.getSalesGroup()));
                        }else {
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }else {
                        // 1.2、对于没有分配查询权限的角色（销售组管理员或更小权限角色），需要判断当前角色具备查询的销售组id
                        if(!Objects.equals(selectPermissionRequest.getSalesGroupId(), Integer.valueOf(request.getSalesGroup()))){
                            throw new RuntimeException("您没有权限进行此操作");
                        }
                    }
                }
            }
        }else if(organization == userSelectType){
            // 当前角色具备查询全部总部信息，直接使用前端筛选查询条件
            if(selectPermissionRequest.getHeadquartersId() == null){
                selectPermissionRequest.setHeadquartersId(request.getHeadquartersId());
            }else {
                // 如果角色具备查询的总部id与当前前端筛选的总部id不匹配，则不具备对应查询权限
                if(request.getHeadquartersId() != null){
                    if(!Objects.equals(selectPermissionRequest.getHeadquartersId(), request.getHeadquartersId())){
                        throw new RuntimeException("您没有权限进行此操作");
                    }
                }
            }
        }else if(system_user == userSelectType){
            // 角色查询四种条件都会筛选，直接递归自己判断
            this.getPermissionBySelectType(selectPermissionRequest, organization, request);
            this.getPermissionBySelectType(selectPermissionRequest, company, request);
            this.getPermissionBySelectType(selectPermissionRequest, saleGroup, request);
        }
    }

}
