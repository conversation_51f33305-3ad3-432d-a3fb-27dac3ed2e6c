package cn.hxsy.utils;

import java.security.SecureRandom;
import java.util.Base64;

public class WeComTokenGenerator {

    private static final String CHARSET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final SecureRandom RANDOM = new SecureRandom();
    private static final int KEY_LENGTH = 43;

    /**
     * 生成随机 token（长度：默认 32 字符）
     */
    public static String generateToken(int length) {
        if (length < 3 || length > 32) {
            throw new IllegalArgumentException("Token 长度应在 3 到 32 字符之间");
        }
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(CHARSET.charAt(RANDOM.nextInt(CHARSET.length())));
        }
        return sb.toString();
    }

    /**
     * 生成 43 位 EncodingAESKey（Base64 字符串）
     */
    public static String generateEncodingAESKey() {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder(KEY_LENGTH);
        for (int i = 0; i < KEY_LENGTH; i++) {
            int index = random.nextInt(CHARSET.length());
            sb.append(CHARSET.charAt(index));
        }
        return sb.toString();    }

    public static void main(String[] args) {
        String token = generateToken(32);
        String aesKey = generateEncodingAESKey();
        System.out.println("Token: " + token);
        System.out.println("EncodingAESKey: " + aesKey);
    }
}