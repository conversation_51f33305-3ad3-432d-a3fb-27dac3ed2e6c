package cn.hxsy.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hxsy.api.qy.feign.auth.QyWxAuthClient;
import cn.hxsy.api.qy.request.auth.QyWechatAuthReq;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.user.feign.vx.QyAppClient;
import cn.hxsy.base.constant.qyWechat.auth.QyWechatAuthType;
import cn.hxsy.base.constant.qyWechat.secretApp.QyWechatConfigType;
import cn.hxsy.base.constant.system.QyWechatQueryType;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.constant.user.CacheConstant;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.request.QyProviderTokenRequest;
import cn.hxsy.service.CompanyQyRelationService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.hxsy.base.constant.qyWechat.auth.QyWechatAuthType.QY_CORP_ACCESS_TOKEN;
import static cn.hxsy.cache.constant.qyWechat.auth.QyAuthCacheConstant.QY_CORP_AUTH_TOKEN;

/**
 * <AUTHOR> XiaQL
 * @description : 企微服务商相关授权token缓存工具类
 * @ClassName : QyWxchatUtils
 * @date: 2025-06-28 16:51
 */
@Component
@Slf4j
public class QyWechatCacheUtils {

    @Resource
    private RedisJsonUtils redisJsonUtils;

    @Resource
    private CompanyQyRelationService companyQyRelationService;

    @Resource
    private QyWxAuthClient qyWxAuthClient;

    @Resource
    private QyAppClient qyAppClient;

    @Value("${feign.provider.qyWx.provideId:ww1382cb0dbbdaf9ee}")
    private String provideId;

    /*
     * 本地测试挡板，为true时可手动传入企微需求token参数
     */
    @Value("${feign.provider.qyWx.mock:false}")
    private Boolean mock;

    /**
     * @description: 先走缓存获取对应场景访问的accessToken
     * 没有再请求企微官方，将对应结果缓存（默认过期时间是2小时，直接用企微响应的过期时间）
     * @param request
     * authType：判断当前是哪个回调场景中使用，需要获取到哪个secret，默认是代开发模版
     * @author: xiaQL
     * @date: 2025/6/28 16:48
     */
    public QyWeChatAuthResponse getAndCacheQyWxToken(QyProviderTokenRequest request) {
        String corpId = request.getCorpId();
        String authType = request.getCorpType();
        QyWechatAuthType qyWechatAuthType = request.getQyWechatAuthType();
        // 0、默认是代开发模版，如果传入，一般是在获取通讯录回调授权，就使用传入的
        if(StringUtils.isEmpty(authType)){
            authType = QyWechatConfigType.PROVIDER_TEMPLATE.getCode();
        }
        // 0.1、先从redis获取对应场景需要的访问token，存在则设置过期时间并返回
        String accessTokenKey = QY_CORP_AUTH_TOKEN + qyWechatAuthType.getInfo() + ":" + corpId + ":" + authType;
        String accessToken = redisJsonUtils.get(accessTokenKey, String.class);
        if(StringUtils.isNotEmpty(accessToken)){
            // 0.1.1、存在则手动构造下过期时间，一是为了redis不存储多余数据，二是为了前端调用的时候知道何时过期可以重新获取
            QyWeChatAuthResponse qyWeChatAuthResponse = new QyWeChatAuthResponse();
            switch (qyWechatAuthType) {
                case PROVIDER_TOKEN:
                    qyWeChatAuthResponse.setProvider_access_token(accessToken);
                case SUITE_ACCESS_TOKEN:
                    qyWeChatAuthResponse.setSuite_access_token(accessToken);
            }
            qyWeChatAuthResponse.setExpires_in(redisJsonUtils.getExpire(accessTokenKey).intValue());
            return qyWeChatAuthResponse;
        }
        // 1、当前企业对应的访问token已过期，需要根据secret与对应corpId去企微官方获取并缓存，先判断是否已经有企微账号配置
        LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyQyRelation::getCorpId, corpId)
                .eq(CompanyQyRelation::getCorpType, authType);
        List<CompanyQyRelation> companyQyRelations = companyQyRelationService.getBaseMapper().selectList(wrapper);
        if(CollectionUtil.isEmpty(companyQyRelations)){
            throw new RuntimeException("当前企业未配置企微账号");
        }
        CompanyQyRelation companyQyRelation = companyQyRelations.get(0);
        // 2、根据当前归属应用的查询场景获取secret，再获取对应访问token
        String accessSecret = companyQyRelation.getCorpSecret();
        QyWeChatAuthResponse qyWeChatAuthResponse = new QyWeChatAuthResponse();
        try {
            switch (qyWechatAuthType) {
                case PROVIDER_TOKEN:
                    log.info("获取服务商{}请求参数：{}，{}", qyWechatAuthType.getInfo(), corpId, accessSecret);
                    JSONObject json = new JSONObject();
                    json.put("corpid", corpId);
                    json.put("provider_secret", accessSecret);
                    qyWeChatAuthResponse = qyWxAuthClient.getProviderToken(json);
                    log.info("获取服务商{}，响应状态码：{}, 响应内容:{}", qyWechatAuthType.getInfo(), qyWeChatAuthResponse.getErrcode(), qyWeChatAuthResponse);
                    if (StringUtils.isNotEmpty(qyWeChatAuthResponse.getErrcode()) && !"0".equals(qyWeChatAuthResponse.getErrcode())) {
                        throw new RuntimeException(qyWeChatAuthResponse.getErrmsg());
                    }
                    redisJsonUtils.set(accessTokenKey, qyWeChatAuthResponse.getProvider_access_token(), qyWeChatAuthResponse.getExpires_in(), TimeUnit.SECONDS);
                    break;
                case SUITE_ACCESS_TOKEN:
                    QyWechatAuthReq qyWechatAuthReq = new QyWechatAuthReq();
                    qyWechatAuthReq.setSuite_id(companyQyRelation.getSuiteId());
                    qyWechatAuthReq.setSuite_secret(companyQyRelation.getSuiteSecret());
                    // suite_ticket由企微每十分钟回调一次，直接到缓存获取
                    if(!mock){
                        qyWechatAuthReq.setSuite_ticket(redisJsonUtils.get(CacheConstant.SUITE_TICKET + corpId + ":" + authType, String.class));
                    }else {
                        qyWechatAuthReq.setSuite_ticket(request.getSuiteTicket());
                    }
                    log.info("获取服务商{}请求参数：{}", qyWechatAuthType.getInfo(), qyWechatAuthReq);
                    qyWeChatAuthResponse = qyWxAuthClient.getSuiteToken(qyWechatAuthReq);
                    log.info("获取服务商{}，响应状态码：{}, 响应内容:{}", qyWechatAuthType.getInfo(), qyWeChatAuthResponse.getErrcode(), qyWeChatAuthResponse);
                    if (StringUtils.isNotEmpty(qyWeChatAuthResponse.getErrcode()) && !"0".equals(qyWeChatAuthResponse.getErrcode())) {
                        throw new RuntimeException(qyWeChatAuthResponse.getErrmsg());
                    }
                    redisJsonUtils.set(accessTokenKey, qyWeChatAuthResponse.getSuite_access_token(), qyWeChatAuthResponse.getExpires_in(), TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.info("获取服务商{}失败，异常：{}", qyWechatAuthType.getInfo(), e.getMessage());
            throw new RuntimeException("企微官方异常，请稍后重试");
        }
        return qyWeChatAuthResponse;
    }

    /**
     * @description: 根据企微id获取企微访问token
     * 先走缓存
     * 没有再请求企微官方，将对应结果缓存（默认过期时间是2小时，直接用企微响应的过期时间吧）
     * @author: xiaQL
     * @date: 2025/6/17 23:56
     */
    public QyWeChatAuthResponse getQyAccessToken(String corpId) {
        String accessTokenKey = QY_CORP_ACCESS_TOKEN.getInfo() + corpId;
        String accessToken = redisJsonUtils.get(accessTokenKey, String.class);
        if(StringUtils.isNotEmpty(accessToken)){
            // 手动构造下过期时间，一是为了redis不存储多余数据，二是为了前端调用的时候知道何时过期可以重新获取
            QyWeChatAuthResponse qyWeChatAuthResponse = new QyWeChatAuthResponse();
            qyWeChatAuthResponse.setAccess_token(accessToken);
            qyWeChatAuthResponse.setExpires_in(redisJsonUtils.getExpire(accessTokenKey).intValue());
            return qyWeChatAuthResponse;
        }
        // 1、当前企业对应的访问token已过期，需要根据secret与对应corpId获取并缓存，先判断是否已经有企微账号配置(管理端是有配置，但是登录场景没有)
        LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyQyRelation::getCorpId, corpId);
        List<CompanyQyRelation> companyQyRelations = companyQyRelationService.getBaseMapper().selectList(wrapper);
        if(CollectionUtil.isEmpty(companyQyRelations)){
            throw new RuntimeException("当前企业未配置企微账号");
        }
        CompanyQyRelation companyQyRelation = companyQyRelations.get(0);
        // 2、根据当前归属应用的查询场景获取secret，再获取对应访问token
        String accessSecret = companyQyRelation.getCorpSecret();
        QyWeChatAuthResponse qyWeChatAuthResponse = qyAppClient.getToken(corpId, accessSecret);
        // 3、校验返回结果，为空则抛异常
        qyWeChatAuthResponse.checkQyResponse();
        log.info("企微{}，获取accessToken，响应状态码：{}, 响应内容:{}", corpId, qyWeChatAuthResponse.getErrcode(), qyWeChatAuthResponse);
        redisJsonUtils.set(accessTokenKey, qyWeChatAuthResponse.getAccess_token(), qyWeChatAuthResponse.getExpires_in(), TimeUnit.SECONDS);
        return qyWeChatAuthResponse;
    }

    /**
     * @description: 根据企微id获取企微访问token
     * 先走缓存
     * 没有再请求企微官方，将对应结果缓存（默认过期时间是2小时，直接用企微响应的过期时间吧）
     * @author: xiaQL
     * @date: 2025/6/17 23:56
     */
    public QyWeChatAuthResponse getQyProviderAccessToken(QyProviderTokenRequest request) {
        String corpId = request.getCorpId();
        String authType = request.getCorpType();
        // 0、默认是代开发模版，如果传入，一般是在获取通讯录回调授权，就使用传入的
        if(StringUtils.isEmpty(authType)){
            authType = QyWechatConfigType.PROVIDER_TEMPLATE.getCode();
        }
        String accessTokenKey = QY_CORP_ACCESS_TOKEN.getInfo() + corpId + ":" + authType;
        String accessToken = redisJsonUtils.get(accessTokenKey, String.class);
        if(StringUtils.isNotEmpty(accessToken)){
            // 手动构造下过期时间，一是为了redis不存储多余数据，二是为了前端调用的时候知道何时过期可以重新获取
            QyWeChatAuthResponse qyWeChatAuthResponse = new QyWeChatAuthResponse();
            qyWeChatAuthResponse.setAccess_token(accessToken);
            qyWeChatAuthResponse.setExpires_in(redisJsonUtils.getExpire(accessTokenKey).intValue());
            return qyWeChatAuthResponse;
        }
        // 1、当前企业对应的访问token已过期，需要根据secret与对应corpId获取并缓存，先判断是否已经有企微账号配置(管理端是有配置，但是登录场景没有)
        LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyQyRelation::getCorpId, corpId);
        List<CompanyQyRelation> companyQyRelations = companyQyRelationService.getBaseMapper().selectList(wrapper);
        if(CollectionUtil.isEmpty(companyQyRelations)){
            throw new RuntimeException("当前企业未配置企微账号");
        }
        CompanyQyRelation companyQyRelation = companyQyRelations.get(0);
        // 2、根据当前归属应用的查询场景获取secret，再获取对应访问token
        String accessSecret = "";
        QyWeChatAuthResponse qyWeChatAuthResponse = new QyWeChatAuthResponse();
        if (QyWechatConfigType.PROVIDER_CONTACT.getCode().equals(authType)){
            accessSecret = companyQyRelation.getContactSecret();
            log.info("获取企微号：{}应用凭证，请求参数:{}", corpId, accessSecret);
            // 2.1、构造服务商应用凭证获取参数
            request.setQyWechatAuthType(QyWechatAuthType.SUITE_ACCESS_TOKEN);
            request.setCorpId(provideId);
            QyWeChatAuthResponse andCacheQyWxToken = this.getAndCacheQyWxToken(request);
            QyWechatAuthReq qyWechatAuthReq = new QyWechatAuthReq();
            qyWechatAuthReq.setAuth_corpid(corpId);
            qyWechatAuthReq.setPermanent_code(accessSecret);
            qyWeChatAuthResponse = qyAppClient.getCorpToken(andCacheQyWxToken.getSuite_access_token(), qyWechatAuthReq);
        }else {
            accessSecret = companyQyRelation.getCorpSecret();
            log.info("企微{}，获取accessToken，请求参数:{}", corpId, accessSecret);
            qyWeChatAuthResponse = qyAppClient.getToken(corpId, accessSecret);
        }
        // 3、校验返回结果，为空则抛异常
        qyWeChatAuthResponse.checkQyResponse();
        log.info("企微{}，获取accessToken，响应状态码：{}, 响应内容:{}", corpId, qyWeChatAuthResponse.getErrcode(), qyWeChatAuthResponse);
        redisJsonUtils.set(accessTokenKey, qyWeChatAuthResponse.getAccess_token(), qyWeChatAuthResponse.getExpires_in(), TimeUnit.SECONDS);
        return qyWeChatAuthResponse;
    }
}
