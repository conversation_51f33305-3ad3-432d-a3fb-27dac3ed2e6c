package cn.hxsy.response;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> XiaQL
 * @description : SystemUserQyRelationResponse
 * @ClassName : SystemUserQyRelationResponse
 * @date: 2025-07-24 15:32
 */
@Data
public class SystemUserQyRelationResponse extends BaseResponse {

	@ApiModelProperty("业务人员id")
	private String systemUserId;

	@ApiModelProperty("业务人员在企业内的UserID")
	private String qyUserId;

	@ApiModelProperty("当前企微账号绑定系统业务人员信息-前端展示企微用户被绑定的系统用户数据使用")
	private SystemUserResponse systemUserResponse;

	@ApiModelProperty("业务人员所在企业ID")
	private String corpId;

	@ApiModelProperty("业务人员在企业的名称")
	private String qyName;

}
