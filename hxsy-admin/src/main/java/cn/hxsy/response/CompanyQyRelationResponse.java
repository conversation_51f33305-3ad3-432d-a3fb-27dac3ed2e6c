package cn.hxsy.response;

import cn.hxsy.base.response.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description : CompanyQyRelationResponse
 * @ClassName : CompanyQyRelationResponse
 * @date: 2025-07-24 15:32
 */
@Data
public class CompanyQyRelationResponse extends BaseResponse {

	@ApiModelProperty("企业ID")
	private String corpId;

	@ApiModelProperty("企业名称")
	private String corpName;

	@ApiModelProperty("企业微信后台推送的ticket")
	private String suiteTicket;

	@ApiModelProperty("企微回调token")
	private String token;

	@ApiModelProperty("企微回调加签key")
	private String encodingAESKey;

	@ApiModelProperty("已绑定公司id")
	private List<String> companyId;

}
