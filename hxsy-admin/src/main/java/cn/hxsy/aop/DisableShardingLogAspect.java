package cn.hxsy.aop;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> XiaQL
 * @description : DisableShardingLogAspect
 * 临时禁用ERROR级别以下的日志，防止打印修改sql作为info级别日志过多
 * @ClassName : DisableShardingLogAspect
 * @date: 2025-06-16 21:12
 */
@Component
@Aspect
@Slf4j
public class DisableShardingLogAspect {

    /*
     * 通过AOP禁用ShardingSphere的DEBUG日志
     */
    private static final String SQL_LOGGER_NAME = "ShardingSphere-SQL";

    /*
     * 通过AOP禁用mybatis-plus的DEBUG日志
     */
    private static final String MP_SQL_LOGGER_NAME = "cn.hxsy.dao";

    @Around(value = "@annotation(cn.hxsy.anno.DisableShardingLog)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        log.info("禁用sql的ERROR级别之下的日志");
        Logger sqlLogger = (Logger) LoggerFactory.getLogger(SQL_LOGGER_NAME);
        Level originalLevel = sqlLogger.getLevel();
        Logger mybatisLogger = (Logger) LoggerFactory.getLogger(MP_SQL_LOGGER_NAME);
        Level mybatisOriginalLevel = mybatisLogger.getLevel();
        try {
            sqlLogger.setLevel(Level.ERROR);
//            log.info("获取到原本日志等级：{}， 当前设置后等级为：{}", originalLevel, sqlLogger.getLevel());
            mybatisLogger.setLevel(Level.ERROR);
            return point.proceed();
        } finally {
            sqlLogger.setLevel(originalLevel); // 恢复sharding日志级别
            mybatisLogger.setLevel(mybatisOriginalLevel); // 恢复MP日志级别
        }
    }

}
