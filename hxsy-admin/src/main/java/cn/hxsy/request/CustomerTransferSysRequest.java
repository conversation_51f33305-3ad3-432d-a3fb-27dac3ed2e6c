package cn.hxsy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 企业微信客户转移请求DTO
 */
@Data
@ApiModel(value = "CustomerTransferRequest", description = "企业微信客户转移对应系统内查询条件")
public class CustomerTransferSysRequest {

    @ApiModelProperty(value = "原跟进业务成员的userId")
    private String handoverSysUserId;

    @ApiModelProperty(value = "接替业务成员的userid")
    private String takeoverSysUserId;

    @ApiModelProperty(value = "待分配客户id列表-批量分配情况")
    private List<String> customerIds;

    @ApiModelProperty(value = "待分配客户id列表-单个客户分配情况")
    private String customerId;

}
