package cn.hxsy.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * 企业微信客户转移前原跟进人员状态记录
 */
@Data
@ApiModel(value = "CustomerTransferMapRequest", description = "企业微信客户转移前原跟进人员状态记录")
public class CustomerTransferMapRequest {

    @ApiModelProperty(value = "原跟进业务成员的userId")
    private String handoverSysUserId;

    @ApiModelProperty(value = "原跟进业务成员的离职状态 true-离职 false-在职")
    private Boolean isResigned;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerTransferMapRequest that = (CustomerTransferMapRequest) o;
        return Objects.equals(handoverSysUserId, that.handoverSysUserId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(handoverSysUserId);
    }
}
