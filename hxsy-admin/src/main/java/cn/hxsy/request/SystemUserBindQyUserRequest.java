package cn.hxsy.request;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.request.BaseRequestDTO;
import cn.hxsy.base.request.SystemUserQyRelationRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 系统用户绑定企微账号请求实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Getter
@Setter
@ApiModel(value = "SystemUserQyRelation对象", description = "业务人员账号与企微信息关联")
public class SystemUserBindQyUserRequest extends BaseRequestDTO {

    @ApiModelProperty("业务人员id 绑定系统中已同步企微下员工信息至业务人员账号使用")
    private String systemUserId;

    @ApiModelProperty("企微组织id 获取系统中已同步企微下员工信息使用")
    private String corpId;

    @ApiModelProperty("公司id 获取系统中已同步企微下员工信息使用")
    private String companyId;

    @ApiModelProperty("本次业务人员的绑定系统中已同步企微下员工信息")
    private List<SystemUserQyRelationRequest> systemUserQyRelationRequest;

    @ApiModelProperty("查询场景，0：单个企微下已同步企微人员信息；1：系统用户关联企微人员，")
    private String queryScene = "0";

    @ApiModelProperty("业务人员id 绑定系统中已同步企微下员工信息至业务人员账号使用")
    private List<String> systemUserIds;

}
