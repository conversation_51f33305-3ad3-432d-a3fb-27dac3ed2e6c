package cn.hxsy.request;

import cn.hxsy.base.request.BaseRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 系统公司-企微信息请求实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Getter
@Setter
@ApiModel(value = "CompanyQyRelationRequest对象", description = "系统公司-企微信息请求实体类")
public class CompanyQyRelationRequest extends BaseRequestDTO {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("公司id")
    private String companyId;

    @ApiModelProperty("企微账号类型 （0-客户企微，1-服务商模版，2-服务商通讯录应用）")
    private String corpType;

    @ApiModelProperty("企业ID")
    private String corpId;

    @ApiModelProperty("该企业对应自建小程序的凭证密钥")
    private String corpSecret;

    @ApiModelProperty("企业名称")
    private String corpName;

    @ApiModelProperty("该企业通讯录管理的凭证密钥")
    private String contactSecret;

    @ApiModelProperty("该企业客户管理的凭证密钥")
    private String customerSecret;

    @ApiModelProperty("第三方应用id或者代开发应用模板id。第三方应用以ww或wx开头应用id（对应于旧的以tj开头的套件id）；代开发应用以dk开头")
    private String suiteId;

    @ApiModelProperty("第三方应用secret 或者代开发应用模板secret")
    private String suiteSecret;

    @ApiModelProperty("企业微信后台推送的ticket")
    private String suiteTicket;

    @ApiModelProperty("企微回调token")
    private String token;

    @ApiModelProperty("企微回调加签key")
    private String encodingAESKey;

    /*
     * 公司与企微关联绑定使用
     */
    @ApiModelProperty("查询绑定关系场景 0-公司绑定企微信息 1-企微绑定公司信息")
    private String queryScene;

    @ApiModelProperty("绑定使用场景：0-公司批量绑定企微账号 1-企微账号批量绑定公司")
    private String bindScene;

    @ApiModelProperty("企业ID集合 批量绑定使用")
    private List<String> corpIds;

    @ApiModelProperty("公司ID集合 批量绑定使用")
    private List<String> companyIds;

    @ApiModelProperty("分页查询当前页数")
    private Integer pageNum = 1;

    @ApiModelProperty("分页查询每页大小")
    private Integer pageSize = 10;

}
