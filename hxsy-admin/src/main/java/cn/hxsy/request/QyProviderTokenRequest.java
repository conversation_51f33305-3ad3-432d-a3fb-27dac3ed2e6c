package cn.hxsy.request;

import cn.hxsy.base.constant.qyWechat.auth.QyWechatAuthType;
import cn.hxsy.base.request.BaseRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 企微服务商认证token请求实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Getter
@Setter
@ApiModel(value = "QyProviderTokenRequest", description = "企微服务商认证token请求实体类")
public class QyProviderTokenRequest extends BaseRequestDTO {

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("企微账号类型 （0-客户企微，1-服务商模版，2-服务商通讯录应用）")
    private String corpType;

    @ApiModelProperty("企业ID")
    private String corpId;

    @ApiModelProperty("该企业对应自建小程序的凭证密钥")
    private String corpSecret;

    @ApiModelProperty("该企业通讯录管理的凭证密钥")
    private String contactSecret;

    @ApiModelProperty("第三方应用id或者代开发应用模板id。第三方应用以ww或wx开头应用id（对应于旧的以tj开头的套件id）；代开发应用以dk开头")
    private String suiteId;

    @ApiModelProperty("第三方应用secret 或者代开发应用模板secret")
    private String suiteSecret;

    @ApiModelProperty("企业微信后台推送的ticket")
    private String suiteTicket;

    @ApiModelProperty("企微服务商认证相关场景使用常量")
    private QyWechatAuthType qyWechatAuthType;

    /*
     * 部门id
     */
    private String deptId;

    /*
     * 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填
     */
    private String cursor;

    /*
     * 分页，预期请求的数据量，取值范围 1 ~ 10000
     */
    private String limit;


    /*
     * open_userid列表，最多不超过1000个。必须是source_agentid对应的应用所获取
     */
    private List<String> openUsers;

    /*
     * open_userid列表，最多不超过1000个。必须是source_agentid对应的应用所获取
     */
    private List<String> users;

    /*
     * 企业授权的代开发自建应用或第三方应用的agentid
     */
    private String agentId;
}
