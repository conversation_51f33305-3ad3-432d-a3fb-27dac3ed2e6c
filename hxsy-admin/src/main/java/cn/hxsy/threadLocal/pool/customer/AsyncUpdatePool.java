package cn.hxsy.threadLocal.pool.customer;

import cn.hxsy.threadLocal.config.customer.RegistryThreadPoolConfig;
import cn.hxsy.cache.config.RedisJsonUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR> XiaQL
 * @ClassName : AsyncRegistryPool
 * @description : AsyncRegistryPool
 * @date: 2025-04-14 22:45
 */
@Component
@Data
@Slf4j
public class AsyncUpdatePool {

    // 引入线程池动态配置类
    @Autowired
    private final RegistryThreadPoolConfig config;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    // 构造方法注入线程池配置类
    public AsyncUpdatePool(RegistryThreadPoolConfig config) {
        this.config = config;
    }

    // 引入容器的线程池
    private ThreadPoolExecutor updateExecutor;

    // 自定义线程名
    ThreadFactory threadFactory = new ThreadFactory() {
        private final AtomicInteger counter = new AtomicInteger(1);
        @Override
        public Thread newThread(Runnable r) {
            return new Thread(r, "custom-update-pool-thread-" + counter.getAndIncrement());
        }
    };

    // 定义拒绝策略（记录日志并降级处理）
    RejectedExecutionHandler rejectionHandler = (Runnable task, ThreadPoolExecutor executor) -> {
        // 日志记录注册失败信息
        log.error("客户更新任务已满: " + task);
        // 降级策略（保存到 Redis 等待补偿）

    };

    // 通过构造方法初始化自定义线程池
    @PostConstruct
    public void init() {
        // 使用配置类中的参数初始化线程池
        updateExecutor = new ThreadPoolExecutor(
                config.getCorePoolSize(), // 核心线程数，满了去队列排队
                config.getMaxPoolSize(), // 线程池最大线程数，队列也满了开始添加临时线程
                config.getKeepAliveTime(), // 线程空闲时间
                TimeUnit.SECONDS, // 线程存活时间单位
                new LinkedBlockingQueue<>(config.getQueueCapacity()), // 指定有界队列大小防止oom
                threadFactory, // 自定义线程名
                rejectionHandler // 拒绝策略
        );
    }

    // 提交任务方法
    public void submitTask(Runnable task) {
        updateExecutor.submit(task);
    }

    // 销毁时关闭线程池（避免资源泄漏）
    @PreDestroy
    public void shutdown() {
        if (updateExecutor != null) {
            updateExecutor.shutdown();
        }
    }
}