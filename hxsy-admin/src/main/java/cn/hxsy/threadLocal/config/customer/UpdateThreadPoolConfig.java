package cn.hxsy.threadLocal.config.customer;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
* @description: 注册线程池配置类
* @author: <PERSON><PERSON><PERSON><PERSON>
* @date: 2025/4/15 0:22
*/
@Component
@ConfigurationProperties(prefix = "async.pool.customer.update")
@Data // 自动注入必须结合set方法才会生效
public class UpdateThreadPoolConfig {

    private int corePoolSize = 4;

    private int maxPoolSize = 8;

    private int queueCapacity = 1000;

    private long keepAliveTime = 60;
}