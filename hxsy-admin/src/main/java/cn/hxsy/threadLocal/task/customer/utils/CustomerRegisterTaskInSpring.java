package cn.hxsy.threadLocal.task.customer.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.request.CustomerRegisterRequest;
import cn.hxsy.base.enums.AccountTypeEnum;
import cn.hxsy.datasource.model.entity.Customer;
import cn.hxsy.datasource.model.entity.CustomerSalesRelation;
import cn.hxsy.datasource.model.entity.UserAuth;
import cn.hxsy.service.CustomerSalesRelationService;
import cn.hxsy.service.CustomerService;
import cn.hxsy.service.UserAuthService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;

/**
 * <AUTHOR> XiaQL
 * 不需要交给spring管理，因为执行任务的时候具体数据都不确定，需要通过new来构造
 * @ClassName : CustomerRegisterTask
 * @description : CustomerRegisterTask
 * @date: 2025-04-15 01:23
 */
@Data
@Slf4j
@Component
public class CustomerRegisterTaskInSpring{

    private CustomerService customerService;

    public void setCustomerService(@Lazy CustomerService customerService) {
        this.customerService = customerService;
    }

    @Autowired
    private CustomerSalesRelationService customerSalesRelationService;

    @Autowired
    private UserAuthService userAuthService;

    @Transactional(rollbackFor = Exception.class)
    public void customerRegisterProxy(Customer customer, CustomerRegisterRequest customerRegisterRequest) {
        // 执行具体的注册逻辑
        try {
            // 1.保存客户注册信息
            UserAuth userAuth = new UserAuth();
            userAuth.setUnionId(customer.getUnionId());
            userAuth.setUserType(AccountTypeEnum.CUSTER.getCode());
            userAuth.setId(customer.getId());
            userAuthService.getBaseMapper().insert(userAuth);
            // 3.客户通过小程序链接进来，需要绑定销售人员以及对应营期大课
            if (ObjectUtil.isNotEmpty(customerRegisterRequest.getSalesId())) {
                CustomerSalesRelation customerSalesRelation = new CustomerSalesRelation();
                customerSalesRelation.setColumnId(customerRegisterRequest.getColumnId());
                customerSalesRelation.setCompanyId(customerRegisterRequest.getCompanyId());
                customerSalesRelation.setCampPeriodId(customerRegisterRequest.getCampPeriodId());
                customerSalesRelation.setSalesId(customerRegisterRequest.getSalesId());
                customerSalesRelation.setSalesGroupId(customerRegisterRequest.getSalesGroupId());
                customerSalesRelation.setCustomerId(customer.getId());
                customerSalesRelationService.saveCustomerSalesRelation(customerSalesRelation);
            }
            // 2.保存客户基础信息
            customer.setUnionId(null);
            customerService.getBaseMapper().insert(customer);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("客户异步注册失败：{}", e.getMessage());
            // 记录到 Redis 等待补偿
        }
    }
}