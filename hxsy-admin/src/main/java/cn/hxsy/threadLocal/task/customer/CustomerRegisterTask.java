package cn.hxsy.threadLocal.task.customer;

import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.service.CustomerService;
import cn.hxsy.service.UserAuthService;
import cn.hxsy.api.user.model.request.CustomerRegisterRequest;
import cn.hxsy.base.enums.AccountTypeEnum;
import cn.hxsy.service.CustomerSalesRelationService;
import cn.hxsy.datasource.model.entity.Customer;
import cn.hxsy.datasource.model.entity.UserAuth;
import cn.hxsy.threadLocal.task.customer.utils.CustomerRegisterTaskInSpring;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.TransactionStatus;

import java.time.LocalDateTime;

/**
 * <AUTHOR> XiaQL
 * 不需要交给spring管理，因为执行任务的时候具体数据都不确定，需要通过new来构造
 * @ClassName : CustomerRegisterTask
 * @description : CustomerRegisterTask
 * @date: 2025-04-15 01:23
 */
@Data
@AllArgsConstructor
@Slf4j
public class CustomerRegisterTask implements Runnable {

    private Customer customer;

    private CustomerRegisterRequest customerRegisterRequest;
//
//    private CustomerService customerService;
//
//    private CustomerSalesRelationService customerSalesRelationService;
//
//    private UserAuthService userAuthService;

    private CustomerRegisterTaskInSpring customerRegisterTaskInSpring;

//    @Override
//    public void run() {
//        // 执行具体的注册逻辑
//        try {
//            // 1.保存客户注册信息
//            UserAuth userAuth = new UserAuth();
//            userAuth.setUnionId(customer.getUnionId());
//            userAuth.setUserType(AccountTypeEnum.CUSTER.getCode());
//            userAuth.setId(customer.getId());
//            userAuthService.getBaseMapper().insert(userAuth);
//            // 3.客户通过小程序链接进来，需要绑定销售人员以及对应营期大课
//            if (ObjectUtil.isNotEmpty(customerRegisterRequest.getSalesId())) {
//                customerSalesRelationService.saveCustomerSalesRelation(customerRegisterRequest.getSalesId(),
//                        customerRegisterRequest.getCampPeriodId(), customer.getId(), LocalDateTime.now());
//            }
//            // 2.保存客户基础信息
//            customer.setUnionId(null);
//            customerService.getBaseMapper().insert(customer);
//        } catch (Exception e) {
//            log.error("客户异步注册失败：{}", e.getMessage());
//            // 记录到 Redis 等待补偿
//        }
//    }

    @Override
    public void run() {
        // 执行具体的注册逻辑
        try {
            customerRegisterTaskInSpring.customerRegisterProxy(customer, customerRegisterRequest);
        } catch (Exception e) {
            log.error("客户异步注册失败：{}", e.getMessage());
            // 记录到 Redis 等待补偿
        }
    }
}