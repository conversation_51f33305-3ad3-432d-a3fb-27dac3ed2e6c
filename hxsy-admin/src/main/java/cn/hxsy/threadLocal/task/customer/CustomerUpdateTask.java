package cn.hxsy.threadLocal.task.customer;

import cn.hxsy.service.CustomerService;
import cn.hxsy.datasource.model.entity.Customer;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
* @description: CustomerUpdateTask
* 客户信息更新task
* @author: xiaQL
* @date: 2025/4/15 10:34
*/
@Data
@AllArgsConstructor
@Slf4j
public class CustomerUpdateTask implements Runnable {

    private Customer customer;

    private CustomerService customerService;

    @Override
    public void run() {
        try {
            // 1.更新客户基础信息，记得走分表键
            LambdaUpdateWrapper<Customer> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Customer::getId, customer.getId())
                    .eq(Customer::getUnionId, customer.getUnionId());
            // 2.置空分表规则与主键，防止意外更新
            customer.setUnionId(null);
            customer.setId(null);
            customerService.getBaseMapper().update(customer, updateWrapper);
        } catch (Exception e) {
            log.error("客户异步更新失败：{}", e.getMessage());
            // 记录到 Redis 等待补偿
        }
    }
}