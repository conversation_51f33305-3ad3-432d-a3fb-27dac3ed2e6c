package cn.hxsy.service;

import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.request.SystemUserBindQyUserRequest;
import cn.hxsy.base.request.SystemUserQyRelationRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.response.SystemUserQyRelationResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 业务人员账号与企微信息关联 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
public interface SystemUserQyRelationService extends IService<SystemUserQyRelation> {

    /**
     * @description: 同步一个企微下员工账号信息
     * 0、校验下是否有该公司的管理权限
     * 1、查询这个企微在当前系统已经同步的用户信息，获取到关联公司，
     * 1.1、如果查询失败，直接返回失败
     * 1.2、查询成功，判断当前系统是否已经有给该企微同步的员工信息（有则在第三步需要进行比对，没有则可以直接全量新增）
     * 2、调用企微侧查询员工账号接口
     * 3、比对当前系统存储与企微返回的员工信息，如果存在则更新，不存在则新增
     * 3.1、获取当前系统已经存储的企微员工，获取到最后一个员工的游标
     * 3.2、据此游标到企微官方获取传入企微组织下后续的员工信息
     * 权限校验：
     *
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<Object> saveQyCompanyUser(SystemUserBindQyUserRequest request);

    /**
    * @description: 给系统用户绑定企微下员工账号信息（可选择不同企微下多个员工账号）
     * 一次给一个系统用户关联
     * 校验：
     * 1、校验是否有对应选择企微关联的公司的管理权限
     * 1.1这个公司id咋传呢，难不成我还专门收集一下传入的企微id然后去查一下对应公司id集合？
     * 2、校验传入的企微用户是否已经被其他系统用户绑定，有则不允许继续绑定
     * 2.1首先前端查的时候就要控可选的企微用户，以systemUserId有值就表示已经绑定了，不可选
     * 2.2后端需要先根据组织id与用户id查出这条企微员工是否有systemUserId的绑定，防止接口对外暴露直接传入
    * @author: xiaQL
    * @date: 2025/6/22 15:56
    */
    Result<Object> bindQyUserToSystemUser(SystemUserBindQyUserRequest request);

    /**
     * @description: 用处：
     * 1、查询当前系统已经同步传入企微组织下的员工相关信息
     * 1.1、若账号已被关联（systemUserId字段不为空），需要查询出关联的系统用户信息，重新封装
     * 1.2、未被关联（systemUserId字段为空），则直接返回查询出的原数据
     * 2、查询某个系统账号在当前选择企微组织下与哪些企微用户账号有关联
     * 校验：
     * 1、是否有该企微关联公司的管理权限
     * 2、该企微是否归属当前公司
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<Object> querySystemQyUser(SystemUserBindQyUserRequest request);

    /**
     * @description: 用处：内部调用
     * 自然也不需要权限校验
     * 0、查询当前系统已经同步传入企微组织下的员工相关信息
     * 1、查询一批系统用户中已关联的企微账号信息-用户管理中分页使用
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<List<SystemUserQyRelation>> querySystemQyUserInner(SystemUserBindQyUserRequest request);

    /**
     * description : 生成联系我二维码(个人)
     * @title: generateQyContactQrCode
     * @param: request
     * <AUTHOR>
     * @date 2025/7/15 20:29
     * @return boolean
     */
    boolean generateQyContactQrCode(SystemUserQyRelation systemUserQyRelation);

    /**
     * description : 分页查询企微员工信息列表
     * @title: queryQyUserPage
     * @param: pageNum
     * @param: pageSize
     * @param: qyUserRequest
     * <AUTHOR>
     * @date 2025/7/20 16:28
     * @return Page<SystemUserQyRelation>
     */
    Page<SystemUserQyRelation> queryQyUserPage(Integer pageNum, Integer pageSize, QyUserRequest qyUserRequest);

}
