package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CustomerTagRelation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 客户标签关联服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface CustomerTagRelationService extends IService<CustomerTagRelation> {
    
    /**
     * 新增客户标签关联信息
     *
     * @param customerTagRelation 客户标签关联信息对象
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean add(CustomerTagRelation customerTagRelation);

    /**
     * 根据ID删除客户标签关联信息
     *
     * @param id 客户标签关联ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean deleteById(Long id);

    /**
     * 更新客户标签关联信息
     *
     * @param customerTagRelation 客户标签关联信息对象，必须包含ID
     * @return 是否更新成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean update(CustomerTagRelation customerTagRelation);

    /**
     * 根据客户ID查询标签关联列表
     *
     * @param customerId 客户ID
     * @return 标签关联列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    List<CustomerTagRelation> getByCustomerId(Long customerId);
}