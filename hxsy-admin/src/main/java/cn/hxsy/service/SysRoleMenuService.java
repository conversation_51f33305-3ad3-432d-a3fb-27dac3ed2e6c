package cn.hxsy.service;

import cn.hxsy.api.system.request.SysRoleMenuRequest;
import cn.hxsy.api.system.response.SysMenuResponse;
import cn.hxsy.api.system.response.SysRoleMenuResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SysRoleMenu;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 角色和菜单关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:06:31
 */
public interface SysRoleMenuService extends IService<SysRoleMenu> {

    /**
    * @description: 根据角色id集合查询关联菜单合集
    * @author: xiaQL
    * @date: 2025/5/2 18:44
    */
    List<SysMenuResponse> getMenuByRoleIds(List<Integer> roleIds);

    /**
     * @description: 根据角色id查询关联菜单合集
     * @author: xiaQL
     * @date: 2025/5/2 18:44
     */
    List<SysMenuResponse> getMenuByRoleId(Integer roleId);


    /**
     * @description: 查询角色具有菜单权限
     * @author: xiaQL
     * @date: 2025/5/7 21:59
     */
    SysRoleMenuResponse getRoleHasMenu(SysRoleMenuRequest request);

    /**
     * @description: 查询角色具有菜单权限
     * @author: xiaQL
     * @date: 2025/5/7 21:59
     */
    Result<Object> saveRoleMenu(SysRoleMenuRequest request);

}
