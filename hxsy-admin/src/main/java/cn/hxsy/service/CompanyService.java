package cn.hxsy.service;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.base.response.CompanyTreeResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.dto.CompanyWithWxCodesDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface CompanyService extends IService<CompanyPO> {
    CompanyTreeResponse getCompanyGroup(Integer id);

    Result<Object> updateSellUrl(Integer id, String url);

    Boolean updateByIds(UpdateStatusRequest updateStatusRequest);

    /**
     * @description: 栏目下公司分页查询
     * 栏目、状态、公司名作为筛选条件
     * @author: xiaQL
     * @date: 2025/5/17 14:26
     */
    IPage<CompanyWithWxCodesDTO> queryCompanyPage(Integer current, Integer size,
                                 OrganizationQueryRequest request);

    /**
     * @description: 根据栏目id查询其下下所有公司id
     * 用于内部角色权限筛选
     * @author: xiaQL
     * @date: 2025/5/17 14:26
     */
    List<Integer> queryCompanyByColumnId(List<String> columnIds);
}