package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.TagGroup;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 标签组服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface TagGroupService extends IService<TagGroup> {
    
    /**
     * 新增标签组信息
     *
     * @param tagGroup 标签组信息对象
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean add(TagGroup tagGroup);

    /**
     * 根据ID删除标签组信息
     *
     * @param id 标签组ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean deleteById(Long id);

    /**
     * 更新标签组信息
     *
     * @param tagGroup 标签组信息对象，必须包含ID
     * @return 是否更新成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean update(TagGroup tagGroup);

    /**
     * 根据父级ID查询子标签组列表
     *
     * @param parentId 父级标签组ID
     * @return 子标签组列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    List<TagGroup> getByParentId(Long parentId);

    /**
     * 根据层级查询标签组列表
     *
     * @param level 层级（1-一级标签组，2-二级标签组）
     * @return 标签组列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    List<TagGroup> getByLevel(Integer level);

}