package cn.hxsy.service;

import cn.hxsy.api.user.model.response.TagGroupTreeResponse;
import cn.hxsy.datasource.model.entity.Tag;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 标签服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface TagService extends IService<Tag> {
    
    /**
     * 新增标签信息
     *
     * @param tag 标签信息对象
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean add(Tag tag);

    /**
     * 根据ID删除标签信息
     *
     * @param id 标签ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean deleteById(Long id);

    /**
     * 更新标签信息
     *
     * @param tag 标签信息对象，必须包含ID
     * @return 是否更新成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean update(Tag tag);

    /**
     * 根据标签组ID查询标签列表
     *
     * @param groupId 标签组ID
     * @return 标签列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    List<Tag> getByGroupId(Long groupId);

    /**
     * 查询所有标签组及其标签（两级结构）
     *
     * @return 标签组及其标签列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    List<TagGroupTreeResponse> getAllTagGroupsWithTags();

    // 根据客户ID查询标签列表
    List<Tag> getTagsByCustomerId(Long customerId);
}