package cn.hxsy.service;

import com.alibaba.fastjson.JSONObject;

public interface WecomCallbackService {


    /**
     * description : 企业微信回调统一入口（GET）（应用服务商）
     * @title: handleGetCallbackProvider
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: echoStr
     * <AUTHOR>
     * @date 2025/6/28 11:07
     * @return String
     */
    String handleGetCallbackProvider(String corpId, String corpType, String msgSignature, String timestamp, String nonce, String echoStr);

    /**
     * description : 企业微信回调统一入口（GET）
     * @title: handleGetCallback
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: echoStr
     * <AUTHOR>
     * @date 2025/6/28 11:07
     * @return String
     */
    String handleGetCallback(String corpId, String msgSignature, String timestamp, String nonce, String echoStr);

    /**
     * description : 企业微信回调统一入口（POST）（应用服务商）
     * @title: handleCallback
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: xml
     * <AUTHOR>
     * @date 2025/6/26 10:54
     * @return String
     */
    String handleCallbackProvider(String corpId, String corpType, String msgSignature, String timestamp, String nonce, String xml);

    /**
     * description : 企业微信回调统一入口（POST）
     * @title: handleCallback
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: xml
     * <AUTHOR>
     * @date 2025/6/26 10:54
     * @return String
     */
    String handleCallback(String corpId, String msgSignature, String timestamp, String nonce, String xml);

    /** 测试接口 */
    String handleUnionidToExternalUserid(String corpId, String unionid, String openid, String subject_type);

    JSONObject licenseTest(String method, JSONObject jsonObject);
}