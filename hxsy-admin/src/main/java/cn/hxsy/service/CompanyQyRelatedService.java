package cn.hxsy.service;

import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CompanyQyRelated;
import cn.hxsy.request.CompanyQyRelationRequest;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 公司与企微账号关联列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26 15:23:52
 */
public interface CompanyQyRelatedService extends IService<CompanyQyRelated> {

	/**
	 * @description: 公司关联企微账号信息
	 * 所需数据：
	 * 1、单个公司批量绑定企微
	 * 公司id
	 * 企微账号id集合
	 * 2、单个企微批量被公司绑定
	 * 企微账号id
	 * 公司id集合
	 * 权限校验：
	 * 1、是否为超管、普管
	 * @author: xiaQL
	 * @date: 2025/6/18 0:44
	 */
	Result<Object> saveCompanyQyBind(CompanyQyRelationRequest request);

	/**
	 * @description: 查询公司与企微关联关系
	 * 1、查询一个公司下所有已关联企微信息
	 * 2、查询一个企微账号下所有已关联公司信息
	 * 鉴权：
	 * 1、首先判断当前人员是否具有该公司的管理权限
	 * @author: xiaQL
	 * @date: 2025/6/17 21:56
	 */
	Result<Object> queryCompanyBindQy(CompanyQyRelationRequest request);

}
