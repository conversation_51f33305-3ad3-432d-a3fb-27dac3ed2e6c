package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CustomerSalesRelation;
import cn.hxsy.api.user.model.request.CustomerAssignRequest;
import cn.hxsy.dto.CSRelationByColumDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户销售关联服务接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface CustomerSalesRelationService extends IService<CustomerSalesRelation> {

    /**
     * 手动分配客户给销售人员
     *
     * @param request 分配请求
     * @return 是否分配成功
     */
    boolean assignCustomer(CustomerAssignRequest request);

    void updateByCustomerId(CustomerSalesRelation customerSalesRelation);

    /**
     * description : 客户通过分享链接进入，关联对应业务人员、营期大课信息
     * @title: saveCustomerSalesRelation
     * <AUTHOR>
     * @date 2025/4/4 18:52
     * @return void
     */
    void saveCustomerSalesRelation(CustomerSalesRelation relation);

    /**
     * description : 根据客户ID查询客户销售关联信息
     * @title: listByCustomerId
     * @param: customerId
     * <AUTHOR>
     * @date 2025/5/18 14:16
     * @return List<CSRelationByColumDTO>
     */
    List<CSRelationByColumDTO> listByCustomerId(Long customerId);
}