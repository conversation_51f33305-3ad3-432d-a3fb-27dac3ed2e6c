package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.HeadquartersPO;
import cn.hxsy.base.response.HeadquartersTreeResponse;
import com.baomidou.mybatisplus.extension.service.IService;

public interface HeadquartersService extends IService<HeadquartersPO> {
    
    /**
     * 获取总部树形结构
     *
     * @param headquartersId 总部ID
     * @return 总部树形结构
     */
    HeadquartersTreeResponse getHeadquartersTree(Integer headquartersId, Integer level);
} 