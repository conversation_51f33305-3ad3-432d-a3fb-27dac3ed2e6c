package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CustomerTags;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 客户标签服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface CustomerTagsService extends IService<CustomerTags> {

    /**
     * 根据客户ID查询标签列表
     *
     * @param customerId 客户ID
     * @return 标签列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    List<CustomerTags> getByCustomerId(Long customerId);

    boolean saveTags(CustomerTags customerTags);

    /**
     * 分页查询客户标签信息
     *
     * @param current 当前页码，默认1
     * @param size 每页大小，默认10
     * @param customerId 客户ID，可选
     * @param campPeriodId 营期ID，可选
     * @param tagsName 标签名称，可选
     * @param status 使用状态，可选
     * @return 分页结果
     * <AUTHOR>
     * @date 2024-04-01
     */
    Page<CustomerTags> page(Integer current, Integer size, Long customerId, Long campPeriodId, String tagsName, Integer status);

    /**
     * 保存手动标签
     *
     * @param customerTags 客户标签信息
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-05-25
     */
    boolean saveManualTag(CustomerTags customerTags);
}