package cn.hxsy.service;

import cn.hxsy.api.user.model.request.CustomerBehaviorPageRequest;
import cn.hxsy.base.enums.BehaviorTypeEnum;
import cn.hxsy.datasource.model.entity.CustomerBehavior;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 客户行为轨迹服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface CustomerBehaviorService extends IService<CustomerBehavior> {

    /**
     * 根据客户ID查询行为轨迹列表
     *
     * @param customerId 客户ID
     * @return 行为轨迹列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    List<CustomerBehavior> getByCustomerId(Long customerId);


    /**
     * 分页查询客户行为轨迹信息
     *
     * @param pageNum 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param customerBehaviorPageRequest
     * @return 分页结果
     * <AUTHOR>
     * @date 2024-04-01
     */
    Page<CustomerBehavior> page(Integer pageNum, Integer pageSize, CustomerBehaviorPageRequest customerBehaviorPageRequest);

    /**
     * description : 保存训练营营期报名行为
     * @title: saveCampEnrollment
     * @param: companyId
     * @param: campPeriodId
     * <AUTHOR>
     * @date 2025/4/7 13:50
     * @return boolean
     */
    boolean saveCampEnrollment(Long customerId, Long companyId, Long campPeriodId, Long salesId);

    /**
     * description : 保存训练营视频课学习行为
     * @title: saveCampVideoCourseLearning
     * @param: companyId
     * @param: campPeriodId
     * @param: courseId
     * <AUTHOR>
     * @date 2025/4/7 13:50
     * @return boolean
     */
    boolean saveCampVideoCourseLearning(Long customerId, Long companyId, Long campPeriodId, Long courseId);

    /**
     * description : 答题
     * @title: saveCampQuiz
     * @param: customerId
     * @param: companyId
     * @param: campPeriodId
     * @param: courseId
     * <AUTHOR>
     * @date 2025/5/9 2:00
     * @return boolean
     */
    boolean saveCampQuiz(Long customerId, Long companyId, Long campPeriodId, Long courseId);

    /**
     * description : 领取红包
     * @title: saveReceiveRedPacket
     * @param: customerId
     * @param: companyId
     * @param: campPeriodId
     * @param: courseId
     * @param: amount
     * @param: type
     * <AUTHOR>
     * @date 2025/5/9 2:00
     * @return boolean
     */
    boolean saveReceiveRedPacket(Long customerId, Long companyId, Long campPeriodId, Long courseId, String amount, Integer type);

    /**
     * 保存添加企微行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param corpName 企业名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @return 是否保存成功
     */
    boolean saveAddWechatEnterprise(Long customerId, Long companyId, String corpId, String corpName, String employeeName, String employeeWeworkName);

    /**
     * 保存删除企微行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param corpName 企业名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @return 是否保存成功
     */
    boolean saveDeleteWechatEnterprise(Long customerId, Long companyId, String corpId, String corpName, String employeeName, String employeeWeworkName);

    /**
     * 保存加入群组行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param groupId 群组ID
     * @param groupName 群组名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @param joinScene 入群方式 0-由成员邀请入群, 3-通过扫描群二维码入群
     * @return 是否保存成功
     */
    boolean saveJoinGroupChat(Long customerId, Long companyId, String corpId, String groupId, String groupName, String employeeName, String employeeWeworkName, Integer joinScene);

    /**
     * 保存退出群组行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param groupId 群组ID
     * @param groupName 群组名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @param quitScene 退群方式 0-自己退群, 1-群主/群管理员移出
     * @return 是否保存成功
     */
    boolean saveExitGroupChat(Long customerId, Long companyId, String corpId, String groupId, String groupName, String employeeName, String employeeWeworkName, Integer quitScene);
}