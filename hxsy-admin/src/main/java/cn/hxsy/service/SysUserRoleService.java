package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.SysUserRole;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户和角色关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:06:05
 */
public interface SysUserRoleService extends IService<SysUserRole> {

    /**
    * @description: 查询一个用户具有的角色
    * @author: xiaQL
    * @date: 2025/5/2 17:14
    */
    List<SysUserRole> getUserRoleByUserId(Long userId);
}
