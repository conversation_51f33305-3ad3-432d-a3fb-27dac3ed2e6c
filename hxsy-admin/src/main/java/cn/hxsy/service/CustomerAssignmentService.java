package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CustomerAssignment;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 客户分配记录服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface CustomerAssignmentService extends IService<CustomerAssignment> {

    /**
     * 分页查询客户分配记录
     *
     * @param pageNum 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param customerId 客户ID，可选
     * @return 分页结果
     * <AUTHOR>
     * @date 2024-04-01
     */
    Page<CustomerAssignment> page(Integer pageNum, Integer pageSize, Long customerId);

    /**
     * description : 保存客户分配操作记录
     * @title: saveCustomerAssignment
     * @param: customerId
     * @param: originalEmployeeId
     * @param: newEmployeeId
     * <AUTHOR>
     * @date 2025/5/17 22:25
     * @return boolean
     */
    boolean saveCustomerAssignment(Long customerId, Long originalColumnId, Long originalCompanyId, Long originalEmployeeId, Long newEmployeeId);
}