package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.WechatUserOpenid;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 微信用户openid关联服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface WechatUserOpenidService extends IService<WechatUserOpenid> {
    
    /**
     * 新增微信用户openid关联信息
     *
     * @param wechatUserOpenid 微信用户openid关联信息对象
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean add(WechatUserOpenid wechatUserOpenid);

    /**
     * 根据ID删除微信用户openid关联信息
     *
     * @param id 微信用户openid关联ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean deleteById(Long id);

    /**
     * 更新微信用户openid关联信息
     *
     * @param wechatUserOpenid 微信用户openid关联信息对象，必须包含ID
     * @return 是否更新成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    boolean update(WechatUserOpenid wechatUserOpenid);

    /**
     * 根据appid和openid查询微信用户openid关联信息
     *
     * @param appId 小程序appid
     * @param openid 微信openid
     * @return 微信用户openid关联信息，如果不存在返回null
     * <AUTHOR>
     * @date 2024-04-01
     */
    WechatUserOpenid getByAppIdAndOpenid(String appId, String openid);

}