package cn.hxsy.service;

import cn.hxsy.api.system.request.SysUserSelectPermissionRequest;
import cn.hxsy.api.system.response.SysUserSelectPermissionResponse;
import cn.hxsy.datasource.model.entity.SysUserSelectPermission;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 系统用户可见查询权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24 12:04:54
 */
public interface SysUserSelectPermissionService extends IService<SysUserSelectPermission> {

    /**
    * @description: 根据用户id获取用户可查询的权限
     * 栏目、公司、部门集合
    * @author: xiaQL
    * @date: 2025/5/24 16:26
    */
    SysUserSelectPermissionResponse getSysUserSelectPermission(SysUserSelectPermissionRequest request);

    /**
     * @description: 新增或更新系统用户可查询的权限
     * 栏目、公司、部门集合
     * @author: xiaQL
     * @date: 2025/5/24 16:26
     */
    Boolean saveOrUpdateSystemSelectPermission(SysUserSelectPermissionRequest request);

}
