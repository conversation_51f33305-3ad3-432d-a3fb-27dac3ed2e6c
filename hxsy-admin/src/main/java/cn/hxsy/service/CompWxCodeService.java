package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CompWxCode;
import cn.hxsy.datasource.model.entity.CompanyPO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 公司邀请码信息服务接口
 */
public interface CompWxCodeService extends IService<CompWxCode> {
    /**
     * 根据公司ID获取关联的小程序信息列表
     *
     * @param companyId 公司ID
     * @return 小程序信息列表
     */
    List<CompWxCode> getMiniProgramsByCompanyId(Long companyId);

    /**
     * 根据小程序ID获取关联的公司信息列表
     *
     * @param miniProgramId 小程序ID
     * @return 公司信息列表
     */
    List<CompanyPO> getCompanyIdsByMiniProgramId(String miniProgramId);

    /**
     * 为公司分配小程序权限
     *
     * @param companyId 公司ID
     * @param miniProgramIds 小程序ID列表
     * @return 是否成功
     */
    boolean assignMiniProgramsToCompany(Long companyId, List<String> miniProgramIds);

    /**
     * 移除公司的小程序权限
     *
     * @param companyId 公司ID
     * @param miniProgramIds 小程序ID列表
     * @return 是否成功
     */
    boolean removeMiniProgramsFromCompany(Long companyId, List<String> miniProgramIds);

    /**
     * 为小程序设置可访问的公司列表
     *
     * @param miniProgramId 小程序ID
     * @param miniProgramName 小程序名称
     * @param companyIds 公司ID列表
     * @return 是否操作成功
     */
    boolean assignCompaniesToMiniProgram(String miniProgramId, String miniProgramName, List<Long> companyIds);

}
