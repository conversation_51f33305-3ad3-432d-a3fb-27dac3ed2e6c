package cn.hxsy.service;

import cn.hxsy.api.user.model.response.CampCourseVideoResponse;
import cn.hxsy.datasource.model.entity.CustomerWecomBinding;
import cn.hxsy.api.qy.request.contact.CustomerTransferRequest;
import cn.hxsy.api.qy.response.contact.CustomerMarkTagResponse;
import cn.hxsy.api.qy.response.contact.CustomerTransferResponse;
import cn.hxsy.request.CustomerTransferSysRequest;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 客户与企微external_userid及销售userid绑定关系服务接口
 */
public interface CustomerWecomBindingService extends IService<CustomerWecomBinding> {

    /**
     * description : 根据企微Callback保存企微关联信息
     * @title: getInfoByPendingId
     * @param: pendingId
     * <AUTHOR>
     * @date 2025/7/3 23:59
     * @return boolean
     */
    boolean saveInfoByCallback(CustomerWecomBinding customerWecomBinding);

    /**
     * description : 逻辑删除企微关联信息
     * @title: deleteCustomerWecomBinding
     * @param: customerWecomBinding
     * <AUTHOR>
     * @date 2025/7/4 21:58
     * @return boolean
     */
    boolean deleteCustomerWecomBinding(CustomerWecomBinding customerWecomBinding);

    /**
     * 分配客户
     * 根据员工状态（在职/离职）将客户分配给在职员工
     *
     * @param request 客户转移请求
     * @param accessToken 企业微信访问令牌
     * @param isResigned 是否是离职员工的客户转移
     * @return 客户转移响应
     */
    CustomerTransferResponse transferCustomer(CustomerTransferRequest request, String accessToken, boolean isResigned);

    /**
     * 1、获取这批客户之前关联的业务人员在职状态
     * 2、获取这次需要关联的业务人员信息，并校验是否是在职业务人员
     * 3、获取对应企微账号所属企微，对应服务商代开发应用的access_token
     * 4、根据这批客户之前关联的业务人员在职状态，判断调用在职或离职接口
     * @param request 客户转移请求
     * @return 客户转移响应
     */
    void transferCustomer(CustomerTransferSysRequest request);

   /**
     * description : 根据unionid和openid保存关联信息
     * @title: saveInfoByUnionIdAndOpenId
     * @param: unionid
     * @param: openid
     * @param: companyId
     * @param: customerId
     * @param: salesId
     * <AUTHOR>
     * @date 2025/7/15 23:04
     * @return CampCourseVideoResponse
     */
    CampCourseVideoResponse saveInfoByUnionIdAndOpenId(String unionid, String openid, Long companyId, Long customerId, Long salesId);

    boolean deleteByCustomerId(Long customerId);

}