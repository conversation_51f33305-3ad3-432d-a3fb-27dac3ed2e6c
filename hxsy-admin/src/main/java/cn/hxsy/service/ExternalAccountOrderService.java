package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.ExternalAccountOrder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ExternalAccountOrderService extends IService<ExternalAccountOrder> {

    /**
     * description : 创建新订单
     * @title: create
     * @param: order
     * <AUTHOR>
     * @date 2025/7/17 21:01
     * @return boolean
     */
    boolean createOrder(ExternalAccountOrder order);

    /**
     * description :  创建续期订单（创建续期任务 + 提交续期任务）（目前只支持1000）
     * @title: createRenewalOrder
     * @param: order
     * <AUTHOR>
     * @date 2025/7/18 14:48
     * @return boolean
     */
    boolean createRenewalOrder(ExternalAccountOrder order);

    /**
     * description : 取消订单(取消接口许可购买和续费订单，只可取消未支付且未失效的订单。)
     * @title: cancelOrder
     * @param: order
     * <AUTHOR>
     * @date 2025/7/17 21:48
     * @return boolean
     */
    boolean cancelOrder(ExternalAccountOrder order);

    /**
     * description : 分页查询互通账号订单列表
     * @title: queryPage
     * @param: pageNum
     * @param: pageSize
     * @param: externalAccountOrder
     * <AUTHOR>
     * @date 2025/7/18 16:23
     * @return Page<ExternalAccountOrder>
     */
    Page<ExternalAccountOrder> queryPage(Integer pageNum, Integer pageSize, ExternalAccountOrder externalAccountOrder);

    /**
     * description : 手动获取订单中激活码
     * @title: getActivationCode
     * @param: orderId
     * <AUTHOR>
     * @date 2025/7/20 21:16
     * @return boolean
     */
    boolean getActivationCode(String orderId);
}