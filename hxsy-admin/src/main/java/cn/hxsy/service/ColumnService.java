package cn.hxsy.service;

import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.ColumnPO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ColumnService extends IService<ColumnPO> {
    Boolean updateByIds(UpdateStatusRequest updateStatusRequest);

    /**
    * @description: 根据栏目id查询
    * @author: xiaQL
    * @date: 2025/5/14 21:57
    */
    public Page<ColumnPO> queryPageById(Integer current, Integer size,
                                       String name, Long headquartersId);
} 