package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.constant.user.UserSelectType;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.datasource.model.entity.CompWxCode;
import cn.hxsy.dto.CompanyWithWxCodesDTO;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.base.response.CompanyTreeResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.base.response.SalesGroupResponse;
import cn.hxsy.dao.CompanyMapper;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.SalesGroupPO;
import cn.hxsy.service.CompWxCodeService;
import cn.hxsy.service.CompanyService;
import cn.hxsy.service.SalesGroupService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Service
public class CompanyServiceImpl extends ServiceImpl<CompanyMapper, CompanyPO> implements CompanyService {

    @Autowired
    private SalesGroupService salesGroupService;

    @Autowired
    private CompWxCodeService compWxCodeService;

    @Autowired
    private UserSelectUtil userSelectUtil;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Override
    public CompanyTreeResponse getCompanyGroup(Integer id) {
        // 获取公司列表（有效状态）
        LambdaQueryWrapper<CompanyPO> companyWrapper = new LambdaQueryWrapper<>();
        companyWrapper.eq(CompanyPO::getId, id)
                .eq(CompanyPO::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .orderByDesc(CompanyPO::getCreatedAt);
        CompanyPO companyPO = baseMapper.selectOne(companyWrapper);

        // 构建公司树形结构
        CompanyTreeResponse companyTreeResponse = new CompanyTreeResponse();
        companyTreeResponse.setId(companyPO.getId());
        companyTreeResponse.setName(companyPO.getCompanyName());

        // 获取销售组列表（有效状态）
        LambdaQueryWrapper<SalesGroupPO> salesGroupWrapper = new LambdaQueryWrapper<>();
        salesGroupWrapper.eq(SalesGroupPO::getCompanyId, companyPO.getId())
                .eq(SalesGroupPO::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .orderByDesc(SalesGroupPO::getCreatedAt);
        List<SalesGroupPO> salesGroups = salesGroupService.list(salesGroupWrapper);

        // 构建销售组列表
        List<SalesGroupResponse> salesGroupResponses = salesGroups.stream().map(salesGroup -> {
            SalesGroupResponse salesGroupResponse = new SalesGroupResponse();
            salesGroupResponse.setId(salesGroup.getId());
            salesGroupResponse.setName(salesGroup.getSalesGroupName());
            return salesGroupResponse;
        }).collect(Collectors.toList());

        companyTreeResponse.setSalesGroups(salesGroupResponses);
        return companyTreeResponse;
    }

    /**
     * @param id
     * @param url
     * @return {@link Result }<{@link Object }>
     * <AUTHOR>
     * @date 2025/04/19
     */

    @Override
    public Result<Object> updateSellUrl(Integer id, String url) {
        LambdaUpdateWrapper<CompanyPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CompanyPO::getId, id)
                .set(CompanyPO::getSellUrl, url);
        int update = getBaseMapper().update(null, updateWrapper);
        if (update>0) {
            return Result.ok();
        }
        return Result.error("保存链接失败");
    }

    @Override
    public Boolean updateByIds(UpdateStatusRequest updateStatusRequest) {
        if (updateStatusRequest == null || updateStatusRequest.getIds() == null || updateStatusRequest.getStatus() == null) {
            return false;
        }

        LambdaUpdateWrapper<CompanyPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CompanyPO::getId, updateStatusRequest.getIds())
                .set(CompanyPO::getStatus, updateStatusRequest.getStatus());
        int update = getBaseMapper().update(null, updateWrapper);
        if (update>0) {
            return true;
        }
        return false;
    }

    @Override
    public IPage<CompanyWithWxCodesDTO> queryCompanyPage(Integer current, Integer size, OrganizationQueryRequest request) {
        // 首先获取用户缓存信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 获取用户角色对应查询权限
        SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserSelfInfo, UserSelectType.company, request);
        // 初始化查询条件
        Integer columnId = null;
        List<String> perColumnId = null;
        Integer companyId = null;
        List<String> perCompanyId = null;
        if(selectPermission != null){
            // 1、对于栏目、公司筛选，需要先看是否返回了可见权限范围，如果带了就走范围查询，没带就是精确查询
            if(CollectionUtils.isNotEmpty(selectPermission.getPerColumnId())){
                perColumnId = selectPermission.getPerColumnId();
            } else {
                // 没有可见权限范围，默认走工具类返回的栏目；超管即全部，其他即自身
                columnId = selectPermission.getColumnId();
            }
            // 2、可见公司范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerCompanyId())){
                perCompanyId = selectPermission.getPerCompanyId();
            } else {
                // 没有可见权限范围，默认走工具类返回的公司；超管即全部，其他即自身
                companyId = selectPermission.getCompanyId();
            }
        }
        Page<CompanyPO> page = new Page<>(current, size);
        LambdaQueryWrapper<CompanyPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtils.isNotEmpty(request.getStatus()), CompanyPO::getStatus, request.getStatus())
                .eq(companyId != null, CompanyPO::getId, companyId)
                .eq(columnId != null, CompanyPO::getColumnId, columnId)
                .in(CollectionUtils.isNotEmpty(perCompanyId), CompanyPO::getId, perCompanyId)
                .in(CollectionUtils.isNotEmpty(perColumnId), CompanyPO::getColumnId, perColumnId)
                .like(StringUtils.isNotEmpty(request.getName()), CompanyPO::getCompanyName, request.getName());
        wrapper.orderByDesc(CompanyPO::getCreatedAt);
        Page<CompanyPO> companyPage = baseMapper.selectPage(page, wrapper);
        // 转换结果并查询微信邀请码
        List<CompanyWithWxCodesDTO> records = companyPage.getRecords().stream().map(company -> {
            CompanyWithWxCodesDTO dto = CompanyWithWxCodesDTO.fromCompanyPO(company);
            // 查询该公司的微信邀请码列表
            LambdaQueryWrapper<CompWxCode> wxCodeWrapper = new LambdaQueryWrapper<>();
            wxCodeWrapper.eq(CompWxCode::getCompanyId, company.getId());
            List<CompWxCode> wxCodes = compWxCodeService.list(wxCodeWrapper);
            // 如果没有微信邀请码，设置空列表
            dto.setCompWxList(wxCodes != null ? wxCodes : Collections.emptyList());
            return dto;
        }).collect(Collectors.toList());

        // 构建返回结果
        Page<CompanyWithWxCodesDTO> resultPage = new Page<>(
            companyPage.getCurrent(),
            companyPage.getSize(),
            companyPage.getTotal()
        );
        resultPage.setRecords(records);
        return resultPage;
    }

    @Override
    public List<Integer> queryCompanyByColumnId(List<String> columnIds) {
        List<CompanyPO> companyPOS = this.lambdaQuery().in(CompanyPO::getColumnId, columnIds).list();
        if (CollectionUtils.isEmpty(companyPOS)) {
            return null;
        }else {
            return companyPOS.stream().map(companypo -> companypo.getId().intValue()).collect(Collectors.toList());
        }
    }
}
