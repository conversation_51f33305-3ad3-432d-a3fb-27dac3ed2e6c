package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.dao.HeadquartersMapper;
import cn.hxsy.datasource.model.entity.HeadquartersPO;
import cn.hxsy.datasource.model.entity.ColumnPO;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.SalesGroupPO;
import cn.hxsy.base.response.HeadquartersTreeResponse;
import cn.hxsy.base.response.ColumnTreeResponse;
import cn.hxsy.base.response.CompanyTreeResponse;
import cn.hxsy.base.response.SalesGroupResponse;
import cn.hxsy.service.HeadquartersService;
import cn.hxsy.service.ColumnService;
import cn.hxsy.service.CompanyService;
import cn.hxsy.service.SalesGroupService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static cn.hxsy.base.constant.user.UserSelectType.organization;

@Service
public class HeadquartersServiceImpl extends ServiceImpl<HeadquartersMapper, HeadquartersPO> implements HeadquartersService {

    @Autowired
    private ColumnService columnService;
    
    @Autowired
    private CompanyService companyService;
    
    @Autowired
    private SalesGroupService salesGroupService;

    @Autowired
    private UserSelectUtil userSelectUtil;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Override
    public HeadquartersTreeResponse getHeadquartersTree(Integer headquartersId, Integer level) {
        // 1、首先获取用户缓存信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 2、获取用户角色对应查询权限
        OrganizationQueryRequest request = new OrganizationQueryRequest();
        request.setHeadquartersId(headquartersId);
        SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserSelfInfo, organization, request);
        Integer columnId;
        List<String> perColumnId;
        Integer companyId;
        List<String> perCompanyId;
        Integer salesGroupId;
        List<String> perSalesGroupId;
        if(selectPermission != null){
            // 2.1、需要判断可见权限范围，如果有返回就优先走范围查询
            headquartersId = selectPermission.getHeadquartersId();
            // 2.1.1、可见栏目范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerColumnId())){
                columnId = null;
                perColumnId = selectPermission.getPerColumnId();
            } else {
                // 没有可见权限范围，默认走工具类返回的栏目；超管即全部，其他即自身
                columnId = selectPermission.getColumnId();
                perColumnId = null;
            }
            // 2.1.2、可见公司范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerCompanyId())){
                companyId = null;
                perCompanyId = selectPermission.getPerCompanyId();
            } else {
                perCompanyId = null;
                // 没有可见权限范围，默认走工具类返回的公司；超管即全部，其他即自身
                companyId = selectPermission.getCompanyId();
            }
            // 2.1.3、可见销售组范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerSalesGroupId())){
                salesGroupId = null;
                perSalesGroupId = selectPermission.getPerSalesGroupId();
            } else {
                // 没有可见权限范围，默认走工具类返回的销售组；超管即全部，其他即自身
                salesGroupId = selectPermission.getSalesGroupId();
                perSalesGroupId = null;
            }
        } else {
            columnId = null;
            perColumnId = null;
            companyId = null;
            perCompanyId = null;
            salesGroupId = null;
            perSalesGroupId = null;
        }
        // 3、直接构建总部树形结构
        HeadquartersTreeResponse treeResponse = new HeadquartersTreeResponse();
        treeResponse.setId(headquartersId);
        // 4、获取栏目列表（有效状态），此处就限制了查询栏目，底下的归属公司id就不需要再区分栏目了
        LambdaQueryWrapper<ColumnPO> columnWrapper = new LambdaQueryWrapper<>();
        columnWrapper.eq(ColumnPO::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .eq(ColumnPO::getHeadquartersId, headquartersId)
                .eq(columnId != null, ColumnPO::getId, columnId)
                .in(CollectionUtils.isNotEmpty(perColumnId), ColumnPO::getId, perColumnId);
        columnWrapper.orderByDesc(ColumnPO::getCreatedAt);
        List<ColumnPO> columns = columnService.list(columnWrapper);
        // 4.1、构建栏目树形结构
        List<ColumnTreeResponse> columnTreeResponses = columns.stream().map(column -> {
            ColumnTreeResponse columnTreeResponse = new ColumnTreeResponse();
            columnTreeResponse.setId(column.getId());
            columnTreeResponse.setName(column.getColumnName());
            if (level >= 2) {
                // 4.1.1、获取单栏目下公司列表（有效状态）
                LambdaQueryWrapper<CompanyPO> companyWrapper = new LambdaQueryWrapper<>();
                companyWrapper.eq(CompanyPO::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                        .eq(CompanyPO::getColumnId, column.getId())
                        .eq(companyId != null, CompanyPO::getId, companyId)
                        .in(CollectionUtils.isNotEmpty(perCompanyId), CompanyPO::getId, perCompanyId);
                companyWrapper.orderByDesc(CompanyPO::getCreatedAt);
                List<CompanyPO> companies = companyService.list(companyWrapper);
                // 4.1.2、构建公司树形结构
                List<CompanyTreeResponse> companyTreeResponses = companies.stream().map(company -> {
                    CompanyTreeResponse companyTreeResponse = new CompanyTreeResponse();
                    companyTreeResponse.setId(company.getId());
                    companyTreeResponse.setName(company.getCompanyName());
                    if (level >= 3) {
                        // 4.1.3、获取单公司下销售组列表（有效状态）
                        LambdaQueryWrapper<SalesGroupPO> salesGroupWrapper = new LambdaQueryWrapper<>();
                        salesGroupWrapper.eq(SalesGroupPO::getCompanyId, company.getId())
                                .eq(SalesGroupPO::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                                .eq(salesGroupId != null, SalesGroupPO::getId, salesGroupId)
                                .in(CollectionUtils.isNotEmpty(perSalesGroupId), SalesGroupPO::getId, perSalesGroupId);
                        salesGroupWrapper.orderByDesc(SalesGroupPO::getCreatedAt);
                        List<SalesGroupPO> salesGroups = salesGroupService.list(salesGroupWrapper);
                        // 4.1.4、构建销售组列表
                        List<SalesGroupResponse> salesGroupResponses = salesGroups.stream().map(salesGroup -> {
                            SalesGroupResponse salesGroupResponse = new SalesGroupResponse();
                            salesGroupResponse.setId(salesGroup.getId());
                            salesGroupResponse.setName(salesGroup.getSalesGroupName());
                            return salesGroupResponse;
                        }).collect(Collectors.toList());
                        companyTreeResponse.setSalesGroups(salesGroupResponses);
                    }
                    return companyTreeResponse;
                }).collect(Collectors.toList());
                columnTreeResponse.setCompanies(companyTreeResponses);
            }
            return columnTreeResponse;
        }).collect(Collectors.toList());

        treeResponse.setColumns(columnTreeResponses);
        return treeResponse;
    }
}