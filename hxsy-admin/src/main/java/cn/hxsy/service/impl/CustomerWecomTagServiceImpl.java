package cn.hxsy.service.impl;

import cn.hxsy.anno.DisableShardingLog;
import cn.hxsy.api.qy.QyBaseResponse;
import cn.hxsy.api.qy.feign.cust.contract.QyWxCustContactClient;
import cn.hxsy.api.qy.request.contact.CustomerMarkTagRequest;
import cn.hxsy.api.qy.request.tag.TagAddRequest;
import cn.hxsy.api.qy.request.tag.TagDeleteRequest;
import cn.hxsy.api.qy.request.tag.TagEditRequest;
import cn.hxsy.api.qy.request.tag.TagListRequest;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.qy.response.tag.TagAddResponse;
import cn.hxsy.api.qy.response.tag.TagCorpResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.datasource.mapper.WecomTagGroupMapper;
import cn.hxsy.datasource.mapper.WecomTagMapper;
import cn.hxsy.datasource.mapper.WecomTagSyncRecordMapper;
import cn.hxsy.datasource.model.entity.CustomerWecomBinding;
import cn.hxsy.datasource.model.entity.WecomTag;
import cn.hxsy.datasource.model.entity.WecomTagGroup;
import cn.hxsy.datasource.model.entity.WecomTagSyncRecord;
import cn.hxsy.dto.CustomerMarkTagBackendDTO;
import cn.hxsy.dto.CustomerMarkTagDTO;
import cn.hxsy.dto.WecomTagGroupDTO;
import cn.hxsy.service.CustomerWecomBindingService;
import cn.hxsy.service.CustomerWecomTagService;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.utils.SpringUtils;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.BeanUtils;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.Timestamp;
import lombok.extern.slf4j.Slf4j;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 企业微信客户标签服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Service
@Slf4j
public class CustomerWecomTagServiceImpl implements CustomerWecomTagService {

    @Autowired
    private QyWxCustContactClient qyWxCustContactClient;

    @Autowired
    private QyAuthService qyAuthService;
    
    @Autowired
    private CustomerWecomBindingService customerWecomBindingService;

    @Autowired
    private WecomTagGroupMapper wecomTagGroupMapper;
    
    @Autowired
    private WecomTagMapper wecomTagMapper;
    
    @Autowired
    private WecomTagSyncRecordMapper wecomTagSyncRecordMapper;

    @Resource
    private UserCacheUtil userCacheUtil;

    /**
     * 获取企业标签库
     * 获取企业设置的所有客户标签，并同步到数据库
     *
     * @param corpId 企业微信id
     * @return 企业标签库响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TagCorpResponse getCorpTagList(String corpId, TagListRequest request) {
        log.info("开始获取企业标签库，标签请求信息：{}", request);

        QyWeChatAuthResponse accessToken = qyAuthService.getQyAccessToken(corpId);
        try {
            // 调用企业微信API获取标签数据
            TagCorpResponse response = qyWxCustContactClient.getCorpTagList(accessToken.getAccess_token(), request);

            if (response != null && Objects.equals(response.getErrcode(), "0")) {
                log.info("获取企业标签库成功，标签组数量：{}",
                        response.getTag_group() != null ? response.getTag_group().size() : 0);
                
                // 创建同步记录
                WecomTagSyncRecord syncRecord = createSyncRecord(corpId, request);
                wecomTagSyncRecordMapper.insert(syncRecord);
                
                // 异步同步标签数据到数据库
                asyncSyncTagsToDatabase(corpId, response, syncRecord.getId());
            } else {
                log.error("获取企业标签库失败，错误码：{}，错误信息：{}",
                        response != null ? response.getErrcode() : "null",
                        response != null ? response.getErrmsg() : "null response");
            }

            return response;
        } catch (Exception e) {
            log.error("调用企业微信API获取企业标签库时发生异常", e);
            TagCorpResponse errorResponse = new TagCorpResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("调用企业微信API异常: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 创建同步记录
     *
     * @param corpId 企业微信ID
     * @param request 标签请求信息
     * @return 同步记录实体
     */
    private WecomTagSyncRecord createSyncRecord(String corpId, TagListRequest request) {
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        WecomTagSyncRecord syncRecord = new WecomTagSyncRecord();
        syncRecord.setCorpId(corpId);
        
        // 判断同步类型
        if (request == null || (request.getGroupId() == null || request.getGroupId().isEmpty())) {
            syncRecord.setSyncType(1); // 全量同步
        } else {
            syncRecord.setSyncType(2); // 指定标签组同步
            syncRecord.setGroupIds(JSON.toJSONString(request.getGroupId()));
        }
        
        syncRecord.setStatus(0); // 进行中
        syncRecord.setStartTime(LocalDateTime.now());
        syncRecord.setGroupCount(0);
        syncRecord.setTagCount(0);
        syncRecord.setCreatedBy(systemUserSelfInfo.getUsername());
        return syncRecord;
    }
    
    /**
     * 异步同步企业微信标签数据到数据库
     *
     * @param corpId 企业微信ID
     * @param response 企业微信API返回的标签数据
     * @param syncRecordId 同步记录ID
     */
    @Async
    public void asyncSyncTagsToDatabase(String corpId, TagCorpResponse response, Long syncRecordId) {
        log.info("开始异步同步企业微信标签数据到数据库，企业ID：{}，同步记录ID：{}", corpId, syncRecordId);
        
        try {
            // 同步标签数据到数据库
            syncTagsToDatabase(corpId, response, syncRecordId);
            
            // 更新同步记录为成功
            updateSyncRecordStatus(syncRecordId, 1, null);
            
            log.info("异步同步企业微信标签数据到数据库完成，企业ID：{}，同步记录ID：{}", corpId, syncRecordId);
        } catch (Exception e) {
            log.error("异步同步企业微信标签数据到数据库时发生异常", e);
            
            // 更新同步记录为失败
            updateSyncRecordStatus(syncRecordId, 2, e.getMessage());
        }
    }
    
    /**
     * 更新同步记录状态
     *
     * @param syncRecordId 同步记录ID
     * @param status 状态：1-成功，2-失败
     * @param errorMsg 错误信息
     */
    private void updateSyncRecordStatus(Long syncRecordId, Integer status, String errorMsg) {
        WecomTagSyncRecord syncRecord = wecomTagSyncRecordMapper.selectById(syncRecordId);
        if (syncRecord != null) {
            syncRecord.setStatus(status);
            syncRecord.setEndTime(LocalDateTime.now());
            if (errorMsg != null) {
                syncRecord.setErrorMsg(errorMsg);
            }
            wecomTagSyncRecordMapper.updateById(syncRecord);
        }
    }
    
    /**
     * 同步企业微信标签数据到数据库
     *
     * @param corpId 企业微信ID
     * @param response 企业微信API返回的标签数据
     * @param syncRecordId 同步记录ID
     */
    private void syncTagsToDatabase(String corpId, TagCorpResponse response, Long syncRecordId) {
        if (response == null || response.getTag_group() == null || response.getTag_group().isEmpty()) {
            log.info("没有标签数据需要同步");
            return;
        }
        
        try {
            log.info("开始同步企业微信标签数据到数据库，企业ID：{}", corpId);
            
            // 获取要同步的标签组ID列表
            List<String> groupIds = new ArrayList<>();
            for (TagCorpResponse.TagGroup tagGroup : response.getTag_group()) {
                groupIds.add(tagGroup.getGroup_id());
            }
            
            // 只清除要同步的标签组数据，而不是清除所有标签数据
            if (!groupIds.isEmpty()) {
                clearExistingTagsByGroupIds(corpId, groupIds);
            }
            
            // 收集所有标签组和标签，准备批量插入
            List<WecomTagGroup> tagGroupList = new ArrayList<>();
            List<WecomTag> tagList = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            
            // 准备标签组和标签数据
            List<TagCorpResponse.TagGroup> tagGroups = response.getTag_group();
            for (TagCorpResponse.TagGroup tagGroup : tagGroups) {
                // 准备标签组数据
                WecomTagGroup wecomTagGroup = new WecomTagGroup();
                wecomTagGroup.setCorpId(corpId);
                wecomTagGroup.setGroupId(tagGroup.getGroup_id());
                wecomTagGroup.setName(tagGroup.getGroup_name());
                wecomTagGroup.setStatus(1); // 默认有效
                wecomTagGroup.setCreatedAt(now);
                wecomTagGroup.setUpdatedAt(now);
                wecomTagGroup.setOrderNum(tagGroup.getOrder());
                wecomTagGroup.setDeleted(tagGroup.getDeleted());
                tagGroupList.add(wecomTagGroup);
                
                // 准备标签数据
                if (tagGroup.getTag() != null && !tagGroup.getTag().isEmpty()) {
                    for (TagCorpResponse.Tag tag : tagGroup.getTag()) {
                        WecomTag wecomTag = new WecomTag();
                        wecomTag.setCorpId(corpId);
                        wecomTag.setGroupId(tagGroup.getGroup_id());
                        wecomTag.setTagId(tag.getId());
                        wecomTag.setName(tag.getName());
                        wecomTag.setStatus(1); // 默认有效
                        wecomTag.setCreatedAt(now);
                        wecomTag.setUpdatedAt(now);
                        wecomTag.setOrderNum(tag.getOrder());
                        wecomTag.setDeleted(tag.getDeleted());
                        tagList.add(wecomTag);
                    }
                }
            }
            
            // 批量插入标签组
            if (!tagGroupList.isEmpty()) {
                log.info("批量插入标签组数据，数量：{}", tagGroupList.size());
                batchInsertTagGroups(tagGroupList);
            }
            
            // 批量插入标签
            if (!tagList.isEmpty()) {
                log.info("批量插入标签数据，数量：{}", tagList.size());
                batchInsertTags(tagList);
            }
            
            // 更新同步记录的标签组和标签数量
            WecomTagSyncRecord syncRecord = wecomTagSyncRecordMapper.selectById(syncRecordId);
            if (syncRecord != null) {
                syncRecord.setGroupCount(tagGroupList.size());
                syncRecord.setTagCount(tagList.size());
                wecomTagSyncRecordMapper.updateById(syncRecord);
            }
            
            log.info("同步企业微信标签数据到数据库完成，企业ID：{}，标签组数量：{}，标签数量：{}", 
                    corpId, tagGroupList.size(), tagList.size());
        } catch (Exception e) {
            log.error("同步企业微信标签数据到数据库时发生异常", e);
            throw e;
        }
    }
    
    /**
     * 批量插入标签组数据
     *
     * @param tagGroupList 标签组列表
     */
    @DisableShardingLog
    private void batchInsertTagGroups(List<WecomTagGroup> tagGroupList) {
        if (tagGroupList == null || tagGroupList.isEmpty()) {
            return;
        }
        
        try {
            // 使用JDBC批量插入，提高性能
            String sql = "INSERT INTO wecom_tag_group (id, corp_id, group_id, name, status, created_at, updated_at, order_num, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            // 获取数据源并创建连接
            DataSource dataSource = SpringUtils.getBean(DataSource.class);
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement ps = conn.prepareStatement(sql)) {
                
                // 关闭自动提交，开启批处理模式
                conn.setAutoCommit(false);
                
                int count = 0;
                int batchSize = 500;
                
                // 获取雪花算法ID生成器
                SnowflakeIdWorker idWorker = SnowflakeIdWorker.getInstance();
                
                for (WecomTagGroup tagGroup : tagGroupList) {
                    // 为每个实体生成唯一ID
                    tagGroup.setId(idWorker.nextId());
                    
                    ps.setLong(1, tagGroup.getId());
                    ps.setString(2, tagGroup.getCorpId());
                    ps.setString(3, tagGroup.getGroupId());
                    ps.setString(4, tagGroup.getName());
                    ps.setInt(5, tagGroup.getStatus());
                    ps.setTimestamp(6, Timestamp.valueOf(tagGroup.getCreatedAt()));
                    ps.setTimestamp(7, Timestamp.valueOf(tagGroup.getUpdatedAt()));
                    ps.setInt(8, tagGroup.getOrderNum());
                    ps.setInt(9, tagGroup.getDeleted() != null && tagGroup.getDeleted() ? 1 : 0);
                    ps.addBatch();
                    
                    if (++count % batchSize == 0) {
                        ps.executeBatch();
                        ps.clearBatch();
                    }
                }
                
                // 执行剩余的批处理
                if (count % batchSize != 0) {
                    ps.executeBatch();
                }
                
                // 提交事务
                conn.commit();
                
                log.info("批量插入标签组数据完成，共插入{}条记录", tagGroupList.size());
            }
        } catch (Exception e) {
            log.error("批量插入标签组数据时发生异常", e);
            throw new RuntimeException("批量插入标签组数据失败", e);
        }
    }
    
    /**
     * 批量插入标签数据
     *
     * @param tagList 标签列表
     */
    @DisableShardingLog
    private void batchInsertTags(List<WecomTag> tagList) {
        if (tagList == null || tagList.isEmpty()) {
            return;
        }
        
        try {
            // 使用JDBC批量插入，提高性能
            String sql = "INSERT INTO wecom_tag (id, corp_id, group_id, tag_id, name, status, created_at, updated_at, order_num, deleted) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            // 获取数据源并创建连接
            DataSource dataSource = SpringUtils.getBean(DataSource.class);
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement ps = conn.prepareStatement(sql)) {
                
                // 关闭自动提交，开启批处理模式
                conn.setAutoCommit(false);
                
                int count = 0;
                int batchSize = 500;
                
                // 获取雪花算法ID生成器
                SnowflakeIdWorker idWorker = SnowflakeIdWorker.getInstance();
                
                for (WecomTag tag : tagList) {
                    // 为每个实体生成唯一ID
                    tag.setId(idWorker.nextId());
                    
                    ps.setLong(1, tag.getId());
                    ps.setString(2, tag.getCorpId());
                    ps.setString(3, tag.getGroupId());
                    ps.setString(4, tag.getTagId());
                    ps.setString(5, tag.getName());
                    ps.setInt(6, tag.getStatus());
                    ps.setTimestamp(7, Timestamp.valueOf(tag.getCreatedAt()));
                    ps.setTimestamp(8, Timestamp.valueOf(tag.getUpdatedAt()));
                    ps.setInt(9, tag.getOrderNum());
                    ps.setInt(10, tag.getDeleted() != null && tag.getDeleted() ? 1 : 0);
                    ps.addBatch();
                    
                    if (++count % batchSize == 0) {
                        ps.executeBatch();
                        ps.clearBatch();
                    }
                }
                
                // 执行剩余的批处理
                if (count % batchSize != 0) {
                    ps.executeBatch();
                }
                
                // 提交事务
                conn.commit();
                
                log.info("批量插入标签数据完成，共插入{}条记录", tagList.size());
            }
        } catch (Exception e) {
            log.error("批量插入标签数据时发生异常", e);
            throw new RuntimeException("批量插入标签数据失败", e);
        }
    }
    
    /**
     * 清除企业现有的标签数据
     *
     * @param corpId 企业微信ID
     */
    private void clearExistingTags(String corpId) {
        log.info("清除企业现有的标签数据，企业ID：{}", corpId);
        
        try {
            // 使用JDBC批量删除，提高性能
            DataSource dataSource = SpringUtils.getBean(DataSource.class);
            try (Connection conn = dataSource.getConnection()) {
                // 关闭自动提交，开启事务
                conn.setAutoCommit(false);
                
                // 删除标签
                try (PreparedStatement ps = conn.prepareStatement("DELETE FROM wecom_tag WHERE corp_id = ?")) {
                    ps.setString(1, corpId);
                    int tagCount = ps.executeUpdate();
                    log.info("删除标签数据完成，共删除{}条记录", tagCount);
                }
                
                // 删除标签组
                try (PreparedStatement ps = conn.prepareStatement("DELETE FROM wecom_tag_group WHERE corp_id = ?")) {
                    ps.setString(1, corpId);
                    int groupCount = ps.executeUpdate();
                    log.info("删除标签组数据完成，共删除{}条记录", groupCount);
                }
                
                // 提交事务
                conn.commit();
            }
        } catch (Exception e) {
            log.error("清除企业现有的标签数据时发生异常", e);
            throw new RuntimeException("清除企业现有的标签数据失败", e);
        }
    }
    
    /**
     * 清除企业指定标签组的标签数据
     *
     * @param corpId 企业微信ID
     * @param groupIds 要清除的标签组ID列表
     */
    @DisableShardingLog
    private void clearExistingTagsByGroupIds(String corpId, List<String> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            log.info("没有指定要清除的标签组ID，跳过清除操作");
            return;
        }
        
        log.info("清除企业指定标签组的标签数据，企业ID：{}，标签组数量：{}", corpId, groupIds.size());
        
        try {
            // 使用JDBC批量删除，提高性能
            DataSource dataSource = SpringUtils.getBean(DataSource.class);
            try (Connection conn = dataSource.getConnection()) {
                // 关闭自动提交，开启事务
                conn.setAutoCommit(false);
                
                // 构建IN子句的参数占位符
                StringBuilder placeholders = new StringBuilder();
                for (int i = 0; i < groupIds.size(); i++) {
                    if (i > 0) {
                        placeholders.append(",");
                    }
                    placeholders.append("?");
                }
                
                // 删除标签
                String tagSql = "DELETE FROM wecom_tag WHERE corp_id = ? AND group_id IN (" + placeholders + ")";
                try (PreparedStatement ps = conn.prepareStatement(tagSql)) {
                    ps.setString(1, corpId);
                    for (int i = 0; i < groupIds.size(); i++) {
                        ps.setString(i + 2, groupIds.get(i));
                    }
                    int tagCount = ps.executeUpdate();
                    log.info("删除指定标签组的标签数据完成，共删除{}条记录", tagCount);
                }
                
                // 删除标签组
                String groupSql = "DELETE FROM wecom_tag_group WHERE corp_id = ? AND group_id IN (" + placeholders + ")";
                try (PreparedStatement ps = conn.prepareStatement(groupSql)) {
                    ps.setString(1, corpId);
                    for (int i = 0; i < groupIds.size(); i++) {
                        ps.setString(i + 2, groupIds.get(i));
                    }
                    int groupCount = ps.executeUpdate();
                    log.info("删除指定标签组数据完成，共删除{}条记录", groupCount);
                }
                
                // 提交事务
                conn.commit();
            }
        } catch (Exception e) {
            log.error("清除企业指定标签组的标签数据时发生异常", e);
            throw new RuntimeException("清除企业指定标签组的标签数据失败", e);
        }
    }

    /**
     * 添加企业客户标签
     * 添加新的标签或标签组
     *
     * @param corpId 企业微信id
     * @param groupId 标签组id，如果创建的是标签，则需要指定此参数
     * @param groupName 标签组名称，如果创建的是标签组，则需要指定此参数
     * @param order 标签组次序值，非必填，默认为0
     * @param tagList 标签列表
     * @return 添加标签响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TagAddResponse addCorpTag(String corpId, String groupId, String groupName, Integer order, List<TagAddRequest.TagInfo> tagList) {
        log.info("开始添加企业客户标签，标签组ID：{}，标签组名称：{}，标签数量：{}",
                groupId, groupName, tagList != null ? tagList.size() : 0);

        QyWeChatAuthResponse accessToken = qyAuthService.getQyAccessToken(corpId);
        try {
            // 构建请求对象
            TagAddRequest request = new TagAddRequest();
            request.setGroup_id(groupId);
            request.setGroup_name(groupName);
            request.setOrder(order);
            request.setTag(tagList);

            TagAddResponse response = qyWxCustContactClient.addCorpTag(accessToken.getAccess_token(), request);

            if (response != null && Objects.equals(response.getErrcode(), "0")) {
                log.info("添加企业客户标签成功，标签组ID：{}",
                        response.getTag_group() != null ? response.getTag_group().getGroup_id() : "未返回");
                
                // 将返回的标签信息保存到本地数据库
                saveTagsToDatabase(corpId, response);
            } else {
                log.error("添加企业客户标签失败，错误码：{}，错误信息：{}",
                        response != null ? response.getErrcode() : "null",
                        response != null ? response.getErrmsg() : "null response");
            }

            return response;
        } catch (Exception e) {
            log.error("调用企业微信API添加企业客户标签时发生异常", e);
            TagAddResponse errorResponse = new TagAddResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("调用企业微信API异常: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 将企业微信API返回的标签信息保存到本地数据库
     *
     * @param corpId 企业微信ID
     * @param response 企业微信API返回的标签添加响应
     */
    private void saveTagsToDatabase(String corpId, TagAddResponse response) {
        if (response == null || response.getTag_group() == null) {
            log.info("没有标签数据需要保存");
            return;
        }
        
        try {
            log.info("开始将企业微信API返回的标签信息保存到本地数据库，企业ID：{}", corpId);
            
            LocalDateTime now = LocalDateTime.now();
            TagAddResponse.TagGroup tagGroup = response.getTag_group();
            
            // 1. 保存标签组
            WecomTagGroup wecomTagGroup = new WecomTagGroup();
            wecomTagGroup.setId(SnowflakeIdWorker.getInstance().nextId());
            wecomTagGroup.setCorpId(corpId);
            wecomTagGroup.setGroupId(tagGroup.getGroup_id());
            wecomTagGroup.setName(tagGroup.getGroup_name());
            wecomTagGroup.setStatus(1); // 默认有效
            wecomTagGroup.setCreatedAt(now);
            wecomTagGroup.setUpdatedAt(now);
            wecomTagGroup.setOrderNum(tagGroup.getOrder());
            wecomTagGroup.setDeleted(false);
            
            // 检查标签组是否已存在
            LambdaQueryWrapper<WecomTagGroup> groupWrapper = new LambdaQueryWrapper<>();
            groupWrapper.eq(WecomTagGroup::getCorpId, corpId);
            groupWrapper.eq(WecomTagGroup::getGroupId, tagGroup.getGroup_id());
            WecomTagGroup existingGroup = wecomTagGroupMapper.selectOne(groupWrapper);
            
            if (existingGroup != null) {
                // 更新现有标签组
                existingGroup.setName(tagGroup.getGroup_name());
                existingGroup.setOrderNum(tagGroup.getOrder());
                existingGroup.setUpdatedAt(now);
                existingGroup.setDeleted(false);
                wecomTagGroupMapper.updateById(existingGroup);
                log.info("更新标签组，ID：{}", tagGroup.getGroup_id());
            } else {
                // 插入新标签组
                wecomTagGroupMapper.insert(wecomTagGroup);
                log.info("插入新标签组，ID：{}", tagGroup.getGroup_id());
            }
            
            // 2. 保存标签
            if (tagGroup.getTag() != null && !tagGroup.getTag().isEmpty()) {
                for (TagAddResponse.Tag tag : tagGroup.getTag()) {
                    WecomTag wecomTag = new WecomTag();
                    wecomTag.setId(SnowflakeIdWorker.getInstance().nextId());
                    wecomTag.setCorpId(corpId);
                    wecomTag.setGroupId(tagGroup.getGroup_id());
                    wecomTag.setTagId(tag.getId());
                    wecomTag.setName(tag.getName());
                    wecomTag.setStatus(1); // 默认有效
                    wecomTag.setCreatedAt(now);
                    wecomTag.setUpdatedAt(now);
                    wecomTag.setOrderNum(tag.getOrder());
                    wecomTag.setDeleted(false);
                    
                    // 检查标签是否已存在
                    LambdaQueryWrapper<WecomTag> tagWrapper = new LambdaQueryWrapper<>();
                    tagWrapper.eq(WecomTag::getCorpId, corpId);
                    tagWrapper.eq(WecomTag::getTagId, tag.getId());
                    WecomTag existingTag = wecomTagMapper.selectOne(tagWrapper);
                    
                    if (existingTag != null) {
                        // 更新现有标签
                        existingTag.setName(tag.getName());
                        existingTag.setGroupId(tagGroup.getGroup_id());
                        existingTag.setOrderNum(tag.getOrder());
                        existingTag.setUpdatedAt(now);
                        existingTag.setDeleted(false);
                        wecomTagMapper.updateById(existingTag);
                        log.info("更新标签，ID：{}", tag.getId());
                    } else {
                        // 插入新标签
                        wecomTagMapper.insert(wecomTag);
                        log.info("插入新标签，ID：{}", tag.getId());
                    }
                }
            }
            
            log.info("企业微信API返回的标签信息保存到本地数据库完成，企业ID：{}", corpId);
        } catch (Exception e) {
            log.error("保存企业微信API返回的标签信息到本地数据库时发生异常", e);
            throw new RuntimeException("保存标签信息到数据库失败", e);
        }
    }

    /**
     * 编辑企业客户标签
     * 修改标签或标签组的名称、次序值
     *
     * @param corpId 企业微信访问令牌
     * @param id 标签或标签组的id
     * @param name 新的标签或标签组名称
     * @param order 标签/标签组的次序值，非必填，默认为0
     * @param groupId 标签组id，如果要修改标签所属的标签组，需要填写此参数
     * @param isGroup 是否是标签组，默认false
     * @return 编辑标签响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public QyBaseResponse editCorpTag(String corpId, String id, String name, Integer order, String groupId, Boolean isGroup) {
        log.info("开始编辑企业客户标签，ID：{}，新名称：{}，是否是标签组：{}", id, name, isGroup);

        QyWeChatAuthResponse accessToken = qyAuthService.getQyAccessToken(corpId);

        try {
            // 构建请求对象
            TagEditRequest request = new TagEditRequest();
            request.setId(id);
            request.setName(name);
            request.setOrder(order);
            request.setGroup_id(groupId);
            request.setIs_group(isGroup);

            QyBaseResponse response = qyWxCustContactClient.editCorpTag(accessToken.getAccess_token(), request);

            if (response != null && Objects.equals(response.getErrcode(), "0")) {
                log.info("编辑企业客户标签成功，ID：{}", id);
                
                // 更新本地数据库中的标签信息
                updateLocalTagInfo(corpId, id, name, order, groupId, isGroup);
            } else {
                log.error("编辑企业客户标签失败，错误码：{}，错误信息：{}",
                        response != null ? response.getErrcode() : "null",
                        response != null ? response.getErrmsg() : "null response");
            }

            return response;
        } catch (Exception e) {
            log.error("调用企业微信API编辑企业客户标签时发生异常", e);
            QyBaseResponse errorResponse = new QyBaseResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("调用企业微信API异常: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 更新本地数据库中的标签信息
     *
     * @param corpId 企业微信ID
     * @param id 标签或标签组的ID
     * @param name 新的标签或标签组名称
     * @param order 标签/标签组的次序值
     * @param groupId 标签组ID，如果要修改标签所属的标签组，需要填写此参数
     * @param isGroup 是否是标签组
     */
    private void updateLocalTagInfo(String corpId, String id, String name, Integer order, String groupId, Boolean isGroup) {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            if (isGroup != null && isGroup) {
                // 更新标签组信息
                log.info("更新本地数据库中的标签组信息，企业ID：{}，标签组ID：{}", corpId, id);
                
                LambdaQueryWrapper<WecomTagGroup> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(WecomTagGroup::getCorpId, corpId);
                wrapper.eq(WecomTagGroup::getGroupId, id);
                
                WecomTagGroup tagGroup = wecomTagGroupMapper.selectOne(wrapper);
                if (tagGroup != null) {
                    // 更新标签组信息
                    tagGroup.setName(name);
                    if (order != null) {
                        tagGroup.setOrderNum(order);
                    }
                    tagGroup.setUpdatedAt(now);
                    
                    wecomTagGroupMapper.updateById(tagGroup);
                    log.info("本地数据库中的标签组信息更新成功，企业ID：{}，标签组ID：{}", corpId, id);
                }
            } else {
                // 更新标签信息
                log.info("更新本地数据库中的标签信息，企业ID：{}，标签ID：{}", corpId, id);
                
                LambdaQueryWrapper<WecomTag> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(WecomTag::getCorpId, corpId);
                wrapper.eq(WecomTag::getTagId, id);
                
                WecomTag tag = wecomTagMapper.selectOne(wrapper);
                if (tag != null) {
                    // 更新标签信息
                    tag.setName(name);
                    if (order != null) {
                        tag.setOrderNum(order);
                    }
                    if (groupId != null) {
                        tag.setGroupId(groupId);
                    }
                    tag.setUpdatedAt(now);
                    
                    wecomTagMapper.updateById(tag);
                    log.info("本地数据库中的标签信息更新成功，企业ID：{}，标签ID：{}", corpId, id);
                }
            }
        } catch (Exception e) {
            log.error("更新本地数据库中的标签信息时发生异常", e);
            throw new RuntimeException("更新本地数据库中的标签信息失败", e);
        }
    }

    /**
     * 删除企业客户标签
     * 删除标签或标签组
     *
     * @param corpId 企业微信访问令牌
     * @param tagIds 标签的id列表
     * @param groupIds 标签组的id列表
     * @param deleteTagWithGroup 删除标签组时是否连同标签一起删除，默认为true
     * @return 删除标签响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public QyBaseResponse deleteCorpTag(String corpId, List<String> tagIds, List<String> groupIds, Boolean deleteTagWithGroup) {
        log.info("开始删除企业客户标签，标签ID数量：{}，标签组ID数量：{}，是否连同标签一起删除：{}",
                tagIds != null ? tagIds.size() : 0,
                groupIds != null ? groupIds.size() : 0,
                deleteTagWithGroup);
        QyWeChatAuthResponse accessToken = qyAuthService.getQyAccessToken(corpId);

        try {
            // 构建请求对象
            TagDeleteRequest request = new TagDeleteRequest();
            request.setTag_id(tagIds);
            request.setGroup_id(groupIds);
            request.setDelete_tag_with_group(deleteTagWithGroup);

            QyBaseResponse response = qyWxCustContactClient.deleteCorpTag(accessToken.getAccess_token(), request);

            if (response != null && Objects.equals(response.getErrcode(), "0")) {
                log.info("删除企业客户标签成功，开始更新本地数据库");
                
                // 更新本地数据库中的标签删除状态
                updateLocalTagsDeleteStatus(corpId, tagIds, groupIds, deleteTagWithGroup);
                
                log.info("本地数据库标签删除状态更新完成");
            } else {
                log.error("删除企业客户标签失败，错误码：{}，错误信息：{}",
                        response != null ? response.getErrcode() : "null",
                        response != null ? response.getErrmsg() : "null response");
            }

            return response;
        } catch (Exception e) {
            log.error("调用企业微信API删除企业客户标签时发生异常", e);
            QyBaseResponse errorResponse = new QyBaseResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("调用企业微信API异常: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 更新本地数据库中标签和标签组的删除状态
     *
     * @param corpId 企业微信ID
     * @param tagIds 标签的ID列表
     * @param groupIds 标签组的ID列表
     * @param deleteTagWithGroup 删除标签组时是否连同标签一起删除
     */
    private void updateLocalTagsDeleteStatus(String corpId, List<String> tagIds, List<String> groupIds, Boolean deleteTagWithGroup) {
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 更新标签的删除状态
            if (tagIds != null && !tagIds.isEmpty()) {
                log.info("更新标签删除状态，标签数量：{}", tagIds.size());
                
                for (String tagId : tagIds) {
                    // 查询标签
                    LambdaQueryWrapper<WecomTag> tagWrapper = new LambdaQueryWrapper<>();
                    tagWrapper.eq(WecomTag::getCorpId, corpId);
                    tagWrapper.eq(WecomTag::getTagId, tagId);
                    
                    WecomTag tag = wecomTagMapper.selectOne(tagWrapper);
                    if (tag != null) {
                        // 更新删除状态
                        tag.setDeleted(true);
                        tag.setUpdatedAt(now);
                        wecomTagMapper.updateById(tag);
                        log.info("标签删除状态已更新，标签ID：{}", tagId);
                    } else {
                        log.warn("未找到要更新的标签，标签ID：{}", tagId);
                    }
                }
            }
            
            // 更新标签组的删除状态
            if (groupIds != null && !groupIds.isEmpty()) {
                log.info("更新标签组删除状态，标签组数量：{}", groupIds.size());
                
                for (String groupId : groupIds) {
                    // 查询标签组
                    LambdaQueryWrapper<WecomTagGroup> groupWrapper = new LambdaQueryWrapper<>();
                    groupWrapper.eq(WecomTagGroup::getCorpId, corpId);
                    groupWrapper.eq(WecomTagGroup::getGroupId, groupId);
                    
                    WecomTagGroup tagGroup = wecomTagGroupMapper.selectOne(groupWrapper);
                    if (tagGroup != null) {
                        // 更新标签组删除状态
                        tagGroup.setDeleted(true);
                        tagGroup.setUpdatedAt(now);
                        wecomTagGroupMapper.updateById(tagGroup);
                        log.info("标签组删除状态已更新，标签组ID：{}", groupId);
                        
                        // 如果需要同时删除标签组下的所有标签
                        if (deleteTagWithGroup != null && deleteTagWithGroup) {
                            // 查询该标签组下的所有标签
                            LambdaQueryWrapper<WecomTag> tagWrapper = new LambdaQueryWrapper<>();
                            tagWrapper.eq(WecomTag::getCorpId, corpId);
                            tagWrapper.eq(WecomTag::getGroupId, groupId);
                            tagWrapper.eq(WecomTag::getDeleted, false); // 只更新未删除的标签
                            
                            List<WecomTag> tags = wecomTagMapper.selectList(tagWrapper);
                            if (tags != null && !tags.isEmpty()) {
                                log.info("更新标签组下所有标签的删除状态，标签组ID：{}，标签数量：{}", groupId, tags.size());
                                
                                for (WecomTag tag : tags) {
                                    tag.setDeleted(true);
                                    tag.setUpdatedAt(now);
                                    wecomTagMapper.updateById(tag);
                                }
                                
                                log.info("标签组下所有标签的删除状态已更新，标签组ID：{}", groupId);
                            }
                        }
                    } else {
                        log.warn("未找到要更新的标签组，标签组ID：{}", groupId);
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新本地数据库中标签和标签组的删除状态时发生异常", e);
            throw new RuntimeException("更新本地数据库中标签和标签组的删除状态失败", e);
        }
    }
    
    /**
     * 查询本地数据库中的企微标签组及标签
     * 
     * @param corpId 企业微信ID
     * @return 标签组及标签列表
     */
    @DisableShardingLog
    @Override
    public List<WecomTagGroupDTO> getLocalTagGroups(String corpId) {
        log.info("开始查询本地数据库中的企微标签组及标签，企业ID：{}", corpId);
        
        try {
            // 查询标签组
            LambdaQueryWrapper<WecomTagGroup> groupWrapper = new LambdaQueryWrapper<>();
            groupWrapper.eq(WecomTagGroup::getCorpId, corpId);
            groupWrapper.eq(WecomTagGroup::getStatus, 1); // 只查询有效的标签组
            groupWrapper.eq(WecomTagGroup::getDeleted, 0); // 只查询有效的标签组
            groupWrapper.orderByDesc(WecomTagGroup::getCreatedAt); // 按创建时间降序排序
            List<WecomTagGroup> tagGroups = wecomTagGroupMapper.selectList(groupWrapper);
            
            if (tagGroups == null || tagGroups.isEmpty()) {
                log.info("未查询到企业的标签组数据，企业ID：{}", corpId);
                return new ArrayList<>();
            }
            
            // 转换为DTO
            List<WecomTagGroupDTO> result = new ArrayList<>(tagGroups.size());
            for (WecomTagGroup tagGroup : tagGroups) {
                WecomTagGroupDTO dto = new WecomTagGroupDTO();
                BeanUtils.copyProperties(tagGroup, dto);
                
                // 查询该标签组下的标签
                LambdaQueryWrapper<WecomTag> tagWrapper = new LambdaQueryWrapper<>();
                tagWrapper.eq(WecomTag::getCorpId, corpId);
                tagWrapper.eq(WecomTag::getGroupId, tagGroup.getGroupId());
                tagWrapper.eq(WecomTag::getStatus, 1); // 只查询有效的标签
                tagWrapper.eq(WecomTag::getDeleted, 0); // 只查询有效的标签
                tagWrapper.orderByDesc(WecomTag::getCreatedAt); // 按创建时间降序排序
                List<WecomTag> tags = wecomTagMapper.selectList(tagWrapper);
                
                // 转换标签为DTO
                if (tags != null && !tags.isEmpty()) {
                    List<WecomTagGroupDTO.WecomTagDTO> tagDTOs = new ArrayList<>(tags.size());
                    for (WecomTag tag : tags) {
                        WecomTagGroupDTO.WecomTagDTO tagDTO = new WecomTagGroupDTO.WecomTagDTO();
                        BeanUtils.copyProperties(tag, tagDTO);
                        tagDTOs.add(tagDTO);
                    }
                    dto.setTags(tagDTOs);
                } else {
                    dto.setTags(new ArrayList<>());
                }
                
                result.add(dto);
            }
            
            log.info("查询本地数据库中的企微标签组及标签完成，企业ID：{}，标签组数量：{}", corpId, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询本地数据库中的企微标签组及标签时发生异常", e);
            throw e;
        }
    }
    
    /**
     * 为客户打标签
     *
     * @param dto 客户打标签请求参数
     * @return 基础响应
     */
    @Override
    public QyBaseResponse markCustomerTag(CustomerMarkTagDTO dto) {
        log.info("开始为客户打标签，企业ID：{}，成员ID：{}，客户ID：{}，添加标签数：{}，移除标签数：{}", 
                dto.getCorpId(), dto.getUserId(), dto.getExternalUserId(), 
                dto.getAddTagIds() != null ? dto.getAddTagIds().size() : 0, 
                dto.getRemoveTagIds() != null ? dto.getRemoveTagIds().size() : 0);
        
        if ((dto.getAddTagIds() == null || dto.getAddTagIds().isEmpty()) && 
            (dto.getRemoveTagIds() == null || dto.getRemoveTagIds().isEmpty())) {
            QyBaseResponse errorResponse = new QyBaseResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("添加标签和移除标签不能同时为空");
            return errorResponse;
        }
        // 根据客户id查询绑定关系
        LambdaQueryWrapper<CustomerWecomBinding> wecomBindingLambdaQueryWrapper = new LambdaQueryWrapper<CustomerWecomBinding>()
                .eq(CustomerWecomBinding::getCorpId, dto.getCorpId())
                .eq(CustomerWecomBinding::getCustomerId, dto.getExternalUserId())
                .eq(CustomerWecomBinding::getSalesUserid, dto.getUserId())
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        CustomerWecomBinding customerWecomBinding = customerWecomBindingService.getOne(wecomBindingLambdaQueryWrapper);
        QyWeChatAuthResponse accessToken = qyAuthService.getQyAccessToken(dto.getCorpId());
        if (customerWecomBinding == null) {
            QyBaseResponse errorResponse = new QyBaseResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("未找到该客户的企微绑定关系");
            return errorResponse;
        }
        try {
            // 构建请求对象
            cn.hxsy.api.qy.request.contact.CustomerMarkTagRequest request = new cn.hxsy.api.qy.request.contact.CustomerMarkTagRequest();
            request.setUserid(dto.getUserId());
            request.setExternal_userid(customerWecomBinding.getExternalUserid());
            request.setAdd_tag(dto.getAddTagIds());
            request.setRemove_tag(dto.getRemoveTagIds());
            
            cn.hxsy.api.qy.response.contact.CustomerMarkTagResponse response = qyWxCustContactClient.markCustomerTag(accessToken.getAccess_token(), request);
            
            if (response != null && Objects.equals(response.getErrcode(), "0")) {
                log.info("为客户打标签成功，企业ID：{}，成员ID：{}，客户ID：{}", dto.getCorpId(), dto.getUserId(), customerWecomBinding.getExternalUserid());
            } else {
                log.error("为客户打标签失败，错误码：{}，错误信息：{}",
                        response != null ? response.getErrcode() : "null",
                        response != null ? response.getErrmsg() : "null response");
            }
            
            return response;
        } catch (Exception e) {
            log.error("调用企业微信API为客户打标签时发生异常", e);
            QyBaseResponse errorResponse = new QyBaseResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("调用企业微信API异常: " + e.getMessage());
            return errorResponse;
        }
    }
    
    /**
     * 批量为客户打标签
     *
     * @param corpId 企业微信ID
     * @param tagList 客户打标签请求参数列表
     * @return 基础响应
     */
    @Override
    public QyBaseResponse batchMarkCustomerTag(String corpId, List<CustomerMarkTagDTO> tagList) {
        log.info("开始批量为客户打标签，企业ID：{}，标签请求数量：{}", corpId, tagList != null ? tagList.size() : 0);
        
        if (tagList == null || tagList.isEmpty()) {
            QyBaseResponse errorResponse = new QyBaseResponse();
            errorResponse.setErrcode(String.valueOf(-1));
            errorResponse.setErrmsg("标签请求列表不能为空");
            return errorResponse;
        }
        
        // 获取企业微信访问令牌，避免重复获取
        QyWeChatAuthResponse accessToken = qyAuthService.getQyAccessToken(corpId);
        
        // 记录成功和失败的数量
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMsgs = new StringBuilder();
        
        // 遍历标签列表，为每个客户打标签
        for (CustomerMarkTagDTO dto : tagList) {
            // 确保使用传入的corpId
            dto.setCorpId(corpId);
            
            // 验证必要参数
            if (dto.getUserId() == null || dto.getExternalUserId() == null || 
                ((dto.getAddTagIds() == null || dto.getAddTagIds().isEmpty()) && 
                 (dto.getRemoveTagIds() == null || dto.getRemoveTagIds().isEmpty()))) {
                failCount++;
                errorMsgs.append("客户ID：").append(dto.getExternalUserId()).append("，参数不完整；");
                continue;
            }
            // 根据客户id查询绑定关系
            LambdaQueryWrapper<CustomerWecomBinding> wecomBindingLambdaQueryWrapper = new LambdaQueryWrapper<CustomerWecomBinding>()
                    .eq(CustomerWecomBinding::getCorpId, dto.getCorpId())
                    .eq(CustomerWecomBinding::getCustomerId, dto.getExternalUserId())
                    .eq(CustomerWecomBinding::getSalesUserid, dto.getUserId())
                    .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
            CustomerWecomBinding customerWecomBinding = customerWecomBindingService.getOne(wecomBindingLambdaQueryWrapper);

            if (customerWecomBinding == null) {
                failCount++;
                errorMsgs.append("客户ID：").append(dto.getExternalUserId()).append("，未找到绑定关系；");
                continue;
            }
            try {
                // 构建请求对象
                CustomerMarkTagRequest request = new cn.hxsy.api.qy.request.contact.CustomerMarkTagRequest();
                request.setUserid(dto.getUserId());
                request.setExternal_userid(customerWecomBinding.getExternalUserid());
                request.setAdd_tag(dto.getAddTagIds());
                request.setRemove_tag(dto.getRemoveTagIds());
                
                // 调用企业微信API
                cn.hxsy.api.qy.response.contact.CustomerMarkTagResponse response = 
                    qyWxCustContactClient.markCustomerTag(accessToken.getAccess_token(), request);
                
                if (response != null && Objects.equals(response.getErrcode(), "0")) {
                    successCount++;
                    log.info("为客户打标签成功，企业ID：{}，成员ID：{}，客户ID：{}", 
                            corpId, dto.getUserId(), customerWecomBinding.getExternalUserid());
                } else {
                    failCount++;
                    String errorMsg = response != null ? response.getErrmsg() : "未知错误";
                    errorMsgs.append("客户ID：").append(dto.getExternalUserId())
                             .append("，错误：").append(errorMsg).append("；");
                    log.error("为客户打标签失败，企业ID：{}，成员ID：{}，客户ID：{}，错误码：{}，错误信息：{}", 
                            corpId, dto.getUserId(), dto.getExternalUserId(),
                            response != null ? response.getErrcode() : "null",
                            response != null ? response.getErrmsg() : "null response");
                }
            } catch (Exception e) {
                failCount++;
                errorMsgs.append("客户ID：").append(dto.getExternalUserId())
                         .append("，异常：").append(e.getMessage()).append("；");
                log.error("为客户打标签时发生异常，企业ID：{}，成员ID：{}，客户ID：{}", 
                        corpId, dto.getUserId(), dto.getExternalUserId(), e);
            }
        }
        
        // 构建返回结果
        QyBaseResponse result = new QyBaseResponse();
        if (failCount == 0) {
            // 全部成功
            result.setErrcode("0");
            result.setErrmsg("批量打标签成功，共处理" + successCount + "条记录");
        } else if (successCount == 0) {
            // 全部失败
            result.setErrcode(String.valueOf(-1));
            result.setErrmsg("批量打标签全部失败，共" + failCount + "条记录。错误详情：" + errorMsgs.toString());
        } else {
            // 部分成功部分失败
            result.setErrcode("0");
            result.setErrmsg("批量打标签部分成功，成功" + successCount + "条，失败" + failCount + "条。错误详情：" + errorMsgs.toString());
        }
        
        log.info("批量为客户打标签完成，企业ID：{}，成功：{}，失败：{}", corpId, successCount, failCount);
        return result;
    }

    /**
     * 后台系统为客户打企业微信标签
     *
     * @param dto 后台打标签请求参数
     * @return 操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean markCustomerTagByBackend(CustomerMarkTagBackendDTO dto) {
        log.info("开始后台为客户打标签，销售ID：{}，客户ID：{}，标签名称：{}", 
                dto.getSalesId(), dto.getCustomerId(), dto.getTagName());
        
        if (dto.getSalesId() == null || dto.getCustomerId() == null || dto.getTagName() == null) {
            log.error("后台为客户打标签参数不完整，销售ID：{}，客户ID：{}，标签名称：{}", 
                    dto.getSalesId(), dto.getCustomerId(), dto.getTagName());
            return false;
        }
        
        try {
            // 1. 根据销售ID和客户ID查询绑定关系
            LambdaQueryWrapper<CustomerWecomBinding> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CustomerWecomBinding::getSalesId, dto.getSalesId())
                    .eq(CustomerWecomBinding::getCustomerId, dto.getCustomerId())
                    .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());

            CustomerWecomBinding customerWecomBinding = customerWecomBindingService.getOne(queryWrapper);
            if (customerWecomBinding == null) {
                log.error("未找到客户与企微的绑定关系，销售ID：{}，客户ID：{}", dto.getSalesId(), dto.getCustomerId());
                return false;
            }
            
            String corpId = customerWecomBinding.getCorpId();
            String externalUserId = customerWecomBinding.getExternalUserid();
            String userId = customerWecomBinding.getSalesUserid();
            
            if (corpId == null || externalUserId == null || userId == null) {
                log.error("客户与企微的绑定关系信息不完整，corpId：{}，externalUserId：{}，userId：{}", 
                        corpId, externalUserId, userId);
                return false;
            }
            
            // 2. 根据标签名称和企业微信ID查询对应的企微标签ID
            List<String> tagIds = findTagIdsByName(corpId, dto.getTagName());
            if (tagIds.isEmpty()) {
                log.error("未找到对应的企微标签，企业ID：{}，标签名称：{}", corpId, dto.getTagName());
                return false;
            }
            
            // 3. 获取企业微信访问令牌
            QyWeChatAuthResponse accessToken = qyAuthService.getQyAccessToken(corpId);
            
            // 4. 构建请求对象
            CustomerMarkTagRequest request = new CustomerMarkTagRequest();
            request.setUserid(userId);
            request.setExternal_userid(externalUserId);
            request.setAdd_tag(tagIds);
            
            // 5. 调用企业微信API为客户打标签
            cn.hxsy.api.qy.response.contact.CustomerMarkTagResponse response = 
                qyWxCustContactClient.markCustomerTag(accessToken.getAccess_token(), request);
            
            if (response != null && Objects.equals(response.getErrcode(), "0")) {
                log.info("后台为客户打标签成功，企业ID：{}，成员ID：{}，客户ID：{}，标签名称：{}", 
                        corpId, userId, externalUserId, dto.getTagName());
                return true;
            } else {
                log.error("后台为客户打标签失败，企业ID：{}，成员ID：{}，客户ID：{}，错误码：{}，错误信息：{}", 
                        corpId, userId, externalUserId,
                        response != null ? response.getErrcode() : "null",
                        response != null ? response.getErrmsg() : "null response");
                return false;
            }
        } catch (Exception e) {
            log.error("后台为客户打标签时发生异常", e);
            return false;
        }
    }
    
    /**
     * 根据标签名称和企业微信ID查询对应的企微标签ID
     *
     * @param corpId 企业微信ID
     * @param tagName 标签名称
     * @return 标签ID列表
     */
    private List<String> findTagIdsByName(String corpId, String tagName) {
        List<String> tagIds = new ArrayList<>();
        
        try {
            // 查询标签
            LambdaQueryWrapper<WecomTag> tagWrapper = new LambdaQueryWrapper<>();
            tagWrapper.eq(WecomTag::getCorpId, corpId)
                    .eq(WecomTag::getName, tagName)
                    .eq(WecomTag::getStatus, 1) // 只查询有效的标签
                    .eq(WecomTag::getDeleted, false); // 只查询未删除的标签

            WecomTag wecomTag = wecomTagMapper.selectOne(tagWrapper);

            if (wecomTag != null) {
                tagIds.add(wecomTag.getTagId());
            } else {
                log.warn("未找到对应的企微标签，企业ID：{}，标签名称：{}", corpId, tagName);
            }

        } catch (Exception e) {
            log.error("查询企微标签ID时发生异常", e);
        }
        
        return tagIds;
    }

    /**
     * 分页查询企业微信标签同步记录
     * 
     * @param page 分页参数
     * @param corpId 企业微信ID
     * @param syncType 同步类型：1-全量同步，2-指定标签组同步
     * @param status 同步状态：0-进行中，1-成功，2-失败
     * @return 分页结果
     */
    @Override
    public IPage<WecomTagSyncRecord> getSyncRecords(Page<WecomTagSyncRecord> page, String corpId, Integer syncType, Integer status) {
        log.info("分页查询企业微信标签同步记录，企业ID：{}，同步类型：{}，同步状态：{}", corpId, syncType, status);
        
        try {
            // 构建查询条件
            LambdaQueryWrapper<WecomTagSyncRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(WecomTagSyncRecord::getCorpId, corpId);
            
            // 如果指定了同步类型，则添加条件
            if (syncType != null) {
                wrapper.eq(WecomTagSyncRecord::getSyncType, syncType);
            }
            
            // 如果指定了同步状态，则添加条件
            if (status != null) {
                wrapper.eq(WecomTagSyncRecord::getStatus, status);
            }
            
            // 按开始时间降序排序
            wrapper.orderByDesc(WecomTagSyncRecord::getStartTime);
            
            // 执行分页查询
            IPage<WecomTagSyncRecord> result = wecomTagSyncRecordMapper.selectPage(page, wrapper);
            
            log.info("分页查询企业微信标签同步记录完成，总记录数：{}，当前页记录数：{}", 
                    result.getTotal(), result.getRecords().size());
            
            return result;
        } catch (Exception e) {
            log.error("分页查询企业微信标签同步记录时发生异常", e);
            throw e;
        }
    }
}
