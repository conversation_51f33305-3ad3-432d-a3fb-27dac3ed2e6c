package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.request.CustomerBehaviorPageRequest;
import cn.hxsy.base.enums.BehaviorTypeEnum;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.dao.CustomerBehaviorMapper;
import cn.hxsy.datasource.model.entity.CustomerBehavior;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.service.CustomerBehaviorService;
import cn.hxsy.service.SystemUserService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户行为轨迹服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class CustomerBehaviorServiceImpl extends ServiceImpl<CustomerBehaviorMapper, CustomerBehavior> implements CustomerBehaviorService {

    @Autowired
    private SnowflakeIdWorker idWorker;

    @Autowired
    private SystemUserService systemUserService;

    /**
     * 根据客户ID查询行为轨迹列表
     *
     * @param customerId 客户ID
     * @return 行为轨迹列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public List<CustomerBehavior> getByCustomerId(Long customerId) {
        LambdaQueryWrapper<CustomerBehavior> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerBehavior::getCustomerId, customerId);
        return list(wrapper);
    }

    /**
     * 分页查询客户行为轨迹信息
     *
     * @param pageNum 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param customerBehaviorPageRequest
     * @return 分页结果
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public Page<CustomerBehavior> page(Integer pageNum, Integer pageSize, CustomerBehaviorPageRequest customerBehaviorPageRequest) {
        Page<CustomerBehavior> page = new Page<>(pageNum, pageSize);
        return baseMapper.queryByType(page, customerBehaviorPageRequest);
    }

    /**
     * 保存训练营报名行为轨迹信息
     *
     * @param companyId 训练营ID
     * @param campPeriodId 营期ID
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean saveCampEnrollment(Long customerId, Long companyId, Long campPeriodId, Long salesId) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.CAMP_ENROLLMENT.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCampPeriodId(campPeriodId);
        SystemUserPO userInfo = systemUserService.getById(salesId);
        customerBehavior.setEmployeeName(userInfo.getUsername());
        return save(customerBehavior);
    }

    /**
     * 保存训练营视频课程学习行为轨迹信息
     *
     * @param companyId 训练营ID
     * @param campPeriodId 营期ID
     * @param courseId 课程ID
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean saveCampVideoCourseLearning(Long customerId, Long companyId, Long campPeriodId, Long courseId) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.CAMP_VIDEO_COURSE_LEARNING.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCampPeriodId(campPeriodId);
        customerBehavior.setCourseId(courseId);
        return save(customerBehavior);
    }

    /**
     * description : 初始化客户行为轨迹信息
     * @title: initCustomerBehavior
     * @param:
     * <AUTHOR>
     * @date 2025/4/7 13:49
     * @return CustomerBehavior
     */
    private CustomerBehavior initCustomerBehavior() {
        LocalDateTime now = LocalDateTime.now();
        CustomerBehavior customerBehavior = new CustomerBehavior();
        customerBehavior.setId(idWorker.nextId());
        customerBehavior.setCreatedAt(now);
        customerBehavior.setUpdatedAt(now);
        customerBehavior.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        return customerBehavior;
    }

    /**
     * description : 答题
     * @title: saveCampQuiz
     * @param: customerId
     * @param: companyId
     * @param: campPeriodId
     * @param: courseId
     * <AUTHOR>
     * @date 2025/5/9 2:00
     * @return boolean
     */
    @Override
    public boolean saveCampQuiz(Long customerId, Long companyId, Long campPeriodId, Long courseId) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.POST_COURSE_ANSWERING.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCampPeriodId(campPeriodId);
        customerBehavior.setCourseId(courseId);
        return save(customerBehavior);
    }

    /**
     * description : 领取红包
     * @title: saveReceiveRedPacket
     * @param: customerId
     * @param: companyId
     * @param: campPeriodId
     * @param: courseId
     * @param: amount
     * @param: type
     * <AUTHOR>
     * @date 2025/5/9 2:00
     * @return boolean
     */
    @Override
    public boolean saveReceiveRedPacket(Long customerId, Long companyId, Long campPeriodId, Long courseId, String amount, Integer type) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.RECEIVE_RED_PACKET.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCampPeriodId(campPeriodId);
        customerBehavior.setCourseId(courseId);
        customerBehavior.setRewardAmount(new BigDecimal(amount));
        customerBehavior.setRewardType(type.toString());
        return save(customerBehavior);
    }

    /**
     * 保存添加企微行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param corpName 企业名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @return 是否保存成功
     */
    @Override
    public boolean saveAddWechatEnterprise(Long customerId, Long companyId, String corpId, String corpName, String employeeName, String employeeWeworkName) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.ADD_WECHAT_ENTERPRISE.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCorpId(corpId);
        customerBehavior.setCorpName(corpName);
        customerBehavior.setEmployeeName(employeeName);
        customerBehavior.setEmployeeWeworkName(employeeWeworkName);
        return save(customerBehavior);
    }

    /**
     * 保存删除企微行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param corpName 企业名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @return 是否保存成功
     */
    @Override
    public boolean saveDeleteWechatEnterprise(Long customerId, Long companyId, String corpId, String corpName, String employeeName, String employeeWeworkName) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.DELETE_WECHAT_ENTERPRISE.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCorpId(corpId);
        customerBehavior.setCorpName(corpName);
        customerBehavior.setEmployeeName(employeeName);
        customerBehavior.setEmployeeWeworkName(employeeWeworkName);
        return save(customerBehavior);
    }

    /**
     * 保存加入群组行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param groupId 群组ID
     * @param groupName 群组名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @param joinScene 入群方式 0-由成员邀请入群, 3-通过扫描群二维码入群
     * @return 是否保存成功
     */
    @Override
    public boolean saveJoinGroupChat(Long customerId, Long companyId, String corpId, String groupId, String groupName, String employeeName, String employeeWeworkName, Integer joinScene) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.JOIN_GROUP_CHAT.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCorpId(corpId);
        customerBehavior.setGroupId(groupId);
        customerBehavior.setGroupName(groupName);
        customerBehavior.setEmployeeName(employeeName);
        customerBehavior.setEmployeeWeworkName(employeeWeworkName);
        customerBehavior.setJoinScene(joinScene);
        return save(customerBehavior);
    }

    /**
     * 保存退出群组行为
     * @param customerId 客户ID
     * @param companyId 企业ID
     * @param corpId 企微企业ID
     * @param groupId 群组ID
     * @param groupName 群组名称
     * @param employeeName 员工姓名
     * @param employeeWeworkName 员工企微昵称
     * @param quitScene 退群方式 0-自己退群, 1-群主/群管理员移出
     * @return 是否保存成功
     */
    @Override
    public boolean saveExitGroupChat(Long customerId, Long companyId, String corpId, String groupId, String groupName, String employeeName, String employeeWeworkName, Integer quitScene) {
        CustomerBehavior customerBehavior = initCustomerBehavior();
        customerBehavior.setBehaviorType(BehaviorTypeEnum.EXIT_GROUP_CHAT.getCode());
        customerBehavior.setCustomerId(customerId);
        customerBehavior.setCompanyId(companyId);
        customerBehavior.setCorpId(corpId);
        customerBehavior.setGroupId(groupId);
        customerBehavior.setGroupName(groupName);
        customerBehavior.setEmployeeName(employeeName);
        customerBehavior.setEmployeeWeworkName(employeeWeworkName);
        customerBehavior.setQuitScene(quitScene);
        return save(customerBehavior);
    }
}