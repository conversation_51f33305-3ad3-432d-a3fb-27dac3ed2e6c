package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.system.request.SysMenuRequest;
import cn.hxsy.api.system.response.SysMenuResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.SysMenuMapper;
import cn.hxsy.dao.SysRoleMenuMapper;
import cn.hxsy.datasource.model.entity.SysRoleMenu;
import cn.hxsy.service.SysMenuService;
import cn.hxsy.datasource.model.entity.SysMenu;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hxsy.cache.constant.system.MenuCacheConstant.SYS_ROLE_MENU;

/**
 * <p>
 * 系统菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:05:44
 */
@Service
@Slf4j
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Override
    public List<SysMenuResponse> querySystemMenu(SysMenuRequest request) {
        String name = request.getName();
        Integer appFrom = request.getAppFrom();
        List<String> menuIds = request.getMenuIds();
        LambdaQueryWrapper<SysMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysMenu::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .eq(appFrom != null, SysMenu::getAppFrom, appFrom)
                .in(CollectionUtils.isNotEmpty(menuIds), SysMenu::getId, menuIds)
                .like(StringUtils.isNotEmpty(name), SysMenu::getName, name);
        List<SysMenu> sysMenus = this.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(sysMenus)){
            return null;
        }
        // 根据id排序，防止前端那边展示菜单顺序错乱
        return sysMenus.stream().sorted(Comparator.comparing(SysMenu::getId))
                .map(sysMenu -> {
            SysMenuResponse sysMenuResponse = new SysMenuResponse();
            BeanUtils.copyProperties(sysMenu, sysMenuResponse);
            sysMenuResponse.setId(String.valueOf(sysMenu.getId()));
            sysMenuResponse.setParentId(sysMenu.getParentId() == null ? null : String.valueOf(sysMenu.getParentId()));
            return sysMenuResponse;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> saveOrUpdateSystemMenu(SysMenuRequest request) {
        // 校验当前角色权限，非超管不允许调用
        SystemUserResponse systemUserResponse = userCacheUtil.getSystemUserInfo(StpUtil.getTokenValue());
        userCacheUtil.checkUserAdmin(systemUserResponse);
        // 保存对应菜单
        SysMenu sysMenu = new SysMenu();
        BeanUtils.copyProperties(request, sysMenu);
        // 判断是创建还是更新
        try {
            if(request.getId() != null){
                sysMenu.setUpdatedBy(String.valueOf(systemUserResponse.getId()));
                this.getBaseMapper().updateById(sysMenu);
                // 还需要给已经关该菜单的角色，重置一下缓存，防止查到修改前的菜单数据，等后续此类角色用户重新登录时，就会补充最新的关联菜单缓存
                LambdaQueryWrapper<SysRoleMenu> roleMenuLambdaQueryWrapper = new LambdaQueryWrapper<>();
                roleMenuLambdaQueryWrapper.eq(SysRoleMenu::getMenuId, request.getId());
                List<SysRoleMenu> sysRoleMenus = sysRoleMenuMapper.selectList(roleMenuLambdaQueryWrapper);
                // 构造所有需要删除的前缀
                List<String> roleWithMenuKeys = sysRoleMenus.stream().map(sysRoleMenu -> SYS_ROLE_MENU + sysRoleMenu.getRoleId())
                        .collect(Collectors.toList());
                redisJsonUtils.deleteBatch(roleWithMenuKeys);
            }else {
                sysMenu.setCreatedBy(String.valueOf(systemUserResponse.getId()));
                this.getBaseMapper().insert(sysMenu);
            }
        } catch (Exception e) {
            throw new RuntimeException("菜单更新失败，请稍后重试");
        }
        return Result.ok();
    }
}
