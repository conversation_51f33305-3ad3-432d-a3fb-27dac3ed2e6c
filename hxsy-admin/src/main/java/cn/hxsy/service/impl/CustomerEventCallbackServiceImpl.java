package cn.hxsy.service.impl;

import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.user.feign.vx.QyAppClient;
import cn.hxsy.base.constant.qyWechat.secretApp.QyWechatConfigType;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.enums.WeComAddStatusEnum;
import cn.hxsy.base.request.QyCallBackRequest;
import cn.hxsy.base.util.WecomXmlUtil;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.datasource.model.entity.Customer;
import cn.hxsy.datasource.model.entity.CustomerWecomBinding;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.service.*;
import cn.hxsy.base.weixin.AesException;
import cn.hxsy.base.weixin.WXBizMsgCrypt;
import cn.hxsy.service.qy.QyAuthService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Service
@Slf4j
public class CustomerEventCallbackServiceImpl implements CustomerEventCallbackService {

    @Autowired
    private CompanyQyRelationService companyQyRelationService;

    @Autowired
    private SystemUserQyRelationService systemUserQyRelationService;

    @Autowired
    private QyAuthService qyAuthService;

    @Autowired
    private CustomerWecomBindingService customerWecomBindingService;

    @Autowired
    private QyAppClient qyAppClient;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerBehaviorService customerBehaviorService;

    /**
     * 企业客户事件ChangeType处理函数映射 Event 为 change_external_contact
     */
    private final Map<String, Function<QyCallBackRequest, String>> CUSTOMER_EVENT_FUNCTION_MAP = ImmutableMap.<String, Function<QyCallBackRequest, String>>builder()
            .put("add_external_contact", this::addExternalContact) // 添加企业客户事件
            .put("edit_external_contact", this::editExternalContact) // 编辑企业客户事件
            .put("add_half_external_contact", this::addHalfExternalContact) // 外部联系人免验证添加成员事件
            .put("del_external_contact", this::delExternalContact) // 删除企业客户事件
            .put("del_follow_user", this::delFollowUser) // 删除跟进成员事件
            .put("transfer_fail", this::transferFail) // 客户接替失败事件
            .build();
    /**
     * 企业客户群事件ChangeType处理函数映射 Event 为 change_external_chat
     */
    private final Map<String, Function<QyCallBackRequest, String>> CUSTOMER_GROUP_EVENT_FUNCTION_MAP = ImmutableMap.<String, Function<QyCallBackRequest, String>>builder()
            .put("create", this::createGroup) // 客户群创建事件
            .put("update", this::updateGroup) // 客户群变更事件
            .put("dismiss", this::dismissGroup) // 客户群解散事件
            .build();

    /**
     * 企业客户标签事件ChangeType处理函数映射 Event 为 change_external_tag
     */
    private final Map<String, Function<QyCallBackRequest, String>> CUSTOMER_TAG_EVENT_FUNCTION_MAP = ImmutableMap.<String, Function<QyCallBackRequest, String>>builder()
            .put("create", this::createCustomerTag) // 新增客户标签事件
            .put("update", this::updateCustomerTag) // 更新客户标签事件
            .put("delete", this::deleteCustomerTag) // 删除客户标签事件
            .put("shuffle", this::shuffleCustomerTag) // 标签重排事件
            .build();

    /**
     * description : 客户变更统一回调（POST）
     * @title: handleCustomerEvent
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: xml
     * <AUTHOR>
     * @date 2025/6/29 23:10
     * @return String
     */
    @Override
    public String handleCustomerEvent(String event, String changeType, QyCallBackRequest qyCallBackRequest) {
        switch (event) {
            case "change_external_contact":
                return this.executeCustomerEvent(CUSTOMER_EVENT_FUNCTION_MAP, changeType, qyCallBackRequest);
            case "change_external_chat":
                return this.executeCustomerEvent(CUSTOMER_GROUP_EVENT_FUNCTION_MAP, changeType, qyCallBackRequest);
            case "change_external_tag":
                return this.executeCustomerEvent(CUSTOMER_TAG_EVENT_FUNCTION_MAP, changeType, qyCallBackRequest);
            default:
                log.info("收到未知类型回调:{}", qyCallBackRequest);
                return "success";
        }
    }

    /**
     * description : 统一执行事件
     * @title: executeCustomerEvent
     * @param: customerEventMap
     * @param: changeType
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 23:29
     * @return String
     */
    private String executeCustomerEvent(Map<String, Function<QyCallBackRequest, String>> customerEventMap, String changeType, QyCallBackRequest qyCallBackRequest) {
        Function<QyCallBackRequest, String> func = customerEventMap.get(changeType);
        if (ObjectUtils.isNotEmpty(func)) {
            return func.apply(qyCallBackRequest);
        } else {
            return this.defaultHandle(qyCallBackRequest);
        }
    }

    /**
     * 添加企业客户事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String addExternalContact(QyCallBackRequest qyCallBackRequest) {
        log.info("处理添加企业客户事件: {}", qyCallBackRequest.toString());
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （整型）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_contact
        //ChangeType	此时固定为add_external_contact
        //UserID	企业服务人员的UserID
        //ExternalUserID	外部联系人的userid，注意不是企业成员的账号
        //State	添加此用户的「联系我」方式配置的state参数，或在获客链接中指定的customer_channel参数，可用于识别添加此用户的渠道
        //WelcomeCode	欢迎语code，可用于发送欢迎语
        this.saveCustomerWecomBinding(qyCallBackRequest);
        return "success";
    }

    /**
     * 保存企微中客户和销售的信息
     * @param qyCallBackRequest
     */
    private void saveCustomerWecomBinding(QyCallBackRequest qyCallBackRequest) {
        Element element = qyCallBackRequest.getElement();
        String corpId = WecomXmlUtil.getNodeText(element, "ToUserName");
        String userId = WecomXmlUtil.getNodeText(element, "UserID");
        String externalUserId = WecomXmlUtil.getNodeText(element, "ExternalUserID");
        String state = WecomXmlUtil.getNodeText(element, "State");
        String welcomeCode = WecomXmlUtil.getNodeText(element, "WelcomeCode");
        // 1. 根据externalUserId查询pendingId
        JSONObject requestJson = new JSONObject();
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyAccessToken(corpId);
        String accessToken = qyAccessToken.getAccess_token();
        requestJson.put("external_userid", externalUserId);
        // 查询业务人员的信息
        LambdaQueryWrapper<SystemUserQyRelation> userQyRelationLambdaQueryWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
        userQyRelationLambdaQueryWrapper.eq(SystemUserQyRelation::getQyUserId, userId)
                .eq(SystemUserQyRelation::getCorpId, corpId);
        SystemUserQyRelation systemUserQyRelation = systemUserQyRelationService.getOne(userQyRelationLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(systemUserQyRelation)) {
            log.error("未找到业务人员信息，externalUserId:{}, userId:{}", externalUserId, userId);
            return;
        }
        // 业务人员id
        String systemUserId = systemUserQyRelation.getSystemUserId();
        // 先判断external_userid和企业用户的业务id绑定关系是否存在
        LambdaQueryWrapper<CustomerWecomBinding> queryWrapper = Wrappers.lambdaQuery(CustomerWecomBinding.class);
        queryWrapper.eq(CustomerWecomBinding::getExternalUserid, externalUserId)
                .eq(CustomerWecomBinding::getSalesUserid, userId)
                .eq(CustomerWecomBinding::getSalesId, systemUserId)
                .eq(CustomerWecomBinding::getCorpId, corpId)
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        CustomerWecomBinding customerWecomBinding = customerWecomBindingService.getOne(queryWrapper);
        // 不存在则查询pendingId
        if (ObjectUtils.isEmpty(customerWecomBinding)) {
            customerWecomBinding = new CustomerWecomBinding();
            JSONObject responseJson = qyAppClient.externalUseridToPendingId(accessToken, requestJson);
            log.info("根据externalUserId查询pendingId response:{}", responseJson.toJSONString());
            JSONArray result = responseJson.getJSONArray("result");
            // 2. 根据pendingId查询是否存在客户信息 进行关联数据保存 或者 预先绑定 externalUserId和企业用户的userId
            if (ObjectUtils.isNotEmpty(result)) {
                JSONObject jsonObject = result.getJSONObject(0);
                String pendingId = jsonObject.getString("pending_id");
                customerWecomBinding.setPendingId(pendingId);
            }
        }
        customerWecomBinding.setSalesId(Long.valueOf(systemUserId));
        customerWecomBinding.setExternalUserid(externalUserId);
        customerWecomBinding.setSalesUserid(userId);
        customerWecomBinding.setState(state);
        customerWecomBinding.setWelcomeCode(welcomeCode);
        customerWecomBinding.setCorpId(corpId);
        customerWecomBindingService.saveInfoByCallback(customerWecomBinding);
    }

    /**
     * 编辑企业客户事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String editExternalContact(QyCallBackRequest qyCallBackRequest) {
        log.info("处理编辑企业客户事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （整型）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_contact
        //ChangeType	此时固定为edit_external_contact
        //UserID	企业服务人员的UserID
        //ExternalUserID	外部联系人的userid，注意不是企业成员的账号
        return "success";
    }

    /**
     * 免验证添加企业客户事件（无需验证自动成为好友）
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String addHalfExternalContact(QyCallBackRequest qyCallBackRequest) {
        log.info("处理免验证添加企业客户事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （整型）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_contact
        //ChangeType	此时固定为add_half_external_contact
        //UserID	企业服务人员的UserID
        //ExternalUserID	外部联系人的userid，注意不是企业成员的账号
        //State	添加此用户的「联系我」方式配置的state参数，或在获客链接中指定的customer_channel参数，可用于识别添加此用户的渠道
        //WelcomeCode	欢迎语code，可用于发送欢迎语
        this.saveCustomerWecomBinding(qyCallBackRequest);
        return "success";
    }

    /**
     * 删除企业客户事件(删除外部联系人时)
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String delExternalContact(QyCallBackRequest qyCallBackRequest) {
        log.info("处理删除企业客户事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （整型）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_contact
        //ChangeType	此时固定为del_external_contact
        //UserID	企业服务人员的UserID
        //ExternalUserID	外部联系人的userid，注意不是企业成员的账号
        //Source	删除客户的操作来源，DELETE_BY_TRANSFER表示此客户是因在职继承自动被转接成员删除
        Element element = qyCallBackRequest.getElement();
        String externalUserId = WecomXmlUtil.getNodeText(element, "ExternalUserID");
        String userID = WecomXmlUtil.getNodeText(element, "UserID");
        CustomerWecomBinding customerWecomBinding = new CustomerWecomBinding();
        customerWecomBinding.setExternalUserid(externalUserId);
        customerWecomBinding.setSalesUserid(userID);
        customerWecomBindingService.deleteCustomerWecomBinding(customerWecomBinding);
        return "success";
    }

    /**
     * 删除跟进成员事件(被外部联系人删除时)
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String delFollowUser(QyCallBackRequest qyCallBackRequest) {
        log.info("处理删除跟进成员事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （整型）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_contact
        //ChangeType	此时固定为del_follow_user
        //UserID	企业服务人员的UserID
        //ExternalUserID	外部联系人的userid，注意不是企业成员的账号
        Element element = qyCallBackRequest.getElement();
        String corpId = WecomXmlUtil.getNodeText(element, "ToUserName");
        String externalUserId = WecomXmlUtil.getNodeText(element, "ExternalUserID");
        String userID = WecomXmlUtil.getNodeText(element, "UserID");
        CustomerWecomBinding customerWecomBinding = new CustomerWecomBinding();
        customerWecomBinding.setExternalUserid(externalUserId);
        customerWecomBinding.setSalesUserid(userID);
        customerWecomBinding.setCorpId(corpId);
        customerWecomBindingService.deleteCustomerWecomBinding(customerWecomBinding);
        // 查询是否还存在其他销售跟进该客户 如果不存在则更新该客户添加企微的状态
        LambdaQueryWrapper<CustomerWecomBinding> queryWrapper = Wrappers.lambdaQuery(CustomerWecomBinding.class);
        queryWrapper.eq(CustomerWecomBinding::getExternalUserid, externalUserId)
                .eq(CustomerWecomBinding::getCorpId, corpId)
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        int count = customerWecomBindingService.count(queryWrapper);
        if (count == 0) {
            // 更新客户添加企微状态
            customerService.updateWecomStatus(customerWecomBinding.getCustomerId(), WeComAddStatusEnum.NOT_ADDED.getCode());
        }

        // TODO 记录删除企微行为轨迹
        return "success";
    }

    /**
     * 客户接替失败事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String transferFail(QyCallBackRequest qyCallBackRequest) {
        log.info("处理客户接替失败事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （整型）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_contact
        //ChangeType	此时固定为transfer_fail
        //FailReason	接替失败的原因, customer_refused-客户拒绝， customer_limit_exceed-接替成员的客户数达到上限
        //UserID	接替失败的企业服务人员的UserID
        //ExternalUserID	外部联系人的userid，注意不是企业成员的账号
        return "success";
    }

    /**
     * 客户群创建事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String createGroup(QyCallBackRequest qyCallBackRequest) {
        log.info("客户群创建事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （unix时间戳）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_chat
        //ChatId	群ID
        //ChangeType	此时固定为create
        return "success";
    }

    /**
     * 客户群变更事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String updateGroup(QyCallBackRequest qyCallBackRequest) {
        log.info("客户群变更事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （unix时间戳）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_chat
        //ChatId	群ID
        //ChangeType	此时固定为update
        //UpdateDetail	变更详情。目前有以下几种： add_member : 成员入群 del_member : 成员退群 change_owner : 群主变更 change_name : 群名变更 change_notice : 群公告变更
        //JoinScene	当是成员入群时有值。表示成员的入群方式
        //0 - 由成员邀请入群（包括直接邀请入群和通过邀请链接入群）
        //3 - 通过扫描群二维码入群
        //QuitScene	当是成员退群时有值。表示成员的退群方式
        //0 - 自己退群
        //1 - 群主/群管理员移出
        //MemChangeCnt	当是成员入群或退群时有值。表示成员变更数量
        //MemChangeList	当是成员入群或退群时有值。变更的成员列表
        //LastMemVer	当是成员入群或退群时有值。 变更前的群成员版本号
        //CurMemVer	当是成员入群或退群时有值。变更后的群成员版本号

        try {
            Element element = qyCallBackRequest.getElement();
            String corpId = WecomXmlUtil.getNodeText(element, "ToUserName");
            String chatId = WecomXmlUtil.getNodeText(element, "ChatId");
            String updateDetail = WecomXmlUtil.getNodeText(element, "UpdateDetail");
            log.info("群变更事件详情: corpId={}, chatId={}, updateDetail={}", corpId, chatId, updateDetail);

            // 只处理成员入群和退群事件
            if ("add_member".equals(updateDetail)) {
                handleMemberJoinGroup(element, corpId, chatId);
            } else if ("del_member".equals(updateDetail)) {
                handleMemberExitGroup(element, corpId, chatId);
            } else {
                log.info("忽略群变更事件: updateDetail={}", updateDetail);
            }
        } catch (Exception e) {
            log.error("处理客户群变更事件异常: {}", e.getMessage(), e);
        }
        return "success";
    }

    /**
     * 处理成员入群事件
     */
    private void handleMemberJoinGroup(Element element, String corpId, String chatId) {
        try {
            String joinSceneStr = WecomXmlUtil.getNodeText(element, "JoinScene");
            Integer joinScene = ObjectUtils.isNotEmpty(joinSceneStr) ? Integer.valueOf(joinSceneStr) : null;

            log.info("成员入群事件: corpId={}, chatId={}, joinScene={}", corpId, chatId, joinScene);

            // 解析MemChangeList中的Item元素
            NodeList memChangeList = element.getElementsByTagName("MemChangeList");
            if (memChangeList.getLength() > 0) {
                Element memChangeListElement = (Element) memChangeList.item(0);
                NodeList itemList = memChangeListElement.getElementsByTagName("Item");

                log.info("成员变更列表数量: {}", itemList.getLength());

                for (int i = 0; i < itemList.getLength(); i++) {
                    Node itemNode = itemList.item(i);
                    String externalUserId = itemNode.getTextContent();

                    log.info("处理入群成员: externalUserId={}", externalUserId);

                    if (StringUtils.isNotBlank(externalUserId)) {
                        // 根据external_userid查找客户信息并记录行为
                        recordJoinGroupBehavior(corpId, chatId, externalUserId, joinScene);
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理成员入群事件异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理成员退群事件
     */
    private void handleMemberExitGroup(Element element, String corpId, String chatId) {
        try {
            String quitSceneStr = WecomXmlUtil.getNodeText(element, "QuitScene");
            Integer quitScene = ObjectUtils.isNotEmpty(quitSceneStr) ? Integer.valueOf(quitSceneStr) : null;

            log.info("成员退群事件: corpId={}, chatId={}, quitScene={}", corpId, chatId, quitScene);

            // 解析MemChangeList中的Item元素
            NodeList memChangeList = element.getElementsByTagName("MemChangeList");
            if (memChangeList.getLength() > 0) {
                Element memChangeListElement = (Element) memChangeList.item(0);
                NodeList itemList = memChangeListElement.getElementsByTagName("Item");

                log.info("成员变更列表数量: {}", itemList.getLength());

                for (int i = 0; i < itemList.getLength(); i++) {
                    Node itemNode = itemList.item(i);
                    String externalUserId = itemNode.getTextContent();

                    log.info("处理退群成员: externalUserId={}", externalUserId);

                    if (StringUtils.isNotBlank(externalUserId)) {
                        // 根据external_userid查找客户信息并记录行为
                        recordExitGroupBehavior(corpId, chatId, externalUserId, quitScene);
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理成员退群事件异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 记录加入群组行为轨迹
     */
    private void recordJoinGroupBehavior(String corpId, String chatId, String externalUserId, Integer joinScene) {
        try {
            // 根据external_userid查找客户绑定信息
            LambdaQueryWrapper<CustomerWecomBinding> bindingWrapper = Wrappers.lambdaQuery(CustomerWecomBinding.class);
            bindingWrapper.eq(CustomerWecomBinding::getCorpId, corpId)
                         .eq(CustomerWecomBinding::getExternalUserid, externalUserId);
            List<CustomerWecomBinding> bindings = customerWecomBindingService.list(bindingWrapper);

            if (ObjectUtils.isNotEmpty(bindings)) {
                CustomerWecomBinding binding = bindings.get(0);
                // 获取企业信息
                LambdaQueryWrapper<CompanyQyRelation> companyWrapper = Wrappers.lambdaQuery(CompanyQyRelation.class);
                companyWrapper.eq(CompanyQyRelation::getCorpId, corpId);
                CompanyQyRelation companyQyRelation = companyQyRelationService.getOne(companyWrapper);

                if (ObjectUtils.isNotEmpty(companyQyRelation)) {
                    // 获取销售用户信息
                    LambdaQueryWrapper<SystemUserQyRelation> userWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
                    userWrapper.eq(SystemUserQyRelation::getSystemUserId, String.valueOf(binding.getSalesId()))
                              .eq(SystemUserQyRelation::getCorpId, corpId);
                    SystemUserQyRelation userQyRelation = systemUserQyRelationService.getOne(userWrapper);

                    String employeeName = ObjectUtils.isNotEmpty(userQyRelation) ? userQyRelation.getQyName() : "未知员工";
                    String employeeWeworkName = ObjectUtils.isNotEmpty(userQyRelation) ? userQyRelation.getQyUserId() : "未知";

                    // 记录加入群组行为
                    boolean success = customerBehaviorService.saveJoinGroupChat(
                        binding.getCustomerId(),
                        Long.valueOf(companyQyRelation.getCompanyId()),
                        corpId,
                        chatId,
                        "群聊", // 群名称，可以后续通过API获取
                        employeeName,
                        employeeWeworkName,
                        joinScene
                    );

                    log.info("记录客户加入群组行为: customerId={}, chatId={}, joinScene={}, success={}",
                            binding.getCustomerId(), chatId, joinScene, success);
                }
            } else {
                log.warn("未找到客户绑定信息: corpId={}, externalUserId={}", corpId, externalUserId);
            }

        } catch (Exception e) {
            log.error("记录加入群组行为异常: corpId={}, chatId={}, externalUserId={}, error={}",
                    corpId, chatId, externalUserId, e.getMessage(), e);
        }
    }

    /**
     * 记录退出群组行为轨迹
     */
    private void recordExitGroupBehavior(String corpId, String chatId, String externalUserId, Integer quitScene) {
        try {
            // 根据external_userid查找客户绑定信息
            LambdaQueryWrapper<CustomerWecomBinding> bindingWrapper = Wrappers.lambdaQuery(CustomerWecomBinding.class);
            bindingWrapper.eq(CustomerWecomBinding::getCorpId, corpId)
                         .eq(CustomerWecomBinding::getExternalUserid, externalUserId);
            List<CustomerWecomBinding> bindings = customerWecomBindingService.list(bindingWrapper);

            if (ObjectUtils.isNotEmpty(bindings)) {
                CustomerWecomBinding binding = bindings.get(0);
                // 获取企业信息
                LambdaQueryWrapper<CompanyQyRelation> companyWrapper = Wrappers.lambdaQuery(CompanyQyRelation.class);
                companyWrapper.eq(CompanyQyRelation::getCorpId, corpId);
                CompanyQyRelation companyQyRelation = companyQyRelationService.getOne(companyWrapper);

                if (ObjectUtils.isNotEmpty(companyQyRelation)) {
                    // 获取销售用户信息
                    LambdaQueryWrapper<SystemUserQyRelation> userWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
                    userWrapper.eq(SystemUserQyRelation::getSystemUserId, String.valueOf(binding.getSalesId()))
                              .eq(SystemUserQyRelation::getCorpId, corpId);
                    SystemUserQyRelation userQyRelation = systemUserQyRelationService.getOne(userWrapper);

                    String employeeName = ObjectUtils.isNotEmpty(userQyRelation) ? userQyRelation.getQyName() : "未知员工";
                    String employeeWeworkName = ObjectUtils.isNotEmpty(userQyRelation) ? userQyRelation.getQyUserId() : "未知";

                    // 记录退出群组行为
                    boolean success = customerBehaviorService.saveExitGroupChat(
                        binding.getCustomerId(),
                        Long.valueOf(companyQyRelation.getCompanyId()),
                        corpId,
                        chatId,
                        "群聊", // 群名称，可以后续通过API获取
                        employeeName,
                        employeeWeworkName,
                        quitScene
                    );

                    log.info("记录客户退出群组行为: customerId={}, chatId={}, quitScene={}, success={}",
                            binding.getCustomerId(), chatId, quitScene, success);
                }
            } else {
                log.warn("未找到客户绑定信息: corpId={}, externalUserId={}", corpId, externalUserId);
            }

        } catch (Exception e) {
            log.error("记录退出群组行为异常: corpId={}, chatId={}, externalUserId={}, error={}",
                    corpId, chatId, externalUserId, e.getMessage(), e);
        }
    }

    /**
     * 客户群解散事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String dismissGroup(QyCallBackRequest qyCallBackRequest) {
        log.info("客户群解散事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （unix时间戳）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_chat
        //ChatId	群ID
        //ChangeType	此时固定为dismiss
        return "success";
    }

    /**
     * 企业客户标签创建事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String createCustomerTag(QyCallBackRequest qyCallBackRequest) {
        log.info("企业客户标签创建事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （unix时间戳）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_tag
        //Id	标签或标签组的ID
        //TagType	创建标签时，此项为tag，创建标签组时，此项为tag_group
        //ChangeType	此时固定为create
        //StrategyId	标签或标签组所属的规则组id，只回调给“客户联系”应用
        return "success";
    }

    /**
     * 企业客户标签变更事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String updateCustomerTag(QyCallBackRequest qyCallBackRequest) {
        log.info("企业客户标签变更事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （unix时间戳）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_tag
        //Id	标签或标签组的ID
        //TagType	变更标签时，此项为tag，变更标签组时，此项为tag_group
        //ChangeType	此时固定为update
        //StrategyId	标签或标签组所属的规则组id，只回调给“客户联系”应用
        return "success";
    }

    /**
     * 企业客户标签删除事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String deleteCustomerTag(QyCallBackRequest qyCallBackRequest) {
        log.info("企业客户标签删除事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （unix时间戳）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_tag
        //Id	标签或标签组的ID
        //TagType	删除标签时，此项为tag，删除标签组时，此项为tag_group
        //ChangeType	此时固定为delete
        //StrategyId	标签或标签组所属的规则组id，只回调给“客户联系”应用
        return "success";
    }

    /**
     * 企业客户标签重排事件
     * 官方文档：https://developer.work.weixin.qq.com/document/path/96361
     * @param qyCallBackRequest 回调请求体，包含解密后的XML Element
     * @return 处理结果字符串
     */
    private String shuffleCustomerTag(QyCallBackRequest qyCallBackRequest) {
        log.info("企业客户标签重排事件: {}", qyCallBackRequest.toString());
        // TODO: 业务逻辑
        //ToUserName	企业微信CorpID
        //FromUserName	此事件该值固定为sys，表示该消息由系统生成
        //CreateTime	消息创建时间 （unix时间戳）
        //MsgType	消息的类型，此时固定为event
        //Event	事件的类型，此时固定为change_external_tag
        //Id	标签组的id，表示只有此标签组内的标签发生了重排，如果为空，则表示全部标签组顺序都发生了变化
        //StrategyId	规则组id，如果修改了规则组标签的顺序，则回调时会带上此标签所属规则组的id
        //ChangeType	此时固定为shuffle
        return "success";
    }

    /**
     * description : 默认处理
     * @title: defaultHandle
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 21:48
     * @return String
     */
    private String defaultHandle(QyCallBackRequest qyCallBackRequest) {
        log.warn("未找到对应的回调处理函数, qyCallBackRequest: {}", qyCallBackRequest.toString());
        return "unsupported";
    }


} 