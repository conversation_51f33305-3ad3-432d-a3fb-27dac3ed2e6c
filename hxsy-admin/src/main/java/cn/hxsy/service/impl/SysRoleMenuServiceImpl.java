package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.system.request.SysMenuRequest;
import cn.hxsy.api.system.request.SysRoleMenuRequest;
import cn.hxsy.api.system.response.SysRoleMenuResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.SysRoleMenuMapper;
import cn.hxsy.service.SysMenuService;
import cn.hxsy.service.SysRoleMenuService;
import cn.hxsy.api.system.response.SysMenuResponse;
import cn.hxsy.datasource.model.entity.SysMenu;
import cn.hxsy.datasource.model.entity.SysRoleMenu;
import cn.hxsy.utils.UserCacheUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static cn.hxsy.base.exception.system.code.SystemUserErrorCode.OPERATION_NOT_PERMISSION;
import static cn.hxsy.cache.constant.system.MenuCacheConstant.SYS_ROLE_MENU;

/**
 * <p>
 * 角色和菜单关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:06:31
 */
@Service
@Slf4j
public class SysRoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenu> implements SysRoleMenuService {

    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Override
    public List<SysMenuResponse> getMenuByRoleIds(List<Integer> roleIds) {
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysRoleMenu::getRoleId, roleIds);
        List<SysRoleMenu> sysRoleMenus = baseMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(sysRoleMenus)){
            return null;
        }
        List<Integer> menuIds = sysRoleMenus.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        LambdaQueryWrapper<SysMenu> menuWrapper = new LambdaQueryWrapper<>();
        menuWrapper.in(SysMenu::getId, menuIds);
        List<SysMenu> sysMenus = sysMenuService.list(menuWrapper);
        if(CollectionUtils.isEmpty(sysMenus)){
            return null;
        }
        // 防止关联多角色下菜单重复，做一个去重
        return sysMenus.stream().distinct().map(sysMenu -> {
            SysMenuResponse sysMenuResponse = new SysMenuResponse();
            BeanUtils.copyProperties(sysMenu, sysMenuResponse);
            sysMenuResponse.setId(String.valueOf(sysMenu.getId()));
            sysMenuResponse.setParentId(sysMenu.getParentId() == null ? null : String.valueOf(sysMenu.getParentId()));
            return sysMenuResponse;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SysMenuResponse> getMenuByRoleId(Integer roleId) {
        // 判断是否已有角色已授权菜单缓存，没有则查询后需要补充
        List<SysMenuResponse> list = redisJsonUtils.getList(SYS_ROLE_MENU + roleId, SysMenuResponse.class);
        if(CollectionUtils.isNotEmpty(list)){
            return list;
        }
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, roleId);
        List<SysRoleMenu> sysRoleMenus = baseMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(sysRoleMenus)){
            return null;
        }
        List<Integer> menuIds = sysRoleMenus.stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        LambdaQueryWrapper<SysMenu> menuWrapper = new LambdaQueryWrapper<>();
        menuWrapper.eq(SysMenu::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .in(SysMenu::getId, menuIds);
        List<SysMenu> sysMenus = sysMenuService.list(menuWrapper);
        if(CollectionUtils.isEmpty(sysMenus)){
            return null;
        }
        // 根据id排序，防止前端那边展示菜单顺序错乱
        List<SysMenuResponse> sysMenuResponses = sysMenus.stream().sorted(Comparator.comparing(SysMenu::getId))
                .map(sysMenu -> {
            SysMenuResponse sysMenuResponse = new SysMenuResponse();
            BeanUtils.copyProperties(sysMenu, sysMenuResponse);
            sysMenuResponse.setId(String.valueOf(sysMenu.getId()));
            sysMenuResponse.setParentId(sysMenu.getParentId() == null ? null : String.valueOf(sysMenu.getParentId()));
            return sysMenuResponse;
        }).collect(Collectors.toList());
        redisJsonUtils.set(SYS_ROLE_MENU + roleId, sysMenuResponses);
        return sysMenuResponses;
    }

    @Override
    public SysRoleMenuResponse getRoleHasMenu(SysRoleMenuRequest request) {
        // 校验当前角色权限，非超管不允许调用
        SystemUserResponse systemUserResponse = userCacheUtil.getSystemUserInfo(StpUtil.getTokenValue());
        userCacheUtil.checkUserAdmin(systemUserResponse);
        SysRoleMenuResponse sysRoleMenuResponse = new SysRoleMenuResponse();
        // 查询当前角色已关联的菜单
        List<SysMenuResponse> roleHasMenu = this.getMenuByRoleId(Integer.valueOf(request.getRoleId()));
        sysRoleMenuResponse.setAuthAppInfos(roleHasMenu);
        // 查询当前系统的全部菜单，用来过滤出当前角色未关联的菜单
        List<SysMenuResponse> sysMenuResponses = sysMenuService.querySystemMenu(new SysMenuRequest());
        // 如果当前用户尚未分配角色，那后续就不需要过滤了
        if(CollectionUtils.isEmpty(roleHasMenu)){
            sysRoleMenuResponse.setAllSysAppInfos(sysMenuResponses);
        }else {
            List<String> collect = roleHasMenu.stream().map(SysMenuResponse::getId).collect(Collectors.toList());
            // 查询当前角色未关联的菜单，即查询全部菜单，过滤掉当前角色已分配菜单
            List<SysMenuResponse> noAuthSysMenuResponses = sysMenuResponses.stream().filter(sysMenuResponse -> !collect.contains(sysMenuResponse.getId())).collect(Collectors.toList());
            sysRoleMenuResponse.setAllSysAppInfos(noAuthSysMenuResponses);
        }
        return sysRoleMenuResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> saveRoleMenu(SysRoleMenuRequest request) {
        // 校验当前角色权限，非超管不允许调用
        SystemUserResponse systemUserResponse = userCacheUtil.getSystemUserInfo(StpUtil.getTokenValue());
        userCacheUtil.checkUserAdmin(systemUserResponse);
        // 删除当前角色已关联的菜单
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, Integer.valueOf(request.getRoleId()));
        baseMapper.delete(wrapper);
        // 保存当前角色关联的菜单，并重置对应缓存
        ArrayList<SysRoleMenu> sysRoleMenus = new ArrayList<>();
        List<String> menuIds = request.getMenuIds();
        menuIds.forEach(menuId -> {
            SysRoleMenu sysRoleMenu = new SysRoleMenu();
            sysRoleMenu.setRoleId(Integer.valueOf(request.getRoleId()));
            sysRoleMenu.setMenuId(Integer.valueOf(menuId));
            sysRoleMenu.setCreatedBy(String.valueOf(systemUserResponse.getId()));
            sysRoleMenus.add(sysRoleMenu);
        });
        SysMenuRequest sysMenuRequest = new SysMenuRequest();
        sysMenuRequest.setMenuIds(menuIds);
        List<SysMenuResponse> sysMenuResponses = sysMenuService.querySystemMenu(sysMenuRequest);
        // 重置对应角色具有的菜单缓存
        redisJsonUtils.set(SYS_ROLE_MENU + request.getRoleId(), sysMenuResponses);
        this.saveBatch(sysRoleMenus);
        return Result.ok();
    }
}
