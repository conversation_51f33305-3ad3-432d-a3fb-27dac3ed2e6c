package cn.hxsy.service.impl;

import cn.hxsy.api.system.request.SysUserSelectPermissionRequest;
import cn.hxsy.api.system.response.SysUserSelectPermissionResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.datasource.model.entity.SysUserSelectPermission;
import cn.hxsy.datasource.mapper.SysUserSelectPermissionMapper;
import cn.hxsy.service.SysUserSelectPermissionService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统用户可见查询权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24 12:04:54
 */
@Service
@Slf4j
public class SysUserSelectPermissionServiceImpl extends ServiceImpl<SysUserSelectPermissionMapper, SysUserSelectPermission> implements SysUserSelectPermissionService {

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Override
    public SysUserSelectPermissionResponse getSysUserSelectPermission(SysUserSelectPermissionRequest request) {
        if (request == null || request.getUserId() == null){
            return null;
        }
        LambdaQueryWrapper<SysUserSelectPermission> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserSelectPermission::getUserId, request.getUserId());
//        SysUserSelectPermissionMapper sysUserSelectPermissionMapper = this.getBaseMapper();
        SysUserSelectPermission selectPermission = this.getOne(wrapper);
        if (selectPermission == null){
            return null;
        }
        log.info("查询到用户可见权限信息：{}", selectPermission);
        // 构造返回数据
        SysUserSelectPermissionResponse response = new SysUserSelectPermissionResponse();
        BeanUtils.copyProperties(selectPermission, response);
        response.setId(String.valueOf(selectPermission.getId()));
        response.setUserId(String.valueOf(selectPermission.getUserId()));
        return response;
    }

    @Override
    public Boolean saveOrUpdateSystemSelectPermission(SysUserSelectPermissionRequest request) {
        LambdaUpdateWrapper<SysUserSelectPermission> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SysUserSelectPermission::getUserId, request.getUserId());
        SysUserSelectPermission selectPermission = this.getOne(wrapper);
        // 设置操作人信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        if(selectPermission != null){
            // 通过实体类更新，避开wrapper不走类型转换
            selectPermission.setPerColumnId(request.getPerColumnId());
            selectPermission.setPerCompanyId(request.getPerCompanyId());
            selectPermission.setPerSalesGroupId(request.getPerSalesGroupId());
            selectPermission.setUpdatedBy(systemUserSelfInfo.getId().toString());
            return this.updateById(selectPermission);
        }else {
            SysUserSelectPermission sysUserSelectPermission = new SysUserSelectPermission();
            BeanUtils.copyProperties(request, sysUserSelectPermission);
            sysUserSelectPermission.setCreatedBy(systemUserSelfInfo.getId().toString());
            return this.save(sysUserSelectPermission);
        }
    }
}
