package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.system.request.SysRoleRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.SysRoleMapper;
import cn.hxsy.service.SysRoleService;
import cn.hxsy.api.system.response.SysRoleResponse;
import cn.hxsy.datasource.model.entity.SysRole;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static cn.hxsy.cache.constant.user.CacheConstant.SYSTEM_ROLE_TYPE;

/**
 * <p>
 * 系统角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:04:30
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Override
    public List<SysRoleResponse> getRolesByIds(List<Long> roleIds) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysRole::getId, roleIds);
        List<SysRole> sysRoles = this.baseMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(sysRoles)){
            return null;
        }
        List<SysRoleResponse> collect = sysRoles.stream().map(sysRole -> {
            SysRoleResponse sysRoleResponse = new SysRoleResponse();
            BeanUtils.copyProperties(sysRole, sysRoleResponse);
            // id类型不同，需要手动设置
            sysRoleResponse.setId(String.valueOf(sysRole.getId()));
            return sysRoleResponse;
        }).collect(Collectors.toList());
        return collect;
    }

    @Override
    public SysRoleResponse getRoleById(Integer roleId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getId, roleId);
        SysRole sysRole = this.baseMapper.selectOne(wrapper);
        if(sysRole == null){
            return null;
        }
        SysRoleResponse sysRoleResponse = new SysRoleResponse();
        BeanUtils.copyProperties(sysRole, sysRoleResponse);
        // id类型不同，需要手动设置
        sysRoleResponse.setId(String.valueOf(sysRole.getId()));
        return sysRoleResponse;
    }

    @Override
    public SysRoleResponse getRoleByRoleType(Integer roleType) {
        // 先从缓存中获取
        SysRoleResponse sysRoleCache = redisJsonUtils.get(SYSTEM_ROLE_TYPE + roleType, SysRoleResponse.class);
        if(sysRoleCache != null){
            return sysRoleCache;
        }
        // 没有对应类型角色缓存，再从数据库查询
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleType, roleType);
        List<SysRole> sysRoles = this.baseMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(sysRoles)){
            return null;
        }
        SysRole sysRole = sysRoles.get(0);
        SysRoleResponse sysRoleResponse = new SysRoleResponse();
        BeanUtils.copyProperties(sysRole, sysRoleResponse);
        // id类型不同，需要手动设置
        sysRoleResponse.setId(String.valueOf(sysRole.getId()));
        // 设置对应类型的角色缓存信息
        redisJsonUtils.set(SYSTEM_ROLE_TYPE + roleType, sysRoleResponse);
        return sysRoleResponse;
    }

    @Override
    public Page<SysRole> getSystemRolePage(Integer pageNum, Integer pageSize, SysRoleRequest request) {
        // 获取当前登录用户信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        Integer roleType = systemUserSelfInfo.getRoleType();
        if(roleType == null){
            return new Page<>(pageNum, pageSize);
        }
        // 构造一下分页查询对象
        Page<SysRole> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        // 判断是否需要构造角色名称、角色码值、状态查询
        String roleCode = request.getRoleCode();
        String roleName = request.getRoleName();
        Integer status = request.getStatus();
        Integer userFrom = request.getUserFrom();
        wrapper.eq(status != null, SysRole::getStatus, status)
                .eq(userFrom != null, SysRole::getUserFrom, userFrom)
                .ge(SysRole::getRoleType, roleType) // 只能查询比自己权限等级低的角色
                .like(StringUtils.isNotEmpty(roleCode), SysRole::getRoleCode, roleCode)
                .like(StringUtils.isNotEmpty(roleName), SysRole::getRoleName, roleName);
        Page<SysRole> sysRolePage = this.baseMapper.selectPage(page, wrapper);
        sysRolePage.setTotal(CollectionUtils.isEmpty(sysRolePage.getRecords()) ? 0 : sysRolePage.getRecords().size());
        return sysRolePage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> saveOrUpdateRole(SysRoleRequest request) {
        // 获取操作人信息
        SystemUserResponse systemUserInfo = userCacheUtil.getSystemUserInfo(StpUtil.getTokenValue());
        // 非超管不允许操作
        userCacheUtil.checkUserAdmin(systemUserInfo);
        SysRole sysRole = new SysRole();
        BeanUtils.copyProperties(request, sysRole);
        if(StringUtils.isEmpty(request.getId())){
            sysRole.setCreatedBy(String.valueOf(systemUserInfo.getId()));
            this.save(sysRole);
        }else{
            sysRole.setId(Integer.parseInt(request.getId()));
            sysRole.setStatus(String.valueOf(request.getStatus()));
            sysRole.setUpdatedBy(String.valueOf(systemUserInfo.getId()));
            this.updateById(sysRole);
        }
        return Result.ok();
    }
}
