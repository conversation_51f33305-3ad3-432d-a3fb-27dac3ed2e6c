package cn.hxsy.service.impl;

import cn.hxsy.api.qy.feign.contract.QyWxContactClient;
import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.qy.response.QyWeChatUserResponse;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.qy.response.contact.QyContactResponse;
import cn.hxsy.api.qy.response.contact.QyContactUserResponse;
import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.request.QyProviderTokenRequest;
import cn.hxsy.request.SystemUserBindQyUserRequest;
import cn.hxsy.base.request.SystemUserQyRelationRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.dao.SystemUserQyRelationMapper;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;

import cn.hxsy.response.SystemUserQyRelationResponse;
import cn.hxsy.service.CompanyQyRelationService;
import cn.hxsy.service.SystemUserQyRelationService;
import cn.hxsy.service.SystemUserService;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.service.qy.QyContactService;
import cn.hxsy.utils.UserCacheUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务人员账号与企微信息关联 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Service
@Slf4j
public class SystemUserQyRelationServiceImpl extends ServiceImpl<SystemUserQyRelationMapper, SystemUserQyRelation> implements SystemUserQyRelationService {

    @Resource
    private UserCacheUtil userCacheUtil;

    @Resource
    private CompanyQyRelationService companyQyRelationService;

    @Resource
    private QyContactService qyContactService;

    @Resource
    private SystemUserService systemUserService;

    @Resource
    private QyWxContactClient qyWxContactClient;
    
    @Resource
    private QyAuthService qyAuthService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> saveQyCompanyUser(SystemUserBindQyUserRequest request) {
        // 0、获取到需要同步企微公司对应关联公司，进行公司层级的权限校验
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        String corpId = request.getCorpId();
        if(StringUtils.isEmpty(corpId)){
            throw new RuntimeException("请确认对应同步企业范围");
        }
        LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyQyRelation::getCorpId, corpId);
        List<CompanyQyRelation> companyQyRelations = companyQyRelationService.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(companyQyRelations) || companyQyRelations.size() > 1){
            throw new RuntimeException("当前企微账号在系统中存在多个公司，请联系管理员处理");
        }
        CompanyQyRelation companyQyRelation = companyQyRelations.get(0);
        userCacheUtil.checkUserCompanyAdmin(systemUserSelfInfo, companyQyRelation.getCompanyId());
        // 1、校验通过，查询这个企微在当前系统已经同步的用户信息
        Result<List<SystemUserQyRelation>> systemUserQyRelationResponse = this.querySystemQyUserInner(request);
        if(!systemUserQyRelationResponse.isSuccess()){
            // 1.1、如果查询失败，直接返回失败
            log.info("企微账号：{}，对应员工信息获取失败：{}", request.getCorpId(), systemUserQyRelationResponse.getMsg());
            return Result.error("当前选择企微组织下员工信息获取失败");
        }
        // 1.2、查询成功
        List<SystemUserQyRelation> systemUserQyRelations = systemUserQyRelationResponse.getData();
        // 2、调用企微侧查询该企微目前的员工账号信息，不用管报错或者不成功了，直接在调用处catch了，直接处理数据
        QyProviderTokenRequest qyProviderTokenRequest = new QyProviderTokenRequest();
        qyProviderTokenRequest.setCorpId(corpId);
        List<QyContactUserResponse> qyUserLists = qyContactService.allUserList(qyProviderTokenRequest);
        if(CollectionUtils.isEmpty(qyUserLists)){
            log.info("企微账号：{}，企微侧查询员工为空", request.getCorpId());
            return Result.ok("当前选择企微组织下在企微侧获取员工信息为空");
        }
        // 3、比对当前系统存储与企微返回的员工信息，如果存在则更新，不存在则新增
        List<SystemUserQyRelation> saveResult = new ArrayList<>();
        if(CollectionUtils.isEmpty(systemUserQyRelations)){
            // 3.1、如果当前企微下没有员工信息，直接调用企微侧接口开始查询后批量新增，无需进行比对
            saveResult = qyUserLists.stream().map(qyWeChatUserResponse -> {
                SystemUserQyRelation systemUserQyRelation = new SystemUserQyRelation();
                systemUserQyRelation.setQyUserId(qyWeChatUserResponse.getUserid());
                systemUserQyRelation.setCorpId(request.getCorpId());
                systemUserQyRelation.setQyName(qyWeChatUserResponse.getName());
                systemUserQyRelation.setCreatedAt(LocalDateTime.now());
                systemUserQyRelation.setCreatedBy(systemUserSelfInfo.getAccountId());
                return systemUserQyRelation;
            }).collect(Collectors.toList());
        }else {
            // 3.2、当前企微下已经有同步过员工信息，开始进行比对算法（算个毛啊）
            SystemUserQyRelation systemUserQyRelation = new SystemUserQyRelation();
            systemUserQyRelation.setCorpId(request.getCorpId());
            systemUserQyRelation.setCreatedAt(LocalDateTime.now());
            systemUserQyRelation.setCreatedBy(systemUserSelfInfo.getUserId());
            saveResult = this.compareQyUserWithSystem(systemUserQyRelations, qyUserLists, systemUserQyRelation);
        }
        if(CollectionUtils.isEmpty(saveResult)){
            return Result.ok("当前企微下尚未有新增员工信息");
        }
        boolean b = this.saveBatch(saveResult);
        if (b) {
            return Result.ok();
        }
        return Result.error("企微下员工信息同步失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> bindQyUserToSystemUser(SystemUserBindQyUserRequest request) {
        // 1、校验是否有该公司管理权限
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        userCacheUtil.checkUserCompanyAdmin(systemUserSelfInfo, request.getCompanyId());
        // 2、校验该企业员工是否已经被其他系统用户绑定

        // 3、构造对应关联数据更新对象
        List<SystemUserQyRelationRequest> systemUserQyRelationRequests = request.getSystemUserQyRelationRequest();
        if(CollectionUtils.isEmpty(systemUserQyRelationRequests)){
            return Result.ok();
        }
        Boolean updateFlag = true;
        for (SystemUserQyRelationRequest systemUserQyRelationRequest : systemUserQyRelationRequests) {
            LambdaUpdateWrapper<SystemUserQyRelation> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(SystemUserQyRelation::getCorpId, systemUserQyRelationRequest.getCorpId())
                    .eq(SystemUserQyRelation::getQyUserId, systemUserQyRelationRequest.getQyUserId())
                    .set(SystemUserQyRelation::getSystemUserId, request.getSystemUserId())
                    .set(SystemUserQyRelation::getUpdatedAt, LocalDateTime.now())
                    .set(SystemUserQyRelation::getUpdatedBy, systemUserSelfInfo.getUserId());
            // 防止覆盖其他字段为空，不带上实体类
            int update = this.getBaseMapper().update(null, wrapper);
            if(update <= 0){
                updateFlag = false;
                break;
            }
        }
        if(updateFlag){
            return Result.ok();
        }
        log.info("系统用户：{}，绑定企微下员工信息失败", request.getSystemUserId());
        return Result.error("企微下员工信息绑定失败");
    }

    @Override
    public Result<Object> querySystemQyUser(SystemUserBindQyUserRequest request) {
        // 1、校验是否有该公司管理权限
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        userCacheUtil.checkUserCompanyAdmin(systemUserSelfInfo, request.getCompanyId());
        // 2、检测当前企微是否归属于当前选择的公司

        // 3、开始获取该企微下的员工信息
        if(StringUtils.isEmpty(request.getCorpId())){
            throw new RuntimeException("请确认对应查询企业组织");
        }
        LambdaQueryWrapper<SystemUserQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemUserQyRelation::getCorpId, request.getCorpId());
        // 3.1、前端在查询单个系统用户关联的企微下员工信息时，需要带上系统用户id
        wrapper.eq(StringUtils.isNotEmpty(request.getSystemUserId()), SystemUserQyRelation::getSystemUserId, request.getSystemUserId());
        List<SystemUserQyRelation> systemUserQyRelations = this.getBaseMapper().selectList(wrapper);
        // 4、查询这批企微用户账号，在系统中已经被哪些员工绑定
        if(CollectionUtils.isNotEmpty(systemUserQyRelations)) {
            // 4.1、收集已关联的系统员工信息id作为一个map，用于前端获取绑定员工信息
            List<String> systemUserIds = systemUserQyRelations.stream().map(SystemUserQyRelation::getSystemUserId)
                    .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
            Map<Long, SystemUserPO> systemUserPOMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(systemUserIds)){
                // 4.2、防止当前企微下员工信息没有一个被系统用户关联时，in查询报错
                LambdaQueryWrapper<SystemUserPO> systemUserWrapper = new LambdaQueryWrapper<>();
                systemUserWrapper.in(SystemUserPO::getId, systemUserIds);
                List<SystemUserPO> systemUserPOS = systemUserService.getBaseMapper().selectList(systemUserWrapper);
                if (CollectionUtils.isNotEmpty(systemUserPOS)) {
                    systemUserPOMap = systemUserPOS.stream().collect(Collectors.toMap(SystemUserPO::getId, systemUserPO -> systemUserPO));
                }
            }
            // 4.3、构建响应对象（附带关联的系统用户信息）
            Map<Long, SystemUserPO> finalSystemUserPOMap = systemUserPOMap;
            List<SystemUserQyRelationResponse> collect = systemUserQyRelations.stream().map(systemUserQyRelation -> {
                SystemUserQyRelationResponse systemUserQyRelationResponse = new SystemUserQyRelationResponse();
                BeanUtils.copyProperties(systemUserQyRelation, systemUserQyRelationResponse);
                systemUserQyRelationResponse.setId(String.valueOf(systemUserQyRelation.getId()));
                if (StringUtils.isNotEmpty(systemUserQyRelation.getSystemUserId())) {
                    SystemUserResponse systemUserResponse = new SystemUserResponse();
                    if(!finalSystemUserPOMap.isEmpty()){
                        SystemUserPO systemUserPO = finalSystemUserPOMap.get(Long.parseLong(systemUserQyRelation.getSystemUserId()));
                        BeanUtils.copyProperties(systemUserPO, systemUserResponse);
                        systemUserQyRelationResponse.setSystemUserResponse(systemUserResponse);
                    }
                }
                return systemUserQyRelationResponse;
            }).collect(Collectors.toList());
            return Result.ok(collect);
        }
        // 4.3、不存在已经同步的用户信息、或不存在绑定系统用户数据时，都直接响应查出的企微用户数据即可
        return Result.ok(systemUserQyRelations);
    }

    @Override
    public Result<List<SystemUserQyRelation>> querySystemQyUserInner(SystemUserBindQyUserRequest request) {
        String queryScene = request.getQueryScene();
        LambdaQueryWrapper<SystemUserQyRelation> wrapper = new LambdaQueryWrapper<>();
        if("0".equals(queryScene)){
            // 1、开始获取该企微下的员工信息
            wrapper.eq(SystemUserQyRelation::getCorpId, request.getCorpId());
            List<SystemUserQyRelation> systemUserQyRelations = this.getBaseMapper().selectList(wrapper);
            return Result.ok(systemUserQyRelations);
        }else if("1".equals(queryScene)){
            // 2、用户管理分页查询系统内部员工信息时，带出已关联的企微用户信息
            if(CollectionUtils.isNotEmpty(request.getSystemUserIds())){
                wrapper.in(SystemUserQyRelation::getSystemUserId, request.getSystemUserIds());
                List<SystemUserQyRelation> systemUserQyRelations = this.getBaseMapper().selectList(wrapper);
                if(CollectionUtils.isNotEmpty(systemUserQyRelations)){
                    List<String> corpIds = systemUserQyRelations.stream().map(SystemUserQyRelation::getCorpId).collect(Collectors.toList());
                    // 2、还需要获取一下企微人员归属企微名称
                    Map<String, String> corpNameById = companyQyRelationService.getCorpNameById(corpIds);
                    systemUserQyRelations.stream().forEach(systemUserQyRelation -> {
                        String corpName = corpNameById.get(systemUserQyRelation.getCorpId());
                        systemUserQyRelation.setCorpName(corpName);
                    });
                }
                return Result.ok(systemUserQyRelations);
            }
        }
        return Result.ok(new ArrayList<>());
    }

    /**
    * @description: 比对企微侧员工信息，与当前系统存储的员工信息差异，返回差异部分的员工信息
     * 不做数据校验了，因为之前的同步方法已经做过了
     * @param systemUserQyRelations 当前系统存储该企微下员工信息
     * @param qyUserLists 企微侧查询出该企微下员工信息
     * @param systemUserQyRelation 基础新增对象数据
     * 使用hashset收集双方的userId，因为当前系统不需要管删除的员工，所以以企微侧为主，remove掉当前系统已经存储的userId，剩余的即为新增的
    * @author: xiaQL
    * @date: 2025/6/22 23:13
    */
    private List<SystemUserQyRelation> compareQyUserWithSystem(List<SystemUserQyRelation> systemUserQyRelations,
                                                               List<QyContactUserResponse> qyUserLists,
                                                               SystemUserQyRelation systemUserQyRelation){
        // 1、提取系统侧已存储ID与企微侧查询出ID集合
        Set<String> systemUserSet = systemUserQyRelations.stream().map(SystemUserQyRelation::getQyUserId).collect(Collectors.toSet());
        Set<String> qyUserSet = qyUserLists.stream().map(QyContactUserResponse::getUserid).collect(Collectors.toSet());
        // 2、用企微侧过滤掉系统侧的id，剩余的即为新增的
        qyUserSet.removeAll(systemUserSet);
        // 3、收集一下企微侧id与用户名的map，用于下面构造新增对象时快速获取企微用户名
        Map<String, String> QyIdWithNameMap = qyUserLists.stream().collect(Collectors.toMap(QyContactUserResponse::getUserid, QyContactUserResponse::getName));
        log.info("企微下新增员工信息：{}", qyUserSet);
        // 3、将新增的id，获取到对应新增的企微员工信息，封装成新增对象，返回
        return qyUserSet.stream().map(qyUserId -> {
            SystemUserQyRelation systemUserQyRelationNew = new SystemUserQyRelation();
            BeanUtils.copyProperties(systemUserQyRelation, systemUserQyRelationNew);
            systemUserQyRelationNew.setQyName(QyIdWithNameMap.get(qyUserId));
            systemUserQyRelationNew.setQyUserId(qyUserId);
            return systemUserQyRelationNew;
        }).collect(Collectors.toList());
    }

    /**
     * description : 生成联系我二维码(个人)
     * @title: generateQyContactQrCode
     * @param: request
     * <AUTHOR>
     * @date 2025/7/15 20:30
     * @return boolean
     */
    @Override
    public boolean generateQyContactQrCode(SystemUserQyRelation systemUserQyRelation) {
        //access_token	是	调用接口凭证
        //type	是	联系方式类型,1-单人, 2-多人
        //scene	是	场景，1-在小程序中联系，2-通过二维码联系
        //style	否	在小程序中联系时使用的控件样式，详见附表
        //remark	否	联系方式的备注信息，用于助记，不超过30个字符
        //skip_verify	否	外部客户添加时是否无需验证，默认为true
        //state	否	企业自定义的state参数，用于区分不同的添加渠道，在调用“获取客户详情”时会返回该参数值，不超过30个字符
        //user	否	使用该联系方式的用户userID列表，在type为1时为必填，且只能有一个
        //party	否	使用该联系方式的部门id列表，只在type为2时有效
        //is_temp	否	是否临时会话模式，true表示使用临时会话模式，默认为false
        //expires_in	否	临时会话二维码有效期，以秒为单位。该参数仅在is_temp为true时有效，默认7天，最多为14天
        //chat_expires_in	否	临时会话有效期，以秒为单位。该参数仅在is_temp为true时有效，默认为添加好友后24小时，最多为14天
        //unionid	否	可进行临时会话的客户unionid，该参数仅在is_temp为true时有效，如不指定则不进行限制
        //is_exclusive	否	是否开启同一外部企业客户只能添加同一个员工，默认为否，开启后，同一个企业的客户会优先添加到同一个跟进人
        //conclusions	否	结束语，会话结束时自动发送给客户，可参考“结束语定义”，仅在is_temp为true时有效
        String corpId = systemUserQyRelation.getCorpId();
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyAccessToken(corpId);
        log.info("获取企微{}，获取转换外部联系人id，响应状态码：{}, 响应内容:{}", corpId, qyAccessToken.getErrcode(), qyAccessToken);
        JSONObject params = new JSONObject();
        params.put("type", 1);
        params.put("scene", 2);
        params.put("style", 1);
        params.put("remark", "");
        params.put("skip_verify", true);
        params.put("user", systemUserQyRelation.getQyUserId());
        JSONObject response = qyWxContactClient.addContactWay(qyAccessToken.getAccess_token(), params);
        log.info("获取企微联系我二维码 userId:{}，corpId:{}，返回{}", systemUserQyRelation.getQyUserId(), corpId, response);
        if (response.getInteger("errcode") != 0) {
            String errMsg = String.format("获取企微联系我二维码失败: [%d] %s",
                    response.getInteger("errcode"),
                    response.getString("errmsg"));
            throw new RuntimeException(errMsg);
        }
        LambdaUpdateWrapper<SystemUserQyRelation> lambdaUpdateWrapper = Wrappers.lambdaUpdate(SystemUserQyRelation.class);
        lambdaUpdateWrapper.eq(SystemUserQyRelation::getId, systemUserQyRelation.getId())
                .set(SystemUserQyRelation::getConfigId, response.getString("config_id"))
                .set(SystemUserQyRelation::getQrCode, response.getString("qr_code"));
        return this.update(lambdaUpdateWrapper);
    }

    /**
     * description : 分页查询企微员工信息列表
     * @title: queryQyUserPage
     * @param: pageNum
     * @param: pageSize
     * @param: qyUserRequest
     * <AUTHOR>
     * @date 2025/7/20 16:28
     * @return Page<SystemUserQyRelation>
     */
    @Override
    public Page<SystemUserQyRelation> queryQyUserPage(Integer pageNum, Integer pageSize, QyUserRequest qyUserRequest) {
        Page<SystemUserQyRelation> page = new Page<>(pageNum, pageSize);
        return baseMapper.queryQyUserPage(page, qyUserRequest);
    }
}
