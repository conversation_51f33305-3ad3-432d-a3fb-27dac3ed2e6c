package cn.hxsy.service.impl;

import cn.hxsy.api.qy.feign.license.QyWxLicenseClient;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.base.enums.AccountStatusEnum;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.dao.ExternalAccountMapper;
import cn.hxsy.datasource.model.entity.ExternalAccount;
import cn.hxsy.datasource.model.entity.ExternalAccountOrder;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.service.ExternalAccountOrderService;
import cn.hxsy.service.ExternalAccountService;
import cn.hxsy.service.SystemUserQyRelationService;
import cn.hxsy.service.qy.QyAuthService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 互通账号池服务实现类
 */
@Service
public class ExternalAccountServiceImpl 
    extends ServiceImpl<ExternalAccountMapper, ExternalAccount> 
    implements ExternalAccountService {

    @Resource
    private QyWxLicenseClient qyWxLicenseClient;

    @Resource
    private QyAuthService qyAuthService;

    @Resource
    private ExternalAccountOrderService externalAccountOrderService;

    @Resource
    private SystemUserQyRelationService systemUserQyRelationService;

    @Resource
    private SnowflakeIdWorker idWorker;

    /**
     * description :获取订单中的激活码并保存
     * @title: saveActivationCode
     * @param: orderId
     * <AUTHOR>
     * @date 2025/7/17 22:04
     * @return boolean
     */
    @Override
    public boolean saveActivationCode(String orderId) {
        // 1、获取 应用服务商的接口调用凭证
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getProviderToken("");
        String providerAccessToken = qyAccessToken.getProvider_access_token();
        JSONObject jsonObject = new JSONObject();

        // 2、查询订单
        LambdaQueryWrapper<ExternalAccountOrder> orderQueryWrapper = Wrappers.lambdaQuery(ExternalAccountOrder.class);
        orderQueryWrapper.eq(ExternalAccountOrder::getOrderId, orderId);
        ExternalAccountOrder accountOrder = externalAccountOrderService.getOne(orderQueryWrapper);
        String corpId = accountOrder.getCorpId();
        Long id = accountOrder.getId();
        String cursor = "";
        Integer hasMore;
        List<ExternalAccount> externalAccountList = new ArrayList<>();
        // 3、分页查询订单账号
        do {
            jsonObject.put("order_id", orderId);
            jsonObject.put("limit", 1000); // 一次性最多获取1000个账号
            if (StringUtils.isNotBlank(cursor)) {
                jsonObject.put("cursor", cursor); // 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用可不填
            }
            JSONObject response = qyWxLicenseClient.listOrderAccount(providerAccessToken, jsonObject);
            if (response.getInteger("errcode") != 0) {
                String errMsg = String.format("获取订单账号失败: [%d] %s",
                        response.getInteger("errcode"),
                        response.getString("errmsg"));
                throw new RuntimeException(errMsg);
            }
            cursor = response.getString("next_cursor");
            hasMore = response.getInteger("has_more");
            JSONArray accountList = response.getJSONArray("account_list");
            for (int i = 0; i < accountList.size(); i++) {
                JSONObject account = accountList.getJSONObject(i);
                ExternalAccount externalAccount = new ExternalAccount();
                externalAccount.setId(idWorker.nextId());
                externalAccount.setOrderId(orderId);
                externalAccount.setActiveCode(account.getString("active_code"));
                externalAccount.setUserid(account.getString("userid"));
                externalAccount.setAccountType(account.getInteger("type"));
                externalAccount.setAccountOrderId(id);
                externalAccount.setCorpId(corpId);
                externalAccountList.add(externalAccount);
            }
        } while (hasMore == 1);
        return saveBatch(externalAccountList);
    }

    /**
     * description : 批量激活账号
     * @title: batchActivation
     * @param: corpId
     * @param: systemUserQyRelations
     * <AUTHOR>
     * @date 2025/7/18 16:05
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchActivation(String corpId, List<SystemUserQyRelation> systemUserQyRelations) {
        int size = systemUserQyRelations.size();
        if (size > 1000) {
            throw new RuntimeException("一次最多只能批量激活1000个账号");
        }
        // 1、获取size数量的互通账号池
        LambdaQueryWrapper<ExternalAccount> queryWrapper = Wrappers.lambdaQuery(ExternalAccount.class);
        queryWrapper.eq(ExternalAccount::getCorpId, corpId)
                .eq(ExternalAccount::getAccountStatus, AccountStatusEnum.UNASSIGNED.getCode())
                .last("LIMIT " + size); // 使用last方法添加LIMIT子句
        List<ExternalAccount> externalAccountList = list(queryWrapper);
        if (size > externalAccountList.size()) {
            throw new RuntimeException("互通账号激活码数量不足，请先购买");
        }
        // 2、获取应用服务商的接口调用凭证
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getProviderToken("");
        if (qyAccessToken == null) {
            throw new RuntimeException("获取企业微信凭证失败");
        }
        String providerAccessToken = qyAccessToken.getProvider_access_token();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("corpid", corpId);
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < size; i++) {
            SystemUserQyRelation relation = systemUserQyRelations.get(i);
            ExternalAccount account = externalAccountList.get(i);
            String activeCode = account.getActiveCode();

            JSONObject active = new JSONObject();
            active.put("userid", relation.getQyUserId());
            active.put("active_code", activeCode);
            jsonArray.add(active);

            relation.setActiveCode(activeCode);
            relation.setAccountType(account.getAccountType());
            account.setAccountStatus(AccountStatusEnum.ASSIGNED.getCode());
        }
        jsonObject.put("active_list", jsonArray);
        // 3、批量激活账号
        JSONObject response = qyWxLicenseClient.batchActiveAccount(providerAccessToken, jsonObject);
        if (response.getInteger("errcode") != 0) {
            String errMsg = String.format("批量激活失败: [%d] %s",
                    response.getInteger("errcode"),
                    response.getString("errmsg"));
            throw new RuntimeException(errMsg);
        }
        // 4、更新账号绑定
        systemUserQyRelationService.updateBatchById(systemUserQyRelations);
        // 5、更新激活码状态
        return this.updateBatchById(externalAccountList);
    }


    /**
     * description : 分页查询互通账号列表
     * @title: queryPage
     * @param: pageNum
     * @param: pageSize
     * @param: qyUserRequest
     * <AUTHOR>
     * @date 2025/7/18 16:18
     * @return Page<ExternalAccount>
     */
    @Override
    public Page<ExternalAccount> queryPage(Integer pageNum, Integer pageSize, QyUserRequest qyUserRequest) {
        Page<ExternalAccount> page = new Page<>(pageNum, pageSize);
        return baseMapper.queryPage(page, qyUserRequest);
//
//        LambdaQueryWrapper<ExternalAccount> wrapper = Wrappers.lambdaQuery(ExternalAccount.class);
//        wrapper.eq(ExternalAccount::getOrderId, externalAccount.getOrderId())
//                .eq(ExternalAccount::getAccountStatus, externalAccount.getAccountStatus())
//                .eq(ExternalAccount::getAccountType, externalAccount.getAccountType());
//        return this.page(page, wrapper);
    }

    /**
     * description :
     * @title: queryActivationCode
     * @param: orderId
     * <AUTHOR>
     * @date 2025/7/24 0:27
     * @return List<ExternalAccount>
     */
    @Override
    public List<ExternalAccount> queryActivationCode(String orderId) {
        LambdaQueryWrapper<ExternalAccount> wrapper = Wrappers.lambdaQuery(ExternalAccount.class);
        wrapper.eq(ExternalAccount::getOrderId, orderId);
        return this.list(wrapper);
    }
}