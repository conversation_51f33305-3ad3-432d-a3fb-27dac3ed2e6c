package cn.hxsy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CompanyQyRelated;
import cn.hxsy.dao.CompanyQyRelatedMapper;

import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.response.CompanyQyRelatedResponse;
import cn.hxsy.service.CompanyQyRelatedService;
import cn.hxsy.service.CompanyQyRelationService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 公司与企微账号关联列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-26 15:23:52
 */
@Service
public class CompanyQyRelatedServiceImpl extends ServiceImpl<CompanyQyRelatedMapper, CompanyQyRelated> implements CompanyQyRelatedService {

	@Resource
	private UserCacheUtil userCacheUtil;

	@Resource
	private CompanyQyRelationService companyQyRelationService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Result<Object> saveCompanyQyBind(CompanyQyRelationRequest request) {
		// 获取用户信息并校验是否有管理员权限
		SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
		userCacheUtil.checkUserAdmin(systemUserSelfInfo);
		// 0、区分前端绑定场景
		String bindScene = request.getBindScene();
		if(StringUtils.isEmpty(bindScene)){
			return Result.error("绑定场景不能为空");
		}
		if("0".equals(bindScene)){
			// 1、开始给公司批量绑定企微账号，先删除之前的绑定关系
			LambdaUpdateWrapper<CompanyQyRelated> wrapper = new LambdaUpdateWrapper<>();
			wrapper.set(CompanyQyRelated::getStatus, UseStatusEnum.INVALID.getCode())
					.set(CompanyQyRelated::getUpdatedAt, LocalDateTime.now())
					.set(CompanyQyRelated::getUpdatedBy, systemUserSelfInfo.getAccountId());
			wrapper.eq(CompanyQyRelated::getCompanyId, request.getCompanyId())
					.eq(CompanyQyRelated::getStatus, UseStatusEnum.EFFECTIVE.getCode());
			this.getBaseMapper().update(null, wrapper);
			// 2、开始构造待插入集合，插入新的绑定关系
			List<String> corpIds = request.getCorpIds();
			if(CollectionUtil.isEmpty(corpIds)){
				return Result.error("企微账号不能为空");
			}
			List<CompanyQyRelated> list = corpIds.stream().map(corpId -> {
				CompanyQyRelated companyQyRelated = new CompanyQyRelated();
				companyQyRelated.setCorpId(corpId);
				companyQyRelated.setCompanyId(request.getCompanyId());
				companyQyRelated.setStatus(UseStatusEnum.EFFECTIVE.getCode());
				companyQyRelated.setCreatedBy(systemUserSelfInfo.getAccountId());
				companyQyRelated.setCreatedAt(LocalDateTime.now());
				return companyQyRelated;
			}).collect(Collectors.toList());
			this.saveBatch(list);
		}else if("1".equals(bindScene)){
			// 1、开始给企微批量绑定公司账号，先删除之前的绑定关系
			LambdaUpdateWrapper<CompanyQyRelated> wrapper = new LambdaUpdateWrapper<>();
			wrapper.set(CompanyQyRelated::getStatus, UseStatusEnum.INVALID.getCode())
					.set(CompanyQyRelated::getUpdatedAt, LocalDateTime.now())
					.set(CompanyQyRelated::getUpdatedBy, systemUserSelfInfo.getAccountId());
			wrapper.eq(CompanyQyRelated::getCorpId, request.getCorpId())
					.eq(CompanyQyRelated::getStatus, UseStatusEnum.EFFECTIVE.getCode());
			this.getBaseMapper().update(null, wrapper);
			// 2、开始构造待插入集合，插入新的绑定关系
			List<String> companyIds = request.getCompanyIds();
			if(CollectionUtil.isEmpty(companyIds)){
				return Result.error("公司账号不能为空");
			}
			List<CompanyQyRelated> list = companyIds.stream().map(companyId -> {
				CompanyQyRelated companyQyRelated = new CompanyQyRelated();
				companyQyRelated.setCorpId(request.getCorpId());
				companyQyRelated.setCompanyId(companyId);
				companyQyRelated.setStatus(UseStatusEnum.EFFECTIVE.getCode());
				companyQyRelated.setCreatedBy(systemUserSelfInfo.getAccountId());
				companyQyRelated.setCreatedAt(LocalDateTime.now());
				return companyQyRelated;
			}).collect(Collectors.toList());
			this.saveBatch(list);
		}
		return Result.ok();
	}

	@Override
	public Result<Object> queryCompanyBindQy(CompanyQyRelationRequest request) {
		// 获取用户信息并校验是否有管理员权限
		SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
		userCacheUtil.checkUserAdmin(systemUserSelfInfo);
		// 0、区分前端查询场景
		String queryScene = request.getQueryScene();
		if(StringUtils.isEmpty(queryScene)){
			return Result.error("查询场景不能为空");
		}
		List<CompanyQyRelated> companyQyRelatedList = new ArrayList<>();
		if("0".equals(queryScene)){
			// 1、开始查询当前公司关联企微信息
			LambdaQueryWrapper<CompanyQyRelated> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(CompanyQyRelated::getCompanyId, request.getCompanyId())
					.eq(CompanyQyRelated::getStatus, UseStatusEnum.EFFECTIVE.getCode());
			companyQyRelatedList = this.getBaseMapper().selectList(wrapper);
		}else if("1".equals(queryScene)){
			// 1、开始查询当前企微关联公司信息
			LambdaQueryWrapper<CompanyQyRelated> wrapper = new LambdaQueryWrapper<>();
			wrapper.eq(CompanyQyRelated::getCorpId, request.getCorpId())
					.eq(CompanyQyRelated::getStatus, UseStatusEnum.EFFECTIVE.getCode());
			companyQyRelatedList = this.getBaseMapper().selectList(wrapper);
		}
		// 3、查询企微信息详情（名称）
		Map<String, String> corpNameByIdMap =
				companyQyRelationService.getCorpNameById(companyQyRelatedList.stream().map(CompanyQyRelated::getCorpId).collect(Collectors.toList()));
		if(CollectionUtil.isNotEmpty(companyQyRelatedList)){
			List<CompanyQyRelatedResponse> collect = companyQyRelatedList.stream().map(companyQyRelated -> {
				CompanyQyRelatedResponse companyQyRelatedResponse = new CompanyQyRelatedResponse();
				BeanUtils.copyProperties(companyQyRelated, companyQyRelatedResponse);
				companyQyRelatedResponse.setId(String.valueOf(companyQyRelated.getId()));
				companyQyRelatedResponse.setCorpName(corpNameByIdMap.get(companyQyRelated.getCorpId()));
				return companyQyRelatedResponse;
			}).collect(Collectors.toList());
			return Result.ok(collect);
		}
		return Result.ok(companyQyRelatedList);
	}
}
