package cn.hxsy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.dao.CustomerTagsMapper;
import cn.hxsy.datasource.model.entity.CustomerTags;
import cn.hxsy.service.CustomerTagsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户标签服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class CustomerTagsServiceImpl extends ServiceImpl<CustomerTagsMapper, CustomerTags> implements CustomerTagsService {

    @Autowired
    private SnowflakeIdWorker idWorker;

    /**
     * 根据客户ID查询标签列表
     *
     * @param customerId 客户ID
     * @return 标签列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public List<CustomerTags> getByCustomerId(Long customerId) {
        LambdaQueryWrapper<CustomerTags> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerTags::getCustomerId, customerId);
        return list(wrapper);
    }

    @Override
    public boolean saveTags(CustomerTags customerTags) {
        if (ObjectUtil.isAllEmpty(customerTags.getCustomerId(), customerTags.getCampPeriodId(), customerTags.getCampPeriodName())) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        LambdaQueryWrapper<CustomerTags> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CustomerTags::getCustomerId, customerTags.getCustomerId())
                .eq(CustomerTags::getCampPeriodId, customerTags.getCampPeriodId());
        CustomerTags tags = getOne(queryWrapper);
        // 1.1 已存在该客户该营期的标签，则更新标签名称
        if (ObjectUtil.isNotEmpty(tags)) {
            String tagsName = tags.getTagsName();
            if (StringUtils.isNotBlank(tagsName)) {
                if (tagsName.contains(customerTags.getTagsName())) {
                    // 该课程标签已存在，不更新
                    return Boolean.TRUE;
                }
                tags.setTagsName(tagsName + StrUtil.COMMA + customerTags.getTagsName());
            } else {
                tags.setTagsName(customerTags.getTagsName());
            }
            LambdaUpdateWrapper<CustomerTags> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(CustomerTags:: getTagsName, tags.getTagsName())
                    .set(CustomerTags::getUpdatedAt, now)
                    .eq(CustomerTags::getId, tags.getId())
                    .eq(CustomerTags::getCustomerId, customerTags.getCustomerId());
            return update(updateWrapper);
        }
        // 1.2 不存在该客户该营期的标签，则新增标签

        String tagsName = customerTags.getTagsName();
        if (StringUtils.isNotBlank(tagsName)) {
            customerTags.setTagsName(customerTags.getCampPeriodName() + StrUtil.COMMA + tagsName);
        } else {
            customerTags.setTagsName(customerTags.getCampPeriodName());
        }
        customerTags.setId(idWorker.nextId());
        customerTags.setCreatedAt(now);
        customerTags.setUpdatedAt(now);
        customerTags.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        return save(customerTags);
    }

    /**
     * 分页查询客户标签信息
     *
     * @param current 当前页码，默认1
     * @param size 每页大小，默认10
     * @param customerId 客户ID，可选
     * @param campPeriodId 营期ID，可选
     * @param tagsName 标签名称，可选
     * @param status 使用状态，可选
     * @return 分页结果
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public Page<CustomerTags> page(Integer current, Integer size, Long customerId, Long campPeriodId, String tagsName, Integer status) {
        Page<CustomerTags> page = new Page<>(current, size);
        LambdaQueryWrapper<CustomerTags> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerTags::getCustomerId, customerId);
        if (campPeriodId != null) {
            wrapper.eq(CustomerTags::getCampPeriodId, campPeriodId);
        }
        if (tagsName != null) {
            wrapper.like(CustomerTags::getTagsName, tagsName);
        }
        if (status != null) {
            wrapper.eq(CustomerTags::getStatus, status);
        }
        return page(page, wrapper);
    }

    /**
     * description : 保存手动标签
     * @title: saveManualTag
     * @param: customerTags
     * <AUTHOR>
     * @date 2025/5/25 20:31
     * @return boolean
     */
    public boolean saveManualTag(CustomerTags customerTags) {
        List<Long> customerIds = customerTags.getCustomerIds();
        if (ObjectUtil.isNotEmpty(customerIds)) {
            LocalDateTime now = LocalDateTime.now();
            for (Long customerId : customerIds) {
                LambdaQueryWrapper<CustomerTags> queryWrapper = Wrappers.lambdaQuery();
                queryWrapper.eq(CustomerTags::getCustomerId, customerId)
                        .eq(CustomerTags::getCampPeriodId, customerTags.getCampPeriodId());
                CustomerTags tags = getOne(queryWrapper);
                if (ObjectUtil.isNotEmpty(tags)) {
                    String manualTagsName = tags.getManualTagsName();
                    if (StringUtils.isNotBlank(manualTagsName)) {
                        tags.setManualTagsName(manualTagsName + StrUtil.COMMA + customerTags.getManualTagsName());
                    } else {
                        tags.setManualTagsName(customerTags.getManualTagsName());
                    }
                    LambdaUpdateWrapper<CustomerTags> updateWrapper = Wrappers.lambdaUpdate();
                    updateWrapper.set(CustomerTags::getManualTagsName, tags.getManualTagsName())
                            .set(CustomerTags::getUpdatedAt, now)
                            .eq(CustomerTags::getId, tags.getId())
                            .eq(CustomerTags::getCustomerId, customerId);
                    update(updateWrapper);
                } else {
                    CustomerTags newTags = new CustomerTags();
                    newTags.setId(idWorker.nextId());
                    newTags.setCustomerId(customerId);
                    newTags.setCampPeriodId(customerTags.getCampPeriodId());
                    newTags.setCampPeriodName(customerTags.getCampPeriodName());
                    newTags.setTagsName(customerTags.getCampPeriodName());
                    newTags.setManualTagsName(customerTags.getManualTagsName());
                    newTags.setStatus(UseStatusEnum.EFFECTIVE.getCode());
                    save(newTags);
                }
            }
        }
        return Boolean.TRUE;
    }
}