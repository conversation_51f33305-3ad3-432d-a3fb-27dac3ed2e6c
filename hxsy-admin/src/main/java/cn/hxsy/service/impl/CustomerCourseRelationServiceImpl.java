package cn.hxsy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.response.CampCoursePesponse;
import cn.hxsy.api.user.model.response.CampPeriodResponse;
import cn.hxsy.api.user.model.response.CourseVideoResponse;
import cn.hxsy.api.user.service.CampPeriodRpcService;
import cn.hxsy.base.enums.CourseStatusEnum;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.dao.CustomerCourseRelationMapper;
import cn.hxsy.datasource.model.entity.CampCoursePO;
import cn.hxsy.datasource.model.entity.CourseVideoVO;
import cn.hxsy.datasource.model.entity.CustomerCourseRelation;
import cn.hxsy.datasource.model.entity.CustomerTags;
import cn.hxsy.dto.CustomerMarkTagBackendDTO;
import cn.hxsy.service.CustomerBehaviorService;
import cn.hxsy.service.CustomerCourseRelationService;
import cn.hxsy.service.CustomerTagsService;
import cn.hxsy.service.CustomerWecomTagService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hxsy.base.constant.ResponseType.Success;

/**
 * 客户课程关联服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Slf4j
@Service
public class CustomerCourseRelationServiceImpl extends ServiceImpl<CustomerCourseRelationMapper, CustomerCourseRelation> implements CustomerCourseRelationService {

    @Autowired
    private SnowflakeIdWorker idWorker;
    @Resource
    private CustomerBehaviorService customerBehaviorService;
    @DubboReference(version = "1.0.0")
    private CampPeriodRpcService campPeriodRpcService;
    @Autowired
    private CustomerWecomTagService customerWecomTagService;

    @Resource
    private CustomerTagsService customerTagsService;


    /**
     * description : 更新课程关联 、更新客户行为轨迹
     *
     * @return void
     * @title: saveCourseCustomerRel
     * @param: customerCourseRelation
     * <AUTHOR>
     * @date 2025/4/5 18:22
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCourseCustomerRel(CustomerCourseRelation customerCourseRelation) {
        Long customerId = customerCourseRelation.getCustomerId();
        Long companyId = customerCourseRelation.getCompanyId();
        Long campPeriodId = customerCourseRelation.getCampPeriodId(); // 营期id（分表键）
        Long courseId = customerCourseRelation.getCourseId();
        // 1 查询是否已存在该课程
        LambdaQueryWrapper<CustomerCourseRelation> customerCourseRelationWrapper = new LambdaQueryWrapper<>();
        customerCourseRelationWrapper.eq(CustomerCourseRelation::getCustomerId, customerId)
                .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId)
                .eq(CustomerCourseRelation::getCourseId, courseId);
        List<CustomerCourseRelation> list = this.list(customerCourseRelationWrapper);
        if (ObjectUtil.isNotEmpty(list) && list.size() > 0) {
            // 1.1 已存在该课程
            // 1.2.2 保存客户行为轨迹(课程学习)
            customerBehaviorService.saveCampVideoCourseLearning(customerId, companyId, campPeriodId, courseId);
            return Boolean.TRUE;
        }
        customerCourseRelationWrapper.clear();
        // 2查询是否已存在营期
        customerCourseRelationWrapper.eq(CustomerCourseRelation::getCustomerId, customerId)
                .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId);
        int count = this.count(customerCourseRelationWrapper);
        if (count > 0) {
            // 2.1 营期存在 不存在该课程
            // 2.1.1保存课程关联信息
            saveCourseRelation(customerCourseRelation);
        } else {
            // 2.2不存在该营期
            // 2.2.1保存客户课程关联信息
            saveCourseRelation(customerCourseRelation);
            // 2.2.2保存客户行为轨迹(营期报名)
            customerBehaviorService.saveCampEnrollment(customerId, companyId, campPeriodId, customerCourseRelation.getSalesId());
            // 2.2.3保存客户营期标签
            CustomerTags customerTags = new CustomerTags();
            customerTags.setCampPeriodId(campPeriodId);
            customerTags.setCustomerId(customerId);
            customerTags.setCampPeriodName(customerCourseRelation.getCampPeriodName());
            customerTagsService.saveTags(customerTags);
            // 设置企微标签
            CustomerMarkTagBackendDTO customerMarkTagBackendDTO = new CustomerMarkTagBackendDTO();
            customerMarkTagBackendDTO.setCustomerId(customerId);
            customerMarkTagBackendDTO.setSalesId(customerCourseRelation.getSalesId());
            customerMarkTagBackendDTO.setTagName(customerCourseRelation.getCampPeriodName());
            customerWecomTagService.markCustomerTagByBackend(customerMarkTagBackendDTO);
        }
        return Boolean.TRUE;
    }

    /**
     * description : 保存客户课程关联表
     *
     * @return void
     * @title: saveCourseRelation
     * @param: relation
     * <AUTHOR>
     * @date 2025/4/4 18:52
     */
    private void saveCourseRelation(CustomerCourseRelation relation) {
        relation.setId(idWorker.nextId());
        relation.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        relation.setCreatedAt(LocalDateTime.now());
        relation.setUpdatedAt(LocalDateTime.now());
        this.save(relation);
    }

    @Override
    public List<CampPeriodResponse> getCampPeriodInfoByCustomerId(Long customerId, Long columnId) {
//        LambdaQueryWrapper<CustomerCourseRelation> queryWrapper = Wrappers.lambdaQuery();
//        queryWrapper.select(CustomerCourseRelation::getCampPeriodId) // 只查询营期ID
//                .eq(CustomerCourseRelation::getColumnId, columnId)
//                .eq(CustomerCourseRelation::getCustomerId, customerId);
//        baseMapper.selectDistinct();
        List<CustomerCourseRelation> list = baseMapper.getByCustomerIdAndColumnId(customerId, columnId);

//        List<CustomerCourseRelation> list = list(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            // 1、去重统计客户已参加的营期id
            List<Long> collect = list.stream()
                    .map(CustomerCourseRelation::getCampPeriodId) // 提取营期ID
                    .distinct() // 去重
                    .collect(Collectors.toList());
            // 2、根据id获取营期信息
            Result<List<CampPeriodResponse>> result = campPeriodRpcService.getCampPeriodsByIds(collect);
            if (result.getCode()!= Success.getCode()) {
                throw new RuntimeException("获取营期信息失败" + result.getMsg());
            }
            return result.getData();
        }
        return Collections.emptyList();
    }

    @Override
    public Page<CourseVideoResponse> getCourseRelationByCustomerId(Integer pageNum, Integer pageSize,Long customerId, Long campPeriodId) {
        LambdaQueryWrapper<CustomerCourseRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CustomerCourseRelation::getCustomerId, customerId)
                .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId)
                .isNotNull(CustomerCourseRelation::getCourseId);
        List<CustomerCourseRelation> list = list(queryWrapper);
        // 课程id作为key 转为map
        Map<Long, CustomerCourseRelation> map = list.stream()
                .collect(
                        Collectors.toMap(
                                CustomerCourseRelation::getCourseId,
                                Function.identity(),
                                (existing, replacement) -> existing));
        // 获取营期课程信息
        Result<List<CampCoursePesponse>> result = campPeriodRpcService.getCampGroupAndCourses(campPeriodId);
        if (result.getCode() != Success.getCode()) {
            throw new RuntimeException("获取营期信息失败: " + result.getMsg());
        }

        // 处理课程信息并转换为响应对象
        List<CourseVideoResponse> courseList = result.getData().stream()
                .flatMap(campCourse -> processCampCourse(campCourse, map)) // 使用独立方法处理每个CampCourse
                .filter(course -> ObjectUtil.isNotEmpty(course.getArrivalStatus())) // 过滤没有课程状态的的记录
                .sorted(Comparator.comparing(CourseVideoResponse::getStartTime, Comparator.nullsFirst(Comparator.naturalOrder()))) // 按时间排序
                .collect(Collectors.toList());
        return new Page<CourseVideoResponse>(pageNum, pageSize, courseList.size())
                .setRecords(courseList);
    }

    /**
     * 处理单个CampCourse对象
     */
    private Stream<CourseVideoResponse> processCampCourse(CampCoursePesponse campCourse, Map<Long, CustomerCourseRelation> relationMap) {
        try {
            String courseInfo = campCourse.getCourseInfo();
            if (ObjectUtil.isEmpty(courseInfo)) return Stream.empty();

            // 解析JSON数组
            List<CourseVideoResponse> coursePOList = JSONObject.parseArray(courseInfo, CourseVideoResponse.class);
            if (ObjectUtil.isEmpty(coursePOList)) return Stream.empty();

            // 转换为响应对象
            return coursePOList.stream()
                    .map(campPO -> {
                        CourseVideoResponse response = new CourseVideoResponse();
                        BeanUtils.copyProperties(campPO, response);
                        // 设置到课状态、到课时间、完播时间、课程时长
                        Optional.ofNullable(relationMap.get(campPO.getId())).ifPresent(relation -> {
                            Integer arrivalStatus = relation.getArrivalStatus();
                            // 给arrivalStatus默认值
                            if (ObjectUtil.isNotEmpty(arrivalStatus)) {
                                response.setArrivalStatus(relation.getArrivalStatus());
                            }
                            response.setArrivalTime(relation.getArrivalTime());
                            response.setCompleteTime(relation.getCompleteTime());
                            response.setPlayProgress(relation.getPlayProgress());
                        });
                        return response;
                    })
                    .filter(Objects::nonNull);

        } catch (Exception e) {
            // 记录详细错误日志（建议替换为实际日志框架）
            log.error("解析课程信息失败: " + campCourse.getCourseInfo(), e);
            return Stream.empty();
        }
    }

    @Override
    public Boolean isCourseArrival(Long customerId, Long campPeriodId, Long courseId) {
        CustomerCourseRelation relation = baseMapper.selectOne(Wrappers.<CustomerCourseRelation>lambdaQuery()
                .eq(CustomerCourseRelation::getCustomerId, customerId)
                .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId)
                .eq(CustomerCourseRelation::getCourseId, courseId));

        if (relation == null) {
            log.warn("客户[{}]在营期[{}]的课程[{}]关系不存在", customerId, campPeriodId, courseId);
            return false;
        }

        boolean isCompleted = (relation.getArrivalStatus() == CourseStatusEnum.COMPLETED.getCode());
        if (isCompleted) {
            log.info("客户[{}]在营期[{}]的课程[{}]已到课", customerId, campPeriodId, courseId);
        }
        return isCompleted;
    }
}