package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hxsy.api.qy.feign.cust.contract.QyWxCustContactClient;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.base.request.BatchRemarkCustomerRequest;
import cn.hxsy.datasource.model.entity.BatchRemarkCustomerVO;
import cn.hxsy.api.user.model.request.*;
import cn.hxsy.api.user.model.response.*;
import cn.hxsy.api.user.service.CampPeriodRpcService;
import cn.hxsy.base.config.CustomerShardingConfig;
import cn.hxsy.base.constant.user.UserRole;
import cn.hxsy.base.constant.user.UserSelectType;
import cn.hxsy.base.enums.*;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.base.request.wxPayRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.config.COSConfig;
import cn.hxsy.dao.CustomerMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.dto.CustomerMarkTagBackendDTO;
import cn.hxsy.service.*;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.threadLocal.pool.customer.AsyncRegisterPool;
import cn.hxsy.threadLocal.pool.customer.AsyncUpdatePool;
import cn.hxsy.threadLocal.task.customer.CustomerRegisterTask;
import cn.hxsy.threadLocal.task.customer.CustomerUpdateTask;
import cn.hxsy.threadLocal.task.customer.utils.CustomerRegisterTaskInSpring;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.hxsy.base.constant.ResponseType.Success;
import static cn.hxsy.cache.constant.user.CacheConstant.*;

/**
 * 客户服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
@Slf4j
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer> implements CustomerService {

    @Resource
    private WechatUserOpenidService wechatUserOpenidService;
    @Resource
    private CustomerCourseRelationService customerCourseRelationService;
    @Resource
    private CustomerSalesRelationService customerSalesRelationService;
    @Resource
    private CustomerTagsService customerTagsService;
    @Resource
    private CustomerBehaviorService customerBehaviorService;
    @Resource
    private CompanyQyRelationService companyQyRelationService;
    @Resource
    private SystemUserQyRelationService systemUserQyRelationService;
    @DubboReference(version = "1.0.0")
    private CampPeriodRpcService campPeriodRpcService;

    @Autowired
    private SnowflakeIdWorker idWorker;

    @Autowired
    private AsyncRegisterPool asyncRegisterPool;

    @Autowired
    private AsyncUpdatePool asyncUpdatePool;

    @Autowired
    private UserAuthService userAuthService;

    @Autowired
    private CustomerRegisterTaskInSpring customerRegisterTaskInSpring;

    @Autowired
    private CustomerShardingConfig customerShardingConfig;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Autowired
    private COSClient cosClient;

    @Autowired
    private COSConfig cosConfig;

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ColumnService columnService;

    @Autowired
    private UserCacheUtil  userCacheUtil;

    @Autowired
    private UserSelectUtil userSelectUtil;

    @Autowired
    private CustomerWecomBindingService customerWecomBindingService;

    @Autowired
    private CustomerWecomTagService customerWecomTagService;

    @Autowired
    private QyWxCustContactClient qyWxCustContactClient;
    
    @Autowired
    private QyAuthService qyAuthService;

    @Value("${system.cosAddr}")
    private String cosAddr;

    final static int MAX_IMAGE_SIZE = 1024 * 1024;// 头像限制1M


    /**
     * description : 客户注册
     *
     * @return CustomerResponse
     * @title: customerRegister
     * @param: customerRegisterRequest
     * <AUTHOR>
     * @date 2025/4/4 18:55
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomerResponse customerRegister(CustomerRegisterRequest customerRegisterRequest) {
        log.info("客户注册服务接收到消息:{}", customerRegisterRequest);
        LocalDateTime now = LocalDateTime.now();
        // 1.需要先判断下客户是否存在，避免注册缓存丢失时重复注册
        LambdaQueryWrapper<Customer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Customer::getUnionId, customerRegisterRequest.getUnionId());
        List<Customer> customers = this.getBaseMapper().selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(customers)) {
            CustomerResponse customerResponse = new CustomerResponse();
            Customer customer = customers.get(0);
            BeanUtils.copyProperties(customer, customerResponse);
            customerResponse.setIsRegister("1");
            return customerResponse;
        }
        Customer customer = new Customer();
        //利用雪花生成id
        customer.setId(idWorker.nextId());
        customer.setNickname(customerRegisterRequest.getNickname());
        customer.setAvatarUrl(customerRegisterRequest.getAvatarUrl());
        customer.setGender(customerRegisterRequest.getGender());
        customer.setUnionId(customerRegisterRequest.getUnionId());
        customer.setWeworkStatus(WeComAddStatusEnum.NOT_ADDED.getCode());
        customer.setOpenid(customerRegisterRequest.getOpenid());
        customer.setLastActiveTime(now);
        customer.setCreatedAt(now);
        customer.setUpdatedAt(now);
        customer.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        // 2.同步执行注册逻辑
        // 2.1 保存客户注册信息
        UserAuth userAuth = new UserAuth();
        userAuth.setUnionId(customer.getUnionId());
        userAuth.setUserType(AccountTypeEnum.CUSTER.getCode());
        userAuth.setId(customer.getId());

        Long campPeriodId = customerRegisterRequest.getCampPeriodId();
        if (ObjectUtil.isNotEmpty(campPeriodId)) {
            Result<CampCourseVideoResponse> campTodayCourse = campPeriodRpcService.getCampTodayCourse(campPeriodId, LocalDate.now());
            if (campTodayCourse.getCode() != Success.getCode()) {
                throw new RuntimeException("查询营期下课程失败：" + campTodayCourse.getMsg());
            }
            CampCourseVideoResponse campCourseVideoResponse = campTodayCourse.getData();
            Boolean autoRegister = campCourseVideoResponse.getAutoRegister();
            if (autoRegister) {
                customer.setForbiddenStatus(DisableStatusEnum.ENABLE.getCode());
                customer.setRedPacketStatus(DisableStatusEnum.ENABLE.getCode());
            } else {
                customer.setForbiddenStatus(DisableStatusEnum.TO_BE_ENABLED.getCode());
                customer.setRedPacketStatus(DisableStatusEnum.TO_BE_ENABLED.getCode());
            }
            log.info("营期下课程自动注册：{}", autoRegister);
        } else {
            // 20250705 初始为 禁用状态 销售手动进行启用
            customer.setForbiddenStatus(DisableStatusEnum.TO_BE_ENABLED.getCode());
            customer.setRedPacketStatus(DisableStatusEnum.TO_BE_ENABLED.getCode());
        }
        log.info("保存客户信息：{}", customer);
        // 2.2 保存客户基本信息
        userAuthService.getBaseMapper().insert(userAuth);
        boolean saveCustomer = this.save(customer);
        // 2.3 客户通过小程序链接进来，需要绑定销售人员以及对应营期大课
        if (ObjectUtil.isNotEmpty(customerRegisterRequest.getSalesId())) {
            CustomerSalesRelation relation = new CustomerSalesRelation();
            relation.setColumnId(customerRegisterRequest.getColumnId());
            relation.setCompanyId(customerRegisterRequest.getCompanyId());
            relation.setCampPeriodId(campPeriodId);
            relation.setSalesId(customerRegisterRequest.getSalesId());
            relation.setSalesGroupId(customerRegisterRequest.getSalesGroupId());
            relation.setCustomerId(customer.getId());
            customerSalesRelationService.saveCustomerSalesRelation(relation);
        }
        // 2.异步执行注册、客户业务关联逻辑，先提前生成响应实体类返回
        // asyncRegister(customer, customerRegisterRequest);
        if (saveCustomer && StringUtils.isNotBlank(customer.getAvatarUrl())) {
            String avatarUrl = customer.getAvatarUrl();
            Long customerId = customer.getId();
            asyncRegisterPool.submitTask(() -> {
                // 更新客户头像信息 上传至 cos中
                this.saveAvatarUrlToCos(avatarUrl, customerId);
            });
        }
        CustomerResponse customerResponse = new CustomerResponse();
        BeanUtils.copyProperties(customer, customerResponse);
        return customerResponse;
    }

    /**
     * description : 获取头像并上传至COS
     *
     * @return void
     * @title: saveAvatarUrlToCos
     * @param: avatarUrl
     * @param: customerId
     * <AUTHOR>
     * @date 2025/5/14 1:02
     */
    private void saveAvatarUrlToCos(String avatarUrl, Long customerId) {
        HttpURLConnection conn = null;
        try {
            URL url = new URL(avatarUrl);
            // 1. 发起 HEAD 请求获取 Content-Length
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("HEAD");
            conn.setConnectTimeout(3000);
            conn.setReadTimeout(3000);
            conn.connect();

            int contentLength = conn.getContentLength();
            if (contentLength == -1) {
                throw new RuntimeException("无法获取图片大小，服务器未返回 Content-Length");
            }

            if (contentLength > MAX_IMAGE_SIZE) {
                throw new RuntimeException("头像图片大小超过限制: " + contentLength + " bytes");
            }

            // 2. 如果大小符合要求，再发起 GET 请求下载
            conn.disconnect();
            conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(3000);
            conn.setReadTimeout(3000);
            conn.connect();

            // 3. 上传至 COS
            String fileKey = "avatars/" + customerId + ".jpg"; // 可根据扩展名动态处理
            try (InputStream inputStream = conn.getInputStream()) {
                cosClient.putObject(cosConfig.getBucketName(), fileKey, inputStream, new ObjectMetadata());
            } catch (Exception e) {
                throw new RuntimeException("上传至COS失败：" + fileKey, e);
            }
            // 5. 构建 COS 地址并更新数据库
            String cosUrl = cosAddr + "/" + fileKey;

            LambdaUpdateWrapper<Customer> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Customer::getId, customerId)
                    .set(Customer::getAvatarUrl, cosUrl);
            this.update(updateWrapper);
        } catch (Exception e) {
            log.error("更新客户头像信息失败，customerId={}, error={}", customerId, e.getMessage(), e);
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    /**
     * description : 保存微信用户openid关联表
     *
     * @return void
     * @title: saveWechatUserOpenid
     * @param: customerRegisterRequest
     * @param: customerId
     * @param: now
     * <AUTHOR>
     * @date 2025/4/4 18:52
     */
    private void saveWechatUserOpenid(CustomerRegisterRequest customerRegisterRequest, Long customerId, LocalDateTime now) {
        WechatUserOpenid wechatUserOpenid = new WechatUserOpenid();
        wechatUserOpenid.setCustomerId(customerId);
        wechatUserOpenid.setAppId(customerRegisterRequest.getAppid());
        wechatUserOpenid.setOpenid(customerRegisterRequest.getOpenid());
        wechatUserOpenid.setCreatedAt(now);
        wechatUserOpenid.setUpdatedAt(now);
        wechatUserOpenid.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        wechatUserOpenidService.save(wechatUserOpenid);
    }

    /**
     * description : 客户登录
     *
     * @return CustomerResponse
     * @title: customerLogin
     * @param: customerLoginRequest
     * <AUTHOR>
     * @date 2025/4/4 18:55
     */
    @Override
    public CustomerResponse customerLogin(CustomerLoginRequest customerLoginRequest) {
        log.info("客户登录服务接收到消息:{}", customerLoginRequest);
        // 客户主键id，unionId一起查询，防止未使用分表键导致全表查询
        Long customerId = customerLoginRequest.getCustomerId();
        String unionId = customerLoginRequest.getUnionId();
        LambdaQueryWrapper<Customer> customerLambdaQueryWrapper = new LambdaQueryWrapper<>();
        customerLambdaQueryWrapper.eq(Customer::getId, customerId)
                .eq(Customer::getUnionId, unionId);
        Customer customer = getOne(customerLambdaQueryWrapper);
        if (ObjectUtil.equals(customer.getForbiddenStatus(), DisableStatusEnum.DISABLE.getCode())) {
            String msg = "您的账号没有登陆权限!";
            log.error("customerId:" + customerId + ",unionId:" + unionId + msg);
            throw new RuntimeException(msg);
        }
        if (ObjectUtil.equals(customer.getForbiddenStatus(), DisableStatusEnum.TO_BE_ENABLED.getCode())) {
            String msg = "请联系您的群主启用您的账号!";
            log.error("customerId:" + customerId + ",unionId:" + unionId + msg);
            throw new RuntimeException(msg);
        }
        CustomerResponse customerResponse = new CustomerResponse();
        // 全量数据拷贝，防止后续auth服务缓存覆盖信息不全
        BeanUtils.copyProperties(customer, customerResponse);
        customer.setLastActiveTime(LocalDateTime.now());
        // 同步更新本次登录时间；记得走分表键；置空分表规则与主键，防止意外更新
        LambdaUpdateWrapper<Customer> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Customer::getId, customer.getId())
                .eq(Customer::getUnionId, customer.getUnionId());
        customer.setOpenid(customerLoginRequest.getOpenid());
        customer.setUnionId(null);
        customer.setId(null);
        this.update(customer, updateWrapper);
        return customerResponse;
    }


    /**
     * description : 更新客户课程关联表
     *
     * @return boolean
     * @title: updCourseCustomerRel
     * @param: customerCourseRelation
     * <AUTHOR>
     * @date 2025/4/12 20:39
     */
    @Override
    public boolean updCourseCustomerRel(CustomerCourseRelation customerCourseRelation) {
        Integer arrivalStatus = customerCourseRelation.getArrivalStatus();
        Long campPeriodId = customerCourseRelation.getCampPeriodId();
        Long customerId = customerCourseRelation.getCustomerId();
        Long courseId = customerCourseRelation.getCourseId();
        if (ObjectUtil.isNull(arrivalStatus) || ObjectUtil.isNull(campPeriodId)) {
            return false;
        }
        // 保存客户标签
        Result<CampPeriodCourseResponse> result = campPeriodRpcService.queryCampPeriod(campPeriodId, courseId);
        if (result.getCode() != Success.getCode()) {
            throw new RuntimeException(result.getMsg());
        }
        LocalDateTime now = LocalDateTime.now();
        CampPeriodCourseResponse periodCourseResponse = result.getData();
        String courseName = periodCourseResponse.getCourseName();
        CustomerTags customerTags = new CustomerTags();
        if (arrivalStatus == CourseStatusEnum.ATTENDED.getCode()) {
            // 已到课
            courseName = courseName + CourseStatusEnum.ATTENDED.getInfo();
            customerTags.setTagsName(courseName);
            customerCourseRelation.setArrivalTime(now);
        } else if (arrivalStatus == CourseStatusEnum.COMPLETED.getCode()) {
            // 已完课
            courseName = courseName + CourseStatusEnum.COMPLETED.getInfo();
            customerTags.setTagsName(courseName);
            customerCourseRelation.setCompleteTime(now);
        }
        customerTags.setCampPeriodId(campPeriodId);
        customerTags.setCustomerId(customerId);
        customerTags.setCampPeriodName(periodCourseResponse.getCampperiodName());
        customerTagsService.saveTags(customerTags);
        // 设置企微标签
        CustomerMarkTagBackendDTO customerMarkTagBackendDTO = new CustomerMarkTagBackendDTO();
        customerMarkTagBackendDTO.setCustomerId(customerId);
        customerMarkTagBackendDTO.setSalesId(customerCourseRelation.getSalesId());
        customerMarkTagBackendDTO.setTagName(courseName);
        customerWecomTagService.markCustomerTagByBackend(customerMarkTagBackendDTO);
        LambdaQueryWrapper<CustomerCourseRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CustomerCourseRelation::getCustomerId, customerId)
                .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId)
                .eq(CustomerCourseRelation::getCourseId, courseId);
        List<CustomerCourseRelation> list = customerCourseRelationService.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            LambdaUpdateWrapper<CustomerCourseRelation> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CustomerCourseRelation::getCustomerId, customerId)
                    .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId)
                    .eq(CustomerCourseRelation::getCourseId, courseId)
                    .set(CustomerCourseRelation::getArrivalStatus, arrivalStatus)
                    .set(ObjectUtil.isNotEmpty(customerCourseRelation.getArrivalTime()),
                            CustomerCourseRelation::getArrivalTime, customerCourseRelation.getArrivalTime())
                    .set(ObjectUtil.isNotEmpty(customerCourseRelation.getCompleteTime()),
                            CustomerCourseRelation::getCompleteTime, customerCourseRelation.getCompleteTime())
                    .set(CustomerCourseRelation::getUpdatedAt, now);
            return customerCourseRelationService.update(updateWrapper);
        }
        customerCourseRelation.setId(idWorker.nextId());
        customerCourseRelation.setArrivalStatus(CourseStatusEnum.NOT_ATTENDED.getCode());
        customerCourseRelation.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        customerCourseRelation.setCreatedAt(now);

        // 更新最近活跃时间
        this.updLastActiveTime(customerId);
        return customerCourseRelationService.save(customerCourseRelation);
    }

    @Override
    public boolean updCourseCustomerRelStatus(CustomerCourseRelation customerCourseRelation) {
        LambdaUpdateWrapper<CustomerCourseRelation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CustomerCourseRelation::getCustomerId, customerCourseRelation.getCustomerId())
                .eq(CustomerCourseRelation::getCampPeriodId, customerCourseRelation.getCampPeriodId())
                .eq(CustomerCourseRelation::getCourseId, customerCourseRelation.getCourseId())
                .set(CustomerCourseRelation::getArrivalStatus, customerCourseRelation.getArrivalStatus());
        return customerCourseRelationService.update(customerCourseRelation, updateWrapper);
    }

    /**
     * description : 更新课程播放时长
     * @title: updCourseDuration
     * @param: customerCourseRelation
     * <AUTHOR>
     * @date 2025/6/6 21:42
     * @return boolean
     */
    @Override
    public boolean updCourseDuration(CustomerCourseRelation customerCourseRelation) {
        LocalDateTime now = LocalDateTime.now();
        Long customerId = customerCourseRelation.getCustomerId();
        Long campPeriodId = customerCourseRelation.getCampPeriodId();
        Long courseId = customerCourseRelation.getCourseId();
        LambdaQueryWrapper<CustomerCourseRelation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CustomerCourseRelation::getCustomerId, customerId)
                .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId)
                .eq(CustomerCourseRelation::getCourseId, courseId);
        List<CustomerCourseRelation> list = customerCourseRelationService.list(queryWrapper);
        if (ObjectUtil.isEmpty(list)) {
            log.error("客户营期课程关系不存在:",  customerId, campPeriodId, courseId);
            return false;
        }
        // 更新最近活跃时间
        this.updLastActiveTime(customerId);
        Integer arrivalStatus = list.get(0).getArrivalStatus();
        if (ObjectUtil.isNotEmpty(arrivalStatus) && ObjectUtil.equals(arrivalStatus, CourseStatusEnum.COMPLETED.getCode())) {
            log.error("客户已到课:",  customerId, campPeriodId, courseId);
            return true;
        }
        LambdaUpdateWrapper<CustomerCourseRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerCourseRelation::getCustomerId, customerId)
                .eq(CustomerCourseRelation::getCampPeriodId, campPeriodId)
                .eq(CustomerCourseRelation::getCourseId, courseId)
                .set(CustomerCourseRelation::getPlayProgress, customerCourseRelation.getPlayProgress())
                .set(CustomerCourseRelation::getUpdatedAt, now);
        return customerCourseRelationService.update(updateWrapper);
    }

    /**
     * description : 更新最近活跃时间
     * @title: updLastActiveTime
     * @param: customer
     * <AUTHOR>
     * @date 2025/6/6 22:04
     * @return boolean
     */
    @Override
    public boolean updLastActiveTime(Long customerId) {
        LambdaUpdateWrapper<Customer> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Customer::getLastActiveTime, LocalDateTime.now())
                .eq(Customer::getId, customerId);
        return update(updateWrapper);
    }

    /**
     * 分页查询客户列表
     *
     * @param current 当前页码
     * @param size    每页大小
     * @param request 查询条件（企微状态、手机号、活跃行为、标签等）
     * @return 客户信息分页结果
     */
    @Override
    public Page<CustomerQueryResponse> queryCustomerPage(long current, long size, CustomerQueryRequest request) {
        int shardCount = customerShardingConfig.getTableCount();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 获取用户角色对应查询权限
        Integer roleType = systemUserSelfInfo.getRoleType();
//        System.out.println("roleId:" + roleType);
        if (ObjectUtil.isEmpty(roleType) || (roleType  != UserRole.ADMIN.getCode()
                && roleType != UserRole.COMMON_ADMIN.getCode()
                && roleType != UserRole.COL_ADMIN.getCode()
                && roleType != UserRole.COM_ADMIN.getCode()
                && roleType != UserRole.SALE_ADMIN.getCode())) {
            // 普通销售
            request.setSalesId(systemUserSelfInfo.getId());
        } else {
            // 管理员
            OrganizationQueryRequest organizationQueryRequest = new OrganizationQueryRequest();
            if (ObjectUtil.isNotEmpty(request.getColumnId())) {
                organizationQueryRequest.setColumnId(String.valueOf(request.getColumnId()));
            }
            if (ObjectUtil.isNotEmpty( request.getCompanyId())) {
                organizationQueryRequest.setCompanyId(String.valueOf(request.getCompanyId()));
            }
            SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserSelfInfo, UserSelectType.system_user, organizationQueryRequest);
            // 2.1、需要判断可见权限范围，如果有返回就优先走范围查询
            if(selectPermission != null) {
                request.setPerColumnId(selectPermission.getPerColumnId());
                request.setPerCompanyId(selectPermission.getPerCompanyId());
                request.setPerSalesGroupId(selectPermission.getPerSalesGroupId());
                request.setSalesGroupId(selectPermission.getSalesGroupId());
            }
        }
        // 判断是否进行了组织架构筛选，有则设置需要销售连接标志不为空，进行连接查询
        if (CollectionUtil.isNotEmpty(request.getPerColumnId()) || CollectionUtil.isNotEmpty(request.getPerCompanyId())
                || CollectionUtil.isNotEmpty(request.getPerSalesGroupId())
                || request.getColumnId() != null || request.getCampPeriodId() != null || request.getSalesId() != null
                || request.getCompanyId() != null || request.getSalesGroupId() != null) {
            request.setNeedJoinSales("1");
        }
        // 对每个分片的数据都进行一次分页查询，首先计算当前分页的查询索引处
        long beginIndex;
        if(current > 0) {
            beginIndex = (current - 1) * size;
        } else {
            beginIndex = 0;
        }
        List<CustomerQueryResponse> customerQueryResponses = IntStream.range(0, shardCount)
                .parallel()
                .mapToObj(i -> baseMapper.queryCustomerPage(i, beginIndex, size, request))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        // 在内存中将最后聚合的数据再做一次按照创建时间排序，保证响应给前端的顺序正确
        List<CustomerQueryResponse> collect = customerQueryResponses.stream()
                .sorted(Comparator.comparing(CustomerQueryResponse::getCustomerCreatedAt).reversed())
                .collect(Collectors.toList());
        // 计算所有分片总数
        int count = IntStream.range(0, shardCount)
                .parallel()
                .map(i -> baseMapper.queryCustomerCount(i, request))
                .sum();
//        log.info("查询客户列表分页结果:{}", customerQueryResponses);
        return new Page<CustomerQueryResponse>(current, size*shardCount, count).setRecords(collect);
    }

    @Override
    public Page<CustomerQueryResponse> queryCustomerPageNew(long current, long size, CustomerQueryRequest request) {
        // 执行分页查询
        // 1.先根据条件筛选出全表中客户信息
        LambdaQueryWrapper<Customer> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(Customer::getCreatedAt, request.getCreateStartTime(), request.getCreateEndTime());
        List<Customer> customers = this.getBaseMapper().selectList(wrapper);
        // 1.1对拿到的客户信息进行倒排后，按照分页参数过滤
        List<Customer> collect = customers.stream()
                .sorted(Comparator.comparing(Customer::getCreatedAt).reversed())
                .skip((int) (current - 1) * size)
                .limit((int) size)
                .collect(Collectors.toList());
        Page<CustomerQueryResponse> page = new Page<>(current, size);
        // 2.根据分页出的客户id集合查询客户标签信息
        List<Long> customerIds = collect.stream().map(Customer::getId).collect(Collectors.toList());
        request.setCustomerIds(customerIds);
        List<CustomerQueryResponse> customerQueryResponses = baseMapper.queryCustomerRelateInfo(request);
        // 3.将客户信息按照客户id导入客户标签集合
        for (CustomerQueryResponse customerQueryResponse : customerQueryResponses) {
            Long customerId = customerQueryResponse.getCustomerId();
            Customer customerInfo = customers.stream()
                    .filter(customer -> customer.getId().equals(customerId)).findFirst().orElseThrow(() -> new RuntimeException("客户标签信息查询失败"));
            BeanUtils.copyProperties(customerInfo, customerQueryResponse);
        }
        page.setRecords(customerQueryResponses);
        page.setTotal(customers.size());
        return page;
    }

    /**
     * @description: 异步注册
     * 除了客户注册、基本信息保存，还涉及关联销售关系保存
     * @author: xiaQL
     * @date: 2025/4/15 1:21
     */

    public void asyncRegister(Customer customer, CustomerRegisterRequest customerRegisterRequest) {
        // 创建任务
        CustomerRegisterTask task = new CustomerRegisterTask(customer, customerRegisterRequest, customerRegisterTaskInSpring);
        // 提交到线程池
        asyncRegisterPool.submitTask(task);
    }

    /**
     * @description: 异步更新客户信息
     * 更新客户上次登录时间
     * @author: xiaQL
     * @date: 2025/4/15 1:21
     */
    public void asyncUpdate(Customer customer) {
        // 创建任务
        CustomerUpdateTask task = new CustomerUpdateTask(customer, this);
        // 提交到线程池
        asyncUpdatePool.submitTask(task);
    }

    /**
     * description : 校验栏目、训练营、营期、销售组、销售
     * @title: verificationData
     * @param: customerCourseVideoRequest
     * <AUTHOR>
     * @date 2025/7/5 11:58
     * @return void
     */
    private void verificationData(CustomerCourseVideoRequest customerCourseVideoRequest) {
        // 校验栏目是否存在
        Long columnId = customerCourseVideoRequest.getColumnId();
        LambdaQueryWrapper<ColumnPO> columnPOLambdaQueryWrapper = Wrappers.lambdaQuery(ColumnPO.class);
        columnPOLambdaQueryWrapper.eq(ColumnPO::getId, columnId);
        Integer columnCount = columnService.getBaseMapper().selectCount(columnPOLambdaQueryWrapper);
        if (columnCount <= 0) {
            log.error("栏目不存在,请求参数：{}", customerCourseVideoRequest);
            throw new RuntimeException("参数异常");
        }
        // 校验训练营是否存在
        Long companyId = customerCourseVideoRequest.getCompanyId();
        LambdaQueryWrapper<CompanyPO> companyPOLambdaQueryWrapper = Wrappers.lambdaQuery(CompanyPO.class);
        companyPOLambdaQueryWrapper.eq(CompanyPO::getId, companyId)
                .eq(CompanyPO::getColumnId, columnId);
        Integer companyCount = companyService.getBaseMapper().selectCount(companyPOLambdaQueryWrapper);
        if (companyCount <= 0) {
            log.error("训练营不存在,请求参数：{}", customerCourseVideoRequest);
            throw new RuntimeException("参数异常");
        }
    }

    /**
     * description : 获取课程视频信息
     * @title: getCourseVideo
     * @param: customerCourseVideoRequest
     * <AUTHOR>
     * @date 2025/7/14 23:03
     * @return CampCourseVideoResponse
     */
    @Override
    public CampCourseVideoResponse getCourseVideo(CustomerCourseVideoRequest customerCourseVideoRequest) {
        log.info("获取课程请求参数，customerCourseVideoRequest:{}", customerCourseVideoRequest);
        Long customerId = customerCourseVideoRequest.getCustomerId();
        Long salesId = customerCourseVideoRequest.getSalesId();
        Long companyId = customerCourseVideoRequest.getCompanyId();
        CampCourseVideoResponse campCourseVideoResponse = new CampCourseVideoResponse();
        LocalDate now = LocalDate.now();
        Result<CampCourseVideoResponse> campTodayCourse = campPeriodRpcService.getCampTodayCourse(customerCourseVideoRequest.getCampPeriodId(), now);
        campCourseVideoResponse = campTodayCourse.getData();
        if (campTodayCourse.getCode() != Success.getCode()) {
            log.error("查询营期下课程失败：{}", campTodayCourse.getMsg());
            throw new RuntimeException("查询营期下课程失败：" + campTodayCourse.getMsg());
        }
        if (!Objects.equals(campCourseVideoResponse.getCampperiodStatus(), "1")) {
            throw new RuntimeException("训练营已过期,联系群主获取新的上课链接!");
        }
        String userType = customerCourseVideoRequest.getUserType();
        if (StringUtils.equals(userType, AccountTypeEnum.SYSTEM_USER.getCodeToString())) {
            LambdaQueryWrapper<SystemUserPO> userPOLambdaQueryWrapper = Wrappers.lambdaQuery(SystemUserPO.class);
            userPOLambdaQueryWrapper.eq(SystemUserPO::getId, customerId)
                    .eq(SystemUserPO::getAuditStatus, AuditStatusEnum.REJECTED.getCode());
            Integer count = systemUserService.getBaseMapper().selectCount(userPOLambdaQueryWrapper);
            if (count <= 0) {
                throw new RuntimeException("您的账号没有权限观看视频！");
            }
            campCourseVideoResponse.setHasCoursePermission(true);
            return  campCourseVideoResponse;
        }
        Customer customer = getById(customerId);
        if (ObjectUtil.equals(customer.getForbiddenStatus(), YesOrNoEnum.YES.getCode())) {
            log.error("客户被禁用,请求参数：{}", customerCourseVideoRequest);
            throw new RuntimeException("您的账号没有权限观看视频！");
        }
        if (ObjectUtil.equals(customer.getForbiddenStatus(), DisableStatusEnum.TO_BE_ENABLED.getCode())) {
            String msg = "请联系您的群主启用您的账号后才能观看视频!";
            log.error("客户账号未启用,请求参数：{}:" + customerCourseVideoRequest);
            throw new RuntimeException(msg);
        }
        // 校验栏目和训练营是否存在
        this.verificationData(customerCourseVideoRequest);
        // 1.先根据栏目和客户id 查询客户销售关联表
        LambdaQueryWrapper<CustomerSalesRelation> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CustomerSalesRelation::getCustomerId, customerId)
                .eq(CustomerSalesRelation::getColumnId, customerCourseVideoRequest.getColumnId());
        List<CustomerSalesRelation> list = customerSalesRelationService.list(wrapper);

        // 2.若有数据判断该栏目下是否存在客户销售关联信息
        if (ObjectUtil.isNotEmpty(list) && ObjectUtil.isNotNull(list.get(0))) {
            CustomerSalesRelation customerSalesRelation = list.get(0);
            // 3.(有数据)判断销售id 是否和传入的销售id 一致
            Long campPeriodId = customerSalesRelation.getCampPeriodId();
            if (ObjectUtil.equals(customerSalesRelation.getSalesId(), salesId)) {
                // 3.1.销售id一致 <返回课程信息>
                // 3.1.3营期id不一致（营期报名） 在客户销售关联表 更新 营期id <返回课程信息>
                if (ObjectUtil.notEqual(campPeriodId, customerCourseVideoRequest.getCampPeriodId())) {
                    customerSalesRelation.setCampPeriodId(customerCourseVideoRequest.getCampPeriodId());
                    customerSalesRelation.setUpdatedAt(LocalDateTime.now());
                    customerSalesRelationService.updateByCustomerId(customerSalesRelation);
                }
            } else {
                // 3.2.销售id不一致 <返回 该销售的信息 >
                SystemUserPO userInfo = systemUserService.getById(salesId); //传入进来的销售信息

                campCourseVideoResponse.setSalesName(customerSalesRelation.getSalesName());//当前所属的销售姓名
                campCourseVideoResponse.setSalesId(String.valueOf(customerSalesRelation.getSalesId()));
                campCourseVideoResponse.setUrlSalesName(userInfo.getUsername());//当前链接所属的销售姓名
                campCourseVideoResponse.setUrlSalesId(String.valueOf(userInfo.getId()));
                campCourseVideoResponse.setHasCoursePermission(false);
                return campCourseVideoResponse;
            }
        } else {
            // 4.(没客户销售关联信息数据)新增客户销售关联表（营期报名）<返回课程信息>
            CustomerSalesRelation relation = new CustomerSalesRelation();
            relation.setColumnId(customerCourseVideoRequest.getColumnId());
            relation.setCompanyId(companyId);
            relation.setCampPeriodId(customerCourseVideoRequest.getCampPeriodId());
            relation.setSalesId(salesId);
            relation.setSalesGroupId(customerCourseVideoRequest.getSalesGroupId());
            relation.setCustomerId(customerId);
            customerSalesRelationService.saveCustomerSalesRelation(relation);
        }
        String openid = customer.getOpenid();
        String unionId = customer.getUnionId();
        CampCourseVideoResponse customerWecomBinding = customerWecomBindingService.saveInfoByUnionIdAndOpenId(unionId, openid, companyId, customerId, salesId);
        if (ObjectUtils.isNotEmpty(campCourseVideoResponse.getNeedAddWechat())
                && campCourseVideoResponse.getNeedAddWechat()
                && !customerWecomBinding.getHasCoursePermission()) {
            return customerWecomBinding;
        }
        final List<CourseVideoResponse> videoList = campTodayCourse.getData().getCourseVideoList();
        if (CollectionUtil.isNotEmpty(videoList)) {
            CourseVideoResponse courseVideoResponse = videoList.get(0);
            CampCourseVideoResponse finalCampCourseVideoResponse = campCourseVideoResponse;
            asyncUpdatePool.submitTask(() -> {
                try {
                    // 关联课程信息
                    CustomerCourseRelation customerCourseRelation = new CustomerCourseRelation();
                    BeanUtils.copyProperties(customerCourseVideoRequest, customerCourseRelation);
                    customerCourseRelation.setCourseId(courseVideoResponse.getId());
                    customerCourseRelation.setCampPeriodName(finalCampCourseVideoResponse.getCampPeriodName());
                    customerCourseRelationService.saveCourseCustomerRel(customerCourseRelation);
                } catch (Exception e) {
                    log.error("异步更新客户信息失败：{}", e.getMessage(), e);
                }
            });
            // 3. 保存客户行为轨迹(课程学习)
            customerBehaviorService.saveCampVideoCourseLearning(customerId, companyId, customerCourseVideoRequest.getCampPeriodId(), courseVideoResponse.getId());
        }
        campCourseVideoResponse.setHasCoursePermission(true);
        // 没有领取红包的权限(将所有营销活动的数据清掉)
        if (ObjectUtil.equals(customer.getRedPacketStatus(), YesOrNoEnum.YES.getCode())) {
            List<CourseVideoResponse> courseVideoList = campCourseVideoResponse.getCourseVideoList();
            if (ObjectUtil.isNotEmpty(courseVideoList)) {
                courseVideoList.forEach(courseVideoResponse -> {
                    if (ObjectUtil.isNotEmpty(courseVideoResponse.getActivityInfo())) {
                        courseVideoResponse.setActivityInfo(null);
                    }
                });
            }
            campCourseVideoResponse.setCourseVideoList(courseVideoList);
        }
        return campCourseVideoResponse;
    }

    /**
     * description : 提交答题
     *
     * @return boolean
     * @title: submitAnswer
     * @param: customerCourseVideoRequest
     * <AUTHOR>
     * @date 2025/5/9 1:37
     */
    @Override
    public boolean submitAnswer(CustomerCourseVideoRequest customerCourseVideoRequest) {
        return customerBehaviorService.saveCampQuiz(customerCourseVideoRequest.getCustomerId(), customerCourseVideoRequest.getCompanyId(), customerCourseVideoRequest.getCampPeriodId(), customerCourseVideoRequest.getCourseId());
    }

    /**
     * description : 更新手机号
     *
     * @return boolean
     * @title: updateMobile
     * @param: customer
     * <AUTHOR>
     * @date 2025/5/13 21:32
     */
    @Override
    public boolean updateMobile(Customer customer) {
        LambdaUpdateWrapper<Customer> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Customer::getMobile, customer.getMobile())
                .set(Customer::getMobileStatus, UseStatusEnum.EFFECTIVE.getCode())
                .set(Customer::getLastActiveTime, LocalDateTime.now())
                .eq(Customer::getId, customer.getId())
                .eq(Customer::getUnionId, customer.getUnionId());
        boolean update = update(updateWrapper);
        if (update) {
            CustomerResponse customerResponse = redisJsonUtils.get(CUSTOM_LOGIN_TOKEN + StpUtil.getTokenValue(), CustomerResponse.class);
            customerResponse.setMobile(customer.getMobile());
            // 更新成功，更新缓存
            redisJsonUtils.update(CUSTOM_LOGIN_TOKEN + StpUtil.getTokenValue(), customerResponse);
        }
        return update;
    }

    @Override
    public boolean updStatus(CustomerBatchRequest customerBatchRequest) {
        LambdaUpdateWrapper<Customer> updateWrapper = Wrappers.lambdaUpdate();
        List<Long> customerIds = customerBatchRequest.getCustomerIds();
        if (ObjectUtils.isNotEmpty(customerIds)) {
            customerIds.forEach(customerId -> {
                if (ObjectUtil.isNotEmpty(customerBatchRequest.getForbiddenStatus())) {
                    userCacheUtil.deleteCustomerCache(customerId);
                }
            });


            if (ObjectUtil.isNotEmpty(customerBatchRequest.getForbiddenStatus())) {
                // 禁用、启用账号的时候同时禁用、启用红包
                updateWrapper.set(Customer::getForbiddenStatus, customerBatchRequest.getForbiddenStatus())
                        .set(Customer::getRedPacketStatus, customerBatchRequest.getForbiddenStatus());
            } else {
                updateWrapper.set(ObjectUtil.isNotEmpty(customerBatchRequest.getRedPacketStatus()), Customer::getRedPacketStatus, customerBatchRequest.getRedPacketStatus());
            }
            updateWrapper.in(Customer::getId, customerIds);
            return update(updateWrapper);
        }
        return false;
    }

    @Override
    public Result<Object> delCustom(CustomerLoginRequest request) {
        // 非超管、普管不允许调用
//        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
//        userCacheUtil.checkUserAdmin(systemUserSelfInfo);
        // 1、删除缓存中
        // 1.1客户注册信息
        String registerKey = USER_REGISTER + request.getUnionId();
        redisJsonUtils.delete(registerKey);
        // 1.2客户登录缓存
        userCacheUtil.deleteCustomerCache(request.getCustomerId());
        // 2、删除数据库
        // 2。1客户注册信息
        LambdaQueryWrapper<UserAuth> authLambdaQueryWrapper = new LambdaQueryWrapper<>();
        authLambdaQueryWrapper.eq(UserAuth::getUnionId, request.getUnionId())
                .eq(UserAuth::getId, request.getCustomerId());
        userAuthService.getBaseMapper().delete(authLambdaQueryWrapper);
        // 2.2删除客户信息
        this.getBaseMapper().deleteById(request.getCustomerId());
        // 删掉企微关联信息
        customerWecomBindingService.deleteByCustomerId(request.getCustomerId());
        return Result.ok();
    }

    @Override
    public Boolean updateRedPacketAndUseStatus(wxPayRequest wxPayRequest) {
        LambdaUpdateWrapper<Customer> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Customer::getRedPacketStatus, YesOrNoEnum.YES.getCode())
                .set(Customer::getForbiddenStatus, YesOrNoEnum.YES.getCode())
                .eq(Customer::getId, wxPayRequest.getCustomerId());
        boolean update = update(updateWrapper);
        // 增加手动疑似红包党标签
        ArrayList<Long> custIds = new ArrayList<>();
        custIds.add(wxPayRequest.getCustomerId());
        CustomerTags customerTags = new CustomerTags();
        customerTags.setManualTagsName("⚠️疑似红包党");
        customerTags.setCustomerIds(custIds);
        customerTags.setCampPeriodId(wxPayRequest.getCampPeriodId());
        customerTagsService.saveManualTag(customerTags);
        // 清除客户登录缓存
        userCacheUtil.deleteCustomerCache(wxPayRequest.getCustomerId());
        return update;
    }

    /**
     * description : 更新添加企微的状态
     * @title: updateWecomStatus
     * @param: customerId
     * @param: status
     * <AUTHOR>
     * @date 2025/7/26 0:00
     * @return Boolean
     */
    public Boolean updateWecomStatus(Long customerId, Integer status) {
        LambdaUpdateWrapper<Customer> customerLambdaUpdateWrapper = Wrappers.lambdaUpdate(Customer.class);
        customerLambdaUpdateWrapper.eq(Customer::getId, customerId)
                .set(Customer::getWeworkStatus, status);
        // TODO 更新添加企微的行为轨迹
        return update(customerLambdaUpdateWrapper);
    }

    /**
     * description : 获取客户详情
     * @title: getCustomerDetail
     * @param: customerId
     * <AUTHOR>
     * @date 2025/7/26 0:54
     * @return JSONArray
     */
    @Override
    public JSONArray getCustomerDetail(Long customerId) {
        // 1. 根据customerId查询企微绑定信息
        LambdaQueryWrapper<CustomerWecomBinding> bindingWrapper = new LambdaQueryWrapper<>();
        bindingWrapper.eq(CustomerWecomBinding::getCustomerId, customerId)
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        List<CustomerWecomBinding> wecomBindings = customerWecomBindingService.list(bindingWrapper);
        
        if (ObjectUtil.isEmpty(wecomBindings)) {
            log.warn("客户ID: {} 未找到企微绑定信息", customerId);
            return new JSONArray();
        }

        // 2. 按企业corpId分组处理客户详情
        JSONArray result = new JSONArray();
        Map<String, List<CustomerWecomBinding>> bindingsByCorpId = new HashMap<>();

        // 将企微绑定信息按corpId分组
        for (CustomerWecomBinding binding : wecomBindings) {
            String corpId = binding.getCorpId();
            if (StrUtil.isNotEmpty(corpId)) {
                bindingsByCorpId.computeIfAbsent(corpId, k -> new ArrayList<>()).add(binding);
            }
        }

        // 3. 预加载所有需要的数据（批量查询优化）
        Set<String> allCorpIds = bindingsByCorpId.keySet();
        Map<String, CompanyQyRelation> corpIdToQyRelation = new ConcurrentHashMap<>();
        Map<String, String> corpIdToAccessToken = new ConcurrentHashMap<>();

        // 批量查询企微配置信息
        if (!allCorpIds.isEmpty()) {
            LambdaQueryWrapper<CompanyQyRelation> qyRelationWrapper = new LambdaQueryWrapper<>();
            qyRelationWrapper.in(CompanyQyRelation::getCorpId, allCorpIds);
            List<CompanyQyRelation> allQyRelations = companyQyRelationService.list(qyRelationWrapper);

            for (CompanyQyRelation qyRelation : allQyRelations) {
                corpIdToQyRelation.put(qyRelation.getCorpId(), qyRelation);
            }
        }

        // 并行获取AccessToken（减少串行等待时间）
        List<CompletableFuture<Void>> accessTokenFutures = allCorpIds.stream()
            .map(corpId -> CompletableFuture.runAsync(() -> {
                try {
                    QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyAccessToken(corpId);
                    if (qyAccessToken != null && StrUtil.isNotEmpty(qyAccessToken.getAccess_token())) {
                        corpIdToAccessToken.put(corpId, qyAccessToken.getAccess_token());
                    }
                } catch (Exception e) {
                    log.error("获取企微AccessToken失败，corpId: {}, error: {}", corpId, e.getMessage());
                }
            }))
            .collect(Collectors.toList());

        // 等待所有AccessToken获取完成
        CompletableFuture.allOf(accessTokenFutures.toArray(new CompletableFuture[0])).join();

        // 4. 为每个企业获取客户详情
        for (Map.Entry<String, List<CustomerWecomBinding>> entry : bindingsByCorpId.entrySet()) {
            String corpId = entry.getKey();
            List<CustomerWecomBinding> corpBindings = entry.getValue();

            try {
                CompanyQyRelation qyRelation = corpIdToQyRelation.get(corpId);
                if (qyRelation == null) {
                    log.warn("corpId: {} 未找到对应的企微配置信息", corpId);
                    continue;
                }

                // 获取企微的accessToken（缓存优化）
                String accessToken = corpIdToAccessToken.get(corpId);
                if (accessToken == null) {
                    QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyAccessToken(corpId);
                    if (qyAccessToken == null || StrUtil.isEmpty(qyAccessToken.getAccess_token())) {
                        log.error("获取企微AccessToken失败，corpId: {}", corpId);
                        continue;
                    }
                    accessToken = qyAccessToken.getAccess_token();
                    corpIdToAccessToken.put(corpId, accessToken);
                }

                // 构建企业分组信息
                JSONObject companyGroup = new JSONObject();
                companyGroup.put("corpId", corpId);
                companyGroup.put("companyId", qyRelation.getCompanyId());

                // 从第一个绑定信息中获取企业名称
                if (!corpBindings.isEmpty()) {
                    CustomerWecomBinding firstBinding = corpBindings.get(0);
                    companyGroup.put("corpName", firstBinding.getCorpName());
                }

                JSONArray customerDetails = new JSONArray();

                // 并行获取客户详情（提高接口调用效率）
                final String finalAccessToken = accessToken; // 创建final变量供lambda使用
                List<CompletableFuture<JSONObject>> customerDetailFutures = corpBindings.stream()
                    .map(binding -> CompletableFuture.supplyAsync(() -> {
                        try {
                            JSONObject customerDetail = qyWxCustContactClient.get(finalAccessToken, binding.getExternalUserid(), null);
                            if (customerDetail != null) {
                                customerDetail.put("bindingInfo", binding);
                                return customerDetail;
                            }
                        } catch (Exception e) {
                            log.error("获取客户详情失败，externalUserId: {}, error: {}", binding.getExternalUserid(), e.getMessage());
                        }
                        return null;
                    }))
                    .collect(Collectors.toList());

                // 等待所有客户详情获取完成并收集结果
                List<JSONObject> customerDetailsList = customerDetailFutures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                // 收集所有oper_userid用于批量查询
                Set<String> corpUserIds = new HashSet<>();
                for (JSONObject customerDetail : customerDetailsList) {
                    JSONArray followUsers = customerDetail.getJSONArray("follow_user");
                    if (followUsers != null) {
                        for (int i = 0; i < followUsers.size(); i++) {
                            JSONObject followUser = followUsers.getJSONObject(i);
                            String userId = followUser.getString("userid");
                            if (StrUtil.isNotEmpty(userId)) {
                                corpUserIds.add(userId);
                            }
                        }
                    }
                }

                // 批量查询当前企业的所有企微用户信息
                Map<String, SystemUserQyRelation> operUserIdToQyRelation = new HashMap<>();
                if (!corpUserIds.isEmpty()) {
                    LambdaQueryWrapper<SystemUserQyRelation> userQyWrapper = new LambdaQueryWrapper<>();
                    userQyWrapper.in(SystemUserQyRelation::getQyUserId, corpUserIds)
                                .eq(SystemUserQyRelation::getCorpId, corpId);
                    List<SystemUserQyRelation> userQyRelations = systemUserQyRelationService.list(userQyWrapper);

                    for (SystemUserQyRelation userQyRelation : userQyRelations) {
                        operUserIdToQyRelation.put(userQyRelation.getQyUserId(), userQyRelation);
                    }
                }

                // 为客户详情添加企微用户信息
                for (JSONObject customerDetail : customerDetailsList) {
                    JSONArray followUsers = customerDetail.getJSONArray("follow_user");
                    if (followUsers != null) {
                        for (int i = 0; i < followUsers.size(); i++) {
                            JSONObject followUser = followUsers.getJSONObject(i);
                            String userId = followUser.getString("userid");

                            if (StrUtil.isNotEmpty(userId)) {
                                SystemUserQyRelation userQyRelation = operUserIdToQyRelation.get(userId);
                                if (userQyRelation != null) {
                                    // 添加企微用户信息到follow_user中
                                    JSONObject qyUserInfo = new JSONObject();
                                    qyUserInfo.put("systemUserId", userQyRelation.getSystemUserId());
                                    qyUserInfo.put("qyUserId", userQyRelation.getQyUserId());
                                    qyUserInfo.put("qyName", userQyRelation.getQyName());
                                    qyUserInfo.put("activeCode", userQyRelation.getActiveCode());
                                    qyUserInfo.put("accountType", userQyRelation.getAccountType());
                                    qyUserInfo.put("activeTime", userQyRelation.getActiveTime());
                                    qyUserInfo.put("configId", userQyRelation.getConfigId());
                                    qyUserInfo.put("qrCode", userQyRelation.getQrCode());
                                    qyUserInfo.put("status", userQyRelation.getStatus());

                                    followUser.put("qyUserInfo", qyUserInfo);
                                }
                            }
                        }
                    }
                    customerDetails.add(customerDetail);
                }

                companyGroup.put("customerDetails", customerDetails);
                result.add(companyGroup);

            } catch (Exception e) {
                log.error("处理corpId: {} 的客户详情时发生异常: {}", corpId, e.getMessage(), e);
            }
        }

        return result;
    }

    /**
     * description : 批量设置客户企微备注
     * @title: batchRemarkCustomer
     * @param: param 批量设置备注参数
     * <AUTHOR>
     * @date 2025/7/26
     * @return BatchRemarkCustomerVO
     */
    @Override
    public BatchRemarkCustomerVO batchRemarkCustomer(BatchRemarkCustomerRequest param) {
        log.info("开始批量设置客户企微备注，参数: {}", JSONObject.toJSONString(param));

        BatchRemarkCustomerVO result = new BatchRemarkCustomerVO();
        result.setTotalCount(param.getCustomerIds().size());
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setSuccessCustomerIds(new ArrayList<>());
        result.setFailureDetails(new ArrayList<>());

        // 1. 验证销售用户ID参数
        Long salesId = param.getSalesId();
        if (salesId == null) {
            log.error("销售用户ID不能为空");
            // 所有客户都标记为失败
            for (Long customerId : param.getCustomerIds()) {
                BatchRemarkCustomerVO.FailureDetail failureDetail = new BatchRemarkCustomerVO.FailureDetail();
                failureDetail.setCustomerId(customerId);
                failureDetail.setFailureReason("销售用户ID不能为空");
                failureDetail.setErrorCode("SALES_USER_ID_EMPTY");
                result.getFailureDetails().add(failureDetail);
            }
            result.setFailureCount(param.getCustomerIds().size());
            return result;
        }

        log.info("使用销售用户ID: {}", salesId);

        // 2. 获取企微AccessToken
        QyWeChatAuthResponse qyAccessToken;
        try {
            qyAccessToken = qyAuthService.getQyAccessToken(param.getCorpId());
            if (qyAccessToken == null || StrUtil.isEmpty(qyAccessToken.getAccess_token())) {
                log.error("获取企微AccessToken失败，corpId: {}", param.getCorpId());
                throw new RuntimeException("获取企微AccessToken失败");
            }
        } catch (Exception e) {
            log.error("获取企微AccessToken异常，corpId: {}, error: {}", param.getCorpId(), e.getMessage());
            // 所有客户都标记为失败
            for (Long customerId : param.getCustomerIds()) {
                BatchRemarkCustomerVO.FailureDetail failureDetail = new BatchRemarkCustomerVO.FailureDetail();
                failureDetail.setCustomerId(customerId);
                failureDetail.setFailureReason("获取企微AccessToken失败: " + e.getMessage());
                failureDetail.setErrorCode("ACCESS_TOKEN_ERROR");
                result.getFailureDetails().add(failureDetail);
            }
            result.setFailureCount(param.getCustomerIds().size());
            return result;
        }

        String accessToken = qyAccessToken.getAccess_token();

        // 3. 根据销售ID和企业ID查询客户的企微绑定信息
        LambdaQueryWrapper<CustomerWecomBinding> bindingWrapper = new LambdaQueryWrapper<>();
        bindingWrapper.in(CustomerWecomBinding::getCustomerId, param.getCustomerIds())
                .eq(CustomerWecomBinding::getCorpId, param.getCorpId())
                .eq(CustomerWecomBinding::getSalesId, salesId)
                .eq(CustomerWecomBinding::getSalesUserid, param.getQyUserId())
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        List<CustomerWecomBinding> wecomBindings = customerWecomBindingService.list(bindingWrapper);

        // 4. 将绑定信息按客户ID分组
        Map<Long, List<CustomerWecomBinding>> bindingsByCustomerId = wecomBindings.stream()
                .collect(Collectors.groupingBy(CustomerWecomBinding::getCustomerId));

        // 5. 检查哪些客户没有找到绑定关系，直接标记为失败
        Set<Long> foundCustomerIds = bindingsByCustomerId.keySet();
        for (Long customerId : param.getCustomerIds()) {
            if (!foundCustomerIds.contains(customerId)) {
                log.warn("客户ID: {} 在企业: {} 中未找到与销售: {} 的绑定关系", customerId, param.getCorpId(), salesId);
                addFailureDetail(result, customerId, null, null,
                               String.format("客户在企业[%s]中未找到与销售[%s]的绑定关系", param.getCorpId(), salesId),
                               "BINDING_NOT_FOUND");
                synchronized (result) {
                    result.setFailureCount(result.getFailureCount() + 1);
                }
            }
        }

        // 6. 并行处理有绑定关系的客户的备注设置
        List<CompletableFuture<Void>> remarkFutures = foundCustomerIds.stream()
                .map(customerId -> CompletableFuture.runAsync(() -> {
                    processCustomerRemark(customerId, param, accessToken, bindingsByCustomerId, result);
                }))
                .collect(Collectors.toList());

        // 5. 等待所有备注设置完成
        CompletableFuture.allOf(remarkFutures.toArray(new CompletableFuture[0])).join();

        log.info("批量设置客户企微备注完成，总数: {}, 成功: {}, 失败: {}",
                result.getTotalCount(), result.getSuccessCount(), result.getFailureCount());

        return result;
    }

    /**
     * 处理单个客户的备注设置
     */
    private void processCustomerRemark(Long customerId, BatchRemarkCustomerRequest param, String accessToken,
                                       Map<Long, List<CustomerWecomBinding>> bindingsByCustomerId,
                                       BatchRemarkCustomerVO result) {
        try {
            List<CustomerWecomBinding> customerBindings = bindingsByCustomerId.get(customerId);

            // 此时customerBindings不会为空，因为在上层已经过滤了没有绑定关系的客户

            // 为该客户的每个绑定关系设置备注
            boolean hasSuccess = false;
            for (CustomerWecomBinding binding : customerBindings) {
                try {
                    // 构建企微备注请求参数
                    JSONObject remarkRequest = new JSONObject();
                    remarkRequest.put("userid", binding.getSalesUserid());
                    remarkRequest.put("external_userid", binding.getExternalUserid());
                    remarkRequest.put("remark", param.getRemark());

                    if (StrUtil.isNotEmpty(param.getDescription())) {
                        remarkRequest.put("description", param.getDescription());
                    }
                    if (StrUtil.isNotEmpty(param.getRemarkCompany())) {
                        remarkRequest.put("remark_company", param.getRemarkCompany());
                    }
                    if (ObjectUtil.isNotEmpty(param.getRemarkMobiles())) {
                        remarkRequest.put("remark_mobiles", param.getRemarkMobiles());
                    }
                    if (StrUtil.isNotEmpty(param.getRemarkPicMediaid())) {
                        remarkRequest.put("remark_pic_mediaid", param.getRemarkPicMediaid());
                    }

                    // 调用企微备注接口
                    JSONObject remarkResponse = qyWxCustContactClient.remark(accessToken, remarkRequest);

                    if (remarkResponse != null && remarkResponse.getInteger("errcode") == 0) {
                        hasSuccess = true;
                        log.debug("客户ID: {} 的企微备注设置成功，externalUserId: {}, salesUserId: {}",
                                customerId, binding.getExternalUserid(), binding.getSalesUserid());
                    } else {
                        String errorMsg = remarkResponse != null ? remarkResponse.getString("errmsg") : "未知错误";
                        String errorCode = remarkResponse != null ? remarkResponse.getString("errcode") : "UNKNOWN";
                        log.error("客户ID: {} 的企微备注设置失败，externalUserId: {}, salesUserId: {}, error: {}",
                                customerId, binding.getExternalUserid(), binding.getSalesUserid(), errorMsg);

                        addFailureDetail(result, customerId, binding.getExternalUserid(), binding.getSalesUserid(),
                                       errorMsg, errorCode);
                    }

                } catch (Exception e) {
                    log.error("客户ID: {} 设置企微备注异常，externalUserId: {}, salesUserId: {}, error: {}",
                            customerId, binding.getExternalUserid(), binding.getSalesUserid(), e.getMessage());

                    addFailureDetail(result, customerId, binding.getExternalUserid(), binding.getSalesUserid(),
                                   "设置备注异常: " + e.getMessage(), "EXCEPTION");
                }
            }

            // 如果至少有一个绑定关系设置成功，则认为该客户设置成功
            if (hasSuccess) {
                synchronized (result) {
                    result.getSuccessCustomerIds().add(customerId);
                    result.setSuccessCount(result.getSuccessCount() + 1);
                }
            } else {
                synchronized (result) {
                    result.setFailureCount(result.getFailureCount() + 1);
                }
            }

        } catch (Exception e) {
            log.error("处理客户ID: {} 的备注设置时发生异常: {}", customerId, e.getMessage(), e);
            addFailureDetail(result, customerId, null, null,
                           "处理异常: " + e.getMessage(), "PROCESS_ERROR");
            synchronized (result) {
                result.setFailureCount(result.getFailureCount() + 1);
            }
        }
    }

    /**
     * 添加失败详情
     */
    private void addFailureDetail(BatchRemarkCustomerVO result, Long customerId, String externalUserId,
                                String salesUserId, String failureReason, String errorCode) {
        BatchRemarkCustomerVO.FailureDetail failureDetail = new BatchRemarkCustomerVO.FailureDetail();
        failureDetail.setCustomerId(customerId);
        failureDetail.setExternalUserId(externalUserId);
        failureDetail.setSalesUserId(salesUserId);
        failureDetail.setFailureReason(failureReason);
        failureDetail.setErrorCode(errorCode);

        synchronized (result) {
            result.getFailureDetails().add(failureDetail);
        }
    }
}