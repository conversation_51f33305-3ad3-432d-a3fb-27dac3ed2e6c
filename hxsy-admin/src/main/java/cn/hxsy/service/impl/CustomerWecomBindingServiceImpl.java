package cn.hxsy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.user.feign.vx.QyAppClient;
import cn.hxsy.api.user.model.response.CampCourseVideoResponse;
import cn.hxsy.api.qy.feign.cust.contract.QyWxCustContactClient;
import cn.hxsy.api.qy.request.contact.CustomerMarkTagRequest;
import cn.hxsy.api.qy.response.contact.CustomerMarkTagResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.enums.WeComAddStatusEnum;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.dao.CustomerWecomBindingMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.api.qy.request.contact.CustomerTransferRequest;
import cn.hxsy.api.qy.response.contact.CustomerTransferResponse;
import cn.hxsy.request.CustomerTransferMapRequest;
import cn.hxsy.request.CustomerTransferSysRequest;
import cn.hxsy.service.*;
import cn.hxsy.service.qy.QyAuthService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户与企微external_userid及销售userid绑定关系服务实现类
 */
@Service
@Slf4j
public class CustomerWecomBindingServiceImpl extends ServiceImpl<CustomerWecomBindingMapper, CustomerWecomBinding>
        implements CustomerWecomBindingService {

    @Autowired
    private QyWxCustContactClient qyWxCustContactClient;

    @Autowired
    private SnowflakeIdWorker idWorker;

    @Autowired
    private QyAuthService qyAuthService;

    @Autowired
    private QyAppClient qyAppClient;

    @Autowired
    private CompanyQyRelationService companyQyRelationService;

    @Autowired
    private CompanyQyRelatedService companyQyRelatedService;

    @Autowired
    private SystemUserQyRelationService systemUserQyRelationService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerBehaviorService customerBehaviorService;

    /**
     * description : 根据企微Callback查询企微信息
     * @title: getInfoByPendingId
     * @param: pendingId
     * <AUTHOR>
     * @date 2025/7/4 0:00
     * @return boolean
     */
    @Override
    public boolean saveInfoByCallback(CustomerWecomBinding customerWecomBinding) {
        LambdaQueryWrapper<CustomerWecomBinding> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CustomerWecomBinding::getSalesId, customerWecomBinding.getSalesId())
                        .eq(CustomerWecomBinding::getCorpId, customerWecomBinding.getCorpId());
        queryWrapper.eq(CustomerWecomBinding::getExternalUserid, customerWecomBinding.getExternalUserid())
                .or()
                .eq(CustomerWecomBinding::getPendingId, customerWecomBinding.getPendingId());
        CustomerWecomBinding relation = getOne(queryWrapper);

        LambdaQueryWrapper<CompanyQyRelation> companyQyRelationServiceLambdaQueryWrapper = Wrappers.lambdaQuery(CompanyQyRelation.class);
        companyQyRelationServiceLambdaQueryWrapper.eq(CompanyQyRelation::getCorpId, customerWecomBinding.getCorpId());
        CompanyQyRelation companyQyRelation = companyQyRelationService.getOne(companyQyRelationServiceLambdaQueryWrapper);
        customerWecomBinding.setCorpName(companyQyRelation.getCorpName());
        LocalDateTime now = LocalDateTime.now();
        if (ObjectUtils.isNotEmpty(relation)) {
            if (!StringUtils.equals(relation.getSalesUserid(), customerWecomBinding.getSalesUserid())) {
                // 则新增企微中雄销售和客户的关联关系
                customerWecomBinding.setId(idWorker.nextId());
                customerWecomBinding.setCreatedAt(now);
                customerWecomBinding.setStatus(UseStatusEnum.INVALID.getCode());
                return save(customerWecomBinding);
            }
            // 已存在企微企业pending_id 则更新 外部联系人ID （先看课 后 加企微的情况）
            relation.setSalesUserid(customerWecomBinding.getSalesUserid());
            relation.setExternalUserid(customerWecomBinding.getExternalUserid());
            if (StringUtils.isNotBlank(customerWecomBinding.getPendingId())) {
                relation.setPendingId(customerWecomBinding.getPendingId());
            }
            relation.setCorpName(companyQyRelation.getCorpName());
            relation.setUpdatedAt(now);
            relation.setStatus(UseStatusEnum.EFFECTIVE.getCode());
            // 更新客户添加企微状态
            customerService.updateWecomStatus(relation.getCustomerId(), WeComAddStatusEnum.ADDED.getCode());
            // 设置添加企微的行为轨迹
            this.saveAddWechatEnterprise(relation);
            return updateById(relation);
        } else {
            // 不存在 则新增企微中雄销售和客户的关联关系
            customerWecomBinding.setId(idWorker.nextId());
            customerWecomBinding.setCreatedAt(now);
            customerWecomBinding.setStatus(UseStatusEnum.EFFECTIVE.getCode());
            return save(customerWecomBinding);
        }
    }

    /**
     * description : 逻辑删除企微中销售和客户的关联关系
     * @title: deleteCustomerWecomBinding
     * @param: customerWecomBinding
     * <AUTHOR>
     * @date 2025/7/4 21:59
     * @return boolean
     */
    @Override
    public boolean deleteCustomerWecomBinding(CustomerWecomBinding customerWecomBinding) {
        // 记录删除企微行为轨迹
        LambdaQueryWrapper<CustomerWecomBinding> lambdaQuery = Wrappers.lambdaQuery(CustomerWecomBinding.class);
        lambdaQuery.eq(CustomerWecomBinding::getExternalUserid, customerWecomBinding.getExternalUserid())
                .eq(CustomerWecomBinding::getCorpId, customerWecomBinding.getCorpId())
                .eq(CustomerWecomBinding::getSalesUserid, customerWecomBinding.getSalesUserid());

        CustomerWecomBinding wecomBinding = getOne(lambdaQuery);
        this.saveDeleteWechatEnterprise(wecomBinding);
        wecomBinding.setStatus(UseStatusEnum.INVALID.getCode());
        wecomBinding.setUpdatedAt(LocalDateTime.now());
        return updateById(wecomBinding);
    }

    /**
     * description : 根据unionid和openid保存关联信息
     * @title: saveInfoByUnionIdAndOpenId
     * @param: unionid
     * @param: openid
     * @param: companyId
     * @param: customerId
     * @param: salesId
     * <AUTHOR>
     * @date 2025/7/15 23:03
     * @return CampCourseVideoResponse
     */
    public CampCourseVideoResponse saveInfoByUnionIdAndOpenId(String unionid, String openid, Long companyId, Long customerId, Long salesId) {
        log.info("saveInfoByUnionIdAndOpenId 保存用户企微关联信息:{}, {}, {}, {}, {}", unionid, openid, companyId, customerId, salesId);

        CampCourseVideoResponse campCourseVideoResponse = new CampCourseVideoResponse();
        // 1 根据companyId获取corpId
        LambdaQueryWrapper<CompanyQyRelated> qyRelatedLambdaQueryWrapper = Wrappers.lambdaQuery(CompanyQyRelated.class);
        qyRelatedLambdaQueryWrapper.eq(CompanyQyRelated::getCompanyId, companyId);
        CompanyQyRelated companyQyRelated = companyQyRelatedService.getOne(qyRelatedLambdaQueryWrapper);
        if (ObjectUtils.isEmpty(companyQyRelated)) {
            log.error("unionid: " + unionid + "  openid: " + openid + "  companyId:" + companyId + "  customerId:" + customerId + "  salesId" + salesId + "  未找到对应公司企微关系");
            throw new RuntimeException("未找到对应公司企微关系");
        }
        String corpId = companyQyRelated.getCorpId();
        LambdaQueryWrapper<CompanyQyRelation> companyQyRelationServiceLambdaQueryWrapper = Wrappers.lambdaQuery(CompanyQyRelation.class);
        companyQyRelationServiceLambdaQueryWrapper.eq(CompanyQyRelation::getCorpId, corpId);
        CompanyQyRelation companyQyRelation = companyQyRelationService.getOne(companyQyRelationServiceLambdaQueryWrapper);
        // 2.根据 unionid、openid、customerId 查询关联信息是否存在
        LambdaQueryWrapper<CustomerWecomBinding> queryWrapper = Wrappers.lambdaQuery(CustomerWecomBinding.class);
        queryWrapper.eq(CustomerWecomBinding::getUnionId, unionid)
                .eq(CustomerWecomBinding::getOpenid, openid)
                .eq(CustomerWecomBinding::getCustomerId, customerId)
                .eq(CustomerWecomBinding::getSalesId, salesId)
                .eq(CustomerWecomBinding::getCorpId, corpId)
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        CustomerWecomBinding queryCustomerWecomBinding = getOne(queryWrapper);
        if (ObjectUtils.isNotEmpty(queryCustomerWecomBinding)) {
            // 2.1外部联系人信息已绑定
            if (StringUtils.isNotBlank(queryCustomerWecomBinding.getExternalUserid())
                    && StringUtils.isNotBlank(queryCustomerWecomBinding.getSalesUserid())) {
                String salesUserid = queryCustomerWecomBinding.getSalesUserid();
                // TODO 根据 salesId 查询 salesUserid 并校验 salesUserid
                campCourseVideoResponse.setHasCoursePermission(true);
                return campCourseVideoResponse;
            }
            // 2.2 删除企微后看课 返回添加企微
            LambdaQueryWrapper<SystemUserQyRelation> userQyRelationLambdaQueryWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
            userQyRelationLambdaQueryWrapper.eq(SystemUserQyRelation::getSystemUserId, salesId)
                    .eq(SystemUserQyRelation::getCorpId, corpId);
            SystemUserQyRelation systemUserQyRelation = systemUserQyRelationService.list(userQyRelationLambdaQueryWrapper).get(0);
            campCourseVideoResponse.setHasCoursePermission(false);
            campCourseVideoResponse.setSalesQrCode(systemUserQyRelation.getQrCode());
            return campCourseVideoResponse;
        }
        // 3.获取企微企业pending_id（当前未加企微） 或 外部联系人userId（已加企微）
        // 3.2 调用企微接口
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyAccessToken(corpId);
        log.info("获取企微{}，获取转换外部联系人id，响应状态码：{}, 响应内容:{}", corpId, qyAccessToken.getErrcode(), qyAccessToken);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("unionid", unionid);
        jsonObject.put("openid", openid);
        jsonObject.put("subject_type", "1"); // 使用服务商
        JSONObject object = qyAppClient.unionidToExternalUserid(qyAccessToken.getAccess_token(), jsonObject);
        Integer errcode = object.getInteger("errcode");
        log.info("获取企微{}，获取转换外部联系人id，响应状态码：{}, 响应内容:{}", corpId, errcode, object);
        // 3.3 获取转换外部联系人pending_id（当前未加企微） 或 外部联系人userId（已加企微）
        String externalUserid = object.getString("external_userid");
        String pendingId = object.getString("pending_id");

        LocalDateTime now = LocalDateTime.now();
        if (ObjectUtils.isNotEmpty(externalUserid)) {
            // 根据externalUserid查询客户详情
            JSONObject customerDetail = qyWxCustContactClient.get(qyAccessToken.getAccess_token(), externalUserid, null);


            // 已添加企微(先添加企微的情况)
            queryWrapper.clear();
            queryWrapper.eq(CustomerWecomBinding::getSalesId, salesId)
                    .eq(CustomerWecomBinding::getCustomerId, customerId)
                    .eq(CustomerWecomBinding::getCorpId, corpId)
                    .eq(CustomerWecomBinding::getExternalUserid, externalUserid)
                    .or()
                    .eq(CustomerWecomBinding::getPendingId, pendingId);
            queryCustomerWecomBinding = getOne(queryWrapper);
            if (ObjectUtils.isEmpty(queryCustomerWecomBinding)) {
                // 正常情况下不会走这里  如果走了这里说明  加了企微但是没有回调成功或者回调数据没有保存
                log.error("错误数据：加了企微但是没有回调成功或者删除过企微！！！！！");
                queryCustomerWecomBinding = new CustomerWecomBinding();
                queryCustomerWecomBinding.setId(idWorker.nextId());
                queryCustomerWecomBinding.setCreatedAt(now);
                queryCustomerWecomBinding.setStatus(UseStatusEnum.EFFECTIVE.getCode());
            } else {
                queryCustomerWecomBinding.setUpdatedAt(now);
            }
            LambdaQueryWrapper<SystemUserQyRelation> userQyRelationLambdaQueryWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
            userQyRelationLambdaQueryWrapper.eq(SystemUserQyRelation::getSystemUserId, salesId)
                    .eq(SystemUserQyRelation::getCorpId, corpId);
            SystemUserQyRelation systemUserQyRelation = systemUserQyRelationService.list(userQyRelationLambdaQueryWrapper).get(0);
            queryCustomerWecomBinding.setSalesUserid(systemUserQyRelation.getQyUserId());
            queryCustomerWecomBinding.setCorpId(corpId);
            queryCustomerWecomBinding.setCorpName(companyQyRelation.getCorpName());
            queryCustomerWecomBinding.setExternalUserid(externalUserid);
            queryCustomerWecomBinding.setCustomerId(customerId);
            queryCustomerWecomBinding.setUnionId(unionid);
            queryCustomerWecomBinding.setOpenid(openid);
            queryCustomerWecomBinding.setSalesId(salesId);
            this.saveOrUpdate(queryCustomerWecomBinding);
            // 更新客户添加企微状态
            customerService.updateWecomStatus(customerId, WeComAddStatusEnum.ADDED.getCode());
            // 设置添加企微的行为轨迹
            this.saveAddWechatEnterprise(queryCustomerWecomBinding);
            campCourseVideoResponse.setHasCoursePermission(true);
            return campCourseVideoResponse;
        }

        if (ObjectUtils.isNotEmpty(pendingId)) {
            // 待添加企微(先点击课程链接)
            CustomerWecomBinding customerWecomBinding = new CustomerWecomBinding();
            customerWecomBinding.setId(idWorker.nextId());
            customerWecomBinding.setCustomerId(customerId);
            customerWecomBinding.setCorpId(corpId);
            customerWecomBinding.setUnionId(unionid);
            customerWecomBinding.setOpenid(openid);
            customerWecomBinding.setPendingId(pendingId);
            customerWecomBinding.setSalesId(salesId);
            customerWecomBinding.setCreatedAt(now);
            this.save(customerWecomBinding);
            // 返回添加企微
            LambdaQueryWrapper<SystemUserQyRelation> userQyRelationLambdaQueryWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
            userQyRelationLambdaQueryWrapper.eq(SystemUserQyRelation::getSystemUserId, salesId)
                    .eq(SystemUserQyRelation::getCorpId, corpId);
            SystemUserQyRelation systemUserQyRelation = systemUserQyRelationService.getOne(userQyRelationLambdaQueryWrapper);
            campCourseVideoResponse.setHasCoursePermission(false);
            campCourseVideoResponse.setSalesQrCode(systemUserQyRelation.getQrCode());
            return campCourseVideoResponse;
        }
        campCourseVideoResponse.setHasCoursePermission(false);
        return campCourseVideoResponse;
    }

    /**
     * 分配客户
     * 根据员工状态（在职/离职）将客户分配给在职员工
     *
     * @param request 客户转移请求
     * @param accessToken 企业微信访问令牌
     * @param isResigned 是否是离职员工的客户转移
     * @return 客户转移响应
     */
    @Override
    public CustomerTransferResponse transferCustomer(CustomerTransferRequest request, String accessToken, boolean isResigned) {
        log.info("开始转移{}员工的客户，原跟进成员：{}，接替成员：{}，客户数量：{}",
                isResigned ? "离职" : "在职",
                request.getHandoverUserId(), request.getTakeoverUserId(), request.getExternalUserIds().size());
        try {
            // 根据员工状态选择调用不同的API
            CustomerTransferResponse response;
            if (isResigned) {
                // 调用离职员工的客户转移API
                response = qyWxCustContactClient.transferResignedCustomer(accessToken, request);
            } else {
                // 调用在职员工的客户转移API
                response = qyWxCustContactClient.transferCustomer(accessToken, request);
            }
            if (response != null && response.getErrcode() == 0) {
                log.info("客户转移成功，原跟进成员：{}，接替成员：{}", request.getHandoverUserId(), request.getTakeoverUserId());

                // 更新本地数据库中的客户绑定关系
                updateCustomerBindings(request);
            } else {
                log.error("客户转移失败，错误码：{}，错误信息：{}",
                        response != null ? response.getErrcode() : "null",
                        response != null ? response.getErrmsg() : "null response");
            }
            return response;
        } catch (Exception e) {
            log.error("调用企业微信API转移客户时发生异常", e);
            CustomerTransferResponse errorResponse = new CustomerTransferResponse();
            errorResponse.setErrcode(-1);
            errorResponse.setErrmsg("调用企业微信API异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 分配在职成员的客户
     * 将在职员工的客户分配给在职员工
     *
     * @param request 客户转移请求
     * @return 客户转移响应
     */
    @Override
    public void transferCustomer(CustomerTransferSysRequest request) {
        // 1、获取这批客户之前关联的企微人员在职状态（直接根据客户绑定关系来查，所以如果没有绑定关系，就不需要再调用后续企微接口了）
        LambdaQueryWrapper<CustomerWecomBinding> customerBindWrapper = Wrappers.lambdaQuery();
        // 1.1、首先要判断当前是批量分配还是单个分配场景
        customerBindWrapper.eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());
        if(CollectionUtil.isNotEmpty(request.getCustomerIds())){
            customerBindWrapper.in(CustomerWecomBinding::getCustomerId, request.getCustomerIds());
        }else if(StringUtils.isNotBlank(request.getCustomerId())){
            customerBindWrapper.eq(CustomerWecomBinding::getCustomerId, request.getCustomerId());
        }else {
            log.error("分配客户，对应客户id不能为空");
            return;
        }
        // 1.2、根据查询出的关联关系来获取客户之前关联企微人员对应企微信息与在职状态
        List<CustomerWecomBinding> customerBindings = this.getBaseMapper().selectList(customerBindWrapper);
        if(CollectionUtil.isEmpty(customerBindings)){
            // 1.3、如果查询绑定结果为空，则不需要后续企微关系转接，直接回去绑定系统关联关系即可
            return;
        }
        // 2、根据这批客户之前关联的业务人员，判断调用在职或离职接口；并根据之前业务人员的企微id来构造待交接的map，用于最后批量请求
        Map<String, Map<CustomerTransferMapRequest, CustomerTransferRequest>> requestHashMap = new HashMap<>();
        for (CustomerWecomBinding customerWecomBinding : customerBindings) {
            // 2.0、一次循环遍历出一个企微下，一个原跟进业务人员与一个待分配客户信息，首先需要判断将当前这个客户关联的企微id归入请求map
            if(!requestHashMap.containsKey(customerWecomBinding.getCorpId())){
                requestHashMap.put(customerWecomBinding.getCorpId(), new HashMap<>());
            }
            // 2.1、获取这次原跟进业务人员信息，并校验是否是在职业务人员
            LambdaQueryWrapper<SystemUserQyRelation> userQyRelationLambdaQueryWrapper = new LambdaQueryWrapper<>();
            userQyRelationLambdaQueryWrapper.eq(SystemUserQyRelation::getSystemUserId, customerWecomBinding.getSalesId())
                    .eq(SystemUserQyRelation::getCorpId, customerWecomBinding.getCorpId())
                    .eq(SystemUserQyRelation::getQyUserId, customerWecomBinding.getSalesUserid());
            List<SystemUserQyRelation> systemUserQyRelations = systemUserQyRelationService.getBaseMapper().selectList(userQyRelationLambdaQueryWrapper);
            if(CollectionUtil.isEmpty(systemUserQyRelations)){
                log.error("分配客户id：{}，对应业务人员绑定企微信息不存在，业务人员id：{}", customerWecomBinding.getCustomerId(), customerWecomBinding.getSalesId());
                continue;
            }
            SystemUserQyRelation systemUserQyRelation = systemUserQyRelations.get(0);
            // 2.2、判断当前企微id下是否已经加入当前遍历的原跟进业务人员对象(对象中根据业务人员id来hash，所以只有id相等就代表对象相同)
            Map<CustomerTransferMapRequest, CustomerTransferRequest> sysUserTransferRequestMap = requestHashMap.get(customerWecomBinding.getCorpId());
            CustomerTransferMapRequest customerTransferMapRequest = new CustomerTransferMapRequest();
            customerTransferMapRequest.setHandoverSysUserId(customerWecomBinding.getSalesUserid());
            if(sysUserTransferRequestMap.containsKey(customerTransferMapRequest)){
                // 2.2.1、如果已经存在，则直接将当前客户信息添加到对应客户列表中
                CustomerTransferRequest customerTransferRequest = sysUserTransferRequestMap.get(customerTransferMapRequest);
                customerTransferRequest.getExternalUserIds().add(customerWecomBinding.getExternalUserid());
            }else {
                // 2.2.1、如果不存在，则为第一次添加此原跟进人下的客户信息，需要全部初始化
                CustomerTransferRequest customerTransferRequest = new CustomerTransferRequest();
                customerTransferRequest.setHandoverUserId(customerWecomBinding.getSalesUserid());
                customerTransferRequest.setTakeoverUserId(request.getTakeoverSysUserId());
                customerTransferRequest.setExternalUserIds(new ArrayList<>());
                customerTransferRequest.getExternalUserIds().add(customerWecomBinding.getExternalUserid());
                // 2.2.1.1、在职状态确定
                if (Objects.equals(systemUserQyRelation.getStatus(), UseStatusEnum.INVALID.getCode())) {
                    customerTransferMapRequest.setIsResigned(true);
                }else {
                    customerTransferMapRequest.setIsResigned(false);
                }
                sysUserTransferRequestMap.put(customerTransferMapRequest, customerTransferRequest);
            }
        }
        log.error("本次批量分配客户id集合：{}，对应企微侧数据构造完成：{}", JSONObject.toJSONString(request.getCustomerIds()),
                JSONObject.toJSONString(requestHashMap));
        // 4、开始根据前面构造的对应企微组织下，对应原跟进人在离职状态，发起企微侧转移请求
        if (requestHashMap.isEmpty()){
            return;
        }
        for (Map.Entry<String, Map<CustomerTransferMapRequest, CustomerTransferRequest>> corpRequestMap : requestHashMap.entrySet()) {
            // 4.1、获取对应企微账号所属企微，对应服务商代开发应用的access_token
            QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyAccessToken(corpRequestMap.getKey());
            String accessToken = qyAccessToken.getAccess_token();
            if (StringUtils.isEmpty(accessToken)){
                log.error("分配客户，获取对应企微access_token失败，企微id：{}", corpRequestMap.getKey());
                continue;
            }
            // 4.2、获取到该企微组织下每个原跟进人的在离职状态（判断调用哪个接口），以前当前需要转接的客户信息
            Map<CustomerTransferMapRequest, CustomerTransferRequest> sysUserRequestMap = corpRequestMap.getValue();
            for (Map.Entry<CustomerTransferMapRequest, CustomerTransferRequest> sysUserTransferRequest : sysUserRequestMap.entrySet()) {
                CustomerTransferMapRequest sysUserTransferRequestKey = sysUserTransferRequest.getKey();
                transferCustomer(sysUserTransferRequest.getValue(), accessToken, sysUserTransferRequestKey.getIsResigned());
            }
        }
    }

    /**
     * 更新客户绑定关系
     * 将客户与原跟进成员的绑定关系设为无效，创建与新跟进成员的绑定关系
     * 同时保留原绑定关系中的其他字段数据
     *
     * @param request 客户转移请求
     */
    private void updateCustomerBindings(CustomerTransferRequest request) {
        LocalDateTime now = LocalDateTime.now();

        // 查询原绑定关系数据
        LambdaQueryWrapper<CustomerWecomBinding> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CustomerWecomBinding::getExternalUserid, request.getExternalUserIds())
                .eq(CustomerWecomBinding::getSalesUserid, request.getHandoverUserId())
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode());

        // 获取所有符合条件的原绑定关系
        List<CustomerWecomBinding> originalBindings = list(queryWrapper);

        // 将原绑定关系映射到一个以externalUserid为键的Map，方便后续查找
        Map<String, CustomerWecomBinding> originalBindingMap = originalBindings.stream()
                .collect(Collectors.toMap(CustomerWecomBinding::getExternalUserid, binding -> binding, (a, b) -> a));

        // 将原跟进成员与客户的绑定关系设为无效
        LambdaUpdateWrapper<CustomerWecomBinding> updateWrapper = Wrappers.lambdaUpdate(CustomerWecomBinding.class);
        updateWrapper.in(CustomerWecomBinding::getExternalUserid, request.getExternalUserIds())
                .eq(CustomerWecomBinding::getSalesUserid, request.getHandoverUserId())
                .eq(CustomerWecomBinding::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .set(CustomerWecomBinding::getStatus, UseStatusEnum.INVALID.getCode())
                .set(CustomerWecomBinding::getUpdatedAt, now);
        update(updateWrapper);

        // 为每个客户创建与新跟进成员的绑定关系，并复制原绑定关系中的其他字段数据
        List<CustomerWecomBinding> newBindings = new ArrayList<>();
        for (String externalUserId : request.getExternalUserIds()) {
            CustomerWecomBinding originalBinding = originalBindingMap.get(externalUserId);

            if (originalBinding != null) {
                CustomerWecomBinding newBinding = new CustomerWecomBinding();
                BeanUtils.copyProperties(originalBinding, newBinding);

                newBinding.setId(idWorker.nextId());
                newBinding.setCreatedAt(now);

                // 更新销售相关信息
                newBinding.setSalesUserid(request.getTakeoverUserId());
                newBinding.setStatus(UseStatusEnum.EFFECTIVE.getCode());

                newBindings.add(newBinding);
            } else {
                log.warn("未找到客户 {} 的原绑定关系，跳过创建新绑定关系", externalUserId);
            }
        }

        // 批量保存新的绑定关系
        if (!newBindings.isEmpty()) {
            saveBatch(newBindings);
        }

        log.info("已更新{}个客户的绑定关系，从{}转移到{}，并保留了原绑定关系中的其他字段数据",
                request.getExternalUserIds().size(), request.getHandoverUserId(), request.getTakeoverUserId());
    }

    /**
     * 保存添加企微的行为轨迹
     * @param relation
     */
    private void saveAddWechatEnterprise(CustomerWecomBinding relation) {
        LambdaQueryWrapper<SystemUserQyRelation> systemUserQyRelationLambdaQueryWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
        systemUserQyRelationLambdaQueryWrapper.eq(SystemUserQyRelation::getQyUserId, relation.getSalesUserid())
                .eq(SystemUserQyRelation::getCorpId, relation.getCorpId());
        SystemUserQyRelation systemUserQyRelation = systemUserQyRelationService.getOne(systemUserQyRelationLambdaQueryWrapper);
        customerBehaviorService.saveAddWechatEnterprise(relation.getCustomerId(), 0L, relation.getCorpId(), relation.getCorpName(), "", systemUserQyRelation.getQyName());
    }

    /**
     * 保存删除企微的行为轨迹
     * @param relation
     */
    private void saveDeleteWechatEnterprise(CustomerWecomBinding relation) {
        LambdaQueryWrapper<SystemUserQyRelation> systemUserQyRelationLambdaQueryWrapper = Wrappers.lambdaQuery(SystemUserQyRelation.class);
        systemUserQyRelationLambdaQueryWrapper.eq(SystemUserQyRelation::getQyUserId, relation.getSalesUserid())
                .eq(SystemUserQyRelation::getCorpId, relation.getCorpId());
        SystemUserQyRelation systemUserQyRelation = systemUserQyRelationService.getOne(systemUserQyRelationLambdaQueryWrapper);
        customerBehaviorService.saveDeleteWechatEnterprise(relation.getCustomerId(), 0L, relation.getCorpId(), relation.getCorpName(), "", systemUserQyRelation.getQyName());
    }

    @Override
    public boolean deleteByCustomerId(Long customerId) {
        return baseMapper.deleteByCustomerId(customerId) > 0;
    }
}