package cn.hxsy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.AssignTypeEnum;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.datasource.model.entity.ColumnPO;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.CustomerSalesRelation;
import cn.hxsy.dao.CustomerSalesRelationMapper;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.dto.CSRelationByColumDTO;
import cn.hxsy.request.CustomerTransferSysRequest;
import cn.hxsy.service.*;
import cn.hxsy.api.user.model.request.CustomerAssignRequest;
import cn.hxsy.thread.QyRecordThread;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户销售关联服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class CustomerSalesRelationServiceImpl extends ServiceImpl<CustomerSalesRelationMapper, CustomerSalesRelation>
        implements CustomerSalesRelationService {

    @Resource
    private SystemUserService systemUserService;
    @Resource
    private CustomerAssignmentService customerAssignmentService;
    @Autowired
    private SnowflakeIdWorker idWorker;
    @Autowired
    private UserCacheUtil  userCacheUtil;
    @Autowired
    private ColumnService columnService;

    @Autowired
    private QyRecordThread qyRecordThread;

    /**
     * description : 为客户分配销售人员
     * @title: assignCustomer
     * @param: request
     * <AUTHOR>
     * @date 2025/5/25 16:06
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignCustomer(CustomerAssignRequest request) {
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        List<Long> customerIds = request.getCustomerIds();
        Long oldColumnId = request.getOldColumnId();
        Long companyId = request.getCompanyId();
        Long columnId = request.getColumnId();
        Long salesId = request.getSalesId();
        String salesName = request.getSalesName();
        Long salesGroupId = request.getSalesGroupId();
        String username = systemUserSelfInfo.getUsername();
        int manual = AssignTypeEnum.MANUAL.getCode();
        LocalDateTime now = LocalDateTime.now();
        customerIds.forEach(customerId -> {
            // 1、查询是否已存在销售关联
            LambdaQueryWrapper<CustomerSalesRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CustomerSalesRelation::getCustomerId, customerId)
                    .eq(CustomerSalesRelation::getColumnId, oldColumnId);
            List<CustomerSalesRelation> list = this.list(wrapper);
            CustomerSalesRelation existingRelation = list.get(0);
            // 2、保存客户分配操作记录
            boolean b = customerAssignmentService.saveCustomerAssignment(customerId, existingRelation.getColumnId(), existingRelation.getCompanyId(), existingRelation.getSalesId(), salesId);
            if (b) {
                // 3、系统客户-销售关系分配
                LambdaUpdateWrapper<CustomerSalesRelation> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.set(CustomerSalesRelation::getColumnId, columnId)
                        .set(CustomerSalesRelation::getCompanyId, companyId)
                        .set(CustomerSalesRelation::getSalesGroupId, salesGroupId)
                        .set(CustomerSalesRelation::getSalesId, salesId)
                        .set(CustomerSalesRelation::getSalesName, salesName)
                        .set(CustomerSalesRelation::getAssignType, manual)
                        .set(CustomerSalesRelation::getAssignBy, username)
                        .set(CustomerSalesRelation::getAssignTime, now)
                        .set(CustomerSalesRelation::getUpdatedAt, now)
                        .eq(CustomerSalesRelation::getColumnId, oldColumnId)
                        .eq(CustomerSalesRelation::getCustomerId, customerId);
                update(updateWrapper);
            }
        });
        // 3、企微侧客户-销售关系转接
        if(StringUtils.isNotBlank(request.getQyUserId())){
            CustomerTransferSysRequest customerTransferSysRequest = new CustomerTransferSysRequest();
            customerTransferSysRequest.setCustomerIds(customerIds.stream().map(String::valueOf).collect(Collectors.toList()));
            customerTransferSysRequest.setTakeoverSysUserId(request.getQyUserId());
            qyRecordThread.transferCustomerAsync(customerTransferSysRequest);
        }
        return Boolean.TRUE;
    }

    public void updateByCustomerId(CustomerSalesRelation customerSalesRelation) {
        LambdaUpdateWrapper<CustomerSalesRelation> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CustomerSalesRelation::getCustomerId, customerSalesRelation.getCustomerId())
                .eq(CustomerSalesRelation::getId, customerSalesRelation.getId());
        update(customerSalesRelation, updateWrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCustomerSalesRelation(CustomerSalesRelation relation) {
        // 根据业务人员id获取出对应业务人员信息
        Long salesId = relation.getSalesId();
        Long customerId = relation.getCustomerId();
        Long columnId = relation.getColumnId();
        Long salesGroupId = relation.getSalesGroupId();
        Long companyId = relation.getCompanyId();
        Long campPeriodId = relation.getCampPeriodId();
        SystemUserPO systemUserPO = systemUserService.getById(salesId);
        if(systemUserPO == null){
            throw new RuntimeException("业务人员不存在");
        }
        LocalDateTime now = LocalDateTime.now();
        CustomerSalesRelation customerSalesRelation = new CustomerSalesRelation();
        // 公司、栏目、销售组id
        customerSalesRelation.setCompanyId(companyId);
        customerSalesRelation.setColumnId(columnId);
        customerSalesRelation.setSalesGroupId(salesGroupId);
        // 设置其余基本关联信息，需要重新设置id，防止同名字段
        customerSalesRelation.setId(idWorker.nextId());
        customerSalesRelation.setSalesId(salesId);
        customerSalesRelation.setSalesName(systemUserPO.getUsername());
        customerSalesRelation.setCustomerId(customerId);
        customerSalesRelation.setCampPeriodId(campPeriodId);
        customerSalesRelation.setAssignTime(now);
        customerSalesRelation.setAssignType(AssignTypeEnum.AUTOMATIC.getCode());
        customerSalesRelation.setCreatedAt(now);
        customerSalesRelation.setUpdatedAt(now);
        customerSalesRelation.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        this.save(customerSalesRelation);
    }

    /**
     * description : 根据客户id查询客户销售关联信息
     *
     * @return List<CSRelationByColumDTO>
     * @title: listByCustomerId
     * @param: customerId
     * <AUTHOR>
     * @date 2025/5/18 14:18
     */
    @Override
    public List<CSRelationByColumDTO> listByCustomerId(Long customerId) {
        LambdaQueryWrapper<CustomerSalesRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerSalesRelation::getCustomerId, customerId);
        // TODO 增加查询权限

        List<CustomerSalesRelation> list = list(wrapper);
        if (ObjectUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 获取所有栏目Id
        List<Long> columnIds = list.stream()
                .map(CustomerSalesRelation::getColumnId)
                .distinct()
                .collect(Collectors.toList());
        // 查询栏目名称
        List<ColumnPO> columns = columnService.listByIds(columnIds);
        // 将这个list转为Map key为id value为columnName
        Map<Long, String> columnMap = columns.stream()
                .collect(
                        Collectors.toMap(
                                ColumnPO::getId,
                                ColumnPO::getColumnName,
                                (existing, replacement) -> existing));
        // 将list 按 columnId 分组
        Map<Long, List<CustomerSalesRelation>> groupByColumnId = list.stream()
                .collect(Collectors.groupingBy(CustomerSalesRelation::getColumnId));
        // 设置栏目名称 并 转为List
        List<CSRelationByColumDTO> result = groupByColumnId.entrySet().stream()
                .map(entry -> {
                    CSRelationByColumDTO dto = new CSRelationByColumDTO();
                    dto.setId(entry.getKey());
                    dto.setColumnName(columnMap.getOrDefault(entry.getKey(), "未知栏目"));
                    dto.setCustomerSalesRelationList(entry.getValue());
                    return dto;
                }).collect(Collectors.toList());

        return result;
    }
} 