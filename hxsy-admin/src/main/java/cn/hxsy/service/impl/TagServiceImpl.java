package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.response.TagGroupTreeResponse;
import cn.hxsy.dao.TagMapper;
import cn.hxsy.datasource.model.entity.Tag;
import cn.hxsy.service.TagService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {
    
    @Override
    public boolean add(Tag tag) {
        return save(tag);
    }
    
    @Override
    public boolean deleteById(Long id) {
        return removeById(id);
    }
    
    @Override
    public boolean update(Tag tag) {
        return updateById(tag);
    }
    
    @Override
    public List<Tag> getByGroupId(Long groupId) {
        LambdaQueryWrapper<Tag> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Tag::getGroupId, groupId)
               .orderByAsc(Tag::getSortOrder);
        return list(wrapper);
    }
    
    @Override
    public List<TagGroupTreeResponse> getAllTagGroupsWithTags() {
        // 一次性查询所有标签组及其标签
        List<TagGroupTreeResponse> allGroups = baseMapper.getAllTagGroupsWithTags();
        // TODO 保存至 Redis
        // 将标签组按父级ID分组
        return allGroups.stream()
                .filter(group -> group.getParentId() == null)
                .peek(group -> {
                    // 设置子标签组
                    group.setChildren(allGroups.stream()
                            .filter(child -> group.getId().equals(child.getParentId()))
                            .collect(Collectors.toList()));
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Tag> getTagsByCustomerId(Long customerId) {
        return baseMapper.getTagsByCustomerId(customerId);
    }


} 