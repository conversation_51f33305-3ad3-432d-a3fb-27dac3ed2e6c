package cn.hxsy.service.impl;

import cn.hxsy.api.qy.feign.license.QyWxLicenseClient;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.base.enums.OrderTypeEnum;
import cn.hxsy.base.enums.PaymentStatusEnum;
import cn.hxsy.base.enums.WeComAccountTypeEnum;
import cn.hxsy.base.util.WecomXmlUtil;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.dao.ExternalAccountOrderMapper;
import cn.hxsy.datasource.model.entity.ExternalAccountOrder;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.service.ExternalAccountOrderService;
import cn.hxsy.service.ExternalAccountService;
import cn.hxsy.service.qy.QyAuthService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;

/**
 * 互通账号订单服务实现类
 */
@Service
@Slf4j
public class ExternalAccountOrderServiceImpl 
    extends ServiceImpl<ExternalAccountOrderMapper, ExternalAccountOrder> 
    implements ExternalAccountOrderService {

    @Resource
    private QyWxLicenseClient qyWxLicenseClient;

    @Resource
    private QyAuthService qyAuthService;

    @Resource
    private SnowflakeIdWorker idWorker;

    @Resource
    private ExternalAccountService externalAccountService;

    /**
     * description : 创建新订单
     * @title: createOrder
     * @param: order
     * <AUTHOR>
     * @date 2025/7/17 21:02
     * @return boolean
     */
    @Override
    public boolean createOrder(ExternalAccountOrder order) {
        Integer duration = order.getAccountDuration();
        if (duration < 31) {
            throw new RuntimeException("开通时长不能小于31天");
        }

        // 1、获取 应用服务商的接口调用凭证
        String corpid = order.getCorpId();
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getProviderToken("");
        String providerAccessToken = qyAccessToken.getProvider_access_token();
        // 2、创建新订单
        //provider_access_token	是	应用服务商的接口调用凭证，获取方法参见服务商的凭证
        //corpid	是	企业id
        //buyer_userid	是	下单人。服务商企业内成员的明文userid。该userid必须登录过企业微信，并且企业微信已绑定微信，且必须为服务商企业内具有“购买接口许可”权限的管理员。最终也支持由其他人支付
        //account_count	是	账号个数详情，基础账号跟互通账号不能同时为0
        //account_count.base_count	否	基础账号个数，最多1000000个。(若企业为服务商测试企业，最多购买1000个)
        //account_count.external_contact_count	否	互通账号个数，最多1000000个。(若企业为服务商测试企业，最多购买1000个)
        //account_duration	是	账号购买时长。总购买时长为(months*31+days)天，最少购买1个月(31天)，最多购买60个月(1860天)。若企业为服务商测试企业，只支持购买1个月，不支持指定天购买
        //account_duration.months	否	购买的月数，每个月按照31天计算
        //account_duration.days	否	购买的天数
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("corpid", corpid);
        jsonObject.put("buyer_userid", order.getBuyerUserid());
        JSONObject accountCount = new JSONObject();
        if (ObjectUtils.isNotEmpty(order.getBaseCount())) {
            accountCount.put("base_count", order.getBaseCount());
        }
        accountCount.put("external_contact_count", order.getExternalContactCount());
        jsonObject.put("account_count", accountCount);
        JSONObject accountDuration = new JSONObject();
        if (duration == 31) {
            accountDuration.put("months", 1);
        } else {
            accountDuration.put("days", duration);
        }
        jsonObject.put("account_duration", accountDuration);
        log.info("创建新订单参数：{}", jsonObject);
        JSONObject response = qyWxLicenseClient.createNewOrder(providerAccessToken, jsonObject);
        log.info("创建新订单响应：{}", response);
        if (response.getInteger("errcode") != 0) {
            String errMsg = String.format("创建新订单失败: [%d] %s",
                    response.getInteger("errcode"),
                    response.getString("errmsg"));
            throw new RuntimeException(errMsg);
        }
        order.setId(idWorker.nextId());
        order.setOrderId(response.getString("order_id"));
        order.setOrderType(OrderTypeEnum.BUY_ACCOUNT.getCode());
        order.setPayStatus(PaymentStatusEnum.PENDING.getCode());
        order.setCreatedAt(LocalDateTime.now());
        // TODO 设置创建人
        order.setCreatedBy("admin");
        return this.save(order);
    }

    /**
     * description : 创建续期订单（创建续期任务 + 提交续期任务）（目前只支持1000）
     * @title: createRenewalOrder
     * @param: order
     * <AUTHOR>
     * @date 2025/7/17 21:02
     * @return boolean
     */
    @Override
    public boolean createRenewalOrder(ExternalAccountOrder order) {

        List<SystemUserQyRelation> systemUserQyRelations = order.getSystemUserQyRelations();
        if (systemUserQyRelations.size() > 1000) {
            throw new RuntimeException("一次最多只能续期1000个账号");
        }
        // 1、获取 应用服务商的接口调用凭证
        String corpid = order.getCorpId();
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getProviderToken("");
        String providerAccessToken = qyAccessToken.getProvider_access_token();
        // 2、批量创建续期任务
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("corpid", corpid);
        JSONArray accountList = new JSONArray();
        int baseCount = 0;
        int externalContactCount = 0;
        for (SystemUserQyRelation systemUserQyRelation : systemUserQyRelations) {
            JSONObject account = new JSONObject();
            account.put("userid", systemUserQyRelation.getQyUserId());
            Integer accountType = systemUserQyRelation.getAccountType();
            if (accountType == WeComAccountTypeEnum.BASIC.getCode()) {
                baseCount++;
            } else if (accountType == WeComAccountTypeEnum.INTERCONNECTED.getCode()) {
                externalContactCount++;
            }
            account.put("type", accountType); // 账号类型
            accountList.add(account);
        }
        jsonObject.put("account_list", accountList);
        //provider_access_token	是	应用服务商的接口调用凭证，获取方法参见服务商的凭证
        //corpid	是	企业id
        //account_list	是	续期的账号列表，每次最多1000个。同一个jobid最多关联1000000个基础账号跟1000000个互通账号
        //account_list.userid	是	续期企业的成员userid。
        //account_list.type	是	续期账号类型。1:基础账号，2:互通账号
        //jobid	否	任务id，若不传则默认创建一个新任务。若指定第一次调用后拿到jobid，可以通过该接口将jobid关联多个userid
        JSONObject response = qyWxLicenseClient.createRenewOrderJob(providerAccessToken, jsonObject);
        if (response.getInteger("errcode") != 0) {
            String errMsg = String.format("创建续期任务失败: [%d] %s",
                    response.getInteger("errcode"),
                    response.getString("errmsg"));
            throw new RuntimeException(errMsg);
        }
        // 3、提交续期任务
        jsonObject = new JSONObject();
        jsonObject.put("job_id", response.getString("job_id"));
        jsonObject.put("buyer_userid", order.getBuyerUserid());
        JSONObject accountDuration = new JSONObject();
        Integer duration = order.getAccountDuration(); // 购买时长（天）
        LocalDateTime now = LocalDateTime.now();
        // 根据当前时间 + 天数 获得新的到期时间
        LocalDateTime serviceExpireTime = now.plusDays(duration);
        long newExpireTime = serviceExpireTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        accountDuration.put("new_expire_time", newExpireTime);
        jsonObject.put("account_duration", accountDuration);
        //provider_access_token	是	应用服务商的接口调用凭证，获取方法参见服务商的凭证
        //jobid	是	任务id
        //buyer_userid	是	下单人，服务商企业内成员的明文userid。该userid必须登录过企业微信，并且企业微信已绑定微信，且必须为服务商企业内具有“购买接口许可”权限的管理员。
        //account_duration	是	账号购买时长
        //account_duration.months	否	与new_expire_time二者填其一。购买的月数，每个月按照31天计算。最多购买60个月。(若企业为服务商测试企业，每次续期只能续期1个月)
        //account_duration.new_expire_time	否	与months二者填其一。指定的新到期时间戳，不可为今天和过去的时间，不可为1860天后的时间。须填当天的24时0分0秒，否则系统自动处理为当天的24时0分0秒。(若企业为服务商测试企业，不支持指定新的到期时间来续期)
        response = qyWxLicenseClient.submitOrderJob(providerAccessToken, jsonObject);
        if (response.getInteger("errcode") != 0) {
            String errMsg = String.format("提交续期任务失败: [%d] %s",
                    response.getInteger("errcode"),
                    response.getString("errmsg"));
            throw new RuntimeException(errMsg);
        }
        // 4、保存订单信息
        order.setId(idWorker.nextId());
        order.setBaseCount(baseCount);
        order.setExternalContactCount(externalContactCount);
        order.setOrderType(OrderTypeEnum.RENEW_ACCOUNT.getCode());
        order.setPayStatus(PaymentStatusEnum.PENDING.getCode());
        // TODO 设置创建人
        order.setCreatedBy("admin");
        order.setOrderId(response.getString("order_id"));
        order.setServiceStartTime(now);
        order.setServiceExpireTime(serviceExpireTime);
        order.setCreatedAt(now);
        return this.save(order);
    }

    /**
     * description : 取消订单(取消接口许可购买和续费订单，只可取消未支付且未失效的订单。)
     * @title: cancelOrder
     * @param: order
     * <AUTHOR>
     * @date 2025/7/17 21:49
     * @return boolean
     */
    @Override
    public boolean cancelOrder(ExternalAccountOrder order) {
        // 1、校验订单是否已支付
        ExternalAccountOrder accountOrder = getById(order.getId());
        if (accountOrder.getPayStatus() != PaymentStatusEnum.PAID.getCode()) {
            throw new RuntimeException("订单已支付，不可取消");
        }
        // 2、获取 应用服务商的接口调用凭证
        String corpid = accountOrder.getCorpId();
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getProviderToken("");
        String providerAccessToken = qyAccessToken.getProvider_access_token();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("order_id", accountOrder.getOrderId());
        // 3、取消订单
        JSONObject response = qyWxLicenseClient.cancelOrder(providerAccessToken, jsonObject);
        if (response.getInteger("errcode") != 0) {
            String errMsg = String.format("取消订单失败: [%d] %s",
                    response.getInteger("errcode"),
                    response.getString("errmsg"));
            throw new RuntimeException(errMsg);
        }
        order.setPayStatus(PaymentStatusEnum.CANCELLED.getCode());
        return this.updateById(order);
    }

    /**
     * description : 分页查询互通账号订单列表
     * @title: queryPage
     * @param: pageNum
     * @param: pageSize
     * @param: externalAccountOrder
     * <AUTHOR>
     * @date 2025/7/18 16:24
     * @return Page<ExternalAccountOrder>
     */
    @Override
    public Page<ExternalAccountOrder> queryPage(Integer pageNum, Integer pageSize, ExternalAccountOrder externalAccountOrder) {
        Page<ExternalAccountOrder> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ExternalAccountOrder> wrapper = Wrappers.lambdaQuery(ExternalAccountOrder.class);
        wrapper.eq(ObjectUtils.isNotEmpty(externalAccountOrder.getCorpId()), ExternalAccountOrder::getCorpId, externalAccountOrder.getCorpId());
        wrapper.eq(ObjectUtils.isNotEmpty(externalAccountOrder.getOrderType()), ExternalAccountOrder::getOrderType, externalAccountOrder.getOrderType());
        wrapper.eq(ObjectUtils.isNotEmpty(externalAccountOrder.getPayStatus()), ExternalAccountOrder::getPayStatus, externalAccountOrder.getPayStatus());
        wrapper.eq(ObjectUtils.isNotEmpty(externalAccountOrder.getOrderId()), ExternalAccountOrder::getOrderId, externalAccountOrder.getOrderId());
        return this.page(page, wrapper);
    }

    /**
     * description : 手动获取订单中激活码
     * @title: getActivationCode
     * @param: orderId
     * <AUTHOR>
     * @date 2025/7/20 21:16
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean getActivationCode(String orderId) {
        // 1、更新订单状态(已支付)
        LambdaUpdateWrapper<ExternalAccountOrder> updateWrapper = Wrappers.lambdaUpdate(ExternalAccountOrder.class);
        updateWrapper.eq(ExternalAccountOrder::getOrderId, orderId)
                .set(ExternalAccountOrder::getPayTime, LocalDateTime.now()) // 支付时间
                .set(ExternalAccountOrder::getPayStatus, PaymentStatusEnum.PAID.getCode()); // 支付状态
        this.update(updateWrapper);
        // 2、获取并保存激活码
        return externalAccountService.saveActivationCode(orderId);
    }
}