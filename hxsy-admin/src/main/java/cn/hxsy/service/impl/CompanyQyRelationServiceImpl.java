package cn.hxsy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hxsy.api.qy.feign.auth.QyWxAuthClient;
import cn.hxsy.api.qy.request.QyUserReq;
import cn.hxsy.api.qy.response.QyWeChatQueryDeptResponse;
import cn.hxsy.api.qy.response.QyWeChatUserResponse;
import cn.hxsy.api.user.feign.vx.QyAppClient;
import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.qy.response.QyAppResponse;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.constant.qyWechat.secretApp.QyWechatConfigType;
import cn.hxsy.base.constant.system.QyWechatQueryType;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.SystemUserQyRelationMapper;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.dao.CompanyQyRelationMapper;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.response.CompanyQyRelationResponse;
import cn.hxsy.service.CompanyQyRelationService;
import cn.hxsy.service.SystemUserService;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hxsy.base.constant.user.UserSelectType.saleGroup;
import static cn.hxsy.cache.constant.user.CacheConstant.QY_CORP_TOKEN;

/**
 * <p>
 * 公司与企微账号关联配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 11:42:49
 */
@Service
@Slf4j
public class CompanyQyRelationServiceImpl extends ServiceImpl<CompanyQyRelationMapper, CompanyQyRelation> implements CompanyQyRelationService {

    @Resource
    private UserSelectUtil userSelectUtil;

    @Resource
    private UserCacheUtil userCacheUtil;

    @Resource
    private RedisJsonUtils redisJsonUtils;

    @Resource
    private QyAppClient qyAppClient;

    @Resource
    private QyWxAuthClient qyWxAuthClient;

    @Resource
    private SystemUserQyRelationMapper systemUserQyRelationMapper;

    @Resource
    private SystemUserService systemUserService;

    @Resource
    private QyAuthService qyAuthService;

    @Override
    public Result<Object> queryCompanyQyToken(QyAppReq qyAppReq) {
        // 1、判断当前是哪个获取token场景
        if(qyAppReq == null){
            return Result.error("请传入对应访问参数");
        }
        String corpId = qyAppReq.getCorpId();
        if(StringUtils.isNotEmpty(qyAppReq.getQyCode())){
            // 1.1企微内部登录
            // 1.1.1先获取对应accessToken，再进行企微内登录流程
            QyWeChatAuthResponse qyWeChatAuthResponse = this.getQyWeChatAuthResponse(corpId, QyWechatQueryType.COMMON.getCode(), null);
            // 1.1.2根据前端code与获取企业内部访问token发起登录
            QyAppResponse qyAppResponse = qyAppClient.jscode2session(qyWeChatAuthResponse.getAccess_token(), qyAppReq.getQyCode(), "authorization_code");
            // 1.2判断企微登录流程是否成功，成功则去获取当前系统下存储该企微账号信息
            String errCode = qyAppResponse.getErrcode();
            if(StringUtils.isEmpty(errCode) || "0".equals(errCode)) {
                // 1.2.1获取当前系统下存储该企微账号信息，用于后续获取当前企微人员关联业务人员信息
                LambdaQueryWrapper<SystemUserQyRelation> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(SystemUserQyRelation::getQyUserId, qyAppResponse.getUserid())
                        .eq(SystemUserQyRelation::getCorpId, qyAppReq.getCorpId());
                List<SystemUserQyRelation> systemUserQyRelations = systemUserQyRelationMapper.selectList(wrapper);
                if(CollectionUtil.isEmpty(systemUserQyRelations)) {
                    return Result.error("系统尚未录入您的员工账号，请先前往管理端进行同步");
                }
                SystemUserQyRelation systemUserQyRelation = systemUserQyRelations.get(0);
                // 1.2.2获取当前企微人员关联业务人员信息，并模拟业务人员登录流程
                SystemUserPO systemUserPO = systemUserService.getById(systemUserQyRelation.getSystemUserId());
                if(systemUserPO == null){
                    return Result.error("当前企微账号未关联业务人员信息，请先前往管理端进行配置");
                }
                // 1.2.3根据业务人员信息构造响应缓存，进行登录
                SystemUserResponse systemUserResponse = systemUserService.qyWxUserLogin(systemUserPO);
                return Result.ok(systemUserResponse);
            }
            log.info("企微内部登录失败:{}", qyAppResponse.getErrcode());
            return Result.error(qyAppResponse.getErrmsg());
        }else {
            // 1.2管理端获取企微信息
            // 1.2.1首先校验当前人员是否具有该公司的管理权限
            this.verifyCompanyAuth(qyAppReq.getCompanyId());
            // 1.2.2判断管理端使用的场景，构造对应响应数据
            LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CompanyQyRelation::getCompanyId, qyAppReq.getCompanyId())
                    .eq(StringUtils.isNotEmpty(qyAppReq.getCorpId()), CompanyQyRelation::getCorpId, qyAppReq.getCorpId());
            List<CompanyQyRelation> companyQyRelations = this.getBaseMapper().selectList(wrapper);
            if(StringUtils.isEmpty(qyAppReq.getCorpId())){
                // 1.2.2.1管理端配置公司下关联企微账号，直接把查询出来的公司已关联企微配置全部返回
                return Result.ok(companyQyRelations);
            }else {
                // 1.2.2.2管理端调用单个企微下api，要获取到对应企微账号访问的access_token
                return Result.ok(this.getQyWeChatAuthResponse(corpId, qyAppReq.getQueryScene(), companyQyRelations.get(0)));
            }
        }
    }

    @Override
    public Result<Object> queryQyLoginToken(QyAppReq qyAppReq) {
        // 1、参数校验
        if(qyAppReq.getQyCode() == null){
            return Result.error("请先获取登录授权code");
        }
        // 2、获取服务商代开发的需要通过suite_access_token
        QyWeChatAuthResponse qyWeChatAuthResponse = qyAuthService.getSuiteToken("", "");
        // 3、根据前端code与获取服务商代开发suite_access_token发起登录
        QyAppResponse qyAppResponse = qyWxAuthClient.jscode2session(qyWeChatAuthResponse.getSuite_access_token(), qyAppReq.getQyCode(), "authorization_code");
        log.info("代开发登录响应:{}", qyAppResponse);
        // 3.1、判断企微登录流程是否成功，成功则去获取当前系统下存储该企微账号信息
        String errCode = qyAppResponse.getErrcode();
        if(StringUtils.isEmpty(errCode) || "0".equals(errCode)) {
            // 3.2、获取当前系统下存储该企微账号信息，用于后续获取当前企微人员关联业务人员信息
            LambdaQueryWrapper<SystemUserQyRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SystemUserQyRelation::getQyUserId, qyAppResponse.getUserid())
                    .eq(SystemUserQyRelation::getCorpId, qyAppResponse.getCorpid());
            List<SystemUserQyRelation> systemUserQyRelations = systemUserQyRelationMapper.selectList(wrapper);
            if(CollectionUtil.isEmpty(systemUserQyRelations)) {
                return Result.error("系统尚未录入您的员工账号，请先前往管理端进行同步");
            }
            SystemUserQyRelation systemUserQyRelation = systemUserQyRelations.get(0);
            // 3.3、获取当前企微人员关联业务人员信息，并模拟业务人员登录流程
            SystemUserPO systemUserPO = systemUserService.getById(systemUserQyRelation.getSystemUserId());
            if(systemUserPO == null){
                return Result.error("当前企微账号未关联业务人员信息，请先前往管理端进行配置");
            }
            // 4、根据业务人员信息构造响应缓存，进行登录
            SystemUserResponse systemUserResponse = systemUserService.qyWxUserLogin(systemUserPO);
            return Result.ok(systemUserResponse);
        }
        log.info("企微内部登录失败:{}", qyAppResponse.getErrcode());
        return Result.error(qyAppResponse.getErrmsg());
    }

    @Override
    public Result<Object> queryCompanyQyDept(QyAppReq qyAppReq) {
        if(StringUtils.isEmpty(qyAppReq.getCorpId())){
            throw new RuntimeException("请确认对应查询企业范围");
        }
        // 1、首先校验当前人员是否具有该公司的管理权限
        this.verifyCompanyAuth(qyAppReq.getCompanyId());
        // 2、获取该企微对应访问token
        QyWeChatAuthResponse qyWeChatAuthResponse = this.getQyWeChatAuthResponse(qyAppReq.getCorpId(), QyWechatQueryType.CONTACT.getCode(), null);
        // 3、获取对应企微部门信息
        QyWeChatQueryDeptResponse qyAppResponse = qyAppClient.deptList(qyWeChatAuthResponse.getAccess_token(), null);
        return Result.ok(qyAppResponse);
    }

    @Override
    public Result<Object> queryCompanyQyUser(QyAppReq qyAppReq) {
        if(StringUtils.isEmpty(qyAppReq.getCorpId())){
            throw new RuntimeException("请确认对应查询企业范围");
        }
        // 1、首先校验当前人员是否具有该公司的管理权限
        this.verifyCompanyAuth(qyAppReq.getCompanyId());
        // 2、获取该企微对应访问token
        QyWeChatAuthResponse qyWeChatAuthResponse = this.getQyWeChatAuthResponse(qyAppReq.getCorpId(), QyWechatQueryType.CONTACT.getCode(), null);
        // 3、获取对应企微成员信息，设置对应分页参数
        QyUserReq qyUserReq = new QyUserReq();
        // 这个游标是以open_userId为游标的，这个列表接口也获取不到，得前端存了然后下一次传过来，第一次查询可以不传
        qyUserReq.setCursor(qyAppReq.getCursor());
        qyUserReq.setLimit(qyAppReq.getLimit());
        QyWeChatQueryDeptResponse qyAppResponse = qyAppClient.userList(qyWeChatAuthResponse.getAccess_token(), qyUserReq);
        return Result.ok(qyAppResponse);
    }

    @Override
    public Result<List<QyWeChatUserResponse>> queryCompanyQyUserInner(QyAppReq qyAppReq) {
        // 1、获取该企微对应访问token
        QyWeChatAuthResponse qyWeChatAuthResponse = this.getQyWeChatAuthResponse(qyAppReq.getCorpId(), QyWechatQueryType.CONTACT.getCode(), null);
        // 2、获取对应企微成员信息，由于是内部企微成员同步接口，就不需要分页了，直接全部查出然后对比处增量数据
        QyWeChatQueryDeptResponse qyAppResponse = qyAppClient.userList(qyWeChatAuthResponse.getAccess_token(), new QyUserReq());
        return Result.ok(qyAppResponse.getDept_user());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> saveQyCompany(CompanyQyRelationRequest request) {
        // 0、获取用户信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 1、判断是否传入id，是则更新，不是则新增
        String id = request.getId();
        boolean save = false;
        CompanyQyRelation companyQyRelation = new CompanyQyRelation();
        if(StringUtils.isEmpty(id)){
            // 1.1、构造企微新增信息所需数据
            BeanUtils.copyProperties(request, companyQyRelation);
            companyQyRelation.setCreatedAt(LocalDateTime.now());
            companyQyRelation.setCreatedBy(systemUserSelfInfo.getAccountId());
            save = this.save(companyQyRelation);
        }else {
            // 1.2、构造企微更新所需数据
            BeanUtils.copyProperties(request, companyQyRelation);
            companyQyRelation.setId(Long.parseLong(id));
            companyQyRelation.setUpdatedAt(LocalDateTime.now());
            companyQyRelation.setUpdatedBy(systemUserSelfInfo.getAccountId());
            save = this.updateById(companyQyRelation);
        }
        if(save){
            return Result.ok();
        }
        return Result.error("企微信息保存失败，请稍后尝试");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> saveQyCompanyInner(CompanyQyRelationRequest request) {
        // 1、判断是否传入id，是则更新，不是则新增
        String id = request.getId();
        boolean save = false;
        CompanyQyRelation companyQyRelation = new CompanyQyRelation();
        if(StringUtils.isEmpty(id)){
            // 1.1、构造企微新增信息所需数据
            BeanUtils.copyProperties(request, companyQyRelation);
            companyQyRelation.setCreatedAt(LocalDateTime.now());
            if (StringUtils.isBlank(companyQyRelation.getCorpType())) {
                companyQyRelation.setCorpType(QyWechatConfigType.CUSTOMER_CORP.getCode());
            }
            save = this.save(companyQyRelation);
        }else {
            // 1.2、构造企微更新所需数据
            BeanUtils.copyProperties(request, companyQyRelation);
            companyQyRelation.setId(Long.parseLong(id));
            companyQyRelation.setUpdatedAt(LocalDateTime.now());
            save = this.updateById(companyQyRelation);
        }
        if(save){
            return Result.ok();
        }
        return Result.error("企微信息保存失败，请稍后尝试");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> saveCompanyBindQy(CompanyQyRelationRequest request) {
        if(CollectionUtil.isEmpty(request.getCorpIds())) {
            return Result.error("传入企微组织为空，请检查");
        }
        // 0、获取用户信息并校验是否有管理员权限
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        userCacheUtil.checkUserAdmin(systemUserSelfInfo);
        // 1、判断传入企微是否已经被其他组织关联，如果是则需要校验一下是否有对应关联公司的权限

        // 2、批量更新该企微的信息，把公司id填入
        LambdaUpdateWrapper<CompanyQyRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CompanyQyRelation::getCorpId, request.getCorpIds())
                .set(CompanyQyRelation::getCompanyId, request.getCompanyId());
        boolean update = this.update(updateWrapper);
        if(update){
            return Result.ok();
        }
        return Result.error("企微信息绑定失败，请稍后尝试");
    }

    //    @Override
//    public Result<Object> saveQyCompanyUser(QyAppReq qyAppReq) {
//        // 1、查询当前系统已经同步的用户信息，这里就不校验是否有该企微归属公司的权限了，因为直接放到这个查询方法里面了
//        SystemUserBindQyUserRequest systemUserBindQyUserRequest = new SystemUserBindQyUserRequest();
//        // 1.1传入公司id、企微id
//        BeanUtils.copyProperties(qyAppReq, systemUserBindQyUserRequest);
//        Result<List<SystemUserQyRelation>> SystemUserQyRelationResponse = systemUserQyRelationService.querySystemQyUser(systemUserBindQyUserRequest);
//        if(!SystemUserQyRelationResponse.isSuccess()){
//            // 1.2当前公司第一次同步这个企微下的员工数据，
//        }
//
//        // 2、查询企微侧对应的账号
//
//        return Result.ok();
//    }

    /**
     * @description: 管理端调用单个企微下api鉴权校验
     * 判断是否具有该公司的访问权限
     * @author: xiaQL
     * @date: 2025/6/17 23:56
     */
    private void verifyCompanyAuth(String companyId) {
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        OrganizationQueryRequest organizationQueryRequest = new OrganizationQueryRequest();
        organizationQueryRequest.setCompanyId(companyId);
        // 手动构造传入公司作为查询参数，如果筛选工具类没有抛出异常，那就代表有这个公司的查询权限
        userSelectUtil.getSelectPermission(systemUserSelfInfo, saleGroup, organizationQueryRequest);
    }

    /**
    * @description: 根据企微id获取企微访问token
     * 先走缓存
     * 没有再请求企微官方，将对应结果缓存（默认过期时间是2小时，直接用企微响应的过期时间吧）
     * @param queryScene 这里还需要区分具体访问的是哪个应用下的secret，像登录和通讯录这两边的secret就是不同的
    * @author: xiaQL
    * @date: 2025/6/17 23:56
    */
    public QyWeChatAuthResponse getQyWeChatAuthResponse(String corpId, String queryScene, CompanyQyRelation companyQyRelation) {
        if(StringUtils.isEmpty(queryScene)){
            throw new RuntimeException("请传入对应访问场景");
        }
        String accessTokenKey = QY_CORP_TOKEN + queryScene + ":" + corpId;
        String accessToken = redisJsonUtils.get(accessTokenKey, String.class);
        if(StringUtils.isNotEmpty(accessToken)){
            // 手动构造下过期时间，一是为了redis不存储多余数据，二是为了前端调用的时候知道何时过期可以重新获取
            QyWeChatAuthResponse qyWeChatAuthResponse = new QyWeChatAuthResponse();
            qyWeChatAuthResponse.setAccess_token(accessToken);
            qyWeChatAuthResponse.setExpires_in(redisJsonUtils.getExpire(accessTokenKey).intValue());
            return qyWeChatAuthResponse;
        }
        // 1、当前企业对应的访问token已过期，需要根据secret与对应corpId获取并缓存，先判断是否已经有企微账号配置(管理端是有配置，但是登录场景没有)
        if(companyQyRelation == null){
            LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CompanyQyRelation::getCorpId, corpId);
            List<CompanyQyRelation> companyQyRelations = this.getBaseMapper().selectList(wrapper);
            if(CollectionUtil.isEmpty(companyQyRelations)){
                throw new RuntimeException("当前企业未配置企微账号");
            }
            companyQyRelation = companyQyRelations.get(0);
        }
        // 2、根据当前归属应用的查询场景获取secret，再获取对应访问token
        String accessSecret = "";
        if(QyWechatQueryType.COMMON.getCode().equals(queryScene)){
            accessSecret =  companyQyRelation.getCorpSecret();
        }else if(QyWechatQueryType.CONTACT.getCode().equals(queryScene)){
            accessSecret = companyQyRelation.getContactSecret();
        }else if(QyWechatQueryType.CUSTOMER.getCode().equals(queryScene)){
            accessSecret = companyQyRelation.getCustomerSecret();
        }
        QyWeChatAuthResponse qyWeChatAuthResponse = qyAppClient.getToken(corpId, accessSecret);
        redisJsonUtils.set(accessTokenKey, qyWeChatAuthResponse.getAccess_token(), qyWeChatAuthResponse.getExpires_in(), TimeUnit.SECONDS);
        return qyWeChatAuthResponse;
    }

    /**
     * description : 根据corpId获取公司的企微信息
     * @title: queryByCorpId
     * @param: corpId
     * <AUTHOR>
     * @date 2025/6/29 17:56
     * @return CompanyQyRelation
     */
    @Override
    public CompanyQyRelation queryByCorpId(CompanyQyRelationRequest request) {
        LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompanyQyRelation::getCorpId, request.getCorpId())
                .eq(CompanyQyRelation::getCorpType, request.getCorpType());
        List<CompanyQyRelation> list = list(wrapper);
        if(ObjectUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    /**
     * description : 获取所有的企微信息
     * @title: queryQyList
     * @param:
     * <AUTHOR>
     * @date 2025/7/21 23:34
     * @return List<CompanyQyRelation>
     */
    @Override
    public List<CompanyQyRelation> queryQyList() {
        LambdaQueryWrapper<CompanyQyRelation> qyRelationLambdaQueryWrapper = Wrappers.lambdaQuery(CompanyQyRelation.class);
        qyRelationLambdaQueryWrapper.eq(CompanyQyRelation::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .eq(CompanyQyRelation::getCorpType, 0); // 0-客户企微，1-服务商模版，2-服务商通讯录应用
        return list(qyRelationLambdaQueryWrapper);
    }

    @Override
    public Page<CompanyQyRelationResponse> queryQyListAuth(CompanyQyRelationRequest request) {
        // 0、获取用户信息并校验是否有管理员权限
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        userCacheUtil.checkUserAdmin(systemUserSelfInfo);
        // 1、根据传入的查询参数构造查询条件
        Page<CompanyQyRelation> companyQyRelationPage = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(request.getCorpId()), CompanyQyRelation::getCorpId, request.getCorpId())
                .eq(CompanyQyRelation::getCorpType, QyWechatConfigType.CUSTOMER_CORP.getCode())
                .like(StringUtils.isNotEmpty(request.getCorpName()), CompanyQyRelation::getCorpName, request.getCorpName());
        Page<CompanyQyRelation> relationPage = this.getBaseMapper().selectPage(companyQyRelationPage, wrapper);
        List<CompanyQyRelation> companyQyRelations = relationPage.getRecords();
        // 2、构造秘钥信息不响应的实体类
        if(CollectionUtil.isEmpty(companyQyRelations)){
            return null;
        }
        List<CompanyQyRelationResponse> collect = companyQyRelations.stream().map(companyQyRelation -> {
            CompanyQyRelationResponse companyQyRelationResponse = new CompanyQyRelationResponse();
            BeanUtils.copyProperties(companyQyRelation, companyQyRelationResponse);
            companyQyRelationResponse.setId(String.valueOf(companyQyRelation.getId()));
            return companyQyRelationResponse;
        }).collect(Collectors.toList());
        Page<CompanyQyRelationResponse> responsePage = new Page<>(request.getPageNum(), request.getPageSize());
        responsePage.setRecords(collect);
        responsePage.setTotal(relationPage.getTotal());
        return responsePage;
    }

    @Override
    public Map<String, String> getCorpNameById(List<String> corpIds) {
        LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CompanyQyRelation::getCorpId, corpIds);
        List<CompanyQyRelation> companyQyRelations = this.getBaseMapper().selectList(wrapper);
        return companyQyRelations.stream()
                .collect(Collectors.toMap(CompanyQyRelation::getCorpId, CompanyQyRelation::getCorpName));
    }
}
