package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.constant.user.UserSelectType;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.dao.SalesGroupMapper;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.SalesGroupPO;
import cn.hxsy.service.CompanyService;
import cn.hxsy.service.SalesGroupService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SalesGroupServiceImpl extends ServiceImpl<SalesGroupMapper, SalesGroupPO> implements SalesGroupService {

    @Autowired
    private UserSelectUtil userSelectUtil;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Autowired
    private CompanyService companyService;

    @Override
    public List<SalesGroupPO> listByCompanyId(Long companyId) {
        LambdaQueryWrapper<SalesGroupPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SalesGroupPO::getCompanyId, companyId)
               .orderByDesc(SalesGroupPO::getCreatedAt);
        return list(wrapper);
    }

    @Override
    public Boolean updateByIds(UpdateStatusRequest updateStatusRequest) {
        if (updateStatusRequest == null || updateStatusRequest.getIds() == null || updateStatusRequest.getStatus() == null) {
            return false;
        }

        LambdaUpdateWrapper<SalesGroupPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(SalesGroupPO::getId, updateStatusRequest.getIds())
                .set(SalesGroupPO::getStatus, updateStatusRequest.getStatus());
        int update = getBaseMapper().update(null, updateWrapper);
        if (update>0) {
            return true;
        }
        return false;
    }

    @Override
    public Page<SalesGroupPO> querySaleGroupPage(Integer current, Integer size, OrganizationQueryRequest request) {
        // 首先获取用户缓存信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 获取用户角色对应查询权限
        SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserSelfInfo, UserSelectType.saleGroup, request);
        // 初始化查询条件
        List<String> perColumnId = new ArrayList<>();
        Integer companyId = null;
        List<Integer> companyIds = new ArrayList<>();
        Integer salesGroupId = null;
        List<String> perSalesGroupId = null;
        // 2.1、需要判断可见权限范围，如果有返回就优先走范围查询
        if(selectPermission != null){
            // 2.1.1、可见栏目范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerColumnId())){
                perColumnId = selectPermission.getPerColumnId();
            } else {
                // 没有可见权限范围，默认走工具类返回的栏目；超管即全部，其他即自身
                perColumnId.add(String.valueOf(selectPermission.getColumnId()));
            }
            // 2.1.2、可见公司范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerCompanyId())){
                companyIds = selectPermission.getPerCompanyId().stream().map(Integer::valueOf).collect(Collectors.toList());
            } else if(selectPermission.getCompanyId() != null){
                // 判断是否做了筛选查询处理
                companyId = selectPermission.getCompanyId();
            }else {
                // 以上都不成立，再构建自身所属栏目下公司的查询条件
                companyIds = companyService.queryCompanyByColumnId(perColumnId);
            }
            // 2.1.3、可见销售组范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerSalesGroupId())){
                perSalesGroupId = selectPermission.getPerSalesGroupId();
            } else {
                // 没有可见权限范围，默认走工具类返回的销售组；超管即全部，其他即自身
                salesGroupId = selectPermission.getSalesGroupId();
            }
        }
        Page<SalesGroupPO> page = new Page<>(current, size);
        LambdaQueryWrapper<SalesGroupPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtils.isNotEmpty(request.getStatus()), SalesGroupPO::getStatus, request.getStatus())
                .eq(companyId != null, SalesGroupPO::getCompanyId, companyId)
                .eq(salesGroupId != null, SalesGroupPO::getId, salesGroupId)
                .in(CollectionUtils.isNotEmpty(companyIds), SalesGroupPO::getCompanyId, companyIds)
                .in(CollectionUtils.isNotEmpty(perSalesGroupId), SalesGroupPO::getId, perSalesGroupId)
                .like(StringUtils.isNotEmpty(request.getName()), SalesGroupPO::getSalesGroupName, request.getName());
        wrapper.orderByDesc(SalesGroupPO::getCreatedAt);
        Page<SalesGroupPO> pageResult = this.page(page, wrapper);
//        pageResult.setTotal(pageResult.getRecords() == null ? 0 : pageResult.getRecords().size());
        return pageResult;
    }
}