package cn.hxsy.service.impl;

import cn.hxsy.api.qy.feign.license.QyWxLicenseClient;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.user.feign.vx.QyAppClient;
import cn.hxsy.base.constant.qyWechat.secretApp.QyWechatConfigType;
import cn.hxsy.base.enums.PaymentStatusEnum;
import cn.hxsy.base.request.QyCallBackRequest;
import cn.hxsy.base.util.WecomXmlUtil;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.constant.user.CacheConstant;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.datasource.model.entity.ExternalAccountOrder;
import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.service.*;
import cn.hxsy.base.weixin.AesException;
import cn.hxsy.base.weixin.WXBizMsgCrypt;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.thread.QyRecordThread;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.w3c.dom.Element;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.function.Function;

@Service
@Slf4j
public class WecomCallbackServiceImpl implements WecomCallbackService {

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Autowired
    private QyAppClient qyAppClient;

    @Resource
    private QyWxLicenseClient qyWxLicenseClient;

    @Autowired
    private QyAuthService qyAuthService;

    @Autowired
    private QyRecordThread qyRecordThread;

    @Autowired
    private CompanyQyRelationService companyQyRelationService;

    @Autowired
    private CustomerEventCallbackService customerEventCallbackService;

    @Autowired
    private ExternalAccountService externalAccountService;

    @Autowired
    private ExternalAccountOrderService externalAccountOrderService;

    /**
     * 应用回调信息类型处理函数映射
     */
    private final Map<String, Function<QyCallBackRequest, String>> INFO_TYPE_FUNCTION_MAP = ImmutableMap.<String, Function<QyCallBackRequest, String>>builder()
            .put("suite_ticket", this::suiteTicket) // 推送ticket
            .put("create_auth", this::createAuth) // 授权成功通知
            .put("change_auth", this::changeAuth) // 变更授权通知
            .put("cancel_auth", this::cancelAuth) // 取消授权通知
            .put("reset_permanent_code", this::resetPermanentCode) // 重置永久授权码
            .put("approve_special_auth", this::approveSpecialAuth) // 获客助手权限确认事件
            .put("cancel_special_auth", this::cancelSpecialAuth) // 获客助手权限取消事件
            .put("license_pay_success", this::licensePaySuccess) // license支付成功通知
            .put("license_refund", this::licenseRefund) // license退款通知
            .put("auto_activate", this::autoActivate) // 自动激活回调通知
            .build();

    /**
     * description : 企业微信回调统一入口（GET）（应用服务商使用）
     * @title: handleGetCallback
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: echoStr
     * <AUTHOR>
     * @date 2025/6/28 11:10
     * @return String
     */
    @Override
    public String handleGetCallbackProvider(String corpId, String corpType, String msgSignature, String timestamp, String nonce, String echoStr) {
        // 先根据 corpId 查询出对应配置的token和aesKey
        log.info("handleGetCallbackProvider => corpId: {}, msgSignature: {}, timestamp: {}, nonce: {}, echoStr: {}", corpId, msgSignature, timestamp, nonce, echoStr);
        // 回包 验证URL
        String sEchoStr = ""; // 需要返回的解密明文
        try {
            CompanyQyRelationRequest request = new CompanyQyRelationRequest();
            request.setCorpId(corpId);
            // 根据回调类型确定具体查询服务商的suitId
            request.setCorpType(corpType);
            CompanyQyRelation companyQyRelation = companyQyRelationService.queryByCorpId(request);
            String encodingAesKey = companyQyRelation.getEncodingAESKey();
            String token = companyQyRelation.getToken();
            // receiveid：企业应用的回调，表示corpid
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey, corpId);
            sEchoStr = wxcpt.VerifyURL(msgSignature, timestamp, nonce, echoStr);
            log.info("验证URL完成：{}", sEchoStr);
        } catch (AesException e) {
            throw new RuntimeException(e);
        }
        return sEchoStr;
    }

    /**
     * description : 企业微信回调统一入口（GET）
     * @title: handleGetCallback
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: echoStr
     * <AUTHOR>
     * @date 2025/6/29 19:14
     * @return String
     */
    @Override
    public String handleGetCallback(String corpId, String msgSignature, String timestamp, String nonce, String echoStr) {
        // 先根据 corpId 查询出对应配置的token和aesKey
        log.info("handleGetCallback => corpId: {}, msgSignature: {}, timestamp: {}, nonce: {}, echoStr: {}", corpId, msgSignature, timestamp, nonce, echoStr);
        // 回包 验证URL
        String sEchoStr = ""; // 需要返回的解密明文
        try {
            // 1、先根据 corpId 查询出对应配置的token和aesKey
            CompanyQyRelationRequest request = new CompanyQyRelationRequest();
            request.setCorpId(corpId);
            // 1.1、客户方企微变更回调，固定类型
            request.setCorpType(QyWechatConfigType.CUSTOMER_CORP.getCode());
            CompanyQyRelation companyQyRelation = companyQyRelationService.queryByCorpId(request);
            String encodingAesKey = companyQyRelation.getEncodingAESKey();
            String token = companyQyRelation.getToken();
            String openCorpId = companyQyRelation.getOpenCorpId();
            if (StringUtils.isBlank(openCorpId)) {
                // 1.获取服务商的 providerAccessToken
                QyWeChatAuthResponse qyWeChatAuthResponse = qyAuthService.getProviderToken("");
                log.info("获取服务商的 providerAccessToken：qyWeChatAuthResponse:{}", qyWeChatAuthResponse);
                String providerAccessToken = qyWeChatAuthResponse.getProvider_access_token();
                // 2. 根据服务商的 providerAccessToken 将 corpId 加密
                JSONObject params = new JSONObject();
                params.put("corpid", corpId);
                JSONObject response = qyAppClient.corpidToOpencorpid(providerAccessToken, params);
                log.info("根据服务商的 providerAccessToken 将 corpId 加密 response:{}", response);
                openCorpId = response.getString("open_corpid");
                companyQyRelation.setOpenCorpId(openCorpId);
                // 异步保存
                qyRecordThread.updateCompanyQyRelation(companyQyRelation);
            }
            // 3. 使用转换后的openCorpId进行解密
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey, openCorpId);
            sEchoStr = wxcpt.VerifyURL(msgSignature, timestamp, nonce, echoStr);
        } catch (AesException e) {
            throw new RuntimeException(e);
        }
        return sEchoStr;
    }

    /**
     * description : 企业微信回调统一入口（POST）（应用服务商）
     * @title: handleCallback
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: xml
     * <AUTHOR>
     * @date 2025/6/26 10:54
     * @return String
     */
    @Override
    public String handleCallbackProvider(String corpId, String corpType, String msgSignature, String timestamp, String nonce, String xml) {
        log.info("handleCallbackProvider corpId:{},msgSignature:{},timestamp:{},nonce:{},xml:{}", corpId, msgSignature, timestamp, nonce, xml);
        try {
            // 1、先根据 corpId 查询出对应配置的token和aesKey
            CompanyQyRelationRequest request = new CompanyQyRelationRequest();
            request.setCorpId(corpId);
            // 1.1、根据回调类型确定具体查询服务商的suitId
            request.setCorpType(corpType);
            CompanyQyRelation companyQyRelation = companyQyRelationService.queryByCorpId(request);
            String encodingAesKey = companyQyRelation.getEncodingAESKey();
            String token = companyQyRelation.getToken();

            Element element = WecomXmlUtil.parseXml(xml);
            String toUserName = WecomXmlUtil.getNodeText(element, "ToUserName");
            log.info("toUserName:{}", toUserName);
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey, toUserName);
            String decryptXml = wxcpt.DecryptMsg(msgSignature, timestamp, nonce, xml);
            log.info("解密成功：decryptXml:{}", decryptXml);
            Element decryptElement = WecomXmlUtil.parseXml(decryptXml);
            String infoType = WecomXmlUtil.getNodeText(decryptElement, "InfoType");
            if (ObjectUtils.isEmpty(infoType)) {
                log.error("infoType为空！");
                return "fail";
            }
            QyCallBackRequest qyCallBackRequest = new QyCallBackRequest();
            qyCallBackRequest.setElement(decryptElement);
            qyCallBackRequest.setCorpId(corpId);
            qyCallBackRequest.setCorpType(corpType);
            return exeInfoTypeFunction(infoType, qyCallBackRequest);
        } catch (Exception e) {
            log.error("handleCallbackProvider异常", e.getMessage(), e);
            return "fail";
        }
    }

    /**
     * description : 企业微信回调统一入口（POST）
     * @title: handleCallback
     * @param: corpId
     * @param: msgSignature
     * @param: timestamp
     * @param: nonce
     * @param: xml
     * <AUTHOR>
     * @date 2025/6/26 10:54
     * @return String
     */
    @Override
    public String handleCallback(String corpId, String msgSignature, String timestamp, String nonce, String xml) {
        log.info("handleCallback corpId:{},msgSignature:{},timestamp:{},nonce:{},xml:{}", corpId, msgSignature, timestamp, nonce, xml);
        try {
            // 1、先根据 corpId 查询出对应配置的token和aesKey
            CompanyQyRelationRequest request = new CompanyQyRelationRequest();
            request.setCorpId(corpId);
            // 1.1、客户方企微变更回调，固定类型
            request.setCorpType(QyWechatConfigType.CUSTOMER_CORP.getCode());
            CompanyQyRelation companyQyRelation = companyQyRelationService.queryByCorpId(request);
            String encodingAesKey = companyQyRelation.getEncodingAESKey();
            String token = companyQyRelation.getToken();

            Element element = WecomXmlUtil.parseXml(xml);
            String toUserName = WecomXmlUtil.getNodeText(element, "ToUserName");
            log.info("toUserName:{}", toUserName);
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey, toUserName);
            String decryptXml = wxcpt.DecryptMsg(msgSignature, timestamp, nonce, xml);
            log.info("解密成功：decryptXml:{}", decryptXml);
            Element decryptElement = WecomXmlUtil.parseXml(decryptXml);

            QyCallBackRequest qyCallBackRequest = new QyCallBackRequest();
            qyCallBackRequest.setElement(decryptElement);
            qyCallBackRequest.setCorpId(corpId);

            String infoType = WecomXmlUtil.getNodeText(decryptElement, "InfoType");
            log.info("获取InfoType:{}", infoType);
            // 应用回调获取 InfoType
            if (ObjectUtils.isNotEmpty(infoType)) {
                return exeInfoTypeFunction(infoType, qyCallBackRequest);
            }

            // 变更回调获取 Event 和 ChangeType
            String event = WecomXmlUtil.getNodeText(decryptElement, "Event");
            log.info("获取Event:{}", event);
            String changeType = WecomXmlUtil.getNodeText(decryptElement, "ChangeType");
            log.info("获取ChangeType:{}", changeType);
            if (ObjectUtils.isNotEmpty(event) && ObjectUtils.isNotEmpty(changeType)) {
                return customerEventCallbackService.handleCustomerEvent(event, changeType, qyCallBackRequest);
            }
            log.warn("企业客户未知事件", element.getTextContent());
            return "fail";
        } catch (Exception e) {
            log.error("handleCallback异常", e.getMessage(), e);
            return "fail";
        }
    }

    /** 测试接口 */
    public String handleUnionidToExternalUserid(String corpId, String unionid, String openid, String subject_type) {
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyAccessToken(corpId);
        log.info("获取企微{}，获取转换外部联系人id，响应状态码：{}, 响应内容:{}", corpId, qyAccessToken.getErrcode(), qyAccessToken);
        return qyAppClient.unionidToExternalUserid(qyAccessToken.getAccess_token(), new JSONObject() {{
            put("unionid", unionid);
            put("openid", openid);
            put("subject_type", subject_type);
        }}).toJSONString();
    }

    /**
     * description : 回调处理
     * @title: exeFunction
     * @param: infoType
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 21:50
     * @return String
     */
    private String exeInfoTypeFunction(String infoType, QyCallBackRequest qyCallBackRequest) {
        Function<QyCallBackRequest, String> func = INFO_TYPE_FUNCTION_MAP.get(infoType);
        if (ObjectUtils.isNotEmpty(func)) {
            return func.apply(qyCallBackRequest);
        } else {
            return defaultHandle(qyCallBackRequest);
        }
    }

    /**
     * description : 企业微信服务器会定时（每十分钟）向指令回调url推送ticket，suite_ticket实际有效期为30分钟，ticket会实时变更，并用于后续接口的调用。
     * @title: saveSuiteTicket
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 21:28
     * @return void
     */
    private String suiteTicket(QyCallBackRequest qyCallBackRequest) {
        String suiteTicket = WecomXmlUtil.getNodeText(qyCallBackRequest.getElement(), "SuiteTicket");
        redisJsonUtils.set(CacheConstant.SUITE_TICKET + qyCallBackRequest.getCorpId() + ":" + qyCallBackRequest.getCorpType(), suiteTicket);
        return "success";
    }

    /**
     * description : 从企业微信应用市场发起授权时，企业微信后台会推送授权成功通知。
     * @title: saveCreateAuth
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 21:33
     * @return String
     */
    private String createAuth(QyCallBackRequest qyCallBackRequest) {
        String authCode = WecomXmlUtil.getNodeText(qyCallBackRequest.getElement(), "AuthCode");
        if (StringUtils.isBlank(qyCallBackRequest.getCorpType())) {
            qyCallBackRequest.setCorpType(QyWechatConfigType.CUSTOMER_CORP.getCode()); // 普通企微
        }
        // 异步解析并存储对应授权企微信息
        qyRecordThread.getCorpInfoAndSave(authCode, qyCallBackRequest.getCorpType());
        return "success";
    }

    /**
     * description : 当授权方（即授权企业）在企业微信管理端的授权管理中，修改了对应用的授权后，企业微信服务器推送变更授权通知。支持第三方应用以及代开发应用。
     * @title: changeAuth
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 22:11
     * @return String
     */
    private String changeAuth(QyCallBackRequest qyCallBackRequest) {
        // 参数说明
        //SuiteId	第三方应用的SuiteId或者代开发应用模板id
        //InfoType	change_auth
        //TimeStamp	时间戳
        //AuthCorpId	授权方的corpid
        //State	构造授权链接指定的state参数
        //ExtraInfo	额外信息，部分场景会携带，在其他文档中分别描述
        log.info("变更授权通知，请求参数：{}", qyCallBackRequest.toString());
        return "success";
    }

    /**
     * description : 当授权方（即授权企业）在企业微信管理端的授权管理中，取消了对应用的授权托管后，企业微信后台会推送取消授权通知。
     * @title: cancelAuth
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 22:13
     * @return String
     */
    private String cancelAuth(QyCallBackRequest qyCallBackRequest) {
        // 参数说明
        //SuiteId	第三方应用的SuiteId或者代开发应用模板id
        //InfoType	cancel_auth
        //TimeStamp	时间戳
        //AuthCorpId	授权方企业的corpid
        log.info("取消授权通知，请求参数：{}", qyCallBackRequest.toString());
        return "success";
    }



    /**
     * description : 重置永久授权码通知
     * @title: resetPermanentCode
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 21:55
     * @return String
     */
    private String resetPermanentCode(QyCallBackRequest qyCallBackRequest) {
        String authCode = WecomXmlUtil.getNodeText(qyCallBackRequest.getElement(), "AuthCode");
        // 异步解析并存储对应授权企微信息
        qyRecordThread.getCorpInfoAndSave(authCode, qyCallBackRequest.getCorpType());
        return "success";
    }

    /**
     * description :获客助手权限确认事件
     * @title: approveSpecialAuth
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 21:56
     * @return String
     */
    private String approveSpecialAuth(QyCallBackRequest qyCallBackRequest) {
        // 参数说明：
        //SuiteId	第三方应用ID
        //AuthCorpId	授权企业的CorpID
        //InfoType	固定为approve_special_auth
        //TimeStamp	时间戳
        //AuthType	此时固定为customer_acquisition
        log.info("获客助手权限确认事件，请求参数：{}", qyCallBackRequest.toString());
        return "success";
    }

    /**
     * description : 获客助手权限取消事件
     * @title: cancelSpecialAuth
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 22:08
     * @return String
     */
    private String cancelSpecialAuth(QyCallBackRequest qyCallBackRequest) {
        // 参数说明：
        //SuiteId	第三方应用ID
        //AuthCorpId	授权企业的CorpID
        //InfoType	固定为cancel_special_auth
        //TimeStamp	时间戳
        //AuthType	此时固定为customer_acquisition
        log.info("获客助手权限取消事件，请求参数：{}", qyCallBackRequest.toString());
        return "success";
    }

    /**
     * description : license支付成功通知
     * @title: licensePaySuccess
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/7/17 10:07
     * @return String
     */
    private String licensePaySuccess(QyCallBackRequest qyCallBackRequest) {
        // 参数说明：
        //ServiceCorpId	服务商CorpID
        //InfoType	事件类型，此时固定为license_pay_success
        //AuthCorpId	客户企业CorpID
        //OrderId	订单号。如果为多企业新购订单，该值为子订单号
        //BuyerUserId	服务商内下单用户UserID
        //TimeStamp	时间戳

        Element element = qyCallBackRequest.getElement();
        String authCorpId = WecomXmlUtil.getNodeText(element, "AuthCorpId");
        String timeStamp = WecomXmlUtil.getNodeText(element, "TimeStamp");
        // 1、更新订单状态(已支付)
        LambdaUpdateWrapper<ExternalAccountOrder> updateWrapper = Wrappers.lambdaUpdate(ExternalAccountOrder.class);
        updateWrapper.eq(ExternalAccountOrder::getOrderId, authCorpId)
                .set(ExternalAccountOrder::getPayTime, LocalDateTime.now()) // 支付时间
                .set(ExternalAccountOrder::getPayStatus, PaymentStatusEnum.PAID.getCode()); // 支付状态
        externalAccountOrderService.update(updateWrapper);
        // 2、获取并保存激活码
        // TODO 续期订单如何处理？
        externalAccountService.saveActivationCode(authCorpId);
        log.info("license支付成功通知，请求参数：{}", qyCallBackRequest.toString());
        return "success";
    }

    /**
     * description : license退款通知
     * @title: licenseRefund
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/7/17 10:10
     * @return String
     */
    private String licenseRefund(QyCallBackRequest qyCallBackRequest) {
        //ServiceCorpId	服务商CorpID
        //InfoType	事件类型，此时固定为license_refund
        //AuthCorpId	客户企业CorpID
        //OrderId	订单号
        //OrderStatus	订单状态，1:退款成功，2:退款被拒绝。
        //TimeStamp	时间戳
        log.info("license退款通知，请求参数：{}", qyCallBackRequest.toString());
        // 更新订单状态
        Element element = qyCallBackRequest.getElement();
        String authCorpId = WecomXmlUtil.getNodeText(element, "AuthCorpId");
        // OrderStatus	订单状态，1:退款成功，2:退款被拒绝
        String OrderStatus = WecomXmlUtil.getNodeText(element, "OrderStatus");
        Integer getPayStatus = StringUtils.equals(OrderStatus, "1") ? PaymentStatusEnum.REFUND_SUCCESS.getCode() : PaymentStatusEnum.REFUND_REJECTED.getCode();
        LambdaUpdateWrapper<ExternalAccountOrder> updateWrapper = Wrappers.lambdaUpdate(ExternalAccountOrder.class);
        updateWrapper.eq(ExternalAccountOrder::getOrderId, authCorpId)
                .set(ExternalAccountOrder::getPayStatus, getPayStatus); // 支付状态
        externalAccountOrderService.update(updateWrapper);
        return "success";
    }

    /**
     * description : 自动激活回调通知
     * @title: autoActivate
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/7/17 10:31
     * @return String
     */
    private String autoActivate(QyCallBackRequest qyCallBackRequest) {
        //ServiceCorpId	服务商CorpID
        //InfoType	事件类型，此时固定为auto_activate
        //AuthCorpId	客户企业CorpID
        //Scene	许可自动激活的时机，1:企业成员主动访问应用，2:服务商调用消息推送接口，3:服务商调用互通接口
        //TimeStamp	时间戳
        //AccountList	激活的许可账号列表
        //ActiveCode	自动激活的许可账号激活码
        //Type	自动激活的许可的类型，1:基础许可，2:互通许可
        //ExpireTime	自动激活后，该许可的到期时间
        //UserId	许可自动激活的成员的UserID
        //PreviousStatus	激活成员自动激活前的许可状态，1:未激活许可，2:已激活许可且许可未过期（即许可的剩余时长小于等于7天），3:已激活许可且许可已过期
        //PreviousActiveCode	仅针对已激活的成员进行自动激活时返回，返回该成员之前激活的旧的激活码
        log.info("自动激活通知，请求参数：{}", qyCallBackRequest.toString());
        return "success";
    }

    /**
     * description : 默认处理
     * @title: defaultHandle
     * @param: qyCallBackRequest
     * <AUTHOR>
     * @date 2025/6/29 21:48
     * @return String
     */
    private String defaultHandle(QyCallBackRequest qyCallBackRequest) {
        log.warn("未找到对应的回调处理函数, qyCallBackRequest: {}", qyCallBackRequest.toString());
        return "unsupported";
    }

    @Override
    public JSONObject licenseTest(String method, JSONObject jsonObject) {
        log.info("licenseTest, method: {}, jsonObject: {}", method, jsonObject);

        // 获取服务商的 providerAccessToken
        QyWeChatAuthResponse qyWeChatAuthResponse = qyAuthService.getProviderToken("");
        log.info("获取服务商的 providerAccessToken：qyWeChatAuthResponse:{}", qyWeChatAuthResponse);
        String providerAccessToken = qyWeChatAuthResponse.getProvider_access_token();

        try {
            // 获取指定方法
            Method targetMethod = QyWxLicenseClient.class.getMethod(method, String.class, JSONObject.class);
            return (JSONObject) targetMethod.invoke(qyWxLicenseClient, providerAccessToken, jsonObject);
        } catch (Exception e) {
            log.error("调用方法失败: " + method, e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

} 