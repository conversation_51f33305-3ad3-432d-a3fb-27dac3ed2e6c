package cn.hxsy.service.impl;

import cn.hxsy.dao.WechatUserOpenidMapper;
import cn.hxsy.datasource.model.entity.WechatUserOpenid;
import cn.hxsy.service.WechatUserOpenidService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 微信用户openid关联服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class WechatUserOpenidServiceImpl extends ServiceImpl<WechatUserOpenidMapper, WechatUserOpenid> implements WechatUserOpenidService {

    /**
     * 新增微信用户openid关联信息
     *
     * @param wechatUserOpenid 微信用户openid关联信息对象
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean add(WechatUserOpenid wechatUserOpenid) {
        return save(wechatUserOpenid);
    }

    /**
     * 根据ID删除微信用户openid关联信息
     *
     * @param id 微信用户openid关联ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean deleteById(Long id) {
        return removeById(id);
    }

    /**
     * 更新微信用户openid关联信息
     *
     * @param wechatUserOpenid 微信用户openid关联信息对象，必须包含ID
     * @return 是否更新成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean update(WechatUserOpenid wechatUserOpenid) {
        return updateById(wechatUserOpenid);
    }

    /**
     * 根据appid和openid查询微信用户openid关联信息
     *
     * @param appId 小程序appid
     * @param openid 微信openid
     * @return 微信用户openid关联信息，如果不存在返回null
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public WechatUserOpenid getByAppIdAndOpenid(String appId, String openid) {
        LambdaQueryWrapper<WechatUserOpenid> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WechatUserOpenid::getAppId, appId)
               .eq(WechatUserOpenid::getOpenid, openid);
        return getOne(wrapper);
    }
} 