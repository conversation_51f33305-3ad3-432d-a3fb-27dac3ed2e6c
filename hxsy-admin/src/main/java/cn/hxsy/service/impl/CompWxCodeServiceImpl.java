package cn.hxsy.service.impl;

import cn.hxsy.dao.CompWxCodeMapper;
import cn.hxsy.datasource.model.entity.CompWxCode;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.service.CompWxCodeService;
import cn.hxsy.service.CompanyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公司邀请码信息服务实现类
 */
@Service
public class CompWxCodeServiceImpl extends ServiceImpl<CompWxCodeMapper, CompWxCode> implements CompWxCodeService {
    @Override
    public List<CompWxCode> getMiniProgramsByCompanyId(Long companyId) {
        LambdaQueryWrapper<CompWxCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompWxCode::getCompanyId, companyId)
                .eq(CompWxCode::getStatus, 1);  // 只查询启用状态的关联

        return this.list(wrapper);
    }

    @Autowired
    private CompanyService companyService;

    @Override
    public List<CompanyPO> getCompanyIdsByMiniProgramId(String miniProgramId) {
        // 1. 查询符合条件的公司ID列表
        LambdaQueryWrapper<CompWxCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompWxCode::getAppid, miniProgramId)
                .eq(CompWxCode::getStatus, 1); // 只查询启用状态的关联

        List<Integer> companyIds = this.list(wrapper).stream()
                .map(CompWxCode::getCompanyId)
                .collect(Collectors.toList());

        // 2. 如果没找到关联公司，返回空列表
        if (companyIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 查询完整的公司信息
        return companyService.listByIds(companyIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignMiniProgramsToCompany(Long companyId, List<String> miniProgramIds) {
        if (companyId == null || miniProgramIds == null || miniProgramIds.isEmpty()) {
            return false;
        }

        // 获取当前已分配的小程序
        List<CompWxCode> existingMiniPrograms = getMiniProgramsByCompanyId(companyId);
        List<String> existingMiniProgramIds = existingMiniPrograms.stream()
                .map(CompWxCode::getAppid)
                .collect(Collectors.toList());

        // 过滤出需要新增的小程序ID
        List<String> newMiniProgramIds = miniProgramIds.stream()
                .filter(id -> !existingMiniProgramIds.contains(id))
                .collect(Collectors.toList());

        // 批量新增关联关系
        if (!newMiniProgramIds.isEmpty()) {
            List<CompWxCode> relations = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (String miniProgramId : newMiniProgramIds) {
                CompWxCode relation = new CompWxCode();
                relation.setCompanyId(Math.toIntExact(companyId));
                relation.setAppid(miniProgramId);
                relation.setStatus(1); // 启用状态
                relation.setCreatedAt(now);
                relations.add(relation);
            }

            return this.saveBatch(relations);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeMiniProgramsFromCompany(Long companyId, List<String> miniProgramIds) {
        if (companyId == null || miniProgramIds == null || miniProgramIds.isEmpty()) {
            return false;
        }

        // 构建删除条件
        LambdaQueryWrapper<CompWxCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CompWxCode::getCompanyId, companyId)
                .in(CompWxCode::getAppid, miniProgramIds);

        // 执行删除操作
        return this.remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignCompaniesToMiniProgram(String miniProgramId, String miniProgramName, List<Long> companyIds) {
        if (miniProgramId == null || miniProgramId.isEmpty() || companyIds == null || companyIds.isEmpty()) {
            return false;
        }
        
        // 小程序名称可以为空，用于记录或将来扩展

        // 1. 查询该小程序现有的公司关联
        LambdaQueryWrapper<CompWxCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CompWxCode::getAppid, miniProgramId);
        List<CompWxCode> existingRelations = this.list(queryWrapper);

        // 2. 找出需要保留、删除和新增的关联
        List<Long> existingCompanyIds = existingRelations.stream()
                .map(relation -> relation.getCompanyId().longValue())
                .collect(Collectors.toList());

        // 需要删除的公司ID：存在于现有关联但不在新传入列表中的
        List<Long> toRemoveIds = existingCompanyIds.stream()
                .filter(id -> !companyIds.contains(id))
                .collect(Collectors.toList());

        // 需要新增的公司ID：存在于新传入列表但不在现有关联中的
        List<Long> toAddIds = companyIds.stream()
                .filter(id -> !existingCompanyIds.contains(id))
                .collect(Collectors.toList());

        // 3. 执行删除操作（只删除不再需要的关联）
        if (!toRemoveIds.isEmpty()) {
            LambdaQueryWrapper<CompWxCode> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(CompWxCode::getAppid, miniProgramId)
                    .in(CompWxCode::getCompanyId, toRemoveIds);
            this.remove(deleteWrapper);
        }

        // 4. 执行新增操作（只新增之前不存在的关联）
        if (!toAddIds.isEmpty()) {
            LocalDateTime now = LocalDateTime.now();
            List<CompWxCode> newRelations = new ArrayList<>();
            
            for (Long companyId : toAddIds) {
                if (companyId == null) {
                    continue;
                }
                
                CompWxCode relation = new CompWxCode();
                relation.setCompanyId(Math.toIntExact(companyId));
                relation.setAppid(miniProgramId);
                relation.setMiniProgramName(miniProgramName); // 设置小程序名称
                relation.setStatus(1); // 启用状态
                relation.setCreatedAt(now);
                newRelations.add(relation);
            }

            return this.saveBatch(newRelations);
        }

        return true;
    }

}
