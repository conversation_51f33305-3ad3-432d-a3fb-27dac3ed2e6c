package cn.hxsy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.hxsy.datasource.model.entity.User;
import cn.hxsy.dao.UserMapper;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.UserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13 16:22:51
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public Result login(User user) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserCode, user.getUserCode())
                .eq(User::getPassword, user.getPassword());
        List<User> users = this.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(users)){
            return Result.error("用户名或密码错误");
        }
        return Result.ok(users.get(0));
    }

    @Override
    public Result loginAuth(User user) {
        return Result.ok();
    }

    @Override
    public User queryUser(User user) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserCode, user.getUserCode())
                .eq(User::getPassword, user.getPassword());
        List<User> users = this.getBaseMapper().selectList(wrapper);
        log.info("users:{}", users.get(0));
        return users.get(0);
    }
}
