package cn.hxsy.service.impl;

import cn.hxsy.dao.CustomerTagRelationMapper;
import cn.hxsy.datasource.model.entity.CustomerTagRelation;
import cn.hxsy.service.CustomerTagRelationService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户标签关联服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class CustomerTagRelationServiceImpl extends ServiceImpl<CustomerTagRelationMapper, CustomerTagRelation> implements CustomerTagRelationService {

    /**
     * 新增客户标签关联信息
     *
     * @param customerTagRelation 客户标签关联信息对象
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean add(CustomerTagRelation customerTagRelation) {
        return save(customerTagRelation);
    }

    /**
     * 根据ID删除客户标签关联信息
     *
     * @param id 客户标签关联ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean deleteById(Long id) {
        return removeById(id);
    }

    /**
     * 更新客户标签关联信息
     *
     * @param customerTagRelation 客户标签关联信息对象，必须包含ID
     * @return 是否更新成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean update(CustomerTagRelation customerTagRelation) {
        return updateById(customerTagRelation);
    }

    /**
     * 根据客户ID查询标签关联列表
     *
     * @param customerId 客户ID
     * @return 标签关联列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public List<CustomerTagRelation> getByCustomerId(Long customerId) {
        LambdaQueryWrapper<CustomerTagRelation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CustomerTagRelation::getCustomerId, customerId);
        return list(wrapper);
    }
} 