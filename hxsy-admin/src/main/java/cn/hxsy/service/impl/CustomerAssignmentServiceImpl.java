package cn.hxsy.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.dao.CustomerAssignmentMapper;
import cn.hxsy.datasource.model.entity.ColumnPO;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.CustomerAssignment;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.service.ColumnService;
import cn.hxsy.service.CompanyService;
import cn.hxsy.service.CustomerAssignmentService;
import cn.hxsy.service.SystemUserService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 客户分配记录服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class CustomerAssignmentServiceImpl extends ServiceImpl<CustomerAssignmentMapper, CustomerAssignment> implements CustomerAssignmentService {

    @Autowired
    private SnowflakeIdWorker idWorker;

    @Autowired
    private SystemUserService systemUserService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private ColumnService columnService;

    /**
     * 分页查询客户分配记录
     *
     * @param pageNum 当前页码，默认1
     * @param pageSize 每页大小，默认10
     * @param customerId 客户ID，可选
     * @return 分页结果
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public Page<CustomerAssignment> page(Integer pageNum, Integer pageSize, Long customerId) {
        Page<CustomerAssignment> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CustomerAssignment> wrapper = new LambdaQueryWrapper<>();
        if (customerId != null) {
            wrapper.eq(CustomerAssignment::getCustomerId, customerId);
        }
        wrapper.orderByDesc(CustomerAssignment::getChangeTime);
        return page(page, wrapper);
    }

    /**
     * description : 保存客户分配操作记录
     * @title: saveCustomerAssignment
     * @param: customerId
     * @param: originalEmployeeId
     * @param: newEmployeeId
     * <AUTHOR>
     * @date 2025/5/17 22:25
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveCustomerAssignment(Long customerId, Long originalColumnId, Long originalCompanyId, Long originalEmployeeId, Long newEmployeeId) {
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        if (ObjectUtil.isAllNotEmpty(customerId, newEmployeeId)) {
            SystemUserPO originalEmployee = systemUserService.getById(originalEmployeeId);
            SystemUserPO newEmployee = systemUserService.getById(newEmployeeId);
            LocalDateTime now = LocalDateTime.now();
            CustomerAssignment customerAssignment = new CustomerAssignment();
            customerAssignment.setId(idWorker.nextId());
            customerAssignment.setCustomerId(customerId);

            ColumnPO columnPO = columnService.getById(originalColumnId);
            customerAssignment.setOriginalColumnId(originalColumnId);
            customerAssignment.setOriginalColumnName(columnPO.getColumnName());

            CompanyPO companyPO = companyService.getById(originalCompanyId);
            customerAssignment.setOriginalCompanyId(originalCompanyId);
            customerAssignment.setOriginalCompanyName(companyPO.getCompanyName());

            customerAssignment.setOriginalEmployeeId(originalEmployeeId);
            customerAssignment.setOriginalEmployeeName(originalEmployee.getUsername());
            customerAssignment.setNewEmployeeId(newEmployeeId);
            customerAssignment.setNewEmployeeName(newEmployee.getUsername());
            // 操作人
            customerAssignment.setOperatorId(systemUserSelfInfo.getId());
            customerAssignment.setOperatorName(systemUserSelfInfo.getUsername());
            customerAssignment.setStatus(UseStatusEnum.EFFECTIVE.getCode());
            customerAssignment.setChangeTime(now);
            customerAssignment.setCreatedAt(now);
            customerAssignment.setUpdatedAt(now);
            return save(customerAssignment);
        }
        return Boolean.FALSE;
    }
}