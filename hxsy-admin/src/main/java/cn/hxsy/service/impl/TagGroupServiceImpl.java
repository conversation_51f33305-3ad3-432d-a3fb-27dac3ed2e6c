package cn.hxsy.service.impl;

import cn.hxsy.dao.TagGroupMapper;
import cn.hxsy.datasource.model.entity.TagGroup;
import cn.hxsy.service.TagGroupService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 标签组服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class TagGroupServiceImpl extends ServiceImpl<TagGroupMapper, TagGroup> implements TagGroupService {

    /**
     * 新增标签组信息
     *
     * @param tagGroup 标签组信息对象
     * @return 是否保存成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean add(TagGroup tagGroup) {
        LocalDateTime now = LocalDateTime.now();
        // TODO 设置创建人

        tagGroup.setCreatedAt(now);
        tagGroup.setCreatedBy("admin");
        tagGroup.setUpdatedAt(now);
        tagGroup.setUpdatedBy("admin");
        return save(tagGroup);
    }

    /**
     * 根据ID删除标签组信息
     *
     * @param id 标签组ID
     * @return 是否删除成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean deleteById(Long id) {
        return removeById(id);
    }

    /**
     * 更新标签组信息
     *
     * @param tagGroup 标签组信息对象，必须包含ID
     * @return 是否更新成功
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public boolean update(TagGroup tagGroup) {
        LocalDateTime now = LocalDateTime.now();
        // TODO 设置创建人
        tagGroup.setUpdatedAt(now);
        tagGroup.setUpdatedBy("admin");
        return updateById(tagGroup);
    }

    /**
     * 根据父级ID查询子标签组列表
     *
     * @param parentId 父级标签组ID
     * @return 子标签组列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public List<TagGroup> getByParentId(Long parentId) {
        LambdaQueryWrapper<TagGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TagGroup::getParentId, parentId)
               .orderByAsc(TagGroup::getSortOrder);
        return list(wrapper);
    }

    /**
     * 根据层级查询标签组列表
     *
     * @param level 层级（1-一级标签组，2-二级标签组）
     * @return 标签组列表
     * <AUTHOR>
     * @date 2024-04-01
     */
    @Override
    public List<TagGroup> getByLevel(Integer level) {
        LambdaQueryWrapper<TagGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TagGroup::getLevel, level)
               .orderByAsc(TagGroup::getSortOrder);
        return list(wrapper);
    }

} 