package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hxsy.api.system.request.SysUserSelectPermissionRequest;
import cn.hxsy.api.system.response.SysMenuResponse;
import cn.hxsy.api.system.response.SysRoleResponse;
import cn.hxsy.api.system.response.SysUserSelectPermissionResponse;
import cn.hxsy.api.user.model.request.*;
import cn.hxsy.api.user.model.response.SystemUserQyRelationResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.constant.ResponseType;
import cn.hxsy.base.constant.user.UserRole;
import cn.hxsy.base.constant.user.UserSelectType;
import cn.hxsy.base.enums.AccountTypeEnum;
import cn.hxsy.base.enums.AuditStatusEnum;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.base.util.Sm4EncryptPo;
import cn.hxsy.dao.SystemUserMapper;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.request.SystemUserBindQyUserRequest;
import cn.hxsy.service.*;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SystemUserServiceImpl extends ServiceImpl<SystemUserMapper, SystemUserPO> implements SystemUserService {

    private final SystemUserMapper systemUserMapper;

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    @Autowired
    private SysUserSelectPermissionService sysUserSelectPermissionService;

    private SystemUserQyRelationService systemUserQyRelationService;

    @Autowired
    private void setSystemUserQyRelationService(@Lazy SystemUserQyRelationService systemUserQyRelationService) {
        this.systemUserQyRelationService = systemUserQyRelationService;
    }

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Autowired
    private UserSelectUtil userSelectUtil;

    public SystemUserServiceImpl(SystemUserMapper systemUserMapper) {
        this.systemUserMapper = systemUserMapper;
    }

    @Override
    public SystemUserPO getUserByUsername(String username) {
        if (!StringUtils.isNotEmpty(username)) {
            return null;
        }
        LambdaQueryWrapper<SystemUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemUserPO::getUsername, username);
        return getOne(wrapper);
    }

    @Override
    public Object getUserPage(SystemUserPageRequest request) {
        // 首先获取用户缓存信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 获取用户角色对应查询权限，先构造查询参数
        OrganizationQueryRequest selectRequest = new OrganizationQueryRequest();
        BeanUtil.copyProperties(request, selectRequest);
        selectRequest.setSalesGroup(request.getSalesGroupId());
        SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserSelfInfo, UserSelectType.system_user, selectRequest);
        LambdaQueryWrapper<SystemUserPO> wrapper = new LambdaQueryWrapper<>();
        // 基础查询条件
        if (StringUtils.isNotEmpty(request.getUsername())) {
            wrapper.like(SystemUserPO::getUsername, request.getUsername());
        }
        if (StringUtils.isNotEmpty(request.getAccountId())) {
            wrapper.like(SystemUserPO::getAccountId, request.getAccountId());
        }
        if (StringUtils.isNotEmpty(request.getPhone())) {
            wrapper.like(SystemUserPO::getPhone, request.getPhone());
        }
        if (request.getStatus() != null) {
            wrapper.eq(SystemUserPO::getStatus, request.getStatus());
        }
        if (request.getAuditStatus() != null) {
            wrapper.eq(SystemUserPO::getAuditStatus, request.getAuditStatus());
        } else {
            wrapper.isNotNull(SystemUserPO::getAuditStatus); // 默认查询不为空的记录
        }
        // 2.1、需要判断可见权限范围，如果有返回就优先走范围查询
        if(selectPermission != null){
            // 2.1.1按照总部id查询
            wrapper.eq(selectPermission.getHeadquartersId() != null, SystemUserPO::getHeadquartersId, selectPermission.getHeadquartersId());
            // 2.1.2、可见栏目范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerColumnId())){
                wrapper.in(CollectionUtils.isNotEmpty(selectPermission.getPerColumnId()), SystemUserPO::getColumnId, selectPermission.getPerColumnId());
            } else {
                // 没有可见权限范围，默认走工具类返回的栏目；超管即全部，其他即自身
                wrapper.eq(selectPermission.getColumnId() != null, SystemUserPO::getColumnId, selectPermission.getColumnId());
            }
            // 2.1.3、可见公司范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerCompanyId())){
                wrapper.in(CollectionUtils.isNotEmpty(selectPermission.getPerCompanyId()), SystemUserPO::getCompanyId, selectPermission.getPerCompanyId());
            } else {
                // 没有可见权限范围，默认走工具类返回的公司；超管即全部，其他即自身
                wrapper.eq(selectPermission.getCompanyId() != null, SystemUserPO::getCompanyId, selectPermission.getCompanyId());
            }
            // 2.1.4、可见销售组范围
            if(CollectionUtils.isNotEmpty(selectPermission.getPerSalesGroupId())){
                wrapper.in(CollectionUtils.isNotEmpty(selectPermission.getPerSalesGroupId()), SystemUserPO::getSalesGroupId, selectPermission.getPerSalesGroupId());
            } else {
                // 没有可见权限范围，默认走工具类返回的销售组；超管即全部，其他即自身
                wrapper.eq(selectPermission.getSalesGroupId() != null, SystemUserPO::getSalesGroupId, selectPermission.getSalesGroupId());
            }
        }
        // 时间范围查询
        if (request.getStartTime() != null && request.getEndTime() != null) {
            wrapper.between(SystemUserPO::getCreatedAt, request.getStartTime(), request.getEndTime());
        } else {
            if (request.getStartTime() != null) {
                wrapper.ge(SystemUserPO::getCreatedAt, request.getStartTime());
            }
            if (request.getEndTime() != null) {
                wrapper.le(SystemUserPO::getCreatedAt, request.getEndTime());
            }
        }
        // 按创建时间倒序排序
        wrapper.orderByDesc(SystemUserPO::getCreatedAt);
        // 执行分页查询
        Page<SystemUserPO> page = new Page<>(request.getCurrent(), request.getSize());
        Page<SystemUserPO> pageResult = page(page, wrapper);
        // 3、增加企微关联后，需要补充关联企微人员信息
        List<SystemUserPO> records = pageResult.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            // 3.1、先收集当前分页结果下的用户id，查询出所有关联的企微账号id
            List<String> collect = records.stream().map(userSelfInfo -> String.valueOf(userSelfInfo.getId())).collect(Collectors.toList());
            SystemUserBindQyUserRequest systemUserBindQyUserRequest = new SystemUserBindQyUserRequest();
            systemUserBindQyUserRequest.setQueryScene("1");
            systemUserBindQyUserRequest.setSystemUserIds(collect);
            try {
                Result<List<SystemUserQyRelation>> listResult = systemUserQyRelationService.querySystemQyUserInner(systemUserBindQyUserRequest);
                if (listResult.getCode() == ResponseType.Success.getCode() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    // 3.2、根据用户id收集为map，方便封装回分页结果集中(因为一个业务人员可能关联多个企微，所有需要分组来收集)
                    List<SystemUserQyRelation> data = listResult.getData();
                    Map<String, List<SystemUserQyRelation>> listMap = data.stream().collect(Collectors.groupingBy(SystemUserQyRelation::getSystemUserId));
                    List<SystemUserResponse> systemUserResponses = records.stream().map(record -> {
                        SystemUserResponse systemUserResponse = new SystemUserResponse();
                        BeanUtil.copyProperties(record, systemUserResponse);
                        List<SystemUserQyRelation> systemUserQyRelations = listMap.get(String.valueOf(systemUserResponse.getId()));
                        if (CollectionUtils.isNotEmpty(systemUserQyRelations)) {
                            List<SystemUserQyRelationResponse> systemUserQyRelationResponses = systemUserQyRelations.stream().map(systemUserQyRelation -> {
                                SystemUserQyRelationResponse systemUserQyRelationResponse = new SystemUserQyRelationResponse();
                                BeanUtil.copyProperties(systemUserQyRelation, systemUserQyRelationResponse);
                                systemUserQyRelationResponse.setId(String.valueOf(systemUserQyRelation.getId()));
                                return systemUserQyRelationResponse;
                            }).collect(Collectors.toList());
                            systemUserResponse.setSystemUserQyRelationResponses(systemUserQyRelationResponses);
                        }
                        return systemUserResponse;
                    }).collect(Collectors.toList());
                    Page<SystemUserResponse> pageResponse = new Page<>(request.getCurrent(), request.getSize());
                    pageResponse.setRecords(systemUserResponses);
                    pageResponse.setTotal(pageResult.getTotal());
                    return pageResponse;
                }else {
                    return pageResult;
                }
            } catch (Exception e) {
                log.error("企微下员工信息查询失败:", e);
                return pageResult;
            }
        }
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(SystemUserPO user) {
        if (user == null) {
            return false;
        }
        // 检查用户名是否已存在
        if (getUserByUsername(user.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }
        
        // 设置创建时间和更新时间
        user.setCreatedAt(LocalDateTime.now());
        user.setUpdatedAt(LocalDateTime.now());
        
        return save(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SystemUserRequest user) {
        // 首先获取用户缓存信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 如果要修改用户角色，需要校验用户是否为超级管理员，超级管理员只能修改自己的信息
//        if (user.getRoleId() != null) {
//            try {
//                userCacheUtil.checkUserAdmin(systemUserSelfInfo);
//            } catch (Exception e) {
//                throw new RuntimeException("非超管、普管无法修改员工角色信息");
//            }
//        }
        if (user == null || user.getId() == null) {
            return false;
        }
        // 检查用户是否存在
        if (getById(user.getId()) == null) {
            throw new RuntimeException("用户不存在");
        }
        // 设置更新时间与更新人
        user.setUpdatedAt(LocalDateTime.now());
        user.setUpdatedBy(systemUserSelfInfo.getId().toString());
        // 删除对应用户信息旧缓存
        userCacheUtil.deleteUserCache(user.getId().toString());
        // 判断用户可见查询权限是否更新，有则需要额外更新
        if(user.getSysUserSelectPermissionResponse() != null){
            SysUserSelectPermissionRequest request = new SysUserSelectPermissionRequest();
            BeanUtil.copyProperties(user.getSysUserSelectPermissionResponse(), request);
            request.setUserId(user.getId());
            sysUserSelectPermissionService.saveOrUpdateSystemSelectPermission(request);
        }
        SystemUserPO systemUserPO = new SystemUserPO();
        BeanUtil.copyProperties(user, systemUserPO);
        return updateById(systemUserPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long id) {
        if (id == null) {
            return false;
        }
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserStatus(UpdateStatusRequest request)  {
        if (request == null || request.getIds() == null || request.getStatus() == null) {
            return false;
        }

        LambdaUpdateWrapper<SystemUserPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(SystemUserPO::getId, request.getIds())
                .set(SystemUserPO::getStatus, request.getStatus())
                .set(SystemUserPO::getUpdatedAt, LocalDateTime.now());

        return update(wrapper);
    }

    /**
     * @return {@link Result }<{@link Object }>
     * <AUTHOR>
     * @date 2025/04/07
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> updateAuditStatusBatch(UpdateStatusRequest request)  {
        if (CollectionUtils.isEmpty(request.getIds())
                || StringUtils.isEmpty(request.getAuditStatus())) {
            return Result.error("参数不能为空");
        }

        // 使用条件构造器批量更新
        LambdaUpdateWrapper<SystemUserPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(SystemUserPO::getId, request.getIds())
                .set(SystemUserPO::getAuditStatus, request.getAuditStatus())
                .set(SystemUserPO::getUpdatedAt, LocalDateTime.now());

        boolean success = update(wrapper);

        return success ? Result.ok() : Result.error("批量更新失败");
    }

    @Override
    public boolean hasCompanyPermission(Long userId, String companyId) {
        // TODO: 实现公司权限校验逻辑
        // 这里需要根据实际业务逻辑实现，比如：
        // 1. 查询用户角色
        // 2. 查询角色对应的公司权限
        // 3. 判断是否有权限
        return true;
    }

    @Override
    public boolean hasSalesGroupPermission(Long userId, String salesGroup) {
        // TODO: 实现销售组权限校验逻辑
        // 这里需要根据实际业务逻辑实现，比如：
        // 1. 查询用户角色
        // 2. 查询角色对应的销售组权限
        // 3. 判断是否有权限
        return true;
    }

    /**
     * @param request
     * @return {@link Result }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Object> updateWxUser(WxUserRegisterRequest request) {
        // 获取用户手机号，判断是否存在
        LambdaQueryWrapper<SystemUserPO> mobileWrapper = new LambdaQueryWrapper<>();
        mobileWrapper.eq(SystemUserPO::getPhone, request.getPhone());
        if (count(mobileWrapper) > 0) {
            return Result.error("该手机号已注册");
        }
        LambdaQueryWrapper<SystemUserPO> accountWrapper = new LambdaQueryWrapper<>();
        accountWrapper.eq(SystemUserPO::getAccountId, request.getAccountId());

        if (count(accountWrapper) > 0) {
            return Result.error("该账号已被使用，请更换");
        }
        // 检查user是否已存在
        LambdaQueryWrapper<SystemUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemUserPO::getId, request.getUserId());
        SystemUserPO user = getBaseMapper().selectOne(wrapper);
        user.setUsername(request.getName());
        user.setAccountId(request.getAccountId());
        // 获取销售类型用户对应的角色id与名称
        SysRoleResponse sysRoleResponse = sysRoleService.getRoleByRoleType(UserRole.OTHER.getCode());
        user.setRoleId(Integer.parseInt(sysRoleResponse.getId()));
        user.setRoleName(sysRoleResponse.getRoleName());
        user.setPassword("A123456");
        user.setAccountType(AccountTypeEnum.SYSTEM_USER.getCode());
        user.setPhone(request.getPhone());
        user.setHeadquartersId(Long.valueOf(request.getHeadquartersId()));
        user.setColumnId(Long.valueOf(request.getColumnId()));
        user.setCompanyId(Long.valueOf(request.getCompanyId()));
        user.setSalesGroupId(Long.valueOf(request.getSalesGroupId()));
        user.setStatus(UseStatusEnum.EFFECTIVE.getCode());
        user.setAuditStatus(AuditStatusEnum.PENDING.getCode()); // 待审核
        user.setUpdatedAt(LocalDateTime.now());
        updateById(user);
        return Result.ok(user);
    }

    /**
     * 注册或获取微信用户信息
     *
     * @param unionId 微信unionId
     * @param userId 用户ID（可选）
     * @return 系统用户响应对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public SystemUserResponse registerWxUser(String unionId, Long userId) {
        if (StringUtils.isEmpty(unionId)) {
            throw new IllegalArgumentException("unionId不能为空");
        }
        SystemUserResponse systemUserResponse = new SystemUserResponse();
        // 检查unionId是否已存在
        LambdaQueryWrapper<SystemUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemUserPO::getUnionId, unionId)
               .eq(ObjectUtils.isNotEmpty(userId), SystemUserPO::getId, userId);
        SystemUserPO systemUserPO = baseMapper.selectOne(wrapper);
        // 用户已存在且已审核
        if (systemUserPO != null) {
            BeanUtil.copyProperties(systemUserPO, systemUserResponse);
            // 如果用户已审核，设置额外信息
            if (systemUserPO.getAuditStatus() != null) {
                systemUserResponse.setIsRegister("1");
                // 安全转换ID字段
                if (systemUserPO.getColumnId() != null) {
                    systemUserResponse.setColumnId(Math.toIntExact(systemUserPO.getColumnId()));
                }
                if (systemUserPO.getSalesGroupId() != null) {
                    systemUserResponse.setSalesGroupId(Math.toIntExact(systemUserPO.getSalesGroupId()));
                }
                if (systemUserPO.getCompanyId() != null) {
                    systemUserResponse.setCompanyId(Math.toIntExact(systemUserPO.getCompanyId()));
                }
                if (systemUserPO.getHeadquartersId() != null) {
                    systemUserResponse.setHeadquartersId(Math.toIntExact(systemUserPO.getHeadquartersId()));
                }
                // 查询用户具有的菜单权限
                Integer roleId = systemUserPO.getRoleId();
                this.setSysRoleAndMenuResponse(systemUserResponse, roleId);
            }
            return systemUserResponse;
        }
        // 用户不存在，创建新用户
        SystemUserPO newUser = new SystemUserPO();
        newUser.setUnionId(unionId);
        newUser.setCreatedAt(LocalDateTime.now());
        newUser.setUpdatedAt(LocalDateTime.now());
        save(newUser);
        // 复制新用户信息到响应对象
        BeanUtil.copyProperties(newUser, systemUserResponse);
        return systemUserResponse;
    }

    @Override
    public SystemUserResponse PcLogin(PcLoginRequest pcLoginRequest) {
        // 对传入密码进行sm4解密
        Sm4EncryptPo sm4EncryptPo = new Sm4EncryptPo().sm4Build(pcLoginRequest.getEncryptKey(), pcLoginRequest.getEncryptInitIv());
        sm4EncryptPo.setEncryptData(pcLoginRequest.getEncryptData());
        String decrypt = sm4EncryptPo.decrypt();
        // 根据用户名和密码对应用户信息
        LambdaQueryWrapper<SystemUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SystemUserPO::getAccountId, pcLoginRequest.getAccountId())
                .eq(SystemUserPO::getPassword, decrypt);
        List<SystemUserPO> systemUserPOS = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(systemUserPOS) || systemUserPOS.size() > 1) {
            return null;
        }
        SystemUserResponse systemUserResponse = new SystemUserResponse();
        SystemUserPO systemUserPO = systemUserPOS.get(0);
        BeanUtil.copyProperties(systemUserPO, systemUserResponse);
        // 2、查询用户具有菜单权限
        Integer roleId = systemUserPO.getRoleId();
        this.setSysRoleAndMenuResponse(systemUserResponse, roleId);
        return systemUserResponse;
    }

    @Override
    public SystemUserResponse qyWxUserLogin(SystemUserPO systemUserPO) {
        // 1、拷贝基本响应对象，用于返回auth服务设置企微侧登录的缓存
        SystemUserResponse systemUserResponse = new SystemUserResponse();
        BeanUtil.copyProperties(systemUserPO, systemUserResponse);
        if (systemUserPO.getColumnId() != null) {
            systemUserResponse.setColumnId(Math.toIntExact(systemUserPO.getColumnId()));
        }
        if (systemUserPO.getSalesGroupId() != null) {
            systemUserResponse.setSalesGroupId(Math.toIntExact(systemUserPO.getSalesGroupId()));
        }
        if (systemUserPO.getCompanyId() != null) {
            systemUserResponse.setCompanyId(Math.toIntExact(systemUserPO.getCompanyId()));
        }
        if (systemUserPO.getHeadquartersId() != null) {
            systemUserResponse.setHeadquartersId(Math.toIntExact(systemUserPO.getHeadquartersId()));
        }
        // 2、查询用户具有菜单权限
        Integer roleId = systemUserPO.getRoleId();
        this.setSysRoleAndMenuResponse(systemUserResponse, roleId);
        return systemUserResponse;
    }

    /**
     * @description: 登录时查询用户具有的角色与菜单权限
     * 栏目、公司、销售组管理员需要额外查询自身可见（栏目、公司、销售组）权限范围
     * @author: xiaQL
     * @date: 2025/5/3 11:36
     */
    private void setSysRoleAndMenuResponse(SystemUserResponse systemUserResponse, Integer roleId) {
        // 用户尚未分配角色，后续菜单、角色查询都可以跳过
        if (roleId == null)  {
            return;
        }
        SysRoleResponse userHasRole = sysRoleService.getRoleById(roleId);
        systemUserResponse.setSysRoleResponse(userHasRole);
        systemUserResponse.setRoleType(userHasRole.getRoleType());
        // 查询关联角色下对应所有关联菜单id
        List<SysMenuResponse> sysMenuResponses = sysRoleMenuService.getMenuByRoleId(roleId);
        systemUserResponse.setSysMenuResponses(sysMenuResponses);
        // 如果是栏目、公司、销售组管理员，需要额外查询自身可见权限范围
        if(UserRole.SALE_ADMIN.getCode() == userHasRole.getRoleType() ||
                UserRole.COM_ADMIN.getCode() == userHasRole.getRoleType() ||
                UserRole.COL_ADMIN.getCode() == userHasRole.getRoleType()){
            SysUserSelectPermissionRequest selectPermissionRequest = new SysUserSelectPermissionRequest();
            selectPermissionRequest.setUserId(systemUserResponse.getId());
            SysUserSelectPermissionResponse sysUserSelectPermission = sysUserSelectPermissionService.getSysUserSelectPermission(selectPermissionRequest);
            systemUserResponse.setSysUserSelectPermissionResponse(sysUserSelectPermission);
        }
    }
}