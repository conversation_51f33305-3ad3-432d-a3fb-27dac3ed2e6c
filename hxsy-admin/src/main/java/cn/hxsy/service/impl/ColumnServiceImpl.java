package cn.hxsy.service.impl;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.dao.ColumnMapper;
import cn.hxsy.datasource.model.entity.ColumnPO;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.service.ColumnService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static cn.hxsy.base.constant.user.UserSelectType.column;

@Service
public class ColumnServiceImpl extends ServiceImpl<ColumnMapper, ColumnPO> implements ColumnService {

    @Autowired
    private UserSelectUtil userSelectUtil;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Override
    public Boolean updateByIds(UpdateStatusRequest request) {
        if (request == null || request.getIds() == null || request.getStatus() == null) {
            return false;
        }
        LambdaUpdateWrapper<ColumnPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ColumnPO::getId, request.getIds())
                .set(ColumnPO::getStatus, request.getStatus());
        int update = getBaseMapper().update(null, updateWrapper);
        if (update>0) {
            return true;
        }
        return false;
    }

    @Override
    public Page<ColumnPO> queryPageById(Integer current, Integer size, String name, Long headquartersId) {
        // 首先获取用户缓存信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 获取用户角色对应查询权限
        OrganizationQueryRequest organizationQueryRequest = new OrganizationQueryRequest();
//        organizationQueryRequest.setColumnId(String.valueOf(headquartersId));
        SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserSelfInfo, column, organizationQueryRequest);
        Integer columnId = null;
        List<String> perColumnId = null;
        if(selectPermission != null){
            headquartersId = selectPermission.getHeadquartersId() != null ? selectPermission.getHeadquartersId().longValue() : null;
            // 1、对于栏目筛选，需要先看是否返回了可见权限范围，如果带了就走范围查询，没带就是精确查询
            if(CollectionUtils.isNotEmpty(selectPermission.getPerColumnId())){
                perColumnId = selectPermission.getPerColumnId();
            } else {
                // 没有可见权限范围，默认走工具类返回的栏目；超管即全部，其他即自身
                columnId = selectPermission.getColumnId();
            }
        }
        Page<ColumnPO> page = new Page<>(current, size);
        LambdaQueryWrapper<ColumnPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(headquartersId != null, ColumnPO::getHeadquartersId, headquartersId)
                .eq(columnId != null, ColumnPO::getId, columnId)
                .in(CollectionUtils.isNotEmpty(perColumnId), ColumnPO::getId, perColumnId)
                .like(StringUtils.isNotEmpty(name), ColumnPO::getColumnName, name);
        wrapper.orderByDesc(ColumnPO::getCreatedAt);
        Page<ColumnPO> pageResult = this.page(page, wrapper);
        return pageResult;
    }
}