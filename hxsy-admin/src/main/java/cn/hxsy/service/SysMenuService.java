package cn.hxsy.service;

import cn.hxsy.api.system.request.SysMenuRequest;
import cn.hxsy.api.system.response.SysMenuResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SysMenu;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 系统菜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:05:44
 */
public interface SysMenuService extends IService<SysMenu> {

    /**
    * @description: 查询目前系统已有的菜单信息
    * @author: xiaQL
    * @date: 2025/5/7 22:50
    */
    List<SysMenuResponse> querySystemMenu(SysMenuRequest request);

    /**
     * @description: 创建或更新系统菜单
     * 根据是否有菜单id来判断是创建还是更新
     * @author: xiaQL
     * @date: 2025/5/7 22:50
     */
    Result<Object> saveOrUpdateSystemMenu(SysMenuRequest request);
}
