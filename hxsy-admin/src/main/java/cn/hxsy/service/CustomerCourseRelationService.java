package cn.hxsy.service;

import cn.hxsy.api.user.model.response.CampPeriodResponse;
import cn.hxsy.api.user.model.response.CourseVideoResponse;
import cn.hxsy.datasource.model.entity.CustomerCourseRelation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 客户课程关联服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface CustomerCourseRelationService extends IService<CustomerCourseRelation> {

    /**
     * description : 更新课程关联 、更新客户行为轨迹
     *
     * @return void
     * @title: saveCourseCustomerRel
     * @param: customerCourseRelation
     * <AUTHOR>
     * @date 2025/4/5 18:22
     */
    boolean saveCourseCustomerRel(CustomerCourseRelation customerCourseRelation);

    List<CampPeriodResponse> getCampPeriodInfoByCustomerId(Long customerId, Long columnId);

    Page<CourseVideoResponse> getCourseRelationByCustomerId(Integer pageNum, Integer pageSize, Long customerId, Long campPeriodId);

    Boolean isCourseArrival(Long customerId, Long campPeriodId, Long courseId);
}