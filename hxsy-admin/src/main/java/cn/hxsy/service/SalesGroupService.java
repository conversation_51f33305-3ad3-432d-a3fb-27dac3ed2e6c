package cn.hxsy.service;

import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.request.UpdateStatusRequest;
import cn.hxsy.datasource.model.entity.ColumnPO;
import cn.hxsy.datasource.model.entity.SalesGroupPO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface SalesGroupService extends IService<SalesGroupPO> {
    
    /**
     * 根据公司ID查询销售组列表
     *
     * @param companyId 公司ID
     * @return 销售组列表
     */
    List<SalesGroupPO> listByCompanyId(Long companyId);

    Boolean updateByIds(UpdateStatusRequest updateStatusRequest);

    /**
    * @description: 公司下销售组分页查询
     * 公司、状态、销售组名作为筛选条件
    * @author: xiaQL
    * @date: 2025/5/17 14:26
    */
    Page<SalesGroupPO> querySaleGroupPage(Integer current, Integer size,
                                            OrganizationQueryRequest request);
} 