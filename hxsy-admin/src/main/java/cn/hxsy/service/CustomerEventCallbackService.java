package cn.hxsy.service;

import cn.hxsy.base.request.QyCallBackRequest;

public interface CustomerEventCallbackService {
    /**
     * description : 处理企业客户事件回调
     * @title: handleCustomerEvent
     * @param: corpId 公司ID
     * @param: event 事件类型
     * @param: changeType 变更类型
     * @param: qyCallBackRequest 回调参数
     * <AUTHOR>
     * @date 2025/7/13 20:58
     * @return String
     */
    String handleCustomerEvent(String event, String changeType, QyCallBackRequest qyCallBackRequest);

} 