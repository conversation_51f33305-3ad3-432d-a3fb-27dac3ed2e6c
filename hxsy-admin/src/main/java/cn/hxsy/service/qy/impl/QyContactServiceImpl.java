package cn.hxsy.service.qy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hxsy.api.qy.feign.contract.QyWxContactClient;
import cn.hxsy.api.qy.request.QyUserReq;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.qy.response.contact.QyContactDeptResponse;
import cn.hxsy.api.qy.response.contact.QyContactResponse;
import cn.hxsy.api.qy.response.contact.QyContactUserResponse;
import cn.hxsy.base.constant.qyWechat.secretApp.QyWechatConfigType;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import cn.hxsy.request.QyProviderTokenRequest;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.service.qy.QyContactService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static cn.hxsy.base.constant.qyWechat.auth.QyWechatAuthType.SUITE_ACCESS_TOKEN;

/**
 * <AUTHOR> XiaQL
 * @description : QyAuthServiceImpl
 * @ClassName : QyAuthServiceImpl
 * @date: 2025-06-28 16:22
 */
@Service
@Slf4j
public class QyContactServiceImpl implements QyContactService {

    @Resource
    private QyAuthService qyAuthService;

    @Resource
    private QyWxContactClient qyWxContactClient;


    @Override
    public QyContactResponse deptList(QyProviderTokenRequest qyProviderTokenRequest) {
        // 1、获取对应企微组织的访问token
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyProviderAccessToken(qyProviderTokenRequest);
        QyContactResponse qyWeChatQueryDeptResponse = qyWxContactClient.deptList(qyAccessToken.getAccess_token(), qyProviderTokenRequest.getDeptId());
        // 2、校验返回结果，为空则抛异常
        qyWeChatQueryDeptResponse.checkQyResponse();
        log.info("获取企微{}，获取部门信息，响应状态码：{}, 响应内容:{}", qyProviderTokenRequest.getCorpId(), qyWeChatQueryDeptResponse.getErrcode(), qyWeChatQueryDeptResponse);
        return qyWeChatQueryDeptResponse;
    }

    @Override
    public QyContactResponse userList(QyProviderTokenRequest qyProviderTokenRequest) {
        // 1、获取对应企微组织的访问token
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyProviderAccessToken(qyProviderTokenRequest);
        QyContactResponse qyWeChatQueryDeptResponse = qyWxContactClient.userList(qyAccessToken.getAccess_token(), qyProviderTokenRequest.getDeptId());
        // 2、校验返回结果，为空则抛异常
        qyWeChatQueryDeptResponse.checkQyResponse();
        log.info("获取企微{}，获取部门下成员信息，响应状态码：{}, 响应内容:{}", qyProviderTokenRequest.getCorpId(), qyWeChatQueryDeptResponse.getErrcode(), qyWeChatQueryDeptResponse);
        return qyWeChatQueryDeptResponse;
    }

    @Override
    public QyContactResponse userListId(QyProviderTokenRequest qyProviderTokenRequest) {
        // 1、获取对应企微组织的访问token
        qyProviderTokenRequest.setCorpType(QyWechatConfigType.PROVIDER_CONTACT.getCode());
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyProviderAccessToken(qyProviderTokenRequest);
        log.info("获取到accessToken:{}", qyAccessToken);
        QyUserReq qyUserReq = new QyUserReq();
        qyUserReq.setLimit(qyProviderTokenRequest.getLimit());
        qyUserReq.setCursor(qyUserReq.getCursor());
        QyContactResponse qyWeChatQueryDeptResponse = qyWxContactClient.userListId(qyAccessToken.getAccess_token(), qyUserReq);
        // 2、校验返回结果，为空则抛异常
        qyWeChatQueryDeptResponse.checkQyResponse();
        log.info("获取企微{}，获取部门下成员信息，响应状态码：{}, 响应内容:{}", qyUserReq.getCorpId(), qyWeChatQueryDeptResponse.getErrcode(), qyWeChatQueryDeptResponse);
        return qyWeChatQueryDeptResponse;
    }

    @Override
    public QyContactResponse openUserToUser(QyProviderTokenRequest qyProviderTokenRequest) {
        // 1、获取对应企微组织的访问token
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyProviderAccessToken(qyProviderTokenRequest);
        log.info("获取到accessToken:{}", qyAccessToken);
        // 2、构造企微处转换openUserId所需参数
        QyUserReq qyUserReq = new QyUserReq();
        qyUserReq.setOpen_userid_list(qyProviderTokenRequest.getOpenUsers());
        qyUserReq.setSource_agentid(qyProviderTokenRequest.getAgentId());
        QyContactResponse qyWeChatQueryDeptResponse = qyWxContactClient.openUserToUser(qyAccessToken.getAccess_token(), qyUserReq);
        // 3、校验返回结果，为空则抛异常
        qyWeChatQueryDeptResponse.checkQyResponse();
        log.info("获取企微{}，获取部门下成员信息，响应状态码：{}, 响应内容:{}", qyUserReq.getCorpId(), qyWeChatQueryDeptResponse.getErrcode(), qyWeChatQueryDeptResponse);
        return qyWeChatQueryDeptResponse;
    }

    @Override
    public QyContactResponse userToOpenUser(QyProviderTokenRequest qyProviderTokenRequest) {
        // 1、获取对应企微组织的访问token
        QyWeChatAuthResponse qyAccessToken = qyAuthService.getQyProviderAccessToken(qyProviderTokenRequest);
        log.info("获取到accessToken:{}", qyAccessToken);
        // 2、构造企微处转换openUserId所需参数
        QyUserReq qyUserReq = new QyUserReq();
        qyUserReq.setUserid_list(qyProviderTokenRequest.getUsers());
        QyContactResponse qyWeChatQueryDeptResponse = qyWxContactClient.userToOpenUser(qyAccessToken.getAccess_token(), qyUserReq);
        // 3、校验返回结果，为空则抛异常
        qyWeChatQueryDeptResponse.checkQyResponse();
        log.info("获取企微{}，获取部门下成员信息，响应状态码：{}, 响应内容:{}", qyUserReq.getCorpId(), qyWeChatQueryDeptResponse.getErrcode(), qyWeChatQueryDeptResponse);
        return qyWeChatQueryDeptResponse;
    }

    @Override
    public List<QyContactUserResponse> allUserList(QyProviderTokenRequest qyProviderTokenRequest) {
        String corpId = qyProviderTokenRequest.getCorpId();
        // 1、获取当前企微下存在的全部组织(至少存在一个主部门，不需要非空校验)
        QyContactResponse qyContactResponse = null;
        try {
            qyContactResponse = this.deptList(qyProviderTokenRequest);
            if(StringUtils.isNotEmpty(qyContactResponse.getErrcode()) && !"0".equals(qyContactResponse.getErrcode())){
                throw new RuntimeException(qyContactResponse.getErrmsg());
            }
        } catch (Exception e) {
            log.error("企微账号：{}，企微侧查询部门信息失败：{}", corpId, e.getMessage());
            throw new RuntimeException("企微侧查询部门信息失败，请稍后重试");
        }
        // 2、根据组织信息，依次获取其下的成员信息
        List<QyContactDeptResponse> departmentList = qyContactResponse.getDepartment_id();
        // 2.1、构造用户获取请求参数，只有部门id在循环，需要重置，corpId是不需要变的
        QyProviderTokenRequest userRequest = new QyProviderTokenRequest();
        userRequest.setCorpId(corpId);
        List<QyContactUserResponse> allQyUserlist = new ArrayList<>();
        for (QyContactDeptResponse department : departmentList) {
            userRequest.setDeptId(String.valueOf(department.getId()));
            QyContactResponse qyUserResponse = null;
            try {
                qyUserResponse = this.userList(userRequest);
                if(StringUtils.isNotEmpty(qyContactResponse.getErrcode()) && !"0".equals(qyContactResponse.getErrcode())){
                    throw new RuntimeException(qyContactResponse.getErrmsg());
                }
            } catch (Exception e) {
                log.error("企微账号：{}，部门：[{}]下成员信息查询失败：{}", corpId, department.getId(), e.getMessage());
                throw new RuntimeException("企微侧部门：[" + department.getId() + "]下成员信息失败，请稍后重试");
            }
            List<QyContactUserResponse> userlist = qyUserResponse.getUserlist();
            // 2.2、将获取到的成员信息，批量收集并构造为系统数据库对应结构，用于后续其他接口调用保存
            if(CollectionUtil.isEmpty(userlist)){
                log.info("获取到corpId:[{}]下的部门：[{}]成员信息为空，跳过同步处理", corpId, department.getId());
                continue;
            }
            allQyUserlist.addAll(userlist);
        }
        return allQyUserlist;
    }

    /**
    * @description: 保存企微下所有部门信息
     * 用于后续遍历查询部门下成员信息
    * @author: xiaQL
    * @date: 2025/7/24 9:49
    */
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAllDept(QyProviderTokenRequest qyProviderTokenRequest) {
        return false;
    }
}
