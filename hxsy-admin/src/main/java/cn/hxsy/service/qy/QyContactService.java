package cn.hxsy.service.qy;

import cn.hxsy.api.qy.request.QyUserReq;
import cn.hxsy.api.qy.response.contact.QyContactResponse;
import cn.hxsy.api.qy.response.contact.QyContactUserResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.request.QyProviderTokenRequest;

import java.util.List;

/**
* @description: 企微服务商认证相关接口
* @author: xiaQL
* @date: 2025/6/28 16:19
*/
public interface QyContactService {

    /**
    * @description: 企微服务端获取子部门ID列表
     * 1、获取对应企微组织的访问token
    * @author: xiaQL
    * @date: 2025/6/29 22:14
    */
    QyContactResponse deptList(QyProviderTokenRequest qyProviderTokenRequest);

    /**
    * @description: 企微服务端获取部门成员
     * 1、获取对应企微组织的访问token
    * @author: xiaQL
    * @date: 2025/6/29 22:13
    */
    QyContactResponse userList(QyProviderTokenRequest qyProviderTokenRequest);

    /**
     * @description: 获取成员ID列表
     * @author: xiaQL
     * @date: 2025/6/29 22:13
     * @return
     * 只响应全部成员的openUserId（基本没用）
     */
    QyContactResponse userListId(QyProviderTokenRequest qyProviderTokenRequest);

    /**
    * @description: 企微userid转换-服务商转换openUserId，获取企微内部使用的userid
    * @author: xiaQL
    * @date: 2025/7/14 22:19
    */
    QyContactResponse openUserToUser(QyProviderTokenRequest qyProviderTokenRequest);

    /**
     * @description: 企微userid转换-服务商转换openUserId，获取企微内部使用的userid
     * @author: xiaQL
     * @date: 2025/7/14 22:19
     */
    QyContactResponse userToOpenUser(QyProviderTokenRequest qyProviderTokenRequest);

    /**
     * @description: 查询当前企微下所有成员
     * 1、先获取对应企微下的所有部门信息
     * 2、依次遍历获取所有部门下的所有成员
     * @param qyProviderTokenRequest 所需查询企微方员工数据的corpId
     * @author: xiaQL
     * @date: 2025/6/29 22:13
     */
    List<QyContactUserResponse> allUserList(QyProviderTokenRequest qyProviderTokenRequest);
}
