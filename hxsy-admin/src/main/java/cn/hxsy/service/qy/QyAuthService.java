package cn.hxsy.service.qy;

import cn.hxsy.api.qy.request.auth.QyWechatAuthReq;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthSecretResponse;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.request.QyProviderTokenRequest;

/**
* @description: 企微服务商认证相关接口
* @author: xiaQL
* @date: 2025/6/28 16:19
*/
public interface QyAuthService{

    /**
    * @description: 获取服务商凭证
     * 由于服务商是我们内部，与其他企微账号公用一张表，此处传入的服务商corpId应该是固定的
    * @author: xiaQL
    * @date: 2025/6/28 16:22
    */
    QyWeChatAuthResponse getProviderToken(String corpId);

    /**
     * @description: 获取代开发应用模板凭证
     * 由于服务商是我们内部，与其他企微账号公用一张表，此处传入的服务商corpId应该是固定的
     * @param authType 代开发授权类型 (1-代开发模版、2-代开发应用)
     * @author: xiaQL
     * @date: 2025/6/28 16:22
     */
    QyWeChatAuthResponse getSuiteToken(String corpId, String authType);

    /**
    * @description: 内部获取代开发授权应用secret，方便响应打印
    * @author: xiaQL
    * @date: 2025/6/29 17:45
    */
    QyWeChatAuthSecretResponse getPermanentCodeInner(String suiteAccessToken, QyWechatAuthReq qyWechatAuthReq);

    /**
     * @description: 代开发授权应用secret的获取
     * 1、得在回调逻辑中调用此方法
     * 1.1、先调用getSuiteToken获取待开发模版凭证suite_access_token
     * 1.2、再根据回调的authCode与当前回调类型获取到对应授权企微的id和操作secret
     * 2、解析对应授权成功的企微信息，判断当前企微信息在数据库是否存储，是则更新，不是则保存
     * 2.1、再加上生成的回调地址、加密key等信息
     * @author: xiaQL
     * @date: 2025/6/28 16:22
     */
    void getPermanentCode(String authCode, String authType, String token, String encodingAesKey);

    QyWeChatAuthResponse getQyAccessToken(String corpId);

    /**
    * @description: 获取服务商代开发应用凭证，等效于自建应用的accessToken
    * @author: xiaQL
    * @date: 2025/7/13 17:32
    */
    QyWeChatAuthResponse getQyProviderAccessToken(QyProviderTokenRequest request);
}
