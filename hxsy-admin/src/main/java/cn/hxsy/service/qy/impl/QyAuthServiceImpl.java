package cn.hxsy.service.qy.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hxsy.api.qy.feign.auth.QyWxAuthClient;
import cn.hxsy.api.qy.request.auth.QyWechatAuthReq;
import cn.hxsy.api.qy.response.auth.AuthCorpInfo;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthSecretResponse;
import cn.hxsy.base.constant.qyWechat.secretApp.QyWechatConfigType;
import cn.hxsy.base.constant.system.QyWechatQueryType;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.request.QyProviderTokenRequest;
import cn.hxsy.service.CompanyQyRelationService;
import cn.hxsy.service.qy.QyAuthService;
import cn.hxsy.utils.QyWechatCacheUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.hxsy.base.constant.qyWechat.auth.QyWechatAuthType.*;

/**
 * <AUTHOR> XiaQL
 * @description : QyAuthServiceImpl
 * @ClassName : QyAuthServiceImpl
 * @date: 2025-06-28 16:22
 */
@Service
@Slf4j
public class QyAuthServiceImpl implements QyAuthService {

    @Resource
    private QyWechatCacheUtils qyWechatCacheUtils;

    @Resource
    private QyWxAuthClient qyWxAuthClient;

    @Resource
    private CompanyQyRelationService companyQyRelationService;

    @Value("${feign.provider.qyWx.provideId:ww1382cb0dbbdaf9ee}")
    private String provideId;

    @Override
    public QyWeChatAuthResponse getProviderToken(String corpId) {
        QyProviderTokenRequest qyProviderTokenRequest = new QyProviderTokenRequest();
        qyProviderTokenRequest.setCorpId(provideId);
        qyProviderTokenRequest.setQyWechatAuthType(PROVIDER_TOKEN);
        return qyWechatCacheUtils.getAndCacheQyWxToken(qyProviderTokenRequest);
    }

    @Override
    public QyWeChatAuthResponse getSuiteToken(String corpId, String authType) {
        QyProviderTokenRequest qyProviderTokenRequest = new QyProviderTokenRequest();
        qyProviderTokenRequest.setCorpId(provideId);
        qyProviderTokenRequest.setCorpType(authType);
        qyProviderTokenRequest.setQyWechatAuthType(SUITE_ACCESS_TOKEN);
        return qyWechatCacheUtils.getAndCacheQyWxToken(qyProviderTokenRequest);
    }

    @Override
    public QyWeChatAuthSecretResponse getPermanentCodeInner(String suiteAccessToken, QyWechatAuthReq qyWechatAuthReq) {
        QyWeChatAuthSecretResponse qyWeChatAuthSecretResponse = qyWxAuthClient.getPermanentCode(suiteAccessToken, qyWechatAuthReq);
        log.info("获取代开发授权应用secret，请求token：{}，请求参数：{}", suiteAccessToken, qyWechatAuthReq);
        log.info("获取代开发授权应用secret，响应状态码：{}, 响应内容:{}", qyWeChatAuthSecretResponse.getErrcode(), qyWeChatAuthSecretResponse);
        if (StringUtils.isNotEmpty(qyWeChatAuthSecretResponse.getErrcode()) && !"0".equals(qyWeChatAuthSecretResponse.getErrcode())) {
            throw new RuntimeException(qyWeChatAuthSecretResponse.getErrmsg());
        }
        return qyWeChatAuthSecretResponse;
    }

    @Override
    public void getPermanentCode(String authCode, String authType, String token, String encodingAesKey) {
        // 1、获取服务商的suite_access_token
        QyWeChatAuthResponse suiteToken = this.getSuiteToken(provideId, authType);
        // 2、根据回调authCode与suite_access_token获取具体授权企微信息
        QyWechatAuthReq qyWechatAuthReq = new QyWechatAuthReq();
        qyWechatAuthReq.setAuth_code(authCode);
        try {
            QyWeChatAuthSecretResponse qyWeChatAuthSecretResponse = this.getPermanentCodeInner(suiteToken.getSuite_access_token(), qyWechatAuthReq);
            // 3、解析对应授权成功的企微信息，并判断当前企微信息在数据库是否存储
            CompanyQyRelationRequest request = new CompanyQyRelationRequest();
            AuthCorpInfo authCorpInfo = qyWeChatAuthSecretResponse.getAuth_corp_info();
            String corpid = authCorpInfo.getCorpid();
            LambdaQueryWrapper<CompanyQyRelation> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CompanyQyRelation::getCorpId, corpid);
            List<CompanyQyRelation> companyQyRelations = companyQyRelationService.getBaseMapper().selectList(wrapper);
            request.setCorpId(corpid);
            request.setCorpName(authCorpInfo.getCorp_name()); // 补充企微名称
            // 3.1、设置秘钥时，需要判断当前是代开发基础secret，还是通讯录secret
            if (QyWechatConfigType.PROVIDER_CONTACT.getCode().equals(authType)) {
                request.setContactSecret(qyWeChatAuthSecretResponse.getPermanent_code());
            }else {
                request.setCorpSecret(qyWeChatAuthSecretResponse.getPermanent_code());
            }
            if(CollectionUtil.isNotEmpty(companyQyRelations)){
                // 存在对应企微信息，不更新相关秘钥与token，直接获取数据库存储id进行更新
                Long id = companyQyRelations.get(0).getId();
                request.setId(String.valueOf(id));
            }else {
                // 不存在对应企微信息，新增对应企微信息
                request.setToken(token);
                request.setEncodingAESKey(encodingAesKey);
            }
            companyQyRelationService.saveQyCompanyInner(request);
        } catch (Exception e) {
            log.info("获取服务商secret失败，异常：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @Override
    public QyWeChatAuthResponse getQyAccessToken(String corpId) {
        return qyWechatCacheUtils.getQyAccessToken(corpId);
    }

    @Override
    public QyWeChatAuthResponse getQyProviderAccessToken(QyProviderTokenRequest request) {
        return qyWechatCacheUtils.getQyProviderAccessToken(request);
    }
}
