package cn.hxsy.service;

import cn.hxsy.api.system.request.SysRoleRequest;
import cn.hxsy.api.system.response.SysRoleResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.SysRole;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 系统角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-02 11:04:30
 */
public interface SysRoleService extends IService<SysRole> {

    /**
    * @description: 根本据角色id集合获取角色信息
    * @author: xiaQL
    * @date: 2025/5/2 17:38
    */
    List<SysRoleResponse> getRolesByIds(List<Long> roleIds);

    /**
     * @description: 根本据角色id获取角色信息
     * @author: xiaQL
     * @date: 2025/5/2 17:38
     */
    SysRoleResponse getRoleById(Integer roleId);

    /**
     * @description: 根本据角色类型获取角色信息
     * @author: xiaQL
     * @date: 2025/5/2 17:38
     */
    SysRoleResponse getRoleByRoleType(Integer roleType);

    /**
     * @description: 前端分页查询系统角色列表
     * 可根据角色名称、角色码值、状态查询
     * @author: xiaQL
     * @date: 2025/5/2 17:38
     */
    Page<SysRole> getSystemRolePage(Integer pageNum, Integer pageSize, SysRoleRequest request);

    /**
     * @description: 新增或修改系统角色
     * @author: xiaQL
     * @date: 2025/5/2 17:38
     */
    Result<Object> saveOrUpdateRole(SysRoleRequest request);

}
