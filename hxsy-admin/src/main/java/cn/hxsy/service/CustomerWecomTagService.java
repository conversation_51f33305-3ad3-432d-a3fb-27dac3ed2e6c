package cn.hxsy.service;

import cn.hxsy.api.qy.QyBaseResponse;
import cn.hxsy.api.qy.request.tag.TagAddRequest;
import cn.hxsy.api.qy.request.tag.TagListRequest;
import cn.hxsy.api.qy.response.tag.TagAddResponse;
import cn.hxsy.api.qy.response.tag.TagCorpResponse;
import cn.hxsy.datasource.model.entity.WecomTagSyncRecord;
import cn.hxsy.dto.CustomerMarkTagBackendDTO;
import cn.hxsy.dto.CustomerMarkTagDTO;
import cn.hxsy.dto.WecomTagGroupDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 企业微信客户标签服务接口
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
public interface CustomerWecomTagService {

    /**
     * 获取企业标签库
     * 获取企业设置的所有客户标签
     *
     * @param corpId 企业微信id
     * @return 企业标签库响应
     */
    TagCorpResponse getCorpTagList(String corpId, TagListRequest request);

    /**
     * 添加企业客户标签
     * 添加新的标签或标签组
     *
     * @param corpId 企业微信id
     * @param groupId 标签组id，如果创建的是标签，则需要指定此参数
     * @param groupName 标签组名称，如果创建的是标签组，则需要指定此参数
     * @param order 标签组次序值，非必填，默认为0
     * @param tagList 标签列表
     * @return 添加标签响应
     */
    TagAddResponse addCorpTag(String corpId, String groupId, String groupName, Integer order, List<TagAddRequest.TagInfo> tagList);

    /**
     * 编辑企业客户标签
     * 修改标签或标签组的名称、次序值
     *
     * @param corpId 企业微信访问令牌
     * @param id 标签或标签组的id
     * @param name 新的标签或标签组名称
     * @param order 标签/标签组的次序值，非必填，默认为0
     * @param groupId 标签组id，如果要修改标签所属的标签组，需要填写此参数
     * @param isGroup 是否是标签组，默认false
     * @return 编辑标签响应
     */
    QyBaseResponse editCorpTag(String corpId, String id, String name, Integer order, String groupId, Boolean isGroup);

    /**
     * 删除企业客户标签
     * 删除标签或标签组
     *
     * @param corpId 企业微信访问令牌
     * @param tagIds 标签的id列表
     * @param groupIds 标签组的id列表
     * @param deleteTagWithGroup 删除标签组时是否连同标签一起删除，默认为true
     * @return 删除标签响应
     */
    QyBaseResponse deleteCorpTag(String corpId, List<String> tagIds, List<String> groupIds, Boolean deleteTagWithGroup);

    /**
     * 查询本地数据库中的企微标签组及标签
     *
     * @param corpId 企业微信ID
     * @return 标签组及标签列表
     */
    List<WecomTagGroupDTO> getLocalTagGroups(String corpId);

    /**
     * 为客户打标签
     *
     * @param dto 客户打标签请求参数
     * @return 基础响应
     */
    QyBaseResponse markCustomerTag(CustomerMarkTagDTO dto);
    
    /**
     * 为客户打标签后端
     */
    Boolean markCustomerTagByBackend(CustomerMarkTagBackendDTO dto);

    /**
     * 批量为客户打标签
     *
     * @param corpId 企业微信ID
     * @param tagList 客户打标签请求参数列表
     * @return 基础响应
     */
    QyBaseResponse batchMarkCustomerTag(String corpId, List<CustomerMarkTagDTO> tagList);

    IPage<WecomTagSyncRecord> getSyncRecords(Page<WecomTagSyncRecord> page, String corpId, Integer syncType, Integer status);
}
