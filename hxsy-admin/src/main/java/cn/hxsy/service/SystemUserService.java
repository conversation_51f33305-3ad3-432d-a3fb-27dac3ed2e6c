package cn.hxsy.service;

import cn.hxsy.api.user.model.request.*;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.base.response.Result;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface SystemUserService extends IService<SystemUserPO> {
    
    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    SystemUserPO getUserByUsername(String username);

    /**
     * 分页查询用户列表（带权限校验）
     *
     * @param request 查询请求
     * @return 分页结果
     */
    Object getUserPage(SystemUserPageRequest request);

    /**
     * 创建用户
     *
     * @param user 用户信息
     * @return 是否创建成功
     */
    boolean createUser(SystemUserPO user);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 是否更新成功
     */
    boolean updateUser(SystemUserRequest user);

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long id);

    /**
     * 更新用户状态
     *
     * @return 是否更新成功
     */
    boolean updateUserStatus(UpdateStatusRequest request);

    /**
     * 检查用户是否有权限查询指定公司
     *
     * @param userId 用户ID
     * @param companyId 公司ID
     * @return 是否有权限
     */
    boolean hasCompanyPermission(Long userId, String companyId);

    /**
     * 检查用户是否有权限查询指定销售组
     *
     * @param userId 用户ID
     * @param salesGroup 销售组
     * @return 是否有权限
     */
    boolean hasSalesGroupPermission(Long userId, String salesGroup);

    /**
     * 注册微信小程序用户
     *
     * @param request 注册请求
     * @return 是否注册成功
     */
    Result<Object> updateWxUser(WxUserRegisterRequest request);

    SystemUserResponse registerWxUser(String unionId, Long userId);

    Result<Object> updateAuditStatusBatch(UpdateStatusRequest request);

    /**
    * @description: 企微侧用户登录系统
     * 1、直接传入企微账号关联的系统用户信息，查询其关联菜单信息并缓存
    * @author: xiaQL
    * @date: 2025/6/24 15:53
    */
    SystemUserResponse qyWxUserLogin(SystemUserPO systemUserPO);

    /**
     * @description: Pc端登录
     * 需要对密码进行sm4解密
     * 别把密码响应出去了
     * @author: xiaQL
     * @date: 2025/4/19 11:03
     */
    SystemUserResponse PcLogin(PcLoginRequest pcLoginRequest);
}