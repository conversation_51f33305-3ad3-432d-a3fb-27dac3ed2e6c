package cn.hxsy.service;

import cn.hxsy.api.user.model.request.*;
import cn.hxsy.api.user.model.response.*;
import cn.hxsy.base.request.wxPayRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.Customer;
import cn.hxsy.datasource.model.entity.CustomerCourseRelation;
import cn.hxsy.base.request.BatchRemarkCustomerRequest;
import cn.hxsy.datasource.model.entity.BatchRemarkCustomerVO;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 客户服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface CustomerService extends IService<Customer> {

    /**
     * description : 客户注册
     * @title: customerRegister
     * @param: customerRegisterRequest
     * <AUTHOR>
     * @date 2025/4/4 18:49
     * @return CustomerResponse
     */
    CustomerResponse customerRegister(CustomerRegisterRequest customerRegisterRequest);

    /**
     * description : 客户登录
     * @title: customerLogin
     * @param: customerLoginRequest
     * <AUTHOR>
     * @date 2025/4/4 18:49
     * @return CustomerResponse
     */
    CustomerResponse customerLogin(CustomerLoginRequest customerLoginRequest);

    /**
     * description : 更新客户课程关联
     * @title: updCourseCustomerRel
     * @param: customerCourseRelation
     * <AUTHOR>
     * @date 2025/4/12 20:40
     * @return boolean
     */
    boolean updCourseCustomerRel(CustomerCourseRelation customerCourseRelation);

    boolean updCourseCustomerRelStatus(CustomerCourseRelation customerCourseRelation);

    /**
     * description : 更新课程时长
     * @title: updCourseDuration
     * @param: customerCourseRelation
     * <AUTHOR>
     * @date 2025/6/6 21:41
     * @return boolean
     */
    boolean updCourseDuration(CustomerCourseRelation customerCourseRelation);

    /**
     * description : 更新最近活跃时间
     * @title: updLastActiveTime
     * @param: customer
     * <AUTHOR>
     * @date 2025/6/6 22:04
     * @return boolean
     */
    boolean updLastActiveTime(Long customerId);

    /**
     * 分页查询客户列表
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param request 查询条件
     * @return 分页结果
     */
    Page<CustomerQueryResponse> queryCustomerPage(long current, long size, CustomerQueryRequest request);

    /**
    * @description: 分页查询客户列表
     * 先在全部客户信息筛选符合的客户，进行分页
     * 再查询后续单个人对应的标签内容
    * @author: xiaQL
    * @date: 2025/5/7 1:55
    */
    Page<CustomerQueryResponse> queryCustomerPageNew(long current, long size, CustomerQueryRequest request);


    /**
     * description : 获取视频课程信息
     * @title: getCourseVideo
     * @param: customerCourseVideoRequest
     * <AUTHOR>
     * @date 2025/5/9 0:18
     * @return CourseVideoResponse
     */
    CampCourseVideoResponse getCourseVideo(CustomerCourseVideoRequest customerCourseVideoRequest);

    /**
     * description : 提交答题
     * @title: submitAnswer
     * @param: customerCourseVideoRequest
     * <AUTHOR>
     * @date 2025/5/9 1:37
     * @return boolean
     */
    boolean submitAnswer(CustomerCourseVideoRequest customerCourseVideoRequest);

    /**
     * description : 更新手机号
     * @title: updateMobile
     * @param: customer
     * <AUTHOR>
     * @date 2025/5/13 21:31
     * @return boolean
     */
    boolean updateMobile(Customer customer);

    /**
     * description : 修改账号状态
     * @title: updStatus
     * @param: customer
     * <AUTHOR>
     * @date 2025/6/8 1:25
     * @return boolean
     */
    boolean updStatus(CustomerBatchRequest customerBatchRequest);

    /**
     * @description: 删除客户账号
     * 其实是给某些销售不小心注册成客户了，删了好让他重新注册
     * 1、删除缓存
     * 1）union_id关联的客户类型注册缓存
     * 2）union_id对应的登录缓存（可以不用，毕竟其实等7天自己也删了）
     * 2、删除数据库中
     * 注册表中union_id关联成客户类型的注册数据
     * 客户表中union_id关联成客户信息（还要走分表）
     * @author: qinLuan
     * @date: 2025/6/12 22:57
     * @param: [customer]
     **/
    Result<Object> delCustom(CustomerLoginRequest request);

    Boolean updateRedPacketAndUseStatus(wxPayRequest wxPayRequest);

    /**
     * description : 更新添加企微的状态
     * @title: updateWecomStatus
     * @param: customerId
     * @param: status
     * <AUTHOR>
     * @date 2025/7/26 0:00
     * @return Boolean
     */
    Boolean updateWecomStatus(Long customerId, Integer status);

    /**
     * description : 获取客户详情
     * @title: getCustomerDetail
     * @param: customerId
     * <AUTHOR>
     * @date 2025/7/26 0:53
     * @return JSONArray
     */
    JSONArray getCustomerDetail(Long customerId);

    /**
     * description : 批量设置客户企微备注
     * @title: batchRemarkCustomer
     * @param: param 批量设置备注参数
     * <AUTHOR>
     * @date 2025/7/26
     * @return BatchRemarkCustomerVO
     */
    BatchRemarkCustomerVO batchRemarkCustomer(BatchRemarkCustomerRequest param);
}