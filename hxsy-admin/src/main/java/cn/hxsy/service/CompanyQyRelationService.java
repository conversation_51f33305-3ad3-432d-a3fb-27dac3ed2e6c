package cn.hxsy.service;

import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.qy.response.QyWeChatUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CompanyQyRelation;
import cn.hxsy.request.CompanyQyRelationRequest;
import cn.hxsy.response.CompanyQyRelationResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 公司与企微账号关联配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 11:42:49
 */
public interface CompanyQyRelationService extends IService<CompanyQyRelation> {

    /**
     * @description: 因为企微相关接口不论客户端、服务端都离不开的access_token，所以这里需要区分下是哪个场景
     * 1、如果传入了企微内登录参数code，不做鉴权直接获取对应的企业秘钥发起登录接口调用
     * 1.1判断企微登录流程是否成功，成功则去获取当前系统下存储该企微账号信息
     * 1.2获取当前系统下存储该企微账号信息
     * 1.3获取系统给当前企微人员关联业务人员信息
     * 2、如果没有传入，则是管理端在获取一个公司下的企业微信关联配置，需要鉴权处理
     * 2.1传入具体corpId，则返回指定公司账号下的企微账号对应的access_token，用于管理端调用单个企微下api（比如部门下用户获取等）
     * 2.2如果传入null，则返回所有公司账号下的企微账号对应配置，用于管理端配置公司下关联的企微账号
     * 关于管理端使用所需要的鉴权：
     * 1、首先判断当前人员是否具有该公司的管理权限
     * @author: xiaQL
     * @date: 2025/6/17 21:56
     */
    Result<Object> queryCompanyQyToken(QyAppReq qyAppReq);

    /**
     * @description: 服务商代开发的企微内登录，其实算作第三方登录，需要通过suite_access_token+js_code来登录
     * @author: xiaQL
     * @date: 2025/6/17 21:56
     */
    Result<Object> queryQyLoginToken(QyAppReq qyAppReq);

    /**
    * @description: 查询管理端公司下关联的单个企业下相关部门信息
    * @author: xiaQL
    * @date: 2025/6/18 0:44
    */
    Result<Object> queryCompanyQyDept(QyAppReq qyAppReq);

    /**
     * @description: 查询管理端公司下关联的单个企业下相关成员信息
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<Object> queryCompanyQyUser(QyAppReq qyAppReq);

    /**
     * @description: 查询管理端公司下关联的单个企业下相关成员信息-对内
     * 不做相关权限校验
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<List<QyWeChatUserResponse>> queryCompanyQyUserInner(QyAppReq qyAppReq);

    /**
     * @description: 新增企微账号信息
     * 所需数据：
     * 企业ID、该企业对应自建小程序的凭证密钥、该企业通讯录管理的凭证密钥
     * 权限校验：
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<Object> saveQyCompany(CompanyQyRelationRequest request);

    /**
     * @description: 新增企微账号信息-内部（企微侧回调，没有更新人信息）
     * 所需数据：
     * 企业ID、该企业对应自建小程序的凭证密钥、该企业通讯录管理的凭证密钥
     * 权限校验：
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<Object> saveQyCompanyInner(CompanyQyRelationRequest request);

    /**
     * @description: 公司批量关联企微账号信息
     * 所需数据：
     * 公司id、企业ID
     * 权限校验：
     * 1、当前操作人员是否有该公司的管理权限
     * @author: xiaQL
     * @date: 2025/6/18 0:44
     */
    Result<Object> saveCompanyBindQy(CompanyQyRelationRequest request);

    /**
    * @description: 同步一个企微下员工账号信息
     * 1、获取当前系统已经存储的企微员工，获取到最后一个员工的游标
     * 2、据此游标到企微官方获取传入企微组织下后续的员工信息
     * 权限校验：
     *
    * @author: xiaQL
    * @date: 2025/6/22 20:55
    */
//    Result<Object> saveQyCompanyUser(QyAppReq qyAppReq);

    /**
     * description : 根据corpId获取公司的企微信息
     * @title: queryByCorpId
     * @param: corpId
     * <AUTHOR>
     * @date 2025/6/29 17:56
     * @return CompanyQyRelation
     */
    CompanyQyRelation queryByCorpId(CompanyQyRelationRequest request);

    /**
     * description : 获取所有的企微信息
     * @title: queryQyList
     * @param:
     * <AUTHOR>
     * @date 2025/7/21 23:34
     * @return List<CompanyQyRelation>
     */
    List<CompanyQyRelation> queryQyList();

    /**
    * @description: 分页获取所有的企微信息
     * 1、条件查询
     * 校验：
     * 1、按归属公司校验
    * @author: xiaQL
    * @date: 2025/7/26 12:10
    */
    Page<CompanyQyRelationResponse> queryQyListAuth(CompanyQyRelationRequest request);

    /**
     * @description: 根据企微id收集企微名称
     * @author: xiaQL
     * @date: 2025/7/26 12:10
     */
    Map<String, String> getCorpNameById(List<String> corpIds);

}
