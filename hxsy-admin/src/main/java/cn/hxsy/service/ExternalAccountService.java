package cn.hxsy.service;

import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.datasource.model.entity.ExternalAccount;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ExternalAccountService extends IService<ExternalAccount> {

    /**
     * description : 获取订单中的激活码并保存
     * @title: saveActivationCode
     * @param: orderId
     * <AUTHOR>
     * @date 2025/7/18 16:04
     * @return boolean
     */
    boolean saveActivationCode(String orderId);

    /**
     * description : 批量激活账号
     * @title: batchActivation
     * @param: corpId
     * @param: systemUserQyRelations
     * <AUTHOR>
     * @date 2025/7/18 16:05
     * @return boolean
     */
    boolean batchActivation(String corpId, List<SystemUserQyRelation> systemUserQyRelations);


    /**
     * description : 分页查询互通账号列表
     * @title: queryPage
     * @param: pageNum
     * @param: pageSize
     * @param: qyUserRequest
     * <AUTHOR>
     * @date 2025/7/18 16:21
     * @return Page<ExternalAccount>
     */
    Page<ExternalAccount> queryPage(Integer pageNum, Integer pageSize, QyUserRequest qyUserRequest);

    /**
     * description : 根据订单号查询激活码
     * @title: queryActivationCode
     * @param: orderId
     * <AUTHOR>
     * @date 2025/7/24 0:26
     * @return List<ExternalAccount>
     */
    List<ExternalAccount> queryActivationCode(String orderId);

}