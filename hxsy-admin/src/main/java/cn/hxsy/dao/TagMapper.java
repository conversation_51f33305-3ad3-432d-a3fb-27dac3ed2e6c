package cn.hxsy.dao;

import cn.hxsy.api.user.model.response.TagGroupTreeResponse;
import cn.hxsy.datasource.model.entity.Tag;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标签Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Mapper
public interface TagMapper extends BaseMapper<Tag> {

    /**
     * 查询所有标签组及其标签（两级结构）
     *
     * @return 标签组及其标签列表
     */
    List<TagGroupTreeResponse> getAllTagGroupsWithTags();

    /**
     * 根据客户ID获取客户标签
     *
     * @param customerId 客户ID
     * @return 标签列表
     */
    List<Tag> getTagsByCustomerId(@Param("customerId") Long customerId);

} 