package cn.hxsy.dao;

import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.datasource.model.entity.SystemUserQyRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 业务人员账号与企微信息关联 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Mapper
public interface SystemUserQyRelationMapper extends BaseMapper<SystemUserQyRelation> {

    Page<SystemUserQyRelation> queryQyUserPage(Page<SystemUserQyRelation> page, @Param("qyUserRequest") QyUserRequest qyUserRequest);
}
