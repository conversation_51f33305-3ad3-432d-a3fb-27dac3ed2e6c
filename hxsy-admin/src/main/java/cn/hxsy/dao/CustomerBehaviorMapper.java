package cn.hxsy.dao;

import cn.hxsy.api.user.model.request.CustomerBehaviorPageRequest;
import cn.hxsy.datasource.model.entity.CustomerBehavior;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户行为轨迹Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Mapper
public interface CustomerBehaviorMapper extends BaseMapper<CustomerBehavior> {

    Page<CustomerBehavior> queryByType(Page<CustomerBehavior> page, @Param("request") CustomerBehaviorPageRequest customerBehaviorPageRequest);
} 