package cn.hxsy.dao;

import cn.hxsy.api.user.model.request.QyUserRequest;
import cn.hxsy.datasource.model.entity.ExternalAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 互通账号池Mapper接口
 */
@Mapper
public interface ExternalAccountMapper extends BaseMapper<ExternalAccount> {

    Page<ExternalAccount> queryPage(Page<ExternalAccount> page, @Param("qyUserRequest") QyUserRequest qyUserRequest);
}