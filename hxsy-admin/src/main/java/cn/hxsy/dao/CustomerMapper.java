package cn.hxsy.dao;

import cn.hxsy.api.user.model.request.CustomerQueryRequest;
import cn.hxsy.api.user.model.response.CustomerQueryResponse;
import cn.hxsy.datasource.model.entity.Customer;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Mapper
public interface CustomerMapper extends BaseMapper<Customer> {

    /**
     * description : 分页查询客户列表
     * @title: queryCustomerPage
     * @param: current
     * @param: size
     * @param: request
     * <AUTHOR>
     * @date 2025/4/21 23:49
     * @return List<CustomerQueryResponse>
     */
    List<CustomerQueryResponse> queryCustomerPage(@Param("shardingNum") int shardingNum,
                                                  @Param("beginIndex") long beginIndex,
                                                  @Param("endIndex") long endIndex, @Param("request") CustomerQueryRequest request);

    /**
     * description : 分页查询客户列表
     * @title: queryCustomerPage
     * @param: current
     * @param: size
     * @param: request
     * <AUTHOR>
     * @date 2025/4/21 23:49
     * @return List<CustomerQueryResponse>
     */
    List<CustomerQueryResponse> queryCustomerRelateInfo(@Param("request") CustomerQueryRequest request);

    /**
     * description : 分页查询客户列表 数量
     * @title: queryCustomerPage
     * @param: current
     * @param: size
     * @param: request
     * <AUTHOR>
     * @date 2025/4/21 23:49
     * @return List<CustomerQueryResponse>
     */
    Integer queryCustomerCount(@Param("shardingNum") int shardingNum, @Param("request") CustomerQueryRequest request);
}