package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.CustomerCourseRelation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户课程关联Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Mapper
public interface CustomerCourseRelationMapper extends BaseMapper<CustomerCourseRelation> {

    /**
     * description : 根据客户id和栏目id查询课程
     * @title: getByCustomerIdAndColumnId
     * @param: customerId
     * @param: columnId
     * <AUTHOR>
     * @date 2025/5/29 1:07
     * @return List<Long>
     */
    List<CustomerCourseRelation> getByCustomerIdAndColumnId(@Param("customerId") Long customerId, @Param("columnId") Long columnId);

} 