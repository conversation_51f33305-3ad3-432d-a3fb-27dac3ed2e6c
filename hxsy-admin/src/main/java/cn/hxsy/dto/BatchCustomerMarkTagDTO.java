package cn.hxsy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量客户打标签请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@ApiModel(value = "BatchCustomerMarkTagDTO", description = "批量客户打标签请求参数")
public class BatchCustomerMarkTagDTO {

    @ApiModelProperty(value = "企业微信ID", required = true, example = "ww12345678")
    private String corpId;

    @ApiModelProperty(value = "客户打标签列表", required = true)
    private List<CustomerMarkTagDTO> tagList;
}
