package cn.hxsy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 客户打标签请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@ApiModel(value = "CustomerMarkTagDTO", description = "客户打标签请求参数")
public class CustomerMarkTagDTO {

    @ApiModelProperty(value = "企业微信ID", required = true, example = "ww12345678")
    private String corpId;

    @ApiModelProperty(value = "企业成员的userid", required = true, example = "zhangsan")
    private String userId;

    @ApiModelProperty(value = "外部联系人userid", required = true, example = "wmxxxxxxxx")
    private String externalUserId;

    @ApiModelProperty(value = "要添加的企业标签ID列表")
    private List<String> addTagIds;

    @ApiModelProperty(value = "要移除的企业标签ID列表")
    private List<String> removeTagIds;
}
