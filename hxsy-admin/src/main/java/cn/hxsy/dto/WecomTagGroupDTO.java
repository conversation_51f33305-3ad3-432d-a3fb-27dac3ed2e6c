package cn.hxsy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 企微标签组数据传输对象
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@ApiModel(value = "WecomTagGroupDTO", description = "企微标签组数据")
public class WecomTagGroupDTO {

    @ApiModelProperty(value = "标签组ID")
    private Long id;

    @ApiModelProperty(value = "企微标签组ID")
    private String groupId;

    @ApiModelProperty(value = "标签组名称")
    private String name;

    @ApiModelProperty(value = "所属企业微信corp_id")
    private String corpId;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedAt;

    @ApiModelProperty(value = "标签列表")
    private List<WecomTagDTO> tags;

    /**
     * 企微标签数据传输对象
     */
    @Data
    @ApiModel(value = "WecomTagDTO", description = "企微标签数据")
    public static class WecomTagDTO {

        @ApiModelProperty(value = "标签ID")
        private Long id;

        @ApiModelProperty(value = "企微标签ID")
        private String tagId;

        @ApiModelProperty(value = "标签名称")
        private String name;

        @ApiModelProperty(value = "所属标签组ID")
        private String groupId;

        @ApiModelProperty(value = "所属企业微信corp_id")
        private String corpId;

        @ApiModelProperty(value = "创建时间")
        private LocalDateTime createdAt;

        @ApiModelProperty(value = "更新时间")
        private LocalDateTime updatedAt;
    }
}
