package cn.hxsy.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 客户打标签请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@ApiModel(value = "CustomerMarkTagDTO", description = "客户打标签请求参数")
public class CustomerMarkTagBackendDTO {


    @ApiModelProperty(value = "系统内部销售ID")
    private Long salesId;

    @ApiModelProperty(value = "系统内部客户ID")
    private Long customerId;

    @ApiModelProperty(value = "标签名称")
    private String tagName;
}
