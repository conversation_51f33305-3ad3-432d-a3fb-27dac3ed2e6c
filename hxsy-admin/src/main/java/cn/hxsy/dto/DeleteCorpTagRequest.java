package cn.hxsy.dto;

import io.swagger.annotations.ApiParam;
import lombok.Data;

import java.util.List;

/**
 * 删除企业客户标签请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
public class DeleteCorpTagRequest {
    @ApiParam(value = "企业微信ID", required = true)
    private String corpId;

    @ApiParam(value = "标签的ID列表")
    private List<String> tagIds;

    @ApiParam(value = "标签组的ID列表")
    private List<String> groupIds;

    @ApiParam(value = "删除标签组时是否连同标签一起删除")
    private Boolean deleteTagWithGroup;
}
