package cn.hxsy.dto;

import cn.hxsy.datasource.model.entity.CompWxCode;
import cn.hxsy.datasource.model.entity.CompanyPO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 包含微信邀请码列表的公司信息DTO
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CompanyWithWxCodesDTO extends CompanyPO {

    /**
     * 公司对应的微信邀请码列表
     */
    private List<CompWxCode> compWxList;

    /**
     * 从CompanyPO创建CompanyWithWxCodesDTO
     *
     * @param companyPO 公司信息
     * @return 包含公司信息的DTO
     */
    public static CompanyWithWxCodesDTO fromCompanyPO(CompanyPO companyPO) {
        CompanyWithWxCodesDTO dto = new CompanyWithWxCodesDTO();
        // 复制所有CompanyPO的属性
        dto.setId(companyPO.getId());
        dto.setCompanyName(companyPO.getCompanyName());
        dto.setColumnId(companyPO.getColumnId());
        dto.setStatus(companyPO.getStatus());
        dto.setCreatedAt(companyPO.getCreatedAt());
        dto.setUpdatedAt(companyPO.getUpdatedAt());
        dto.setCreatedBy(companyPO.getCreatedBy());
        dto.setUpdatedBy(companyPO.getUpdatedBy());
        dto.setSellUrl(companyPO.getSellUrl());
        return dto;
    }
}
