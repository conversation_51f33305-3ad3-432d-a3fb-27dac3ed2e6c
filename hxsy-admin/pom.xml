<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.hxsy</groupId>
        <artifactId>hxsy-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <artifactId>hxsy-admin</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>hxsy-admin</name>
    <description>hxsy-admin</description>

    <properties>
        <application.name>hxsy-admin</application.name>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <!-- 强制指定依赖版本 -->
    <dependencyManagement>
    </dependencyManagement>

    <dependencies>
        <!--公共依赖-->
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-config</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-sa-token</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-web</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>hxsy-admin</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>cn.hxsy.HxsyAdminApplication</mainClass>
<!--                    <skip>true</skip>-->
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <debug>true</debug>
                    <debuglevel>lines,vars,source</debuglevel>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
