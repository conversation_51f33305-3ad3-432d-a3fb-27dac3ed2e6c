package cn.hxsy.auth.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.user.model.response.WeChatUserInfoResponse;
import cn.hxsy.auth.param.LoginParam;
import cn.hxsy.auth.param.PcLoginParam;
import cn.hxsy.auth.service.LoginService;
import cn.hxsy.auth.vo.LoginVO;
import cn.hxsy.base.response.Result;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 认证相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("auth")
public class AuthController {

    @Autowired
    private LoginService loginService;

    //    @PostMapping("/register")
//    public Result<Boolean> register(@Valid @RequestBody RegisterParam registerParam) {
//        //验证码校验
//        String cachedCode = redisTemplate.opsForValue().get(CAPTCHA_KEY_PREFIX + registerParam.getTelephone());
//        if (!StringUtils.equalsIgnoreCase(cachedCode, registerParam.getCaptcha())) {
//            throw new AuthException(AuthErrorCode.VERIFICATION_CODE_WRONG);
//        }
//        //注册
//        UserRegisterRequest userRegisterRequest = new UserRegisterRequest();
//        userRegisterRequest.setTelephone(registerParam.getTelephone());
//        userRegisterRequest.setInviteCode(registerParam.getInviteCode());
//        UserOperatorResponse registerResult = userFacadeService.register(userRegisterRequest);
//        if(registerResult.getok()){
//            return Result.ok(true);
//        }
//        return Result.error(registerResult.getResponseCode(), registerResult.getResponseMessage());
//    }

    /**
     * 登录方法
     *
     * @param loginParam 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public Result<LoginVO> login(@RequestBody LoginParam loginParam) {
        return loginService.login(loginParam);
    }

    @PostMapping("/logout")
    public Result<Boolean> logout() {
        // 网关未校验，只能校验了
        if (!StpUtil.isLogin()){
            return Result.ok();
        }
        String tokenValue = StpUtil.getTokenValue();
        StpUtil.logout();
        Boolean logout = loginService.logout(tokenValue);
        return Result.ok(logout);
    }



    /**
     * @description: 业务人员PC端登录
     * @author: xiaQL
     * @date: 2025/4/19 10:39
     */
    @ApiOperation("业务人员PC端登录")
    @PostMapping("/web-login")
    public Result<LoginVO> PcLogin(@RequestBody PcLoginParam loginParam)  {
        return loginService.PcLogin(loginParam);
    }

    // 公众号根据code获取用户信息
    @ApiOperation("公众号 根据code获取用户信息")
    @GetMapping("/get-wx-user-info")
    public Result<WeChatUserInfoResponse> getWxUserInfo(@RequestParam String code, @RequestParam String appid)  {
        return loginService.getWxUserInfo(code, appid);
    }
}
