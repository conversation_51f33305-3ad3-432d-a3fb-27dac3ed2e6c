package cn.hxsy.auth;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"cn.hxsy"})
@EnableDubbo
@EnableFeignClients(basePackages = {"cn.hxsy.api.user.feign"})
public class HxsyAuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(HxsyAuthApplication.class, args);
    }

}
