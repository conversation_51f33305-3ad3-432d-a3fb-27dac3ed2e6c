package cn.hxsy.auth.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class RegisterParam {
    /**
     * 微信登录code，五分钟有效期
     */
    private String registerCode;

    /**
     * 小程序来源，区分三个端侧小程序
     */
    private String appId;

    /**
     * 登录人员类型
     */
    private String userType;

    /**
     * 分享栏目id
     */
    private Long columnId;

    /**
     * 营期ID
     */
    private Long campPeriodId;

    /**
     * 训练营ID
     */
    private Long companyId;

    /**
     * 销售组ID
     */
    private Long salesGroupId;

    /**
     * 销售人员姓名
     */
    private String salesName;

    /**
     * 销售ID
     */
    private Long salesId;


}
