package cn.hxsy.auth.param;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class LoginParam extends RegisterParam {
    /**
     * 多小程序关联unionId
     */
    private String unionId;

    /**
     * 单小程序wx关联openId
     */
    private String openid;

    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 用户账号
     */
    private String userId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 手机号
     */
    private String telPhone;

    /**
     * 登录渠道，是否从链接跳转
     */
    private String isUrl;

    /**
     * 客户头像URL
     */
    private String avatarUrl;

    /**
     * 微信昵称
     */
    private String nickname;

    /**
     * 性别（0-未知 1-男 2-女）
     */
    private Integer gender;

    /** 客户注册使用字段 用于关联客户和销售信息 */

    /** 栏目ID */
    private Long columnId;

    /** 训练营ID */
    private Long companyId;

    /** 营期ID */
    private Long campPeriodId;

    /** 销售组ID */
    private Long salesGroupId;

    /** 销售人员ID */
    private Long salesId;

    /** 销售人员姓名 */
    private String salesName;

    /** 课程ID */
    private Long courseId;

    /**
     * 当前使用场景(0-企微)
     */
    private String queryScene;

    /**
     * 企微内部登录code
     */
    private String qyCode;

    /**
     * 企微id
     */
    private String corpId;
}
