package cn.hxsy.auth.service;

import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.user.model.response.AppletInfoResponse;
import cn.hxsy.api.qy.response.QyAppResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.api.user.model.response.WeChatUserInfoResponse;
import cn.hxsy.auth.param.LoginParam;
import cn.hxsy.auth.param.PcLoginParam;
import cn.hxsy.auth.vo.LoginVO;
import cn.hxsy.base.response.Result;

/**
 * <AUTHOR> XiaQL
 * @ClassName : LoginService
 * @description : LoginService
 * @date: 2025-04-04 20:47
 */
public interface LoginService {

    /**
    * @description: 小程序一键注册并登录
    * @author: Wuxiaochen
    * @date: 2025/4/4 20:48
    */
    Result<LoginVO> login(LoginParam loginParam);

    /**
     * @description: 请求wx获取小程序unionId
     * @param: appType 当前登录小程序类型
     * @param: jsCode 前端wx.login获取code
     * @author: Wuxiaochen
     * @date: 2025/4/4 20:48
     */
    AppletInfoResponse getUnionIdByWx(String appType, String jsCode);

    /**
     * @description: 企微内用户登录自建小程序流程
     * 传入登录code，rpc获取对应的access_token，再发起登录流程
     * @author: Wuxiaochen
     * @date: 2025/6/17 20:48
     */
    SystemUserResponse qyLogin(QyAppReq qyAppReq);

    /**
     * @description: 业务人员pc端密码登录
     * @param: appType 当前登录小程序类型
     * @param: jsCode 前端wx.login获取code
     * @author: Wuxiaochen
     * @date: 2025/4/4 20:48
     */
    Result<LoginVO> PcLogin(PcLoginParam pcLoginParam);

    /**
     * @description: 用户退出登录
     * 用户登录token缓存通过satoken框架自己清除，还需要清除下自定义的用户信息缓存
     * 根据传入token，清除用户信息缓存
     * @author: xiaQL
     * @date: 2025/4/19 16:11
     */
    Boolean logout(String token);

    /**
     * @description: 根据code获取用户信息
     */
    Result<WeChatUserInfoResponse> getWxUserInfo(String code, String appid);
}
