package cn.hxsy.auth.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.app.model.request.TencentWxInfoRequest;
import cn.hxsy.api.app.model.response.TencentWxInfoResponse;
import cn.hxsy.api.app.service.TencentWxInfoRpcService;
import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.api.qy.response.QyAppResponse;
import cn.hxsy.api.system.response.SysMenuResponse;
import cn.hxsy.api.user.feign.vx.AppletClient;
import cn.hxsy.api.user.model.request.*;
import cn.hxsy.api.user.model.response.*;
import cn.hxsy.api.user.service.CompanyQyRelationRpcService;
import cn.hxsy.api.user.service.SystemUserQyRelationRpcService;
import cn.hxsy.api.user.service.UserInfoRpcService;
import cn.hxsy.auth.param.LoginParam;
import cn.hxsy.auth.param.PcLoginParam;
import cn.hxsy.auth.service.LoginService;
import cn.hxsy.auth.vo.LoginVO;
import cn.hxsy.base.enums.AccountTypeEnum;
import cn.hxsy.base.enums.UserOperateTypeEnum;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.base.exception.rpc.code.WxErrorCode;
import cn.hxsy.base.request.SystemUserQyRelationRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.model.user.UserRegisterCacheModel;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.hxsy.base.constant.ResponseType.Success;
import static cn.hxsy.base.enums.UserOperateTypeEnum.*;
import static cn.hxsy.cache.constant.user.CacheConstant.*;

/**
 * <AUTHOR> XiaQL
 * @ClassName : LoginServiceImpl
 * @description : LoginServiceImpl
 * @date: 2025-04-04 20:48
 */
@Service
@Slf4j
public class LoginServiceImpl implements LoginService {

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @DubboReference(version = "1.0.0")
    UserInfoRpcService userInfoRpcService;

    @DubboReference(version = "1.0.0")
    TencentWxInfoRpcService tencentWxInfoRpcService;

    @DubboReference(version = "1.0.0")
    CompanyQyRelationRpcService companyQyRelationRpcService;

    @DubboReference(version = "1.0.0")
    SystemUserQyRelationRpcService systemUserQyRelationRpcService;



    @Resource
    private AppletClient appletClient;

    @Value("${vx.default.appid:wx6e578b58ea67e255}")
    private String defaultAppId;
    /**
     * 默认登录超时时间：7天
     */
    private static final Integer DEFAULT_LOGIN_SESSION_TIMEOUT = 60 * 60 * 24 * 7;

    @Override
    public Result<LoginVO> login(LoginParam loginParam) {
        String loginId = "";
        Object userInfoCache = null;
        // 1、查询sa-token判断是否已经登录，登录则查询已缓存对应用户信息
        if(StpUtil.isLogin()){
            loginId = (String) StpUtil.getLoginId();
            //根据登录类型，查询对应类型用户缓存，默认当做客户去查询
            if(AccountTypeEnum.SYSTEM_USER.getCodeToString().equals(loginParam.getUserType())){
                userInfoCache = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), Object.class);
            }else {
                userInfoCache = redisJsonUtils.get(CUSTOM_LOGIN_TOKEN + StpUtil.getTokenValue(), Object.class);
            }
        }
        log.info("用户登录信息缓存:{}", JSONObject.toJSONString(userInfoCache));
        // 2、用户缓存为空，判断是否需要到数据库中获取用户信息
        if (userInfoCache == null) {
            // 2.1、首先判断是否是企微侧登录，是则直接去获取对应业务人员的数据并登录，不走注册缓存的逻辑了
            if("0".equals(loginParam.getQueryScene())){
                QyAppReq qyAppReq = new QyAppReq();
                qyAppReq.setQyCode(loginParam.getQyCode());
                qyAppReq.setCorpId(loginParam.getCorpId());
                SystemUserResponse systemUserResponse = this.qyLogin(qyAppReq);
                LoginVO loginVO = this.setUserCache(LOGIN, AccountTypeEnum.SYSTEM_USER.getCodeToString(), systemUserResponse, loginId);
                return Result.ok(loginVO);
            }
            UserAuthResponse userAuthCache = null;
            String unionId = "";
            // 2.2、用户信息缓存为空，首先判断是否已经注册，看前端是否传入unionId，没有则到wx官方获取多小程序的unionId
            if(StringUtils.isNotEmpty(loginParam.getUnionId())){
                unionId = loginParam.getUnionId();
            }else {
                AppletInfoResponse appletInfoResponse = this.getUnionIdByWx(loginParam.getAppId(), loginParam.getRegisterCode());
                if(StringUtils.isNotEmpty(appletInfoResponse.getErrcode()) && !Success.getCodeToString().equals(appletInfoResponse.getErrcode())){
                    return Result.error(appletInfoResponse.getErrmsg());
                }
                unionId = appletInfoResponse.getUnionid();
                loginParam.setUnionId(unionId);
            }
            // 2.3、首先判断需要获取的redisKey
            String registerRedisKey = USER_REGISTER + unionId;
            userAuthCache = redisJsonUtils.get(registerRedisKey, UserAuthResponse.class);
            log.info("用户注册信息缓存:{}", JSONObject.toJSONString(userAuthCache));
            if(userAuthCache == null){
                // 2.3.1、认证缓存信息为空，即未注册，前端肯定没有unionId，需要设置上小程序查询到的unionId
                UserRegisterRequest userRegisterRequest = new UserRegisterRequest();
                loginParam.setUnionId(unionId);
                BeanUtils.copyProperties(loginParam, userRegisterRequest);
                //后续需通过mq解耦调用注册服务
                Result<Object> userRegisterResult = userInfoRpcService.register(userRegisterRequest);
                if (userRegisterResult.getCode() != Success.getCode() ) {
                    return Result.error(userRegisterResult.getMsg());
                }
                userInfoCache = userRegisterResult.getData();
                // 2.3.1.1、生成的认证信息，根据用户类别设置不同类别注册缓存
                this.setUserCache(REGISTER, registerRedisKey, userInfoCache, "");
            }else {
                // 2.3.2、认证缓存信息不为空，表示已注册。只是超过7天未登录导致缓存清空。查询对应用户信息
                UserInfoRequest userInfoRequest = new UserInfoRequest();
                loginParam.setUserType(userAuthCache.getUserType());
                BeanUtils.copyProperties(loginParam, userInfoRequest);
                //前端肯定不会带userId，需要从缓存中获取设置上，去数据库才能查询
                userInfoRequest.setId(userAuthCache.getId());
                userInfoRequest.setUnionId(unionId);
                userInfoRequest.setOpenid(loginParam.getOpenid());
                Result<Object> userResultDataBase = userInfoRpcService.query(userInfoRequest);
                if (userResultDataBase.getCode() != Success.getCode() ) {
                    return Result.error(userResultDataBase.getMsg());
                }
                userInfoCache = userResultDataBase.getData();
            }
            log.info("数据库获取到用户信息:{}", JSONObject.toJSONString(userInfoCache));
        }
        // 3、根据用户类别设置不同类别登录缓存
        LoginVO loginVO = this.setUserCache(LOGIN, String.valueOf(loginParam.getUserType()), userInfoCache, loginId);
        return Result.ok(loginVO);
    }

    @Override
    public AppletInfoResponse getUnionIdByWx(String appId, String jsCode) {
        String newAppId = StringUtils.isNotBlank(appId)? appId : defaultAppId;
        // 先从缓存中获取小程序配置
        String appCacheKey = APP_CONFIG_KEY_PREFIX + newAppId;
        TencentWxInfoResponse tencentWxInfoResponse = redisJsonUtils.get(appCacheKey, TencentWxInfoResponse.class);
        //此处需要判断系统获取配置与小程序分别请求是否成功
        if (tencentWxInfoResponse == null) {
            TencentWxInfoRequest tencentWxInfoRequest = new TencentWxInfoRequest();
            tencentWxInfoRequest.setAppid(newAppId);
            Result<TencentWxInfoResponse> tencentWxInfoQueryResponse = tencentWxInfoRpcService.queryWxConfig(tencentWxInfoRequest);
            if (tencentWxInfoQueryResponse.getCode() != Success.getCode()
                    || tencentWxInfoQueryResponse.getData() == null) {
                throw new BizException(WxErrorCode.request_failure);
            }
            tencentWxInfoResponse = tencentWxInfoQueryResponse.getData();
        }
        AppletInfoResponse authorizationCode = appletClient.jscode2session(tencentWxInfoResponse.getAppid(),
                tencentWxInfoResponse.getSecret(), jsCode, "authorization_code");
        log.info("小程序用户信息获取:{}", authorizationCode);
        return authorizationCode;
    }

    @Override
    public SystemUserResponse qyLogin(QyAppReq qyAppReq) {
        Result<Object> queryResult = companyQyRelationRpcService.query(qyAppReq);
        if (queryResult.getCode() != Success.getCode()) {
            throw new RuntimeException(queryResult.getMsg());
        }
        return (SystemUserResponse)queryResult.getData();
    }

    @Override
    public Result<LoginVO> PcLogin(PcLoginParam pcLoginParam) {
        PcLoginRequest pcLoginRequest = new PcLoginRequest();
        BeanUtils.copyProperties(pcLoginParam, pcLoginRequest);
        // 直接走数据库判断账号密码
        Result<SystemUserResponse> systemUserResponseResult = userInfoRpcService.PcLogin(pcLoginRequest);
        if (systemUserResponseResult.getCode() != Success.getCode() ) {
            return Result.error(systemUserResponseResult.getMsg());
        }
        SystemUserResponse systemUserResponse = systemUserResponseResult.getData();
        // 查询该用户的企微信息
        SystemUserQyRelationRequest systemUserQyRelationRequest = new SystemUserQyRelationRequest();
        systemUserQyRelationRequest.setSystemUserId(String.valueOf(systemUserResponse.getId()));
        Result<Object> objectResult = systemUserQyRelationRpcService.querySystemQyUserInner(systemUserQyRelationRequest);
        if (objectResult.getCode() != Success.getCode()) {
            return Result.error(objectResult.getMsg());
        }
        if (ObjectUtils.isNotEmpty(objectResult.getData())) {
            @SuppressWarnings("unchecked")
            List<SystemUserQyRelationResponse> systemUserQyRelationResponses = (List<SystemUserQyRelationResponse>) objectResult.getData();
            systemUserResponse.setSystemUserQyRelationResponses(systemUserQyRelationResponses);
        }
        // 设置用户缓存，响应后续请求token
        LoginVO loginVO = this.setUserCache(LOGIN, AccountTypeEnum.SYSTEM_USER.getCodeToString(), systemUserResponse, String.valueOf(systemUserResponse.getId()));
        return Result.ok(loginVO);
    }

    @Override
    public Boolean logout(String token) {
        // 清除用户信息缓存
        return redisJsonUtils.delete(SYS_USER_LOGIN_TOKEN + token);
    }

    /**
     * 根据不同场景，设置不同用户相关缓存
     * 需要注意:
     * 当没指定用户类型时，全部都以客户类型缓存，防止后续权限问题
     * @param typeEnum 当前用户操作场景
     * @param redisKeyOrLoginType 用户注册缓存对应key；登录信息过期或注册时拿不到token，所以给类型自己设置登录缓存key
     * @param userInfo 用户信息，不论注册与响应，通过场景判断具体转什么类型
     */
    private LoginVO setUserCache(UserOperateTypeEnum typeEnum, String redisKeyOrLoginType,
                                 Object userInfo, String loginId){
        //注册场景，缓存用户注册信息
        if (typeEnum == REGISTER) {
            //此处需要做json格式转对象，bean拷贝U与强转可能有问题。直接转成注册所需字段，别的直接忽略
            UserRegisterCacheModel userInfoResponse = JSONObject.parseObject(JSONObject.toJSONString(userInfo), UserRegisterCacheModel.class);
            //union匹配用户id、用户类型，并且此key时间永不过期
            redisJsonUtils.set(redisKeyOrLoginType, userInfoResponse);
            return null;
        }else {
            //登录场景，获取到登录后的用户信息，重新设置7天的登录缓存，并响应前端;
            if(AccountTypeEnum.SYSTEM_USER.getCodeToString().equals(redisKeyOrLoginType)){
                SystemUserResponse userInfoResponse = JSONObject.parseObject(JSONObject.toJSONString(userInfo), SystemUserResponse.class);
                userInfoResponse.setUserType(AccountTypeEnum.SYSTEM_USER.getCodeToString());
                if(StringUtils.isEmpty(loginId)){
                    loginId = String.valueOf(userInfoResponse.getId());
                }
                StpUtil.login(loginId, DEFAULT_LOGIN_SESSION_TIMEOUT);
//                StpUtil.getSession().set(loginId, userInfoResponse);
                // 业务人员对应的菜单缓存在角色管理处统一处理就行，不需要给用户单独设置，但是需要响应给前端，因为是同一个对象引用，所以只能先置空再重新设置
                List<SysMenuResponse> sysMenuResponses = userInfoResponse.getSysMenuResponses();
                List<SystemUserQyRelationResponse> systemUserQyRelationResponses = userInfoResponse.getSystemUserQyRelationResponses();
                userInfoResponse.setSysMenuResponses(null);
                userInfoResponse.setSystemUserQyRelationResponses(null);
                redisJsonUtils.set(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), userInfoResponse, DEFAULT_LOGIN_SESSION_TIMEOUT, TimeUnit.SECONDS);
                userInfoResponse.setSysMenuResponses(sysMenuResponses);
                userInfoResponse.setSystemUserQyRelationResponses(systemUserQyRelationResponses);
                return new LoginVO(userInfoResponse);
            }else {
                CustomerResponse userInfoResponse = JSONObject.parseObject(JSONObject.toJSONString(userInfo), CustomerResponse.class);
                userInfoResponse.setUserType(AccountTypeEnum.CUSTER.getCodeToString());
                //loginId不为空时，表明在前面的登录校验就已根据token获取拿到
                if(StringUtils.isEmpty(loginId)){
                    loginId = String.valueOf(userInfoResponse.getId());
                }
                StpUtil.login(loginId, DEFAULT_LOGIN_SESSION_TIMEOUT);
//                StpUtil.getSession().set(loginId, userInfoResponse);
                redisJsonUtils.set(CUSTOM_LOGIN_TOKEN + StpUtil.getTokenValue(), userInfoResponse, DEFAULT_LOGIN_SESSION_TIMEOUT, TimeUnit.SECONDS);
                return new LoginVO(userInfoResponse);
            }
        }
    }

    /**
     * description : 获取微信用户信息
     */
    public Result<WeChatUserInfoResponse> getWxUserInfo(String code, String appid) {
        TencentWxInfoResponse appConf = getAppConf(appid);
        // 先根据code 获取access_token
        WeChatAuthResponse authorizationCode = appletClient.snsOauth2AccessToken(appid, appConf.getSecret(), code, "authorization_code");
        // 根据access_token获取用户信息
        WeChatUserInfoResponse weChatUserInfoResponse = appletClient.snsUserinfo(authorizationCode.getAccess_token(), authorizationCode.getOpenid(), "zh_CN");
        return Result.ok(weChatUserInfoResponse);
    }

    private TencentWxInfoResponse getAppConf(String appId) {
        // 先从缓存中获取小程序配置
        String appCacheKey = APP_CONFIG_KEY_PREFIX + appId;
        TencentWxInfoResponse tencentWxInfoResponse = redisJsonUtils.get(appCacheKey, TencentWxInfoResponse.class);
        //此处需要判断系统获取配置与小程序分别请求是否成功
        if (tencentWxInfoResponse == null) {
            TencentWxInfoRequest tencentWxInfoRequest = new TencentWxInfoRequest();
            tencentWxInfoRequest.setAppid(appId);
            Result<TencentWxInfoResponse> tencentWxInfoQueryResponse = tencentWxInfoRpcService.queryWxConfig(tencentWxInfoRequest);
            if (tencentWxInfoQueryResponse.getCode() != Success.getCode()
                    || tencentWxInfoQueryResponse.getData() == null) {
                throw new BizException(WxErrorCode.request_failure);
            }
            tencentWxInfoResponse = tencentWxInfoQueryResponse.getData();
        }
        return tencentWxInfoResponse;
    }
}
