spring:
  application:
    name: @application.name@
#  config:
#    import: classpath:base.yml,classpath:cache.yml #这里爆红可以直接忽略，没有任何影响
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  # 是否读取cookie
  is-read-cookie: false
  # 是否向响应写入cookie
  is-write-header: false
  # 是否读取header
  is-read-header: true
server:
  port: 13301
