SELECT 
    c.id AS customer_id,
    wu.nickname AS wechat_nickname,
    wu.avatar_url,
    wuo.mobile,
    wuo.mobile_status,
    c.wework_status,
    c.wechat_remark,
    c.source,
    c.sales_id,
    c.sales_name,
    c.created_at AS customer_created_at,
    GROUP_CONCAT(DISTINCT t.id) AS tag_ids,
    GROUP_CONCAT(DISTINCT t.tag_name) AS tag_names,
    GROUP_CONCAT(DISTINCT ccr.camp_period_id) AS camp_period_ids,
    GROUP_CONCAT(DISTINCT cb.behavior_type) AS active_types,
    GROUP_CONCAT(DISTINCT cb.created_at) AS active_times,
    GROUP_CONCAT(DISTINCT comp.id) AS company_ids,
    GROUP_CONCAT(DISTINCT comp.name) AS company_names
FROM 
    customer c
    LEFT JOIN wechat_user wu ON c.wechat_user_id = wu.id
    LEFT JOIN wechat_user_openid wuo ON c.wechat_user_id = wuo.wechat_user_id
    LEFT JOIN customer_tag_relation ctr ON c.id = ctr.customer_id
    LEFT JOIN tag t ON ctr.tag_id = t.id
    LEFT JOIN customer_course_relation ccr ON c.id = ccr.customer_id
    LEFT JOIN customer_behavior cb ON c.id = cb.customer_id
    LEFT JOIN company comp ON ccr.column_id = comp.section_id
WHERE 
    1=1
    -- 企微添加状态
    AND (c.wework_status = ? OR ? IS NULL)
    -- 手机号状态
    AND (wuo.mobile_status = ? OR ? IS NULL)
    -- 活跃行为类型
    AND (cb.behavior_type = ? OR ? IS NULL)
    -- 活跃行为时间范围
    AND (cb.created_at BETWEEN ? AND ? OR ? IS NULL OR ? IS NULL)
    -- 微信备注
    AND (c.wechat_remark LIKE ? OR ? IS NULL)
    -- 微信昵称（支持模糊或精确）
    AND (wu.nickname LIKE ? OR wu.nickname = ? OR ? IS NULL)
    -- 客户创建时间范围
    AND (c.created_at BETWEEN ? AND ? OR ? IS NULL OR ? IS NULL)
    -- 手机号
    AND (wuo.mobile = ? OR ? IS NULL)
    -- 销售人员ID
    AND (c.sales_id = ? OR ? IS NULL)
    -- 标签ID
    AND (t.id = ? OR ? IS NULL)
    -- 营期ID
    AND (ccr.camp_period_id = ? OR ? IS NULL)
    -- 部门ID
    AND (comp.id = ? OR ? IS NULL)
GROUP BY 
    c.id,
    wu.nickname,
    wu.avatar_url,
    wuo.mobile,
    wuo.mobile_status,
    c.wework_status,
    c.wechat_remark,
    c.source,
    c.sales_id,
    c.sales_name,
    c.created_at
ORDER BY 
    c.created_at DESC;

-- wechat_user
-- wechat_user_openid
-- customer 客户表
-- section 栏目表
-- company 公司表
-- sales_group 销售组
-- system_user 系统用户表

select * from section left join company on section.id = company.section_id 
left join sales_group on company.sales_group_id = sales_group.id
left join system_user on sales_group.id = system_user.sales_group_id
left join customer on system_user.account_id = customer.sales_id
left join wechat_user on customer.wechat_user_id = wechat_user.id
left join wechat_user_openid on wechat_user.id = wechat_user_openid.wechat_user_id


-- 参数 客户unionid、链接拼接的参数：栏目id、训练营id、销售id
-- 1. 根据 客户unionid 查询客户信息（可能有多条：一个栏目一条客户信息 对应一个销售<跟进人>）
-- 2. 栏目id、训练营id、销售id 用于判断 客户信息是否新增 
-- 3. 客户信息新增 则将客户信息新增到客户表 （客户unionid、栏目id、训练营id、销售id）
-- 4. 返回视频链接、客户信息




  
  

