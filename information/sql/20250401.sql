-- 微信用户openid关联表
CREATE TABLE wechat_user_openid (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '关联客户ID',
    app_id VARCHAR(50) NOT NULL COMMENT '小程序appid',
    openid VARCHAR(128) NOT NULL COMMENT '微信openid',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by <PERSON><PERSON><PERSON><PERSON>(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VA<PERSON><PERSON><PERSON>(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIG<PERSON> KEY (customer_id) REFERENCES customer(id),
    UNIQUE KEY uk_app_openid (app_id, openid),
    INDEX idx_customer_id (customer_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户openid关联表';

-- 客户表
CREATE TABLE customer (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',

    avatar_url VARCHAR(255) DEFAULT '' COMMENT '客户头像URL',
    nickname VARCHAR(64) DEFAULT '' COMMENT '微信昵称',
    last_active_time DATETIME DEFAULT NULL COMMENT '最近活跃时间',
    mobile VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    mobile_status TINYINT DEFAULT 0 COMMENT '手机号状态（0无手机号/1有手机号）',
    gender TINYINT DEFAULT NULL COMMENT '性别（0-未知 1-男 2-女）',
    wework_status TINYINT DEFAULT 0 COMMENT '企微添加状态（0未添加/1已添加/9已删除）',

    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户表';

-- 标签组表
CREATE TABLE tag_group (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    parent_id BIGINT UNSIGNED DEFAULT NULL COMMENT '父级标签组ID',
    group_name VARCHAR(100) NOT NULL COMMENT '标签组名称',
    level TINYINT NOT NULL COMMENT '层级（1-一级标签组，2-二级标签组）',
    sort_order INT DEFAULT 0 COMMENT '排序号',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (parent_id) REFERENCES tag_group(id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签组表';

-- 标签表
CREATE TABLE tag (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    group_id BIGINT UNSIGNED NOT NULL COMMENT '标签组ID',
    tag_name VARCHAR(100) NOT NULL COMMENT '标签名称',
    sort_order INT DEFAULT 0 COMMENT '排序号',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (group_id) REFERENCES tag_group(id),
    INDEX idx_group_id (group_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 客户标签关联表
CREATE TABLE customer_tag_relation (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    tag_id BIGINT UNSIGNED NOT NULL COMMENT '标签ID',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (customer_id) REFERENCES customer(id),
    FOREIGN KEY (tag_id) REFERENCES tag(id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_tag_id (tag_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户标签关联表';

-- 客户标签表
CREATE TABLE customer_tags (
                               id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                               customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    -- 营期id
                               camp_period_id BIGINT NOT NULL COMMENT '营期ID',
    -- 营期名称
                               camp_period_name VARCHAR(100) NOT NULL COMMENT '营期名称',
    -- 标签名称
                               tags_name VARCHAR(500) NOT NULL COMMENT '标签名称',

                               remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
                               status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
                               created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
                               created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
                               updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               FOREIGN KEY (customer_id) REFERENCES customer(id),
                               FOREIGN KEY (camp_period_id) REFERENCES camp_period(id),
                               INDEX idx_customer_id (customer_id),
                               INDEX idx_camp_period_id (camp_period_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户标签表';

-- 客户课程关联表
CREATE TABLE customer_course_relation (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    column_id BIGINT UNSIGNED DEFAULT NULL COMMENT '栏目ID',
    company_id BIGINT UNSIGNED DEFAULT NULL COMMENT '训练营ID',
    camp_period_id BIGINT UNSIGNED DEFAULT NULL COMMENT '营期ID',
    course_id BIGINT UNSIGNED DEFAULT NULL COMMENT '课程ID',
    course_status TINYINT DEFAULT NULL COMMENT '课程状态（0未到课,1已到课,2已完课）',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (customer_id) REFERENCES customer(id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_column_id (column_id),
    INDEX idx_company_id (company_id),
    INDEX idx_camp_period_id (camp_period_id),
    INDEX idx_course_id (course_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户课程关联表';

-- 客户行为轨迹表
CREATE TABLE customer_behavior (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    behavior_type TINYINT NOT NULL COMMENT '行为类型（1-客户注册 2-训练营营期报名 3-训练营视频课学习 4-课后答题 5-领取红包 6-添加企微 7-删除企微 8-加入群聊）',
    course_id BIGINT UNSIGNED DEFAULT NULL COMMENT '课程ID',
    course_name VARCHAR(255) DEFAULT NULL COMMENT '课程名称',
    company_id BIGINT UNSIGNED DEFAULT NULL COMMENT '训练营ID',
    company_name VARCHAR(255) DEFAULT NULL COMMENT '训练营名称',
    camp_period_id BIGINT UNSIGNED DEFAULT NULL COMMENT '营期ID',
    camp_period_name VARCHAR(255) DEFAULT NULL COMMENT '营期名称',
    reward_amount DECIMAL(10,2) DEFAULT NULL COMMENT '奖励金额',
    reward_type VARCHAR(50) DEFAULT NULL COMMENT '红包类型',
    reward_rule VARCHAR(50) DEFAULT NULL COMMENT '奖励规则',
    employee_name VARCHAR(64) DEFAULT NULL COMMENT '员工姓名',
    employee_wework_name VARCHAR(64) DEFAULT NULL COMMENT '员工企微昵称',
    company_name VARCHAR(100) DEFAULT NULL COMMENT '企业微信名称',
    access_url VARCHAR(500) DEFAULT NULL COMMENT '访问地址',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间（行为时间）',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (customer_id) REFERENCES customer(id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_behavior_type (behavior_type),
    INDEX idx_course_id (course_id),
    INDEX idx_company_id (company_id),
    INDEX idx_camp_period_id (camp_period_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户行为轨迹表';

-- -- 活跃行为关联表(暂时不使用)
-- CREATE TABLE customer_active_relation (
--     id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
--     customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
--     active_type VARCHAR(50) NOT NULL COMMENT '活跃行为类型',
--     active_time DATETIME NOT NULL COMMENT '活跃行为时间',
--     active_content JSON DEFAULT NULL COMMENT '活跃行为内容',
--     active_source VARCHAR(50) DEFAULT NULL COMMENT '活跃来源',
--     active_ip VARCHAR(50) DEFAULT NULL COMMENT '活跃IP',
--     active_device VARCHAR(255) DEFAULT NULL COMMENT '活跃设备信息',
--     status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
--     created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
--     created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--     updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
--     updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--     FOREIGN KEY (customer_id) REFERENCES customer(id),
--     INDEX idx_customer_id (customer_id),
--     INDEX idx_active_type (active_type),
--     INDEX idx_active_time (active_time)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活跃行为关联表';

-- 客户销售关联表
CREATE TABLE customer_sales_relation (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    company_id BIGINT UNSIGNED NOT NULL COMMENT '公司ID',
    column_id BIGINT UNSIGNED NOT NULL COMMENT '栏目ID',
    sales_group_id BIGINT UNSIGNED NOT NULL COMMENT '销售组ID',
    sales_id BIGINT UNSIGNED NOT NULL COMMENT '销售人员ID',
    sales_name VARCHAR(64) NOT NULL COMMENT '销售人员姓名',

    wechat_remark VARCHAR(255) DEFAULT NULL COMMENT '客户微信备注',
    source VARCHAR(50) DEFAULT NULL COMMENT '来源渠道',

    assign_time DATETIME NOT NULL COMMENT '分配时间',
    assign_type TINYINT NOT NULL COMMENT '分配类型（1-手动分配 2-自动分配）',
    assign_by VARCHAR(64) DEFAULT NULL COMMENT '分配人',
    remark VARCHAR(255) DEFAULT NULL COMMENT '备注',
    status TINYINT DEFAULT 1 COMMENT '使用状态（1-有效，0-无效）',
    created_by VARCHAR(64) DEFAULT NULL COMMENT '创建人',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(64) DEFAULT NULL COMMENT '更新人',
    updated_at DATETIME DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_customer_company_column (customer_id, company_id, column_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_company_id (company_id),
    INDEX idx_column_id (column_id),
    INDEX idx_sales_group_id (sales_group_id),
    INDEX idx_sales_id (sales_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户销售关联表';

