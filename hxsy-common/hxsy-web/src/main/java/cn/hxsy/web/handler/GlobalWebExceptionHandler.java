package cn.hxsy.web.handler;

import cn.hxsy.base.constant.ResponseType;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.base.exception.system.SystemException;
import cn.hxsy.base.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.HashMap;
import java.util.Map;
/**
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
public class GlobalWebExceptionHandler {

    /**
    * @description: 自定义方法参数校验异常处理器
    * @author: xiaQL
    * @date: 2025/4/23 21:59
    */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ResponseBody
    public Map<String, String> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        return errors;
    }

    /**
    * @description: 自定义系统异常处理器
    * @author: xiaQL
    * @date: 2025/4/23 21:59
    */
    @ExceptionHandler(SystemException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Result<String> systemExceptionHandler(SystemException systemException) {
        Result<String> result = new Result<>();
        result.setCode(ResponseType.Failure.getCode());
        if (systemException.getMessage() == null) {
            result.setMsg(systemException.getErrorCode().getMessage());
        } else {
            result.setMsg(systemException.getMessage());
        }
        return result;
    }

    /**
    * @description: 自定义系统异常处理器
    * @author: xiaQL
    * @date: 2025/4/23 22:00
    */
    @ExceptionHandler(Throwable.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Result<String> throwableHandler(Throwable throwable) {
        // 重新打印异常信息，否则会因为被全局异常捕获导致原本异常日志丢失
        log.error("系统异常:", throwable);
        Result<String> result = new Result<>();
        result.setCode(ResponseType.Failure.getCode());
        result.setMsg("哎呀，当前网络比较拥挤，请您稍后再试~");
        return result;
    }

    /**
    * @description: 全局运行异常处理器
     * 通常来说是捕获自定义异常，为了防止有时候忘记了自定义异常，直接throw的runtime
    * @author: xiaQL
    * @date: 2025/4/23 21:58
    */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Result<String> runtimeExceptionHandler(RuntimeException runtimeException) {
        // 重新打印异常信息，否则会因为被全局异常捕获导致原本异常日志丢失
        log.error("系统运行时异常:", runtimeException);
        Result<String> result = new Result<>();
        result.setCode(ResponseType.Failure.getCode());
        // 判断是否是自定义的异常
        if (runtimeException instanceof BizException) {
            BizException bizException = (BizException) runtimeException;
            if (bizException.getMessage() == null) {
                result.setMsg(bizException.getErrorCode().getMessage());
            } else {
                result.setMsg(bizException.getMessage());
            }
        }else {
            result.setMsg(runtimeException.getMessage());
        }
        return result;
    }
}
