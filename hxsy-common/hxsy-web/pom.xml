<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.hxsy</groupId>
        <artifactId>hxsy-common</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>hxsy-web</artifactId>
    <description>Web组件</description>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-base</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-sa-token</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-cache</artifactId>
        </dependency>

        <!--    Spring Boot Web   -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

    </dependencies>

</project>