package cn.hxsy.cache.config.snowId;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Enumeration;

/**
 * @ClassName : IdWorkerConfig
 * @Description : id生成器
 * <AUTHOR> ChenRui
 * @Date: 2020-05-14 10:40
 */
@Configuration
@Slf4j
public class IdWorkerConfig {

    @Value("${spring.application.name}") 
    private String applicationName;
    
    @Value("${server.port}")
    private String port;
    
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    
    /**
    * @description: 雪花对象
    * @author: <PERSON><PERSON><PERSON><PERSON>
    * @date: 2025/4/14 20:17
    */
    @Bean
    public SnowflakeIdWorker idWorker() {
//        log.info("开始生成雪花对象");
        Long dataCenterId = this.genDataCenterId();
        Long workerId = this.genWorkerId(dataCenterId);
        SnowflakeIdWorker snowflakeIdWorker = new SnowflakeIdWorker(workerId,dataCenterId);
        SnowflakeIdWorker.setInstance(snowflakeIdWorker);
        return snowflakeIdWorker;
    }
    
    /**
     * @Title
     * @description：根据服务器ip，当前服务名与端口，生成唯一工作区ID
     * @author: Wuxiaochen
     * @date: 2025/4/14 20:17
     */
    private Long genDataCenterId() {
       Long dataCenterId;
       String curentServiceCenterKey = "{system}:snow:" + applicationName + ": " + getUrl().replace("http://","") + "-" + port;
       String dataCenterIdStr = stringRedisTemplate.opsForValue().get(curentServiceCenterKey);
       // key：雪花算法:数据中心:XX:应用服务名:服务示例ip-port，使用的workID value：YY
        if (dataCenterIdStr == null) {
            dataCenterId = RandomUtils.nextLong(0, 31);
            String snowCenterKey = "{system}:snow:dataCentId:"+dataCenterId+":workerIncrement";
            String workerIdStr = stringRedisTemplate.opsForValue().get(snowCenterKey);
            if (workerIdStr != null) {
                int num = Integer.parseInt(workerIdStr);
                if (num>31) {
                    return genDataCenterId();
                }
            }
            stringRedisTemplate.opsForValue().getAndSet(curentServiceCenterKey, dataCenterId.toString());
        }else {
            dataCenterId = Long.parseLong(dataCenterIdStr);
        }
        log.info("\r\n当前服务实例工作区数据中心CentID：{}", dataCenterId);   
        return dataCenterId;
    }
    
    /**
     * @Title
     * @description：生成工作区ID
     * @author: Wuxiaochen
     * @date: 2025/4/14 20:17
     */
    private Long genWorkerId(Long dataCenterId) {
       Integer workerId;
       Long workIdLong = 0L;
       // 根据当前数据中心已经分配的workID；按照自增长进行处理，若增长超过31则报错
       // 雪花算法:数据中心:XX 目前分配的workID自增长到对应的value
       String snowCenterKey = "{system}:snow:dataCentId:"+dataCenterId+":workerIncrement";
       // key：雪花算法:数据中心:XX:应用服务名:服务示例ip-port，使用的workID value：YY
       String curentServiceInstanceKey = "{system}:snow:" + applicationName + ":work:" + getUrl().replace("http://","") + "-" + port;
       String workerIdStr = stringRedisTemplate.opsForValue().get(curentServiceInstanceKey);
        if (workerIdStr == null) {
            workerId = stringRedisTemplate.opsForValue().increment(snowCenterKey).intValue();
            stringRedisTemplate.opsForValue().getAndSet(curentServiceInstanceKey, workerId.toString());
        }else {
            workerId = Integer.parseInt(workerIdStr);
        }
        workIdLong = workerId.longValue();
        if(workIdLong  > SnowflakeIdWorker.MAX_WORKER_ID) {
           throw new RuntimeException("当前数据中心已经分配的workID已达上限");
        } 
        log.info("\r\n当前服务实例工作区ID：{}", workerId);   
        return workIdLong;
    }

    /**
     * 根据网卡获得IP地址
     * @return
     * @throws SocketException
     * @throws UnknownHostException
     */
    public  static String getUrl() {
        String ip = "";
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements();) {
                NetworkInterface intf = en.nextElement();
                String name = intf.getName();
                if (!name.contains("docker") && !name.contains("lo")) {
                    for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements();) {
                        //获得IP
                        InetAddress inetAddress = enumIpAddr.nextElement();
                        if (!inetAddress.isLoopbackAddress()) {
                            String ipaddress = inetAddress.getHostAddress().toString();
                            if (!ipaddress.contains("::") && !ipaddress.contains("0:0:") && !ipaddress.contains("fe80")) {
                                System.out.println(ipaddress);
                                if(!"127.0.0.1".equals(ip)){
                                    ip = ipaddress;
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取IP信息失败");
        }
        return ip;
    }
    
}
