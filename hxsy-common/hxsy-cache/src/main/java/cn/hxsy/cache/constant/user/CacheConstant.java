package cn.hxsy.cache.constant.user;

/**
* @description: 登录缓存常量
* @author: qingluan
* @date: 2025/3/31 1:12
*/
public class CacheConstant {

    /**
     * 缓存key分隔符
     */
    public static final String CACHE_KEY_SEPARATOR = ":";

    /**
     * 小程序配置缓存前缀
     */
    public static final String APP_CONFIG_KEY_PREFIX = "app:config:type:";

    /**
     * 业务人员注册前缀
     */
    public static final String SYS_USER_REGISTER = "sys:user:register:";

    /**
     * 客户注册前缀
     */
    public static final String CUSTOM_REGISTER = "custom:register:";

    /**
     * 用户统一注册前缀
     */
    public static final String USER_REGISTER = "user:register:";

    /**
     * 业务人员登录token与业务人员信息缓存前缀
     */
    public static final String SYS_USER_LOGIN_TOKEN = "sys:user:login:token:";

    /**
     * 客户登录token与客户信息缓存前缀
     */
    public static final String CUSTOM_LOGIN_TOKEN = "custom:login:token:";

    /**
     * 客户登录token与客户信息缓存前缀
     */
    public static final String SYSTEM_ROLE_TYPE = "sys:role:type";

    /**
     * 企微对应access_token缓存前缀
     */
    public static final String QY_CORP_TOKEN = "qy:corp:token:";

    /**
     * 企微对应suite_ticket缓存前缀
     */
    public final static String SUITE_TICKET = "qy:suite_ticket:";

}
