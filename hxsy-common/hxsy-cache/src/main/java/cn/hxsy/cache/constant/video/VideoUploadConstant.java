package cn.hxsy.cache.constant.video;

/**
* @description: 文件上传常量
* @author: qing<PERSON>an
* @date: 2025/3/31 1:12
*/
public class VideoUploadConstant {

    /**
     * 缓存key分隔符
     */
    public static final String CACHE_KEY_SEPARATOR = ":";

    /**
     * 文件上传前缀key
     */
    public static final String VIDEO_UPLOAD_PREFIX = "video:upload:";

    /**
     * 文件上传完成后合并前缀key
     */
    public static final String VIDEO_UPLOAD_MERGE_PREFIX = "video:upload:merge:";


    /**
     * 文件分片默认保存时间，超过则自动删除
     */
    public static final int VIDEO_UPLOAD_SAVE_TIME = 1;
}
