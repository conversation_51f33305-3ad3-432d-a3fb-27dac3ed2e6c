package cn.hxsy.cache.config;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static cn.hxsy.cache.constant.user.CacheConstant.SYS_USER_LOGIN_TOKEN;

/**
 * <AUTHOR>
 * @ClassName : RedisJsonUtils
 * @description : RedisJsonUtils
 * @date: 2025-04-04 16:47
 */
@Component
@Slf4j
public class RedisJsonUtils {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // 存储对象（转为 JSON）
    public <T> void set(String key, T value) {
        try {
            String json = JSONObject.toJSONString(value);
            stringRedisTemplate.opsForValue().set(key, json);
        } catch (Exception e) {
            throw new RuntimeException("Redis 存储失败", e);
        }
    }

    /**
     * @description: 获取对象过期时间
     * @author: xiaQL
     * @date: 2025/6/18 00:19
     */
    public Long getExpire(String key) {
        return stringRedisTemplate.getExpire(key);
    }

    /**
    * @description: 分布式锁获取
     * 时间单位：秒级
     * @param lockKey 锁对应key
     * @return 返回true即可setKey成功，表示当前操作未进行过
    * @author: xiaQL
    * @date: 2025/6/7 14:19
    */
    public Boolean getLock(String lockKey, long timeout) {
        return stringRedisTemplate.opsForValue()
                .setIfAbsent(lockKey, "1", timeout, TimeUnit.SECONDS);
    }

    // 读取对象（JSON 转对象）
    public <T> T get(String key, Class<T> clazz) {
        String json = stringRedisTemplate.opsForValue().get(key);
        if (json == null) {
            return null;
        }
        try {
            return JSONObject.parseObject(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Redis 读取失败", e);
        }
    }

    /**
    * @description: 如果确定存入为list类型对象，获取时就用词方法转换
    * @author: xiaQL
    * @date: 2025/5/9 2:11
    */
    public <T> List<T> getList(String key, Class<T> clazz) {
        String json = stringRedisTemplate.opsForValue().get(key);
        if (json == null) {
            return null;
        }
        try {
            return JSONObject.parseArray(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Redis 读取失败", e);
        }
    }

    // 根据用户请求头标识获取业务人员信息json数据
    public Object getSystemUserInfo(String key) {
        try {
            String systemUserCacheJson = stringRedisTemplate.opsForValue().get(SYS_USER_LOGIN_TOKEN + key);
            if (StringUtils.isEmpty(systemUserCacheJson)){
                // 为空直接抛出异常，让统一异常处理
                throw new RuntimeException("未获取到对应信息，请重新登录尝试");
            }
            return JSON.parse(systemUserCacheJson);
        } catch (Exception e) {
            throw new RuntimeException("业务人员信息获取异常：" +e.getMessage());
        }
    }

    // 存储对象（转为 JSON） 并设置过期时间
    public <T> void set(String key, T value, long timeout, TimeUnit unit) {
        try {
            String json = JSONObject.toJSONString(value);
//            log.info("工具类重置业务缓存:{}", json);
            stringRedisTemplate.opsForValue().set(key, json, timeout, unit);
        } catch (Exception e) {
            throw new RuntimeException("Redis 存储失败", e);
        }
    }

    // 更新对象 获取并使用旧的过期时间
    public <T> void update(String key, T value) {
        try {
            String json = JSONObject.toJSONString(value);
            Long expire = stringRedisTemplate.getExpire(key);
            stringRedisTemplate.opsForValue().set(key, json);
            if (expire > 0) {
                stringRedisTemplate.expire(key, expire, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            throw new RuntimeException("Redis 存储失败", e);
        }
    }

    // 读取对象（JSON 转对象）并续期
    public <T> T get(String key, Class<T> clazz, long timeout, TimeUnit unit) {
        String json = stringRedisTemplate.opsForValue().get(key);
        if (json == null) {
            return null;
        }
        try {
            // 如果设置了过期时间，则使用过期时间
            if (timeout > 0) {
                stringRedisTemplate.opsForValue().set(key, json, timeout, unit);
            }
            return JSONObject.parseObject(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Redis 读取失败", e);
        }
    }

    // 单key删除
    public Boolean delete(String key) {
        Boolean delete = true;
        try {
            delete = stringRedisTemplate.delete(key);
        } catch (Exception e) {
            throw new RuntimeException("Redis 存储失败", e);
        }
        return delete;
    }

    // 批量删除
    public Long deleteBatch(List<String> key) {
        Long delete = 0L;
        try {
            delete = stringRedisTemplate.delete(key);
        } catch (Exception e) {
            throw new RuntimeException("Redis 存储失败", e);
        }
        return delete;
    }
}

