package cn.hxsy.datasource.model.entity;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "wechat_account_information", autoResultMap = true)
@ApiModel(value = "WechatAccountPO", description = "企业微信账号信息")
public class WechatAccountPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业名称")
    @TableField("corp_name")
    private String corpName;

    @ApiModelProperty(value = "企业ID")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty(value = "应用Secret")
    @TableField("secret")
    private String secret;

    @ApiModelProperty(value = "应用AgentId")
    @TableField("agentId")
    private String agentId;

    @ApiModelProperty(value = "小程序配置")
    @TableField(value = "mini_config", typeHandler = JacksonTypeHandler.class)
    private JSONObject miniConfig;

    @ApiModelProperty(value = "事件配置")
    @TableField(value = "event_config", typeHandler = JacksonTypeHandler.class)
    private JSONObject eventConfig;

    @ApiModelProperty(value = "侧边栏配置")
    @TableField(value = "sidebar_config", typeHandler = JacksonTypeHandler.class)
    private JSONObject sidebarConfig;

    @ApiModelProperty(value = "扩展字段1")
    @TableField("field1")
    private String field1;

    @ApiModelProperty(value = "扩展字段2")
    @TableField("field2")
    private String field2;

    @ApiModelProperty(value = "扩展字段3")
    @TableField("field3")
    private String field3;

    @ApiModelProperty(value = "扩展字段4")
    @TableField("field4")
    private String field4;

    @ApiModelProperty(value = "扩展字段5")
    @TableField("field5")
    private String field5;
}
