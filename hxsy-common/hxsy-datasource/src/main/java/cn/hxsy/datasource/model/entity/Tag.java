package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Tag", description = "标签信息")
@TableName("tag")
public class Tag extends BaseEntity {
    
    @ApiModelProperty(value = "标签组ID")
    @TableField("group_id")
    private Long groupId;
    
    @ApiModelProperty(value = "标签名称")
    @TableField("tag_name")
    private String tagName;
    
    @ApiModelProperty(value = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;
} 