package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CompWxCode", description = "公司邀请码信息")
@TableName("comp_wx_code")
public class CompWxCode extends BaseEntity {

    @ApiModelProperty("公司ID")
    @TableField("company_id")
    private Integer companyId;

    @ApiModelProperty("小程序APPID")
    @TableField("appid")
    private String appid;

    @ApiModelProperty("小程序名称")
    @TableField("mini_program_name")
    private String miniProgramName;

    @ApiModelProperty("公司邀请码链接")
    @TableField("sell_url")
    private String sellUrl;

}
