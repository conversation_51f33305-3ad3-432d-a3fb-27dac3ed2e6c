package cn.hxsy.datasource.model.entity;

import lombok.Data;
import org.joda.time.LocalDate;

import java.time.LocalDateTime;

@Data
public class CustomerInfoReq {

    private Long columnId;
    private Long companyId;
    private Long campPeriodId;
    private Long courseId;
    private Long salesGroupId;
    private Long salesId;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private Integer pageNum;
    private Integer pageSize;
    private Integer isFlag;

}
