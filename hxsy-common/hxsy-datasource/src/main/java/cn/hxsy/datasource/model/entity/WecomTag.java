package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企微标签实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wecom_tag")
@ApiModel(value = "WecomTag", description = "企微标签表")
public class WecomTag extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企微标签ID")
    @TableField("tag_id")
    private String tagId;

    @ApiModelProperty(value = "标签名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "所属标签组ID")
    @TableField("group_id")
    private String groupId;

    @ApiModelProperty(value = "所属企业微信corp_id")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty(value = "排序号")
    @TableField("order_num")
    private Integer orderNum;

    @ApiModelProperty(value = "是否删除：0-未删除，1-已删除")
    @TableField("deleted")
    private Boolean deleted;

}
