package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@Data
@TableName("activity_record")
@ApiModel(value = "ActivityRecordPO", description = "营销活动记录表")
@Builder
public class ActivityRecordPO extends BaseEntity {

    @ApiModelProperty("用户ID")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty("活动ID")
    @TableField("activity_id")
    private Long activityId;

    @ApiModelProperty("活动类型（1-红包/2-优惠券/3-礼品/4-积分/5-答题）")
    @TableField("activity_type")
    private String activityType;

    @ApiModelProperty("操作类型（1-领取/2-使用/3-过期）")
    @TableField("action_type")
    private String actionType;

    @ApiModelProperty("操作详情（JSON格式）")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private String detail;

}
