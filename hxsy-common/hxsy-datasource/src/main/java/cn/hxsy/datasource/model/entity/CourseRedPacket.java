
package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 课程红包记录实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseRedPacket", description = "课程红包记录信息")
@TableName("course_red_packet")
public class CourseRedPacket extends BaseEntity {
    
    @ApiModelProperty(value = "栏目ID")
    @TableField("column_id")
    private Long columnId;
    
    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty(value = "营期ID")
    @TableField("camp_period_id")
    private Long campPeriodId;

    @ApiModelProperty(value = "课程ID")
    @TableField("course_id")
    private Long courseId;
    
    @ApiModelProperty(value = "销售组ID")
    @TableField("sales_group_id")
    private Long salesGroupId;

    @ApiModelProperty(value = "销售ID")
    @TableField("sales_id")
    private Long salesId;

    @ApiModelProperty(value = "用户ID")
    @TableField("customer_id")
    private Long customerId;
    
    @ApiModelProperty(value = "用户名称")
    @TableField("customer_name")
    private String customerName;

    @ApiModelProperty(value = "课程名称")
    @TableField("course_name")
    private String courseName;
    
    @ApiModelProperty(value = "转账金额")
    @TableField("transfer_amount")
    private String transferAmount;
    
    @ApiModelProperty(value = "商户号")
    @TableField("merchant_id")
    private String merchantId;
    
    @ApiModelProperty(value = "商户单号")
    @TableField("out_bill_no")
    private String outBillNo;

    @ApiModelProperty(value = "微信转账单号")
    @TableField("transfer_bill_no")
    private String transferBillNo;

    @ApiModelProperty(value = "转账状态（1:已受理, 2:锁定资金中, 3:待用户确认, 4:转账中, 5:转账成功, 6:转账失败, 7:撤销中, 8:已撤销）")
    @TableField("state")
    private Integer state;

    @ApiModelProperty(value = "跳转领取页面的package信息")
    @TableField("package_info")
    private String packageInfo;
}