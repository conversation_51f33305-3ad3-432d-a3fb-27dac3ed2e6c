package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("video_upload")
@ApiModel(value = "VideoUploadPO对象", description = "视频上传表")
public class VideoUploadPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "视频分组")
    @TableField("video_groupid")
    private Long videoGroupid;

    @ApiModelProperty(value = "视频名称")
    @TableField("video_name")
    private String videoName;

    @ApiModelProperty(value = "视频URL")
    @TableField("video_url")
    private String videoUrl;

    @ApiModelProperty(value = "转码状态")
    @TableField("trans_status")
    private String transStatus;

    @ApiModelProperty(value = "推流专用视频")
    @TableField("stream_status")
    private String streamStatus;

    @ApiModelProperty(value = "视频来源")
    @TableField("video_source")
    private String videoSource;

    @ApiModelProperty(value = "视频时长")
    @TableField("video_duration")
    private LocalTime videoDuration;

    @ApiModelProperty(value = "视频大小")
    @TableField("video_size")
    private Double videoSize;

}
