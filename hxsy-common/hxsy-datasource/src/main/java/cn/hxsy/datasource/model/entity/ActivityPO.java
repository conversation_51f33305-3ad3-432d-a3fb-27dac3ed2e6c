package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("activity")
@ApiModel(value = "ActivityPO对象", description = "营销活动信息表")
public class ActivityPO extends BaseEntity {

    @ApiModelProperty(value = "分组ID")
    @TableField("group_id")
    private Long groupId;

    @ApiModelProperty(value = "活动类型（1-红包，2-优惠券，3-礼品，4-积分，5-答题）")
    @TableField("activity_type")
    private String activityType;

    @ApiModelProperty(value = "活动名称")
    @TableField("title")
    private String title;

    @ApiModelProperty(value = "生效时间")
    @TableField("start_time")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "失效时间")
    @TableField("end_time")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "总发放数量")
    @TableField("total_quantity")
    private Integer totalQuantity;

    @ApiModelProperty(value = "剩余数量")
    @TableField("remaining_quantity")
    private Integer remainingQuantity;

    @ApiModelProperty(value = "问题内容（答题活动专用）")
    @TableField("question_text")
    private String questionText;

    @ApiModelProperty(value = "题型（1-单选，2-多选）")
    @TableField("question_type")
    private String questionType;

    @ApiModelProperty(value = "答案选项（JSON格式）")
    @TableField("answer_options")
    private String answerOptions;

    @ApiModelProperty(value = "正确答案ID集合（JSON格式）")
    @TableField("correct_answer")
    private String correctAnswer;

    @ApiModelProperty(value = "通过分数")
    @TableField("pass_score")
    private Integer passScore;

    @ApiModelProperty(value = "最大尝试次数")
    @TableField("max_attempts")
    private Integer maxAttempts;

    @ApiModelProperty(value = "活动配置（JSON格式）")
    @TableField("config")
    private String config;
}
