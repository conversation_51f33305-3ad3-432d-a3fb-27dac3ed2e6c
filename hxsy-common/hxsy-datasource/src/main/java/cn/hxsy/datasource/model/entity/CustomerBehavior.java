package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 客户行为轨迹实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerBehavior", description = "客户行为轨迹信息")
@TableName("customer_behavior")
public class CustomerBehavior extends BaseEntity {
    
    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;
    
    @ApiModelProperty(value = "行为类型（1-客户注册 2-训练营营期报名 3-训练营视频课学习 4-课后答题 5-领取红包 6-添加企微 7-删除企微 8-加入群聊）")
    @TableField("behavior_type")
    private Integer behaviorType;
    
    @ApiModelProperty(value = "课程ID")
    @TableField("course_id")
    private Long courseId;
    
    @ApiModelProperty(value = "课程名称")
    @TableField("course_name")
    private String courseName;
    
    @ApiModelProperty(value = "训练营ID")
    @TableField("company_id")
    private Long companyId;
    
    @ApiModelProperty(value = "训练营名称")
    @TableField("company_name")
    private String companyName;
    
    @ApiModelProperty(value = "营期ID")
    @TableField("camp_period_id")
    private Long campPeriodId;
    
    @ApiModelProperty(value = "营期名称")
    @TableField("camp_period_name")
    private String campPeriodName;
    
    @ApiModelProperty(value = "奖励金额")
    @TableField("reward_amount")
    private BigDecimal rewardAmount;
    
    @ApiModelProperty(value = "红包类型")
    @TableField("reward_type")
    private String rewardType;
    
    @ApiModelProperty(value = "奖励规则")
    @TableField("reward_rule")
    private String rewardRule;
    
    @ApiModelProperty(value = "员工姓名")
    @TableField("employee_name")
    private String employeeName;
    
    @ApiModelProperty(value = "员工企微昵称")
    @TableField("employee_wework_name")
    private String employeeWeworkName;
    
    @ApiModelProperty(value = "企业名称")
    @TableField("corp_name")
    private String corpName;

    @ApiModelProperty(value = "群组名称")
    @TableField("group_name")
    private String groupName;

    @ApiModelProperty(value = "群组ID")
    @TableField("group_id")
    private String groupId;

    @ApiModelProperty(value = "企微企业ID")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty(value = "入群方式")
    @TableField("join_scene")
    private Integer joinScene;

    @ApiModelProperty(value = "退群方式")
    @TableField("quit_scene")
    private Integer quitScene;

    @ApiModelProperty(value = "访问地址")
    @TableField("access_url")
    private String accessUrl;
} 