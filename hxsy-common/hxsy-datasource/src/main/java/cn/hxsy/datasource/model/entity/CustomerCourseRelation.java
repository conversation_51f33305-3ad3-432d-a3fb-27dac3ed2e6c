package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 客户课程关联实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerCourseRelation", description = "客户课程关联信息")
@TableName("customer_course_relation")
public class CustomerCourseRelation extends BaseEntity {
    
    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;
    
    @ApiModelProperty(value = "营期ID")
    @TableField("camp_period_id")
    private Long campPeriodId;
    
    @ApiModelProperty(value = "课程ID")
    @TableField("course_id")
    private Long courseId;
    
    @ApiModelProperty(value = "到课状态（0未到课,1已到课,2已完课）")
    @TableField("arrival_status")
    private Integer arrivalStatus;

    @ApiModelProperty(value = "到课时间")
    @TableField("arrival_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime arrivalTime;

    @ApiModelProperty(value = "完播时间")
    @TableField("complete_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "播放时长(秒)")
    @TableField("play_progress")
    private Integer playProgress;

    @TableField(exist = false)
    private Long salesId;

    @TableField(exist = false)
    private Long columnId;

    @TableField(exist = false)
    private Long companyId;

    @TableField(exist = false)
    private String campPeriodName;

} 