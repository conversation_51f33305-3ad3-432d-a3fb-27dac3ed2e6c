package cn.hxsy.datasource.config;

import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;

import java.util.*;

/**
 * @ProjectName: mybatis-plus
 * @Package: com.jiangfeixiang.mybatisplus.mpconfig
 * @ClassName: CodeGenerator
 * @Author: jiangfeixiang
 * @email: <EMAIL>
 * @Description: 代码生成器
 * @Date: 2019/5/10/0010 21:41
 */
public class MyBatisPlusTool {
    // 数据库连接配置
    private static final String URL = "*****************************************************************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "323698";

    // 是否生成增删改查接口样例
    private static final Boolean GEN_EXAMPLE = true;
    // 代码作者
    private static final String AUTHOR = "XiaQL";
    // 数据库表名
    private static final String TABLE_NAME = "user_info";
    // 模块路径
    private static final String MODULE_NAME = "/salary-common/salary-datasource";

    public static void main(String[] args) {
        // 生成代码
        execute();
    }

    // 搜索条件字段，大写驼峰命名
    private static List<String> searchableAttribute = new ArrayList<String>() {
        {
            add("TenantName");
            add("UserName");
            add("UserCode");
        }
    };

    public static void execute() {
        List<String> tables = new ArrayList<>();
        tables.add(TABLE_NAME);

        Map<String, Object> injectionMap = new HashMap<String, Object>();
        injectionMap.put("genExample", GEN_EXAMPLE);
        injectionMap.put("tableNameMiddleLine", TABLE_NAME.replace("_","-"));
        injectionMap.put("activeRecord", false);
        injectionMap.put("searchableAttribute", searchableAttribute);

        FastAutoGenerator.create(URL, USERNAME, PASSWORD)
                .globalConfig(builder -> {
                    // 获取当前项目所在根目录，如果有父工程需要配置上面的module，在当前目录下生成
                    builder.outputDir(System.getProperty("user.dir") + MODULE_NAME + "/src/main/java")
                            .author(AUTHOR)
                            .fileOverride()
                            .enableSwagger()
                            .dateType(DateType.TIME_PACK)
                            .commentDate("yyyy-MM-dd HH:mm:ss")
                            .disableOpenDir()
                            .build();

                })
                .packageConfig(builder -> {
                    builder.parent("cn.qingluan.datasource")
                            .entity("model.entity")
                            .service("service")
                            .serviceImpl("service.impl")
                            .mapper("dao")
                            .xml("mapper.xml")
                            .controller("controller")
                            .pathInfo(Collections.singletonMap(OutputFile.xml, System.getProperty("user.dir") + MODULE_NAME + "/src/main/resources/mapper"))
                            .build();
                })
                .templateConfig(builder -> {
                    builder.entity("templates/entity.java")
                            .controller("templates/controller.java")
                            .service("templates/service.java")
                            .serviceImpl("templates/serviceImpl.java")
                            .mapper("templates/mapper.java")
                            .xml("templates/mapper.xml").entityKt("")
                            .build();

                })
                .injectionConfig(builder -> {
                    builder.beforeOutputFile((tableInfo, objectMap) -> {
                                System.out.println("tableInfo========>: " + tableInfo.getEntityName() + " objectMap: " + objectMap.size());
                                System.out.println(tableInfo.toString());
                                System.out.println(tableInfo.getFields());
                                System.out.println(tableInfo.getFieldNames());
                            })
                            .fileOverride()
                            .customMap(injectionMap)
                            .build();
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tables)
                            // 1、Entity 策略配置
                            .entityBuilder()
                            .fileOverride()
//                            .enableChainModel()
                            .enableLombok()
                            .enableRemoveIsPrefix()
                            .enableTableFieldAnnotation()
                            .enableActiveRecord()
                            .naming(NamingStrategy.underline_to_camel)
                            .columnNaming(NamingStrategy.underline_to_camel)
                            .formatFileName("%s")

                            // 2、Controller 策略配置
                            .controllerBuilder()
                            .fileOverride()
                            .formatFileName("%sController")

                            ///3、Service 策略配置
                            .serviceBuilder()
                            .fileOverride()
                            .superServiceClass(IService.class)
                            .superServiceImplClass(ServiceImpl.class)
                            .formatServiceFileName("%sService")
                            .formatServiceImplFileName("%sServiceImpl")

                            // 4、Mapper 策略配置
                            .mapperBuilder()
                            .fileOverride()
                            .superClass(BaseMapper.class)
                            .enableMapperAnnotation()
                            .enableBaseResultMap()
                            .enableBaseColumnList()
                            .formatMapperFileName("%sMapper")
                            .enableMapperAnnotation()
                            .formatXmlFileName("%sMapper")
                            .build();
                })
                .execute();
    }
}