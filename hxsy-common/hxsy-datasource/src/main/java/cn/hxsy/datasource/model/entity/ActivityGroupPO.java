package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("activity_group")
@ApiModel(value = "ActivityGroupPO", description = "营销分组")
public class ActivityGroupPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "分组名称")
    @TableField("group_name")
    private String groupName;

//    @ApiModelProperty(value = "可见范围")
//    @TableField(value = "visual_range", typeHandler = JacksonTypeHandler.class)
//    private List<String> visualRange;

    @ApiModelProperty(value = "扩展字段1")
    @TableField("field1")
    private String field1;

    @ApiModelProperty(value = "扩展字段2")
    @TableField("field2")
    private String field2;

    @ApiModelProperty(value = "扩展字段3")
    @TableField("field3")
    private String field3;

    @ApiModelProperty(value = "扩展字段4")
    @TableField("field4")
    private String field4;

    @ApiModelProperty(value = "扩展字段5")
    @TableField("field5")
    private String field5;
}