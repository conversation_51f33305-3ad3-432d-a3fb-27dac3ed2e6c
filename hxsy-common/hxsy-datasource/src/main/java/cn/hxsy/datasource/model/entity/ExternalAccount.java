package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 互通账号池实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ExternalAccount", description = "互通账号池信息")
@TableName("external_account")
public class ExternalAccount extends BaseEntity {
    
    @ApiModelProperty(value = "互通账号订单表主键")
    @TableField("account_order_id")
    private Long accountOrderId;
    
    @ApiModelProperty(value = "订单ID")
    @TableField("order_id")
    private String orderId;
    
    @ApiModelProperty(value = "激活码所属企业")
    @TableField("corp_id")
    private String corpId;
    
    @ApiModelProperty(value = "账号激活码(用于激活账号)")
    @TableField("active_code")
    private String activeCode;
    
    @ApiModelProperty(value = "互通账号ID(续期时企业微信返回的userid)")
    @TableField("userid")
    private String userid;
    
    @ApiModelProperty(value = "账号类型 1-基础账号，2-互通账号")
    @TableField("account_type")
    private Integer accountType;
    
    @ApiModelProperty(value = "账号状态：0-未分配 1-已分配")
    @TableField("account_status")
    private Integer accountStatus;
    
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty("业务人员在企业的名称")
    @TableField(exist = false)
    private String qyName;

    @ApiModelProperty("业务人员在企微的UserID")
    @TableField(exist = false)
    private String qyUserId;

    @ApiModelProperty("账号激活时间")
    @TableField(exist = false)
    private LocalDateTime activeTime;

    @ApiModelProperty(value = "服务开始时间")
    @TableField(exist = false)
    private LocalDateTime serviceStartTime;

    @ApiModelProperty(value = "服务到期时间")
    @TableField(exist = false)
    private LocalDateTime serviceExpireTime;

}