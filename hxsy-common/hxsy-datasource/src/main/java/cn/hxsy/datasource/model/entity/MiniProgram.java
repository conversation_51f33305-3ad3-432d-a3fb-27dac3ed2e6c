package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 小程序实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MiniProgram", description = "小程序信息")
@TableName("mini_program")
public class MiniProgram extends BaseEntity {
    
    @ApiModelProperty(value = "小程序名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "类型：1：微信小程序，2：微信公众号")
    @TableField("type")
    private String type;

    @ApiModelProperty(value = "小程序AppID")
    @TableField("appid")
    private String appid;
    
    @ApiModelProperty(value = "小程序AppSecret")
    @TableField("secret")
    private String secret;
    
    @ApiModelProperty(value = "小程序状态（0:禁用, 1:启用）")
    @TableField("status")
    private Integer status;

} 