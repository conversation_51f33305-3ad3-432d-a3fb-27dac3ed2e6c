package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 客户标签实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerTags", description = "客户标签信息")
@TableName("customer_tags")
public class CustomerTags extends BaseEntity {

    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;

    @ApiModelProperty(value = "营期ID")
    @TableField("camp_period_id")
    private Long campPeriodId;

    @ApiModelProperty(value = "营期名称")
    @TableField("camp_period_name")
    private String campPeriodName;

    @ApiModelProperty(value = "自动标签名称")
    @TableField("tags_name")
    private String tagsName;

    @ApiModelProperty(value = "手动标签名称")
    @TableField("manual_tags_name")
    private String manualTagsName;

    // 客户id集合（用于批量打手动标签）
    @TableField(exist = false)
    List<Long> customerIds;
}