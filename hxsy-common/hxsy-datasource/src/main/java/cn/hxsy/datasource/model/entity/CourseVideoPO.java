package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("course_video")
@ApiModel(value = "CourseVideoPO对象", description = "课程视频信息表")
public class CourseVideoPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程名称")
    @TableField("course_name")
    private String courseName;

    @ApiModelProperty(value = "课程分组")
    @TableField("group_id")
    private Long groupId;

    @ApiModelProperty(value = "课程封面")
    @TableField("cover_path")
    private String coverPath;

    @ApiModelProperty(value = "课程封面id")
    @TableField("cover_id")
    private String coverId;

    @ApiModelProperty(value = "课程简介")
    @TableField("course_introduction")
    private String courseIntroduction;

    @ApiModelProperty(value = "课程介绍")
    @TableField("course_description")
    private String courseDescription;

    @ApiModelProperty(value = "课程内容")
    @TableField("course_content")
    private String courseContent;

    @ApiModelProperty(value = "课程开始时间")
    @TableField("start_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startTime;

    @ApiModelProperty(value = "视频前贴片")
    @TableField("patch_path")
    private String patchPath;

    @ApiModelProperty(value = "片头设置")
    @TableField("opening_setting")
    private String openingSetting;

    @ApiModelProperty(value = "全屏观看设置")
    @TableField("fullscreen_setting")
    private String fullscreenSetting;

    @ApiModelProperty(value = "课程视频")
    @TableField("video_path")
    private String videoPath;

    @ApiModelProperty(value = "课程视频")
    @TableField("video_id")
    private Long videoId;

    @ApiModelProperty(value = "所属流量池")
    @TableField(value = "course_pool", typeHandler = JacksonTypeHandler.class)
    private List<String> coursePool;

    @ApiModelProperty(value = "项目")
    @TableField("course_project")
    private String courseProject;

    @ApiModelProperty(value = "来源")
    @TableField("course_source")
    private String courseSource;

    @ApiModelProperty(value = "标签")
    @TableField(value = "course_tag", typeHandler = JacksonTypeHandler.class)
    private List<String> courseTag;

    @ApiModelProperty(value = "售卖方式")
    @TableField("course_salesmethod")
    private String courseSalesmethod;

    @ApiModelProperty(value = "课程价格")
    @TableField("course_price")
    private BigDecimal coursePrice;

    @ApiModelProperty(value = "划线价格")
    @TableField("underlined_price")
    private BigDecimal underlinedPrice;

    @ApiModelProperty(value = "上架设置")
    @TableField("course_status")
    private String courseStatus;

    @ApiModelProperty(value = "扩展字段1（排序序号）")
    @TableField("field1")
    private String field1;

    @ApiModelProperty(value = "扩展字段2")
    @TableField("field2")
    private String field2;

    @ApiModelProperty(value = "扩展字段3")
    @TableField("field3")
    private String field3;

    @ApiModelProperty(value = "扩展字段4")
    @TableField("field4")
    private String field4;

    @ApiModelProperty(value = "扩展字段5")
    @TableField("field5")
    private String field5;

    @ApiModelProperty(value = "课程考题")
    @TableField("course_exam")
    private String courseExam;

    @ApiModelProperty(value = "奖励形式")
    @TableField("reward_form")
    private String rewardForm;

    @ApiModelProperty(value = "现金形式")
    @TableField("cash_form")
    private String cashForm;

    @ApiModelProperty(value = "奖励金额")
    @TableField("reward_amount")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "奖励积分")
    @TableField("rewards_points")
    private Integer rewardsPoints;

    @ApiModelProperty(value = "错误次数限制")
    @TableField("error_limit")
    private Integer errorLimit;

    @ApiModelProperty(value = "现金形式")
    @TableField("activity_info")
    private String activityInfo;


}
