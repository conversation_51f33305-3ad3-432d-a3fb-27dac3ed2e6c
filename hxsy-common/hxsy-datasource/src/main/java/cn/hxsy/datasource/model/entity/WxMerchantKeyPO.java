package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 微信商户密钥
 */
@Data
@TableName("wx_merchant_key")
public class WxMerchantKeyPO extends BaseEntity {

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty(value = "销售组ID")
    @TableField("sales_group_id")
    private Long salesGroupId;

    @ApiModelProperty(value = "商户ID")
    @TableField("merchant_id")
    private String merchantId;

    @ApiModelProperty(value = "商户密钥apiV3")
    @TableField("private_key")
    private String privateKey;

    @ApiModelProperty(value = "商户证书公钥ID(公钥模式使用)")
    @TableField("public_key_id")
    private String publicKeyId;

    @ApiModelProperty(value = "商户证书序列号")
    @TableField("merchant_serial_number")
    private String merchantSerialNumber;

    @ApiModelProperty(value = "平台证书序列号")
    @TableField("platform_serial_number")
    private String platformSerialNumber;

    @ApiModelProperty(value = "商户API公钥路径(公钥模式使用)")
    @TableField("public_key_path")
    private String publicKeyPath;

    @ApiModelProperty(value = "商户API私钥路径(公钥模式使用)")
    @TableField("private_key_path")
    private String privateKeyPath;

    @ApiModelProperty(value = "密钥版本（0：旧版，1：新版）")
    @TableField("key_version")
    private Integer keyVersion;

    @ApiModelProperty(value = "模式（0：证书，1：公钥）")
    @TableField("mode")
    private Integer mode;

}
