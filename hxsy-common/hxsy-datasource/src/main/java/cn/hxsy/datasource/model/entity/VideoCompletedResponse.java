package cn.hxsy.datasource.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("视频完播响应结果")
@AllArgsConstructor
public class VideoCompletedResponse {
    @ApiModelProperty("成功处理的活动ID列表")
    private List<Long> successIds;

    @ApiModelProperty("失败的活动列表")
    private List<FailedActivity> failedList;
}
