package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserWechatBindingPO", description = "用户企微绑定信息")
@TableName("user_wechat_binding")
public class UserWechatBindingPO extends BaseEntity {

    @ApiModelProperty(value = "用户ID")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty(value = "企业名称")
    @TableField("enterprise_name")
    private String enterpriseName;

    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty(value = "企微ID")
    @TableField("wechat_id")
    private String wechatId;
} 