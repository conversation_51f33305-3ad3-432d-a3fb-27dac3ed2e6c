package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企业微信标签同步记录实体类
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wecom_tag_sync_record")
@ApiModel(value = "WecomTagSyncRecord", description = "企业微信标签同步记录表")
public class WecomTagSyncRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业微信ID")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty(value = "同步类型：1-全量同步，2-指定标签组同步")
    @TableField("sync_type")
    private Integer syncType;

    @ApiModelProperty(value = "同步状态：0-进行中，1-成功，2-失败")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "同步开始时间")
    @TableField("start_time")
    private java.time.LocalDateTime startTime;

    @ApiModelProperty(value = "同步结束时间")
    @TableField("end_time")
    private java.time.LocalDateTime endTime;

    @ApiModelProperty(value = "同步的标签组数量")
    @TableField("group_count")
    private Integer groupCount;

    @ApiModelProperty(value = "同步的标签数量")
    @TableField("tag_count")
    private Integer tagCount;

    @ApiModelProperty(value = "同步的标签组ID列表，JSON格式")
    @TableField("group_ids")
    private String groupIds;

    @ApiModelProperty(value = "错误信息")
    @TableField("error_msg")
    private String errorMsg;
}
