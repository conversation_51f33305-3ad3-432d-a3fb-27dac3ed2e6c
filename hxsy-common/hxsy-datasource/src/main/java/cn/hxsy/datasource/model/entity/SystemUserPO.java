package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SystemUserPO", description = "系统用户")
@TableName("system_user")
public class SystemUserPO extends BaseEntity {

    @ApiModelProperty(value = "账号ID")
    @TableField("account_id")
    private String accountId;

    @ApiModelProperty(value = "unionid")
    @TableField("union_id")
    private String unionId;

    @ApiModelProperty(value = "账号类型")
    @TableField("account_type")
    private Integer accountType;

    @ApiModelProperty(value = "用户名")
    @TableField("user_name")
    private String username;

    @ApiModelProperty(value = "用户Id")
    @TableField("user_id")
    private String userId;

    @ApiModelProperty(value = "所属总部")
    @TableField("headquarters_id")
    private Long headquartersId;

    @ApiModelProperty(value = "栏目")
    @TableField("column_id")
    private Long columnId;

    @ApiModelProperty(value = "公司")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty(value = "销售组")
    @TableField("sales_group_id")
    private Long salesGroupId;

    @ApiModelProperty(value = "角色名称")
    @TableField("role_name")
    private String roleName;

    @ApiModelProperty(value = "角色ID")
    @TableField("role_id")
    private Integer roleId;

    @ApiModelProperty(value = "手机号")
    @TableField("phone")
    private String phone;

    @ApiModelProperty(value = "出生日期")
    @TableField("birth_date")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate birthDate;

    @ApiModelProperty(value = "绑定企微ID")
    @TableField("wechat_bind_id")
    private Integer wechatBindId;

    @ApiModelProperty(value = "审核状态")
    @TableField("audit_status")
    private Integer auditStatus;

    @ApiModelProperty(value = "账号密码")
    @TableField("password")
    private String password;
}
