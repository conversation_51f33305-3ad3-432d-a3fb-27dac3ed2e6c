package cn.hxsy.datasource.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class FileUploadRequest {

    @ApiModelProperty(value = "文件名数组", required = true)
    @NotNull(message = "文件名数组不能为空")
    private List<String> fileNamesArray; // 文件名数组

    @ApiModelProperty(value = "分片文件名", required = true)
    @NotNull(message = "分片文件名不能为空")
    private String fileNames; // 文件名

    @ApiModelProperty(value = "分片编号", required = true)
    @NotNull(message = "分片编号不能为空")
    private String partNumbers; // 分片编号

    @ApiModelProperty(value = "视频分组ID", required = true)
    @NotNull(message = "视频分组ID不能为空")
    private String groupId; // 视频分组ID

    @ApiModelProperty(value = "视频分组总数", required = true)
    @NotNull(message = "视频分组总数不能为空")
    private String totalParts; // 视频分组总数

}
