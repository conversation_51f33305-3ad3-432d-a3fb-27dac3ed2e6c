package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("headquarters")
@ApiModel("总部信息")
public class HeadquartersPO extends BaseEntity {

    @ApiModelProperty("总部名称")
    @TableField("headquarters_name")
    private String headquartersName;
}