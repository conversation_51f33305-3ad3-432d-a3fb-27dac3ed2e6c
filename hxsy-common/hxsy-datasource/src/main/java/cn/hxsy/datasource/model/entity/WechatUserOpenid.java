package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信用户openid关联实体类
 *
 * <AUTHOR>
 * @date  2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "WechatUserOpenid", description = "微信用户openid关联")
@TableName("wechat_user_openid")
public class WechatUserOpenid extends BaseEntity {

    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;
    
    @ApiModelProperty(value = "小程序appid")
    @TableField("app_id")
    private String appId;
    
    @ApiModelProperty(value = "微信openid")
    @TableField("openid")
    private String openid;
} 