package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 客户销售关联实体
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerSalesRelation", description = "客户销售关联信息")
@TableName("customer_sales_relation")
public class CustomerSalesRelation extends BaseEntity {
    
    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;
    
    @ApiModelProperty(value = "公司ID")
    @TableField("company_id")
    private Long companyId;
    
    @ApiModelProperty(value = "栏目ID")
    @TableField("column_id")
    private Long columnId;
    
    @ApiModelProperty(value = "销售组ID")
    @TableField("sales_group_id")
    private Long salesGroupId;

    @ApiModelProperty("营期ID")
    @TableField("camp_period_id")
    private Long campPeriodId;
    
    @ApiModelProperty(value = "销售人员ID")
    @TableField("sales_id")
    private Long salesId;
    
    @ApiModelProperty(value = "销售人员姓名")
    @TableField("sales_name")
    private String salesName;
    
    @ApiModelProperty(value = "客户微信备注")
    @TableField("wechat_remark")
    private String wechatRemark;
    
    @ApiModelProperty(value = "来源渠道")
    @TableField("source")
    private String source;
    
    @ApiModelProperty(value = "分配时间")
    @TableField("assign_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime assignTime;
    
    @ApiModelProperty(value = "分配类型（1-手动分配 2-自动分配）")
    @TableField("assign_type")
    private Integer assignType;
    
    @ApiModelProperty(value = "分配人")
    @TableField("assign_by")
    private String assignBy;

    @ApiModelProperty(value = "栏目名称")
    @TableField(exist = false)
    private String columnName;

    @ApiModelProperty(value = "公司名称")
    @TableField(exist = false)
    private String companyName;

} 