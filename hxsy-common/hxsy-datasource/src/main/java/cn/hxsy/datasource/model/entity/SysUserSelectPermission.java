package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 系统用户可见查询权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-24 12:04:54
 */
@TableName(value = "sys_user_select_permission", autoResultMap = true)
@ApiModel(value = "SysUserSelectPermission对象", description = "系统用户可见查询权限表")
@Data
public class SysUserSelectPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    @TableId("id")
    private Long id;

    @ApiModelProperty("用户ID")
    @TableField("user_id")
    private Long userId;

    @ApiModelProperty("可见栏目范围")
    @TableField(value = "per_column_id", typeHandler = JacksonTypeHandler.class)
    private List<String> perColumnId;

    @ApiModelProperty("可见公司范围")
    @TableField(value = "per_company_id", typeHandler = JacksonTypeHandler.class)
    private List<String> perCompanyId;

    @ApiModelProperty("可见销售组范围")
    @TableField(value = "per_sales_group_id", typeHandler = JacksonTypeHandler.class)
    private List<String> perSalesGroupId;

    @ApiModelProperty("创建人")
    @TableField("created_by")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @ApiModelProperty("更新人")
    @TableField("updated_by")
    private String updatedBy;

    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;


}
