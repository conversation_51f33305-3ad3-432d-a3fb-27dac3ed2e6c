package cn.hxsy.datasource.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CampPeriodResponseDTO {
    private Long id;
    private String name;
    private String startingFlag;
    private Boolean campperiodRedPack;
    private List<String> redPackRange;
    private String campperiodRedPackAmount;
    private String explicitName;//营期外显名称

    private List<CourseDTO> courses; // 改为复数形式
}

