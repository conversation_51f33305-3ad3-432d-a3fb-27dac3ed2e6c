package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-03 23:55:47
 */
@Getter
@Setter
@TableName("user_auth")
@ApiModel(value = "UserAuth对象", description = "")
public class UserAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("多小程序一键登录id")
    @TableId("union_id")
    private String unionId;

    @ApiModelProperty("业务人员、客户id")
    @TableField("id")
    private Long id;

    @ApiModelProperty("角色类型 0-业务人员 1-客户")
    @TableField("user_type")
    private Integer userType;


}
