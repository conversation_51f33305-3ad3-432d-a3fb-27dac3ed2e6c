package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户标签关联实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerTagRelation", description = "客户标签关联信息")
@TableName("customer_tag_relation")
public class CustomerTagRelation extends BaseEntity {
    
    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;
    
    @ApiModelProperty(value = "标签ID")
    @TableField("tag_id")
    private Long tagId;
} 