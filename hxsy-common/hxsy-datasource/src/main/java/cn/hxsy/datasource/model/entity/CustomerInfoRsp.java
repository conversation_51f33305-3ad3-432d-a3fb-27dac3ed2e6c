package cn.hxsy.datasource.model.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
public class CustomerInfoRsp {
    private Long memberId;      // 会员ID
    private String avatarUrl;   // 头像URL
    private String nickname;    // 昵称
    private String realName;    // 姓名（使用remark字段）
    private String mobile;      // 手机号
    private Integer statusDesc;  // 状态描述
    private Integer videoViewCount;
    private LocalDateTime createdAt;
    private LocalDateTime lastActiveTime;
    private String salesMan;
    private Integer redPacketCount;
    private Integer viewCourseCount;
    private Integer completeCourseCount;

    private String viewCourseTime;
    private String completeCourseTime;
}