package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "camp_period", autoResultMap = true)
@ApiModel(value = "CampPeriodPO对象", description = "营期信息表")
public class CampPeriodPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "训练营分组")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty(value = "营期名称")
    @TableField("campperiod_name")
    private String campperiodName;

    @ApiModelProperty(value = "营期简介")
    @TableField("campperiod_introduction")
    private String campperiodIntroduction;

    @ApiModelProperty(value = "营期封面")
    @TableField("campperiod_coverpath")
    private String campperiodCoverpath;

    @ApiModelProperty(value = "营期内容")
    @TableField("campperiod_content")
    private String campperiodContent;

    @ApiModelProperty(value = "所属流量池")
    @TableField(value = "campperiod_pool", typeHandler = JacksonTypeHandler.class)
    private List<String> campperiodPool;

    @ApiModelProperty(value = "项目")
    @TableField("campperiod_project")
    private String campperiodProject;

    @ApiModelProperty(value = "来源")
    @TableField("campperiod_source")
    private String campperiodSource;

    @ApiModelProperty(value = "标签")
    @TableField(value = "campperiod_tag", typeHandler = JacksonTypeHandler.class)
    private List<String> campperiodTag;

    @ApiModelProperty(value = "可见范围")
    @TableField(value = "sales_group_id", typeHandler = JacksonTypeHandler.class)
    private List<String> salesGroupId;

    @ApiModelProperty(value = "营期可见设置")
    @TableField(value = "visual_list", typeHandler = JacksonTypeHandler.class)
    private List<String> visualList;

    @ApiModelProperty(value = "售卖方式")
    @TableField("campperiod_salesmethod")
    private String campperiodSalesmethod;

    @ApiModelProperty(value = "课程价格")
    @TableField("campperiod_price")
    private BigDecimal campperiodPrice;

    @ApiModelProperty(value = "划线价格")
    @TableField("underlined_price")
    private BigDecimal underlinedPrice;

    @ApiModelProperty(value = "上架设置")
    @TableField("campperiod_status")
    private String campperiodStatus;

    @ApiModelProperty(value = "招生时间")
    @TableField(value = "enrollment_time", typeHandler = JacksonTypeHandler.class)
    private List<String> enrollmentTime;

    @ApiModelProperty(value = "开课时间标志")
    @TableField("starting_flag")
    private String startingFlag;

    @ApiModelProperty(value = "开课时间")
    @TableField(value = "starting_time", typeHandler = JacksonTypeHandler.class)
    private List<String> startingTime;

    @ApiModelProperty(value = "仅招生时间可报名")
    @TableField("registered_flag")
    private String registeredFlag;

    @ApiModelProperty(value = "课程目录模式")
    @TableField("catalog_mode")
    private String catalogMode;

    @ApiModelProperty(value = "关联系列课标志")
    @TableField("series_flag")
    private String seriesFlag;

    @ApiModelProperty(value = "关联系列课")
    @TableField(value = "series_courses", typeHandler = JacksonTypeHandler.class)
    private List<String> seriesCourses;

    @ApiModelProperty(value = "目录解锁模式")
    @TableField("catalog_unlock")
    private String catalogUnlock;

    @ApiModelProperty(value = "目录解锁时间")
    @TableField(value = "unlock_time", typeHandler = JacksonTypeHandler.class)
    private List<String> unlockTime;

    @ApiModelProperty(value = "领取红包是否需要获取用户手机号：true-需要，false-不需要")
    @TableField("need_phone")
    private Boolean needPhone;

    @ApiModelProperty(value = "营期新用户是否自动注册，不需要账号审核：true-需要，false-不需要")
    @TableField("auto_register")
    private Boolean autoRegister;

    @ApiModelProperty(value = "营期是否校验添加企微：true-需要，false-不需要")
    @TableField("need_add_wechat")
    private Boolean needAddWechat;

    @ApiModelProperty(value = "营期红包开关：true-开启，false-关闭")
    @TableField("campperiod_red_pack")
    private Boolean campperiodRedPack;

    @ApiModelProperty(value = "营期红包范围")
    @TableField(value = "red_pack_range", typeHandler = JacksonTypeHandler.class)
    private List<String> redPackRange;

    @ApiModelProperty(value = "营期红包金额")
    @TableField("campperiod_red_pack_amount")
    private String campperiodRedPackAmount;

    @ApiModelProperty(value = "扩展字段1")
    @TableField("field1")
    private String field1;

    @ApiModelProperty(value = "扩展字段2")
    @TableField("field2")
    private String field2;

    @ApiModelProperty(value = "扩展字段3")
    @TableField("field3")
    private String field3;

    @ApiModelProperty(value = "扩展字段4")
    @TableField("field4")
    private String field4;

    @ApiModelProperty(value = "扩展字段5")
    @TableField("field5")
    private String field5;
}
