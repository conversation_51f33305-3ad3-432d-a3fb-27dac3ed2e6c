package cn.hxsy.datasource.model.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class BatchActivationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("企业ID")
    @NotNull(message = "企业ID不能为空")
    private String corpId;

    @ApiModelProperty("用户集合")
    private List<SystemUserQyRelation> users;
}
