package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("course_image")
@ApiModel(value = "ImageInfoPO对象", description = "图片上传传表")
public class ImageInfoPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "图片名称")
    @TableField("image_name")
    private String imageName;

    @ApiModelProperty(value = "图片大小")
    @TableField("image_size")
    private Double imageSize;

    @ApiModelProperty(value = "图片地址")
    @TableField("image_url")
    private String imageUrl;

    @ApiModelProperty(value = "图片路径")
    @TableField("image_path")
    private String imagePath;

}
