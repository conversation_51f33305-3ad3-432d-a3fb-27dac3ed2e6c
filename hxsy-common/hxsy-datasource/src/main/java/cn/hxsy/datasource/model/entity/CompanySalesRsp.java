package cn.hxsy.datasource.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanySalesRsp implements Serializable {
    private Long totalSalesAccounts;    // 总销售账号数
    private Long monthNewSalesAccounts; // 本月新增销售账号数
}
