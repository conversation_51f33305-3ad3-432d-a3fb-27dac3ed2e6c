package cn.hxsy.datasource.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@ApiModel("视频完播请求参数")
public class VideoCompletedRequest {
    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(value = "用户ID", required = true)
    private Long userId;

    @NotEmpty(message = "活动ID列表不能为空")
    @ApiModelProperty(value = "关联的活动ID列表", required = true)
    private List<Long> activityIds;

    // 新增答题数据：key=活动ID, value=用户答案ID列表
    private Map<Long, List<Integer>> answers = new HashMap<>();
}
