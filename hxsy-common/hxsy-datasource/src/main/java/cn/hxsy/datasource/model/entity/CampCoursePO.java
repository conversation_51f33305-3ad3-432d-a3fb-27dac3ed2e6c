package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("camp_course")
@ApiModel(value = "CampCoursePO对象", description = "营期课程信息表")
public class CampCoursePO extends BaseEntity{

    @ApiModelProperty(value = "营期ID")
    @TableField("camp_id")
    private Long campId;

    @ApiModelProperty(value = "课程分组ID")
    @TableField("group_id")
    private Long groupId;

    @ApiModelProperty(value = "外显名称")
    @TableField("external_name")
    private String externalName;


    @ApiModelProperty(value = "课程分组下配置课程小节json")
    @TableField("course_info")
    private String courseInfo;


}
