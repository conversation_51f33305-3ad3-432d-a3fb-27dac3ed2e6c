package cn.hxsy.datasource.model.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CampCourseDataRsp {
    private String courseId;           // 课节ID
    private String courseName;       // 课节名称
    private String courseTime;       // 课节时间
    private Integer viewCount;       // 观看人数
    private Integer completeCount;   // 完播人数
    private String completionRate;   // 完播率
    private Integer redPacketCount;  // 发放红包个数
    private BigDecimal redPacketAmount; // 发放红包金额(元)
}
