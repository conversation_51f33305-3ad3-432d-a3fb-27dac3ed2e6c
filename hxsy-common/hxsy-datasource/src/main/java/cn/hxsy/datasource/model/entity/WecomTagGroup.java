package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 企微标签组实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wecom_tag_group")
@ApiModel(value = "WecomTagGroup", description = "企微标签组表")
public class WecomTagGroup extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企微标签组ID")
    @TableField("group_id")
    private String groupId;

    @ApiModelProperty(value = "标签组名称")
    @TableField("name")
    private String name;

    @ApiModelProperty(value = "所属企业微信corp_id")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty(value = "排序号")
    @TableField("order_num")
    private Integer orderNum;

    @ApiModelProperty(value = "是否删除：0-未删除，1-已删除")
    @TableField("deleted")
    private Boolean deleted;
}
