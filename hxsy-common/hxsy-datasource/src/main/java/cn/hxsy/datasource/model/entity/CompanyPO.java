package cn.hxsy.datasource.model.entity;

import cn.hxsy.datasource.model.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("company")
@ApiModel("公司信息")
public class CompanyPO extends BaseEntity {

    @ApiModelProperty("公司名称")
    @TableField("company_name")
    private String companyName;

    @ApiModelProperty("所属栏目ID")
    @TableField("column_id")
    private Long columnId;

    @ApiModelProperty("公司邀请码链接")
    @TableField("sell_url")
    private String sellUrl;

    @ApiModelProperty("联系人")
    @TableField("rel_person")
    private String relPerson;

    @ApiModelProperty("联系人手机号")
    @TableField("phone")
    private String phone;

}