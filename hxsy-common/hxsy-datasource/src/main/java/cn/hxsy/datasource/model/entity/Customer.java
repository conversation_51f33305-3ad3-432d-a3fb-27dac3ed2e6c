package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 客户实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Customer", description = "客户信息")
@TableName("customer")
public class Customer extends BaseEntity {
    
    @ApiModelProperty(value = "客户头像URL")
    @TableField("avatar_url")
    private String avatarUrl;
    
    @ApiModelProperty(value = "微信昵称")
    @TableField("nickname")
    private String nickname;
    
    @ApiModelProperty(value = "最近活跃时间")
    @TableField("last_active_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastActiveTime;
    
    @ApiModelProperty(value = "手机号")
    @TableField("mobile")
    private String mobile;
    
    @ApiModelProperty(value = "手机号状态（0无手机号/1有手机号）")
    @TableField("mobile_status")
    private Integer mobileStatus;
    
    @ApiModelProperty(value = "性别（0-未知 1-男 2-女）")
    @TableField("gender")
    private Integer gender;
    
    @ApiModelProperty(value = "企微添加状态（0未添加/1已添加/9已删除）")
    @TableField("wework_status")
    private Integer weworkStatus;

    @ApiModelProperty("多小程序登录关联微信号")
    @TableField("union_id")
    private String unionId;

    @ApiModelProperty("小程序openid")
    @TableField("openid")
    private String openid;

    @ApiModelProperty(value = "账号禁用状态（0-否/1-是）")
    @TableField("forbidden_status")
    private Integer forbiddenStatus;

    @ApiModelProperty(value = "红包禁用状态（0-否/1-是）")
    @TableField("red_packet_status")
    private Integer redPacketStatus;

} 