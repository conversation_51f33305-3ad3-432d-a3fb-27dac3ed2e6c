package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签组实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TagGroup", description = "标签组信息")
@TableName("tag_group")
public class TagGroup extends BaseEntity {
    
    @ApiModelProperty(value = "父级标签组ID")
    @TableField("parent_id")
    private Long parentId;
    
    @ApiModelProperty(value = "标签组名称")
    @TableField("group_name")
    private String groupName;
    
    @ApiModelProperty(value = "层级（1-一级标签组，2-二级标签组）")
    @TableField("level")
    private Integer level;
    
    @ApiModelProperty(value = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;
} 