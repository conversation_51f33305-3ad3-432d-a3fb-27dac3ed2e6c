package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 公司与企微账号关联配置
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 11:42:49
 */
@Getter
@Setter
@TableName("company_qy_relation")
@ApiModel(value = "CompanyQyRelation对象", description = "公司与企微账号关联配置")
public class CompanyQyRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("公司id")
    @TableField("company_id")
    private String companyId;

    @ApiModelProperty("企微账号类型 （0-客户企微，1-服务商模版，2-服务商通讯录应用）")
    @TableField("corp_type")
    private String corpType;

    @ApiModelProperty("企业ID")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty("该企业对应自建小程序的凭证密钥")
    @TableField("corp_secret")
    private String corpSecret;

    @ApiModelProperty("企业名称")
    @TableField("corp_name")
    private String corpName;

    @ApiModelProperty("该企业通讯录管理的凭证密钥")
    @TableField("contact_secret")
    private String contactSecret;

    @ApiModelProperty("该企业客户管理的凭证密钥")
    @TableField("customer_secret")
    private String customerSecret;

    @ApiModelProperty("第三方应用id或者代开发应用模板id。第三方应用以ww或wx开头应用id（对应于旧的以tj开头的套件id）；代开发应用以dk开头")
    @TableField("suite_id")
    private String suiteId;

    @ApiModelProperty("第三方应用secret 或者代开发应用模板secret")
    @TableField("suite_secret")
    private String suiteSecret;

    @ApiModelProperty("企业微信后台推送的ticket")
    @TableField("suite_ticket")
    private String suiteTicket;

    @ApiModelProperty("企微回调token")
    @TableField("token")
    private String token;

    @ApiModelProperty("企微回调加签key")
    @TableField("encoding_aes_key")
    private String encodingAESKey;

    @ApiModelProperty("加密corpid")
    @TableField("open_corp_id")
    private String openCorpId;

    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty("使用状态（1-有效，0-无效）")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("创建人")
    @TableField("created_by")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @ApiModelProperty("更新人")
    @TableField("updated_by")
    private String updatedBy;

    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;


}
