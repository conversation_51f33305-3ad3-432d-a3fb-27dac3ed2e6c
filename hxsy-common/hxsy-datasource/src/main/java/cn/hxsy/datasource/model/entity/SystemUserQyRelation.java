package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 业务人员账号与企微信息关联
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Getter
@Setter
@TableName("system_user_qy_relation")
@ApiModel(value = "SystemUserQyRelation对象", description = "业务人员账号与企微信息关联")
@JsonIgnoreProperties(ignoreUnknown = true)
public class SystemUserQyRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("业务人员id")
    @TableField("system_user_id")
    private String systemUserId;

    @ApiModelProperty("业务人员在企业内的UserID")
    @TableField("qy_user_id")
    private String qyUserId;

    @ApiModelProperty("业务人员所在企业ID")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty("业务人员在企业的名称")
    @TableField("qy_name")
    private String qyName;

    @ApiModelProperty(value = "账号激活码(用于激活账号)")
    @TableField("active_code")
    private String activeCode;

    @ApiModelProperty("账号类型 1-基础账号，2-互通账号")
    @TableField("account_type")
    private Integer accountType;

    @ApiModelProperty("账号激活时间")
    @TableField("active_time")
    private LocalDateTime activeTime;

    @ApiModelProperty("企微客户联系方式的配置id")
    @TableField("config_id")
    private String configId;

    @ApiModelProperty("联系我二维码链接")
    @TableField("qr_code")
    private String qrCode;

    @ApiModelProperty("备注")
    @TableField("remark")
    private String remark;

    @ApiModelProperty("使用状态（1-有效，0-无效）")
    @TableField("status")
    private Integer status;

    @ApiModelProperty("创建人")
    @TableField("created_by")
    private String createdBy;

    @ApiModelProperty("创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @ApiModelProperty("更新人")
    @TableField("updated_by")
    private String updatedBy;

    @ApiModelProperty("更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @ApiModelProperty("业务账号名称")
    @TableField(exist = false)
    private String systemUserName;

    @ApiModelProperty("互通账号状态")
    @TableField(exist = false)
    private Integer accountStatus;

    @ApiModelProperty("业务账号状态")
    @TableField(exist = false)
    private Integer systemUserStatus;

    @ApiModelProperty("企业名称")
    @TableField(exist = false)
    private String corpName;

}
