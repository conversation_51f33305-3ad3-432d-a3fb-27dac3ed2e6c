package cn.hxsy.datasource.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量设置客户企微备注响应结果
 *
 * <AUTHOR>
 * @date 2025/7/26
 */
@Data
@ApiModel(value = "BatchRemarkCustomerVO", description = "批量设置客户企微备注响应结果")
public class BatchRemarkCustomerVO {

    @ApiModelProperty(value = "总处理数量")
    private Integer totalCount;

    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    @ApiModelProperty(value = "失败数量")
    private Integer failureCount;

    @ApiModelProperty(value = "成功的客户ID列表")
    private List<Long> successCustomerIds;

    @ApiModelProperty(value = "失败的客户详情列表")
    private List<FailureDetail> failureDetails;

    @Data
    @ApiModel(value = "FailureDetail", description = "失败详情")
    public static class FailureDetail {
        
        @ApiModelProperty(value = "客户ID")
        private Long customerId;
        
        @ApiModelProperty(value = "外部联系人ID")
        private String externalUserId;
        
        @ApiModelProperty(value = "销售企微ID")
        private String salesUserId;
        
        @ApiModelProperty(value = "失败原因")
        private String failureReason;
        
        @ApiModelProperty(value = "错误码")
        private String errorCode;
    }
}
