package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 互通账号订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ExternalAccountOrder", description = "互通账号订单信息")
@TableName("external_account_order")
public class ExternalAccountOrder extends BaseEntity {
    
    @ApiModelProperty(value = "企业ID")
    @TableField("corp_id")
    private String corpId;
    
    @ApiModelProperty(value = "下单人")
    @TableField("buyer_userid")
    private String buyerUserid;
    
    @ApiModelProperty(value = "企业微信订单号")
    @TableField("order_id")
    private String orderId;
    
    @ApiModelProperty(value = "基础账号数量")
    @TableField("base_count")
    private Integer baseCount;
    
    @ApiModelProperty(value = "互通账号数量")
    @TableField("external_contact_count")
    private Integer externalContactCount;
    
    @ApiModelProperty(value = "购买时长(天)")
    @TableField("account_duration")
    private Integer accountDuration;
    
    @ApiModelProperty(value = "订单类型")
    @TableField("order_type")
    private Integer orderType;
    
    @ApiModelProperty(value = "支付状态")
    @TableField("pay_status")
    private Integer payStatus;
    
    @ApiModelProperty(value = "订单金额(分)")
    @TableField("price")
    private Long price;
    
    @ApiModelProperty(value = "服务开始时间")
    @TableField("service_start_time")
    private LocalDateTime serviceStartTime;
    
    @ApiModelProperty(value = "服务到期时间")
    @TableField("service_expire_time")
    private LocalDateTime serviceExpireTime;
    
    @ApiModelProperty(value = "支付时间")
    @TableField("pay_time")
    private LocalDateTime payTime;
    
    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;

    /** 续期订单使用 */
    @TableField(exist = false)
    private List<SystemUserQyRelation> systemUserQyRelations;
}