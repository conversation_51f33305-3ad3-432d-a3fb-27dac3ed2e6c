package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sales_group")
@ApiModel("销售组信息")
public class SalesGroupPO extends BaseEntity {

    @ApiModelProperty("销售组名称")
    @TableField("sales_group_name")
    private String salesGroupName;

    @ApiModelProperty("所属公司ID")
    @TableField("company_id")
    private Long companyId;

    @ApiModelProperty("联系人")
    @TableField("rel_person")
    private String relPerson;

    @ApiModelProperty("联系人手机号")
    @TableField("phone")
    private String phone;
}