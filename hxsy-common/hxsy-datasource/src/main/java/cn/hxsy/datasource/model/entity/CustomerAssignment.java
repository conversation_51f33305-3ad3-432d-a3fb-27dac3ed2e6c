package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 客户分配记录实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerAssignment", description = "客户分配记录信息")
@TableName("customer_assignment")
public class CustomerAssignment extends BaseEntity {

    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;

    @ApiModelProperty(value = "原栏目ID")
    @TableField("original_column_id")
    private Long originalColumnId;

    @ApiModelProperty(value = "原栏目名称")
    @TableField("original_column_name")
    private String originalColumnName;

    @ApiModelProperty(value = "原公司ID")
    @TableField("original_company_id")
    private Long originalCompanyId;

    @ApiModelProperty(value = "原公司名称")
    @TableField("original_company_name")
    private String originalCompanyName;

    @ApiModelProperty(value = "原负责人ID")
    @TableField("original_employee_id")
    private Long originalEmployeeId;

    @ApiModelProperty(value = "原负责人姓名")
    @TableField("original_employee_name")
    private String originalEmployeeName;

    @ApiModelProperty(value = "新负责人ID")
    @TableField("new_employee_id")
    private Long newEmployeeId;

    @ApiModelProperty(value = "新负责人姓名")
    @TableField("new_employee_name")
    private String newEmployeeName;

    @ApiModelProperty(value = "操作人ID")
    @TableField("operator_id")
    private Long operatorId;

    @ApiModelProperty(value = "操作人姓名")
    @TableField("operator_name")
    private String operatorName;

    @ApiModelProperty(value = "变更时间")
    @TableField("change_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime changeTime;

    @ApiModelProperty(value = "备注")
    @TableField("remark")
    private String remark;
}