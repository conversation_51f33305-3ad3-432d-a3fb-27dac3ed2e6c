package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("`column`")
@ApiModel("栏目信息")
public class ColumnPO extends BaseEntity {

    @ApiModelProperty("栏目名称")
    @TableField("column_name")
    private String columnName;

    @ApiModelProperty("联系人")
    @TableField("rel_person")
    private String relPerson;

    @ApiModelProperty("联系人手机号")
    @TableField("phone")
    private String phone;

    @ApiModelProperty("所属总部ID")
    @TableField("headquarters_id")
    private Long headquartersId;
}