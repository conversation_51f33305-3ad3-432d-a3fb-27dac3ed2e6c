package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("stats_summary")
public class StatsSummaryDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Long totalMembers;
    private Long todayNewMembers;
    private Long todayViews;
    private Long todayComplete;
    private Long todayRedPackets;
    private Long totalCompanies;
    private Long monthNewCompanies;
    private Long totalSalesAccounts;
    private Long monthNewSalesAccounts;
    private LocalDate recordDate;
}
