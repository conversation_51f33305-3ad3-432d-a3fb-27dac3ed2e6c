package cn.hxsy.datasource.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerWecomBinding", description = "客户与企微销售绑定关系表")
@TableName("customer_wecom_binding")
public class CustomerWecomBinding extends BaseEntity {

    @ApiModelProperty(value = "客户ID")
    @TableField("customer_id")
    private Long customerId;

    @ApiModelProperty(value = "微信unionid（冗余）")
    @TableField("union_id")
    private String unionId;

    @ApiModelProperty(value = "微信open_id（冗余）")
    @TableField("openid")
    private String openid;

    @ApiModelProperty(value = "用户在企微中的ID（外部联系人ID）")
    @TableField("external_userid")
    private String externalUserid;

    @ApiModelProperty(value = "企微企业pending_id")
    @TableField("pending_id")
    private String pendingId;

    @ApiModelProperty(value = "企微企业corp_id")
    @TableField("corp_id")
    private String corpId;

    @ApiModelProperty(value = "企业名称")
    @TableField("corp_name")
    private String corpName;

    @ApiModelProperty(value = "销售在企微的userid")
    @TableField("sales_userid")
    private String salesUserid;

    @ApiModelProperty(value = "销售在企微的名称")
    @TableField("sales_user_name")
    private String salesUserName;

    @ApiModelProperty(value = "系统销售ID")
    @TableField("sales_id")
    private Long salesId;

    @ApiModelProperty(value = "系统销售名称")
    @TableField("sales_name")
    private Long salesName;

    @ApiModelProperty(value = "添加此用户的「联系我」方式配置的state参数")
    @TableField("state")
    private String state;

    @ApiModelProperty(value = "欢迎语code")
    @TableField("welcome_code")
    private String welcomeCode;

} 