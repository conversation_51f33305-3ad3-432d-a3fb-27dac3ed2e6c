package cn.hxsy.datasource.model.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatsSummary implements Serializable {
    private Long totalMembers;          // 会员总数
    private Long todayNewMembers;       // 今日新增会员
    private Long todayViews;            // 今日观看(次)
    private Long todayComplete;
    private Long todayRedPackets;       // 今日发放红包(个)
    private Long totalCompanies;        // 总公司数
    private Long monthNewCompanies;     // 本月新增公司数
    private Long totalSalesAccounts;    // 总销售账号数
    private Long monthNewSalesAccounts; // 本月新增销售账号数

    private String recordDate;       // 记录日期 (yyyy-MM-dd)
    private String refreshDate;      // 数据更新时间 (yyyy-MM-dd HH:mm:ss)

    // 获取当前整点时间的方法
    public static String getCurrentHour() {
        return LocalDateTime.now().truncatedTo(ChronoUnit.HOURS)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
}
