package cn.hxsy.api.user.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 小程序码生成请求参数封装
 * 官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getQRCode.html
 */
@Data
public class QRCodeRequest {

    /**
     * 扫码进入的小程序页面路径（必填）
     * 格式：pages/index/index?query=xxx
     */
    private String path;

    /**
     * 二维码的宽度（单位：px）
     * 默认值：430
     */
    private Integer width = 430;

    /**
     * 是否自动配置线条颜色
     * 默认值：false
     */
    @JsonProperty("auto_color")
    private Boolean autoColor = false;

    /**
     * 当 auto_color 为 false 时生效的颜色RGB值
     * 格式：{"r":"0","g":"0","b":"0"}
     */
    @JsonProperty("line_color")
    private LineColor lineColor;

    /**
     * 是否需要透明底色
     */
    @JsonProperty("is_hyaline")
    private Boolean isHyaline = false;

    /**
     * 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
     */
    @JsonProperty("env_version")
    private String envVersion;

    /**
     * 颜色RGB值封装
     */
    @Data
    public static class LineColor {
        @JsonProperty("r")
        private String red;
        @JsonProperty("g")
        private String green;
        @JsonProperty("b")
        private String blue;

        public LineColor(String rgb) {
            String[] colors = rgb.split(",");
            this.red = colors[0];
            this.green = colors[1];
            this.blue = colors[2];
        }
    }

    // 快速构建方法（可选）
    public static QRCodeRequest build(String path) {
        QRCodeRequest request = new QRCodeRequest();
        request.setPath(path);
        return request;
    }
}
