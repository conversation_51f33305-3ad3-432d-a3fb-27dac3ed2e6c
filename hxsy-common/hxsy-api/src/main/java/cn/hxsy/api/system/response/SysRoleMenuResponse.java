package cn.hxsy.api.system.response;

import cn.hxsy.base.response.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description : SysRoleMenuResponse
 * @ClassName : SysRoleMenuResponse
 * @date: 2025-05-02 11:24
 */
@Data
public class SysRoleMenuResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("系统全局菜单列表")
    private List<SysMenuResponse> allSysAppInfos;

    @ApiModelProperty("当前角色已分配菜单列表")
    private List<SysMenuResponse> authAppInfos;

}
