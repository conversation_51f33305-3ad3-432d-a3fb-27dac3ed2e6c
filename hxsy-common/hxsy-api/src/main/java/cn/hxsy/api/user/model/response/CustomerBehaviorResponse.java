package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 客户行为轨迹实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerBehaviorResponse", description = "客户行为轨迹信息响应")
public class CustomerBehaviorResponse extends BaseEntityResponse {
    
    @ApiModelProperty(value = "客户ID")
    private Long customerId;
    
    @ApiModelProperty(value = "行为类型（1-客户注册 2-训练营营期报名 3-训练营视频课学习 4-课后答题 5-领取红包 6-添加企微 7-删除企微 8-加入群聊）")
    private String behaviorType;
    
    @ApiModelProperty(value = "课程ID")
    private Long courseId;
    
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    
    @ApiModelProperty(value = "训练营ID")
    private Long companyId;
    
    @ApiModelProperty(value = "训练营名称")
    private String companyName;
    
    @ApiModelProperty(value = "营期ID")private Long campPeriodId;
    
    @ApiModelProperty(value = "营期名称")
    private String campPeriodName;
    
    @ApiModelProperty(value = "奖励金额")
    private BigDecimal rewardAmount;
    
    @ApiModelProperty(value = "红包类型")
    private String rewardType;
    
    @ApiModelProperty(value = "奖励规则")
    private String rewardRule;
    
    @ApiModelProperty(value = "员工姓名")
    private String employeeName;
    
    @ApiModelProperty(value = "员工企微昵称")
    private String employeeWeworkName;
    
    @ApiModelProperty(value = "企业微信名称")
    private String enterpriseWeChatName;
    
    @ApiModelProperty(value = "访问地址")
    private String accessUrl;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "使用状态（1-有效，0-无效）")
    private Integer status;
} 