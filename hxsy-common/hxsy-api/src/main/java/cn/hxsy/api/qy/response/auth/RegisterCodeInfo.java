package cn.hxsy.api.qy.response.auth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> XiaQL
 * @description : 推广二维码安装相关信息
 * @ClassName : RegisterCodeInfo
 * @date: 2025-06-29 01:58
 */
@Data
public class RegisterCodeInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "注册码")
    private String register_code;

    @ApiModelProperty(value = "推广包ID")
    private String template_id;

    @ApiModelProperty(value = "仅当获取注册码指定该字段时才返回")
    private String state;

}
