package cn.hxsy.api.qy.request.tag;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 添加企业客户标签请求
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagAddRequest {

    /**
     * 标签组id，非必填，如果创建的是标签，则需要指定此参数
     */
    private String group_id;

    /**
     * 标签组名称，如果创建的是标签组，则需要指定此参数，标签组名称不能超过30个字符
     */
    private String group_name;

    /**
     * 标签组次序值，非必填，默认为0，排序以此值升序排列
     */
    private Integer order;

    /**
     * 添加的标签列表
     */
    private List<TagInfo> tag;

    /**
     * 标签信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TagInfo {

        /**
         * 标签名称，不能超过30个字符
         */
        private String name;

        /**
         * 标签次序值，非必填，默认为0，排序以此值升序排列
         */
        private Integer order;
    }
}
