package cn.hxsy.api.system.request;

import cn.hxsy.base.request.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description : SysMenuResponse
 * @ClassName : SysMenuResponse
 * @date: 2025-05-02 11:24
 */
@Data
public class SysRoleMenuRequest extends BaseRequest {

    @ApiModelProperty("角色ID")
    private String roleId;

    @ApiModelProperty("关联菜单id")
    private List<String> menuIds;

}
