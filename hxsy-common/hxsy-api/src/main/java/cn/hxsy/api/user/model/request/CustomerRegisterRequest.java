package cn.hxsy.api.user.model.request;

import cn.hxsy.base.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户注册请求实体
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel("客户注册请求实体")
public class CustomerRegisterRequest extends BaseRequest {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("微信关联unionId")
    private String unionId;

    @ApiModelProperty("微信appid")
    private String appid;

    @ApiModelProperty("微信openid")
    private String openid;

    @ApiModelProperty(value = "客户头像URL")
    private String avatarUrl;

    @ApiModelProperty(value = "微信昵称")
    private String nickname;

    @ApiModelProperty("性别（0-未知 1-男 2-女）")
    private Integer gender;
    
    @ApiModelProperty("栏目ID")
    private Long columnId;

    @ApiModelProperty("训练营ID")
    private Long companyId;

    @ApiModelProperty("营期ID")
    private Long campPeriodId;

    @ApiModelProperty("销售组ID")
    private Long salesGroupId;

    @ApiModelProperty("销售ID")
    private Long salesId;

    @ApiModelProperty(value = "销售人员姓名")
    private String salesName;

    @ApiModelProperty("课程ID")
    private Long courseId;

} 