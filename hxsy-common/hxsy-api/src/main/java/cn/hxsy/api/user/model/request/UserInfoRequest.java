package cn.hxsy.api.user.model.request;

import cn.hxsy.base.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 系统用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31 10:19:01
 */
@Getter
@Setter
@ApiModel(value = "客户、业务人员实体类查询公共request")
public class UserInfoRequest extends BaseRequest {

    private Long id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("登录账号")
    private String userCode;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("手机号")
    private String telPhone;

    @ApiModelProperty("登录渠道，是否从链接跳转")
    private String isUrl;

    @ApiModelProperty("微信号关联多小程序的unionId")
    private String unionId;

    @ApiModelProperty("小程序openid")
    private String openid;

    @ApiModelProperty("小程序来源，区分三个端侧小程序")
    private Integer appChannel;

    @ApiModelProperty("登录人员类型")
    private String userType;
}
