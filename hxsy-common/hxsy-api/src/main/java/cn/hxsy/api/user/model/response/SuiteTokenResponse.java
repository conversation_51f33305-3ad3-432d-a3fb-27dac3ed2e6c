package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 获取第三方应用凭证响应实体
 */
@Data
@ApiModel(value = "SuiteTokenResponse", description = "获取第三方应用凭证响应")
public class SuiteTokenResponse implements Serializable {

    @ApiModelProperty(value = "错误码")
    private Integer errcode;

    @ApiModelProperty(value = "错误信息")
    private String errmsg;

    @ApiModelProperty(value = "第三方应用凭证")
    private String suite_access_token;

    @ApiModelProperty(value = "凭证有效期，单位秒")
    private Integer expires_in;
} 