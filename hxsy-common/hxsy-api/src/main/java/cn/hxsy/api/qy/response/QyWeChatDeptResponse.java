package cn.hxsy.api.qy.response;

import cn.hxsy.api.qy.QyBaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
* @description: 企业内应用开发-企微内部门信息响应实体
* @author: xiaQL
* @date: 2025/6/17 11:22
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "企微内部门信息响应实体", description = "企微内部门信息响应实体")
public class QyWeChatDeptResponse {

    @ApiModelProperty(value = "创建的部门id")
    Integer id;

    @ApiModelProperty(value = "英文名称，此字段从2019年12月30日起，对新创建第三方应用不再返回，2020年6月30日起，对所有历史第三方应用不再返回该字段")
    String name_en;

    @ApiModelProperty(value = "部门名称，代开发自建应用需要管理员授权才返回；此字段从2019年12月30日起，对新创建第三方应用不再返回，2020年6月30日起，对所有历史第三方应用不再返回name，返回的name字段使用id代替，后续第三方仅通讯录应用可获取，未返回名称的情况需要通过通讯录展示组件来展示部门名称")
    String name;

    @ApiModelProperty(value = "部门负责人的UserID；第三方仅通讯录应用可获取")
    List<String> department_leader;

    @ApiModelProperty(value = "父部门id。根部门为1")
    Integer parentid;

    @ApiModelProperty(value = "在父部门中的次序值。order值大的排序靠前。值范围是[0, 2^32)")
    Integer order;

}
