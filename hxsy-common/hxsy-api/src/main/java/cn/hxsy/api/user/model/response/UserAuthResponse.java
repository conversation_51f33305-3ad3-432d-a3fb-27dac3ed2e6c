package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 系统用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31 10:19:01
 */
@Data
@ApiModel(value = "UserAuth对象", description = "登录认证表")
public class UserAuthResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("多小程序一键登录id")
    private String unionId;

    @ApiModelProperty("业务人员、客户id")
    private Long id;

    @ApiModelProperty("角色类型 0-业务人员 1-客户")
    private String userType;
}
