package cn.hxsy.api.user.model.request;

import cn.hxsy.base.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户获取课程视频链接请求实体
 *
 * <AUTHOR>
 * @date 2024-04-08
 */
@Data
@ApiModel("客户获取课程视频链接请求实体")
public class CustomerCourseVideoRequest extends BaseRequest {

    @ApiModelProperty("微信unionid")
    private String unionid;
    
    @ApiModelProperty("栏目ID")
    private Long columnId;

    @ApiModelProperty("训练营ID")
    private Long companyId;

    @ApiModelProperty("营期ID")
    private Long campPeriodId;

    @ApiModelProperty("销售组ID")
    private Long salesGroupId;

    @ApiModelProperty("销售ID")
    private Long salesId;

    @ApiModelProperty("课程ID")
    private Long courseId;

    @ApiModelProperty("客户ID")
    private Long customerId;

    @ApiModelProperty("用户类型")
    private String userType;

} 