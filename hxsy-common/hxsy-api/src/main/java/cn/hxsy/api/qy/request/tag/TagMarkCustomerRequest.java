package cn.hxsy.api.qy.request.tag;

import lombok.Data;

import java.util.List;

/**
 * 为客户打标签请求
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
public class TagMarkCustomerRequest {

    /**
     * 添加外部联系人的userid
     */
    private String userid;

    /**
     * 外部联系人userid
     */
    private String external_userid;

    /**
     * 要标记的标签列表
     */
    private List<String> add_tag;

    /**
     * 要移除的标签列表
     */
    private List<String> remove_tag;
}
