package cn.hxsy.api.user.service;

import cn.hxsy.base.response.Result;

/**
 * description : 客户信息远程调用
 */
public interface CustomerRpcService {

    /**
     * description : 记录行为轨迹-领取红包
     * @title: saveReceiveRedPacket
     * @param: customerId
     * @param: companyId
     * @param: campPeriodId
     * @param: courseId
     * @param: amount
     * @param: type
     * <AUTHOR>
     * @date 2025/5/10 16:28
     * @return boolean
     */
    Result saveReceiveRedPacket(Long customerId, Long companyId, Long campPeriodId, Long courseId, String amount, Integer type);

    /**
     * 获取用户红包状态并检查课程完成状态
     * @param wxPayRequest 支付请求对象，包含用户ID、营期ID和课程ID等信息
     * @return 红包状态
     */
    Result<Integer> getRedPacketStatus(cn.hxsy.base.request.wxPayRequest wxPayRequest);

    /**
     * 更新用户红包状态和使用状态
     * @param wxPayRequest 支付请求对象，包含用户ID等信息
     * @return 更新结果
     */
    Result<Boolean> updateRedPacketAndUseStatus(cn.hxsy.base.request.wxPayRequest wxPayRequest);

}
