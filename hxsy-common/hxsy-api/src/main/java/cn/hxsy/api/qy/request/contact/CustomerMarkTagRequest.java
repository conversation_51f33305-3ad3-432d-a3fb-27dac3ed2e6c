package cn.hxsy.api.qy.request.contact;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 编辑客户企业标签请求
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerMarkTagRequest {

    /**
     * 添加外部联系人的企业成员id
     */
    private String userid;

    /**
     * 外部联系人userid
     */
    private String external_userid;

    /**
     * 要添加的企业标签id列表，最多可添加30个标签
     */
    private List<String> add_tag;

    /**
     * 要移除的企业标签id列表，最多可移除30个标签
     */
    private List<String> remove_tag;
}
