package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新用户unionId请求类
 *
 * <AUTHOR>
 * @date 2025/08/03
 */
@Data
@ApiModel(value = "UpdateUnionIdRequest", description = "更新用户unionId请求")
public class UpdateUnionIdRequest {

    @ApiModelProperty(value = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long id;

    @ApiModelProperty(value = "新的unionId", required = true)
    @NotBlank(message = "unionId不能为空")
    private String unionId;

    @ApiModelProperty(value = "用户类型 0-业务人员 1-客户", notes = "可选，默认为0")
    private Integer userType;
}
