package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 标签组树形响应
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel(value = "TagGroupTreeResponse", description = "标签组树形信息")
public class TagGroupTreeResponse {
    
    @ApiModelProperty(value = "标签组ID")
    private Long id;
    
    @ApiModelProperty(value = "标签组名称")
    private String groupName;
    
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;
    
    @ApiModelProperty(value = "父级标签组ID")
    private Long parentId;
    
    @ApiModelProperty(value = "标签组级别（1-一级标签组，2-二级标签组）")
    private Integer level;
    
    @ApiModelProperty(value = "子标签组列表")
    private List<TagGroupTreeResponse> children;
    
    @ApiModelProperty(value = "标签列表")
    private List<TagResponse> tags;
} 