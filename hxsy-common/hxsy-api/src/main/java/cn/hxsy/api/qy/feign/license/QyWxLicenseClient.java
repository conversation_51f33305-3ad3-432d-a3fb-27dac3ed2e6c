package cn.hxsy.api.qy.feign.license;

import cn.hxsy.api.user.feign.vx.DisableLoadBalanceConfiguration;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * description : 企业-代理服务商-接口调用许可账号请求接口
 * <AUTHOR>
 */
@FeignClient(
        url = "${feign.provider.qyWx.url}" + QyWxLicenseClient.TK,
        name = "${feign.provider.qyWx.license.id}",
        configuration = DisableLoadBalanceConfiguration.class
)
public interface QyWxLicenseClient {

    String TK = "/cgi-bin";

    String LICENSE = "/license";

    /**
     * description : 下单购买账号 <a href="https://developer.work.weixin.qq.com/document/path/95644">...</a>
     * @title: createNewOrder
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 购买账号的参数
     * {
     * 	"corpid" : "wwxxx",
     * 	"buyer_userid":"xxxx",
     * 	"account_count":{
     * 	  "base_count":100,
     *  	  "external_contact_count":100
     *        },
     * 	"account_duration": {
     * 		"months":2,
     * 		"days":20
     *    }
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:26
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"order_id": "xxxx"
     * }
     */
    @PostMapping(value = LICENSE + "/create_new_order", consumes = MediaType.ALL_VALUE)
    JSONObject createNewOrder(@RequestParam(name = "provider_access_token") String providerAccessToken,
                              @RequestBody JSONObject jsonObject);

    /**
     * description : 创建续期任务 <a href="https://developer.work.weixin.qq.com/document/path/95646#%E5%88%9B%E5%BB%BA%E7%BB%AD%E6%9C%9F%E4%BB%BB%E5%8A%A1">...</a>
     * @title: createRenewOrderJob
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 续期账号的参数
     * {
     * 	"corpid" : "wwxxx",
     * 	"account_list":[
     *                {
     * 			"userid":"userid1",
     * 			"type":1
     *        }
     * 	],
     * 	"jobid":"JOBID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:30
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"jobid": "xxxx",
     * 	"invalid_account_list":[
     *                {
     * 			"errcode": 1,
     * 			"errmsg": "xxx",
     * 			"userid":"userid1",
     * 			"type":1
     *        }
     * 	]
     * }
     */
    @PostMapping(value = LICENSE + "/create_renew_order_job", consumes = MediaType.ALL_VALUE)
    JSONObject createRenewOrderJob(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                   @RequestBody JSONObject jsonObject);

    /**
     * description : 提交续期订单 <a href="https://developer.work.weixin.qq.com/document/path/95646#%E5%88%9B%E5%BB%BA%E7%BB%AD%E6%9C%9F%E4%BB%BB%E5%8A%A1">...</a>
     * @title: submitOrderJob
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 提交续期订单的参数
     * {
     * 	"corpid" : "wwxxx",
     * 	"account_list":[
     * 		        {
     * 			"userid":"userid1",
     * 			"type":1
     *        }
     * 	],
     * 	"jobid":"JOBID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:53
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"jobid": "xxxx",
     * 	"invalid_account_list":[
     * 		        {
     * 			"errcode": 1,
     * 			"errmsg": "xxx",
     * 			"userid":"userid1",
     * 			"type":1
     *        }
     * 	]
     * }
     */
    @PostMapping(value = LICENSE + "/submit_order_job", consumes = MediaType.ALL_VALUE)
    JSONObject submitOrderJob(@RequestParam(name = "provider_access_token") String providerAccessToken,
                              @RequestBody JSONObject jsonObject);

    /**
     * description : 获取订单列表 <a href="https://developer.work.weixin.qq.com/document/path/95647">...</a>
     * @title: listOrder
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 获取订单列表的参数
     * {
     * 	"corpid":"xxxxx",
     * 	"start_time":**********,
     * 	"end_time":**********,
     * 	"cursor":"xxx",
     * 	"limit":10
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:32
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"next_cursor":"xxx",
     * 	"has_more":1,
     * 	"order_list":[
     *                {
     * 			"order_id":"xxx",
     * 			"order_type":1
     *        }
     * 	]
     * }
     */
    @PostMapping(value = LICENSE + "/list_order", consumes = MediaType.ALL_VALUE)
    JSONObject listOrder(@RequestParam(name = "provider_access_token") String providerAccessToken,
                         @RequestBody JSONObject jsonObject);

    /**
     * description : 获取订单详情 <a href="https://developer.work.weixin.qq.com/document/path/95648">...</a>
     * @title: getOrder
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 获取订单详情的参数
     * {
     * 	"order_id":"xxxxx"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:37
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"order":{
     * 		"order_id":"xxxxx",
     * 		"order_type":1,
     * 		"order_status":1,
     * 		"corpid":"wpxxxx",
     * 		"price":10000,
     * 		"account_count":{
     * 	  	   "base_count":100,
     *       	   "external_contact_count":100
     * 	              },
     * 		 "account_duration":
     *         {
     * 	   	  	"months":2,
     * 	   	  	"days":20,
     * 			"new_expire_time":**********
     *             },
     * 		"create_time":*********,
     * 	    "pay_time":**********
     * 	}
     * }
     */
    @PostMapping(value = LICENSE + "/get_order", consumes = MediaType.ALL_VALUE)
    JSONObject getOrder(@RequestParam(name = "provider_access_token") String providerAccessToken,
                       @RequestBody JSONObject jsonObject);

    /**
     * description : 获取订单中的账号列表 <a href="https://developer.work.weixin.qq.com/document/path/95649">...</a>
     * @title: listOrderAccount
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 获取订单中的账号列表的参数
     * {
     * 	"order_id" : "XXXXXXXX",
     * 	"limit":1000,
     * 	"cursor":"xxxx"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:40
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"next_cursor": "xxxx",
     * 	"has_more":1,
     * 	"account_list":[
     * 		        {
     * 			"active_code": "code1",
     * 			"userid":"XXX",
     * 			"type": 1
     *        },
     *        {
     * 			"active_code": "code2",
     * 			"userid":"XXX",
     * 			"type": 2
     *        }
     * 	]
     * }
     */
    @PostMapping(value = LICENSE + "/list_order_account", consumes = MediaType.ALL_VALUE)
    JSONObject listOrderAccount(@RequestParam(name = "provider_access_token") String providerAccessToken,
                               @RequestBody JSONObject jsonObject);

    /**
     * description : 取消订单 <a href="https://developer.work.weixin.qq.com/document/path/96106">...</a>
     * @title: cancelOrder
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 取消订单的参数
     * {
     * 	"order_id":"xxxxx",
     * 	"corpid":"CORPID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:50
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok"
     * }
     */
    @PostMapping(value = LICENSE + "/cancel_order", consumes = MediaType.ALL_VALUE)
    JSONObject cancelOrder(@RequestParam(name = "provider_access_token") String providerAccessToken,
                         @RequestBody JSONObject jsonObject);

    /**
     * description : 创建多企业新购任务 <a href="https://developer.work.weixin.qq.com/document/path/98892#%E5%88%9B%E5%BB%BA%E5%A4%9A%E4%BC%81%E4%B8%9A%E6%96%B0%E8%B4%AD%E4%BB%BB%E5%8A%A1">...</a>
     * @title: createNewOrderJob
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 创建多企业新购任务的参数
     * {
     * 	"buy_list": [{
     * 		"corpid" : "CORPID",
     * 		"account_count": {
     * 			"base_count": 100,
     * 			"external_contact_count": 100
     * 		        },
     * 		"account_duration": {
     * 			"months": 2,
     * 			"days": 20
     *        },
     * 		"auto_active_status": 1    * 	}],
     * 	"jobid": "JOBID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:55
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"jobid": "BUYJOBID",
     * 	"invalid_list":[
     * 		        {
     * 			"corpid":"CORPID",
     * 			"errcode": 1,
     * 			"errmsg": "xxx"
     *        }
     * 	]
     * }
     */
    @PostMapping(value = LICENSE + "/create_new_order_job", consumes = MediaType.ALL_VALUE)
    JSONObject createNewOrderJob(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                @RequestBody JSONObject jsonObject);

    /**
     * description : 提交多企业新购订单 <a href="https://developer.work.weixin.qq.com/document/path/98892#%E6%8F%90%E4%BA%A4%E5%A4%9A%E4%BC%81%E4%B8%9A%E6%96%B0%E8%B4%AD%E8%AE%A2%E5%8D%95">...</a>
     * @title: submitNewOrderJob
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 提交多企业新购订单的参数
     * {
     * 	"jobid" : "BUYJOBID",
     * 	"buyer_userid":"xxxx"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:57
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok"
     * }
     */
    @PostMapping(value = LICENSE + "/submit_new_order_job", consumes = MediaType.ALL_VALUE)
    JSONObject submitNewOrderJob(@RequestParam(name = "provider_access_token") String providerAccessToken,
                               @RequestBody JSONObject jsonObject);

    /**
     * description : 获取多企业新购订单提交结果 <a href="https://developer.work.weixin.qq.com/document/path/98892#%E8%8E%B7%E5%8F%96%E5%A4%9A%E4%BC%81%E4%B8%9A%E6%96%B0%E8%B4%AD%E8%AE%A2%E5%8D%95%E6%8F%90%E4%BA%A4%E7%BB%93%E6%9E%9C">...</a>
     * @title: newOrderJobResult
     * @param: providerAccessToken
     * @param: jsonObject
     * {
     * 	"jobid" : "BUYJOBID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 13:59
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"status": 1,
     * 	"order_id": "xxxx",
     * 	"fail_list": [{
     * 		"corpid": "CORPID",
     * 		"errcode": 700400,
     * 		"errmsg": "xxx"
     * 	    }]
     * }
     */
    @PostMapping(value = LICENSE + "/new_order_job_result", consumes = MediaType.ALL_VALUE)
    JSONObject newOrderJobResult(@RequestParam(name = "provider_access_token") String providerAccessToken,
                               @RequestBody JSONObject jsonObject);

    /**
     * description : 获取多企业订单详情 <a href="https://developer.work.weixin.qq.com/document/path/98893">...</a>
     * @title: getUnionOrder
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 获取多企业订单详情的参数
     * {
     * 	"order_id": "ORDERID",
     * 	"limit": 1000,
     * 	"cursor": "CURSOR"
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:01
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"order":{
     * 		"order_id":"ORDERID",
     * 		"order_type":1,
     * 		"order_status":1,
     * 		"price":10000,
     * 		"create_time":*********,
     * 	    "pay_time":**********
     * 	    },
     * 	"has_more": 1,
     * 	"next_cursor": "CURSOR",
     * 	"buy_list": [{
     * 		"sub_order_id": "SUBORDERID",
     * 		"corpid":"CORPID",
     * 		"account_count": {
     * 	  	   "base_count":100,
     *       	   "external_contact_count":100
     *         },
     * 		 "account_duration": {
     * 	   	  	"months": 2,
     * 			"days": 20
     *             }
     *    }]
     * }
     */
    @PostMapping(value = LICENSE + "/get_union_order", consumes = MediaType.ALL_VALUE)
    JSONObject getUnionOrder(@RequestParam(name = "provider_access_token") String providerAccessToken,
                           @RequestBody JSONObject jsonObject);

    /**
     * description : 提交余额支付订单任务 <a href="https://developer.work.weixin.qq.com/document/path/99415#%E6%8F%90%E4%BA%A4%E4%BD%99%E9%A2%9D%E6%94%AF%E4%BB%98%E8%AE%A2%E5%8D%95%E4%BB%BB%E5%8A%A1">...</a>
     * @title: submitPayJob
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 提交余额支付订单任务的参数
     * {
     * 	"payer_userid": "USERID",
     * 	"order_id": "ORDERID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:03
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"jobid": "JOBID"
     * }
     */
    @PostMapping(value = LICENSE + "/submit_pay_job", consumes = MediaType.ALL_VALUE)
    JSONObject submitPayJob(@RequestParam(name = "provider_access_token") String providerAccessToken,
                          @RequestBody JSONObject jsonObject);

    /**
     * description : 获取订单支付结果 <a href="https://developer.work.weixin.qq.com/document/path/99415#%E8%8E%B7%E5%8F%96%E8%AE%A2%E5%8D%95%E6%94%AF%E4%BB%98%E7%BB%93%E6%9E%9C">...</a>
     * @title: payJobResult
     * @param: providerAccessToken 应用服务商的接口调用凭证
     * @param: jsonObject 获取订单支付结果的参数
     * {
     * 	"jobid": "JOBID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:04
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"status": 3,
     * 	"pay_job_result": {
     * 		"errcode": 700001,
     * 		"errmsg": "xxx",
     * 		"fail_corp_list": [{
     * 			"corpid": "wwxxx",
     * 			"errcode": 700002,
     * 			"errmsg": "xxx"
     * 		 }]
     * 	 }
     * }
     */
    @PostMapping(value = LICENSE + "/pay_job_result", consumes = MediaType.ALL_VALUE)
    JSONObject payJobResult(@RequestParam(name = "provider_access_token") String providerAccessToken,
                          @RequestBody JSONObject jsonObject);

    /**
     * description : 激活账号 <a href="https://developer.work.weixin.qq.com/document/path/95553#%E6%BF%80%E6%B4%BB%E8%B4%A6%E5%8F%B7">...</a>
     * @title: activeAccount
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject 激活账号的参数
     * {
     * 	"active_code" : "XXXXXXXX",
     * 	"corpid": "CORPID",
     * 	"userid": "USERID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:06
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok"
     * }
     */
    @PostMapping(value = LICENSE + "/active_account", consumes = MediaType.ALL_VALUE)
    JSONObject activeAccount(@RequestParam(name = "provider_access_token") String providerAccessToken,
                           @RequestBody JSONObject jsonObject);

    /**
     * description : 批量激活账号 <a href="https://developer.work.weixin.qq.com/document/path/95553#%E6%89%B9%E9%87%8F%E6%BF%80%E6%B4%BB%E8%B4%A6%E5%8F%B7">...</a>
     * @title: batchActiveAccount
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject 批量激活账号的参数
     * {
     * 	"corpid": "CORPID",
     * 	"active_list":[
     * 	    {
     * 		"active_code" : "XXXXXXXX",
     * 		"userid": "USERID"
     *    },
     *    {
     * 		"active_code" : "XXXXXXXX",
     * 		"userid": "USERID"
     *    }]
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:07
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"active_result":[
     * 	    {
     * 		"active_code" : "XXXXXXXX",
     * 		"userid": "USERID",
     * 		"errcode":0
     *    },
     *    {
     * 		"active_code" : "XXXXXXXX",
     * 		"userid": "USERID",
     * 		"errcode":0
     *    }]
     * }
     */
    @PostMapping(value = LICENSE + "/batch_active_account", consumes = MediaType.ALL_VALUE)
    JSONObject batchActiveAccount(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                @RequestBody JSONObject jsonObject);

    /**
     * description : 获取激活码详情 <a href="https://developer.work.weixin.qq.com/document/path/95552#%E8%8E%B7%E5%8F%96%E6%BF%80%E6%B4%BB%E7%A0%81%E8%AF%A6%E6%83%85">...</a>
     * @title: getActiveInfoByCode
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject
     * {
     * 	"corpid":"xxx",
     * 	"active_code" : "XXXXXXXX"
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:13
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"active_info": {
     * 		"active_code": "code1",
     * 		"type": 1,
     * 		"status": 1,
     * 		"userid": "USERID",
     * 		"create_time":**********,
     * 		"active_time": **********,
     * 		"expire_time":**********,
     * 		"merge_info":
     * 		        {
     * 			  "to_active_code":"code_new",
     * 			  "from_active_code":"code_old"
     *        },
     * 		"share_info":
     *        {
     * 			"to_corpid":"CORPID",
     * 			"from_corpid":"CORPID"
     *        }
     *   }
     * }
     */
    @PostMapping(value = LICENSE + "/get_active_info_by_code", consumes = MediaType.ALL_VALUE)
    JSONObject getActiveInfoByCode(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                 @RequestBody JSONObject jsonObject);

    /**
     * description : 批量获取激活码详情 <a href="https://developer.work.weixin.qq.com/document/path/95552#%E6%89%B9%E9%87%8F%E8%8E%B7%E5%8F%96%E6%BF%80%E6%B4%BB%E7%A0%81%E8%AF%A6%E6%83%85">...</a>
     * @title: batchGetActiveInfoByCode
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject
     * {
     * 	"corpid":"xxx",
     * 	"active_code_list" : ["XXXXXXXX","YYYYYYYY","ZZZZZZZZ"]
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:15
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"active_info_list": [
     * 		        {
     * 			"active_code": "XXXXXXXX",
     * 			"type": 1,
     * 			"status": 1,
     * 			"userid": "USERID1",
     * 			"create_time":**********,
     * 			"active_time": **********,
     * 			"expire_time":**********,
     * 			"merge_info":
     *            {
     * 				"to_active_code":"code_new",
     * 				"from_active_code":"code_old"
     *            },
     * 			"share_info":
     *            {
     * 				"to_corpid":"CORPID",
     * 				"from_corpid":"CORPID"
     *            }
     *        },
     *        {
     * 			"active_code": "YYYYYYYY",
     * 			"type": 2,
     * 			"status": 1,
     * 			"userid": "USERID2",
     * 			"create_time":**********,
     * 			"active_time": **********,
     * 			"expire_time":**********,
     * 			"merge_info":
     *            {
     * 				"to_active_code":"code_new",
     * 				"from_active_code":"code_old"
     *            },
     * 			"share_info":
     *            {
     * 				"to_corpid":"CORPID",
     * 				"from_corpid":"CORPID"
     *            }
     *        }
     * 	],
     * 	"invalid_active_code_list":["ZZZZZZZZ"]
     * }
     */
    @PostMapping(value = LICENSE + "/batch_get_active_info_by_code", consumes = MediaType.ALL_VALUE)
    JSONObject batchGetActiveInfoByCode(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                     @RequestBody JSONObject jsonObject);

    /**
     * description : 获取企业的账号列表 <a href="https://developer.work.weixin.qq.com/document/path/95544">...</a>
     * @title: listActivedAccount
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject
     * {
     * 	"corpid" : "CORPID",
     * 	"limit":500,
     * 	"cursor":"xxxx"
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:17
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"next_cursor":"xxx",
     * 	"has_more":1,
     * 	"account_list":[
     *                {
     * 			"userid": "user1",
     * 			"type": 1,
     * 			"expire_time":**********,
     * 			"active_time":**********
     *        },
     *        {
     * 			"userid": "user2",
     * 			"type": 1,
     * 			"expire_time":**********,
     * 			"active_time":**********
     *        }
     * 	]
     * }
     */
    @PostMapping(value = LICENSE + "/list_actived_account", consumes = MediaType.ALL_VALUE)
    JSONObject listActivedAccount(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                  @RequestBody JSONObject jsonObject);

    /**
     * description : 获取成员的激活详情 <a href="https://developer.work.weixin.qq.com/document/path/95555">...</a>
     * @title: getActiveInfoByUser
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject
     * {
     * 	"corpid": "CORPID",
     * 	"userid": "USERID"
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:20
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"active_status": 1,
     * 	"active_info_list":
     * 	[
     * 		  {
     * 			"active_code": "code1",
     * 			"type": 1,
     * 			"userid": "USERID",
     * 			"active_time": **********,
     * 			"expire_time":**********
     *          },
     *       {
     * 			"active_code": "code1",
     * 			"type": 2,
     * 			"userid": "USERID",
     * 			"active_time":**********,
     * 			"expire_time":**********
     *          }
     *        ]
     * }
     */
    @PostMapping(value = LICENSE + "/get_active_info_by_user", consumes = MediaType.ALL_VALUE)
    JSONObject getActiveInfoByUser(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                   @RequestBody JSONObject jsonObject);

    /**
     * description : 账号继承 <a href="https://developer.work.weixin.qq.com/document/path/95673">...</a>
     * @title: batchTransferLicense
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject
     * {
     * 	"corpid": "CORPID",
     * 	"transfer_list":[
     * 	  {
     * 		"handover_userid":"USERID",
     * 		"takeover_userid":"USERID"
     *    },
     *    {
     * 	 	"handover_userid":"USERID",
     * 		"takeover_userid":"USERID"
     *    }]
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:24
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"transfer_result":[
     *    {
     * 		"handover_userid":"USERID",
     * 		"takeover_userid":"USERID",
     * 		"errcode":0
     *    }]
     * }
     */
    @PostMapping(value = LICENSE + "/batch_transfer_license", consumes = MediaType.ALL_VALUE)
    JSONObject batchTransferLicense(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                    @RequestBody JSONObject jsonObject);

    /**
     * description : 分配激活码给下游/下级企业 <a href="https://developer.work.weixin.qq.com/document/path/96059">...</a>
     * @title: batchShareActiveCode
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject
     * {
     * 	"from_corpid": "FROMCORPID",
     * 	"to_corpid": "ToCORPID",
     * 	"share_list":[
     * 	    {
     * 		"active_code":"ACTIVE_CODE1"
     *    },
     *    {
     * 	 	"active_code":"ACTIVE_CODE2"
     *    }],
     * 	"corp_link_type": 0
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:25
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"share_result":[
     * 	    {
     * 		"active_code": "ACTIVE_CODE",
     * 		"errcode": 700100,
     * 		"errmsg": "error"
     *    }]
     * }
     */
    @PostMapping(value = LICENSE + "/batch_share_active_code", consumes = MediaType.ALL_VALUE)
    JSONObject batchShareActiveCode(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                    @RequestBody JSONObject jsonObject);

    /**
     * description : 获取应用的接口许可状态 <a href="https://developer.work.weixin.qq.com/document/path/97194">...</a>
     * @title: getAppLicenseInfo
     * @param: providerAccessToken 应用服务提供商的接口调用凭证
     * @param: jsonObject
     * {
     * 	"corpid" : "xxxx",
     * 	"suite_id": "xxxx",
     * 	"appid": 1
     * }
     * <AUTHOR>
     * @date 2025/7/13 14:31
     * @return JSONObject
     * {
     * 	"errcode":0,
     * 	"errmsg":"ok",
     * 	"license_status":1,
     * 	"trail_info":
     * 	    {
     * 		"start_time": **********,
     * 		"end_time": **********
     *    },
     * 	"license_check_time": **********
     * }
     */
    @PostMapping(value = LICENSE + "/get_app_license_info", consumes = MediaType.ALL_VALUE)
    JSONObject getAppLicenseInfo(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                 @RequestBody JSONObject jsonObject);

    /**
     * description : 设置企业的许可自动激活状态 <a href="https://developer.work.weixin.qq.com/document/path/97199">...</a>
     * @title: setAutoActiveStatus
     * @param: providerAccessToken
     * @param: jsonObject
     * {
     * 	"corpid":"CORPID1",
     * 	"auto_active_status":1
     * }
     * <AUTHOR>
     * @date 2025/7/17 10:27
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok"
     * }
     */
    @PostMapping(value = LICENSE + "/set_auto_active_status", consumes = MediaType.ALL_VALUE)
    JSONObject setAutoActiveStatus(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                   @RequestBody JSONObject jsonObject);

    /**
     * description : 查询企业的许可自动激活状态 <a href="https://developer.work.weixin.qq.com/document/path/97200">...</a>
     * @title: getAutoActiveStatus
     * @param: providerAccessToken
     * @param: jsonObject
     * {
     * 	"corpid": "CORPID1"
     * }
     * <AUTHOR>
     * @date 2025/7/17 10:28
     * @return JSONObject
     * {
     * 	"errcode": 0,
     * 	"errmsg": "ok",
     * 	"auto_active_status":1
     * }
     * auto_active_status	许可自动激活状态。0：关闭，1：打开
     */
    @PostMapping(value = LICENSE + "/get_auto_active_status", consumes = MediaType.ALL_VALUE)
    JSONObject getAutoActiveStatus(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                   @RequestBody JSONObject jsonObject);
}