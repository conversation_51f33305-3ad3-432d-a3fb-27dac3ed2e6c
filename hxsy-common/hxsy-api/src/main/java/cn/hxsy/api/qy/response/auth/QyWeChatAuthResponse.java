
package cn.hxsy.api.qy.response.auth;

import cn.hxsy.api.qy.QyBaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
* @description: 企业微信授权响应实体
 * 需要缓存access_token，用于后续接口的调用（注意：不能频繁调用gettoken接口，否则会受到频率拦截）。当access_token失效或过期时，需要重新获取。
 * access_token的有效期通过返回的expires_in来传达，正常情况下为7200秒（2小时）。
 * 企业微信可能会出于运营需要，提前使access_token失效，开发者应实现access_token失效时重新获取的逻辑。
* @author: xiaQL
* @date: 2025/6/17 10:34
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "企业微信授权响应实体", description = "企业微信授权响应实体")
@ToString
public class QyWeChatAuthResponse extends QyBaseResponse {

    @ApiModelProperty(value = "访问令牌")
    private String access_token;

    @ApiModelProperty(value = "服务商的access_token")
    private String provider_access_token;

    @ApiModelProperty(value = "第三方或者代开发应用模板access_token,最长为512字节")
    private String suite_access_token;

    @ApiModelProperty(value = "访问令牌过期时间（秒）")
    private Integer expires_in;

}