package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 获取第三方应用凭证请求实体
 */
@Data
@ApiModel(value = "SuiteTokenRequest", description = "获取第三方应用凭证请求")
public class SuiteTokenRequest implements Serializable {

    @ApiModelProperty(value = "第三方应用的suite_id")
    private String suite_id;

    @ApiModelProperty(value = "第三方应用的secret")
    private String suite_secret;

    @ApiModelProperty(value = "企业微信推送的ticket")
    private String suite_ticket;
} 