package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/04/04
 */
@Data
@ApiModel("公司注册邀请链接请求体")
public class CompanyUrlRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("小程序appId")
    private String appId;

    @ApiModelProperty("总部id")
    private Integer headquartersId;

    @ApiModelProperty("栏目id")
    private Integer columnId;

    @ApiModelProperty("公司id")
    private Integer companyId;

}
