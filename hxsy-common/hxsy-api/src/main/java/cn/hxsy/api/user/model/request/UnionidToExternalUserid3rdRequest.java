package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * 企业微信第三方unionid转external_userid请求实体
 */
@Data
@ApiModel(value = "UnionidToExternalUserid3rdRequest", description = "企业微信第三方unionid转external_userid请求")
public class UnionidToExternalUserid3rdRequest implements Serializable {
    /**
     * 第三方主体下的unionid，必填
     */
    @ApiModelProperty(value = "第三方主体下的unionid，必填")
    private String unionid;
    /**
     * unionid对应的openid，非必填
     */
    @ApiModelProperty(value = "unionid对应的openid，非必填")
    private String openid;
    /**
     * 企业ID，非必填
     */
    @ApiModelProperty(value = "企业ID，非必填")
    private String corpid;
} 