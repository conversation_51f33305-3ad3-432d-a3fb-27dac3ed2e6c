package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CampPeriodCourseResponse对象", description = "课程营期信息响应")
public class CampPeriodCourseResponse extends BaseEntityResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "训练营分组")
    private Long companyId;

    @ApiModelProperty(value = "营期名称")
    private String campperiodName;

    @ApiModelProperty(value = "排序")
    private Integer orderNumber;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

}
