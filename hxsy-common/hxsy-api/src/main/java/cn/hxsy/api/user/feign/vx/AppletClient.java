package cn.hxsy.api.user.feign.vx;

import cn.hxsy.api.user.model.request.AppletTokenReq;
import cn.hxsy.api.user.model.request.QRCodeRequest;
import cn.hxsy.api.user.model.response.AppletInfoResponse;
import cn.hxsy.api.user.model.response.AppletTokenResponse;
import cn.hxsy.api.user.model.response.WeChatAuthResponse;
import cn.hxsy.api.user.model.response.WeChatUserInfoResponse;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(
        url = "${feign.provider.applet.url}",
        name = "${feign.provider.applet.id}",
        contextId = "applet",
        configuration = DisableLoadBalanceConfiguration.class
)
public interface AppletClient {

    String TK = "/cgi-bin";
    String WXA_SCHEME = "/wxa/generatescheme";
    String WXA_CODE = "/wxa/getwxacode";
    String WXA_CODE_UNLIMITED = "/wxa/getwxacodeunlimit";
    String JSCODE_2_SESSION = "/sns/jscode2session";

    // 公众号 用code换取access_token
    String SNS_OAUTH_2_ACCESS_TOKEN = "/sns/oauth2/access_token";
    // 公众号 用access_token换取用户信息
    String SNS_USERINFO = "/sns/userinfo";

    /**
     * 生成小程序码（适用于数量较少场景）
     * 官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getQRCode.html
     *
     * @param accessToken 接口调用凭证
     * @param request 请求参数：
     *                     - path（必填）扫码进入的页面路径
     *                     - width 二维码宽度（默认430px）
     *                     - auto_color 是否自动配置线条颜色
     *                     - line_color 自定义线条颜色（格式：{"r":"0","g":"0","b":"0"}）
     *                     - is_hyaline 是否需要透明底色
     * @return 图片二进制数据（PNG格式）
     */
    @PostMapping(value = WXA_CODE, consumes = MediaType.APPLICATION_JSON_VALUE)
    byte[] getQRCode(
            @RequestParam("access_token") String accessToken,
            @RequestBody QRCodeRequest request);

    /**
     * 生成无限制数量的小程序码（推荐）
     * 官方文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/qrcode-link/qr-code/getUnlimitedQRCode.html
     *
     * @param accessToken 接口调用凭证
     * @param requestBody 请求参数：
     *                     - scene（必填）最大32个字符的场景值
     *                     - page 默认进入的页面路径
     *                     - width 二维码宽度（默认430px）
     *                     - check_path 检查页面是否存在（默认true）
     *                     - env_version 要打开的小程序版本（release/trial/develop）
     * @return 图片二进制数据（PNG格式）
     */
    @PostMapping(value = WXA_CODE_UNLIMITED, consumes = MediaType.APPLICATION_JSON_VALUE)
    byte[] getUnlimitedQRCode(
            @RequestParam("access_token") String accessToken,
            @RequestBody JSONObject requestBody);


    /**
     * 通过授权码获取小程序用户数据
     *
     * @param appId
     * @param secret
     * @param grantType
     */
    @GetMapping(value = TK + "/token", consumes = MediaType.ALL_VALUE)
    AppletTokenResponse accessToken(@RequestParam(name = "appid") String appId,
                                    @RequestParam(name = "secret") String secret,
                                    @RequestParam(name = "grant_type") String grantType);

    /**
     * 获取小程序稳定版接口调用凭据
     *
     * @param request
     */
    @PostMapping(value = TK + "/stable_token", consumes = MediaType.ALL_VALUE)
    AppletTokenResponse stableAccessToken(@RequestBody AppletTokenReq request);

    /**
     * 通过accessToken获取URLScheme
     *
     * @param accessToken
     * @param jumpWxa
     * @param isExpire
     * @param expireType
     * @param expireInterval
     */
    @PostMapping(value = WXA_SCHEME, consumes = MediaType.ALL_VALUE)
    String urlScheme(@RequestParam(name = "access_token") String accessToken,
            @RequestBody JSONObject jumpWxa,
            @RequestParam(name = "is_expire") Boolean isExpire,
            @RequestParam(name = "expire_type") Long expireType,
            @RequestParam(name = "expire_interval") Long expireInterval);

    /**
     * 小程序登录
     * @param appId 小程序对应appId
     * @param secret 小程序对应秘钥
     * @param code 小程序wx.login获取的code，5分钟有效
     * @param grantType 固定为“authorization_code”
     */
    @GetMapping(value = JSCODE_2_SESSION, consumes = MediaType.ALL_VALUE)
    AppletInfoResponse jscode2session(@RequestParam(name = "appid") String appId,
                                      @RequestParam(name = "secret") String secret,
                                      @RequestParam(name = "js_code") String code,
                                      @RequestParam(name = "grant_type") String grantType);

    @GetMapping(value = SNS_OAUTH_2_ACCESS_TOKEN, consumes = MediaType.ALL_VALUE)
    WeChatAuthResponse snsOauth2AccessToken(@RequestParam(name = "appid") String appId,
                                            @RequestParam(name = "secret") String secret,
                                            @RequestParam(name = "code") String code,
                                            @RequestParam(name = "grant_type") String grantType);

    @GetMapping(value = SNS_USERINFO, consumes = MediaType.ALL_VALUE)
    WeChatUserInfoResponse snsUserinfo(@RequestParam(name = "access_token") String accessToken,
                                       @RequestParam(name = "openid") String openid,
                                       @RequestParam(name = "lang") String lang);
}