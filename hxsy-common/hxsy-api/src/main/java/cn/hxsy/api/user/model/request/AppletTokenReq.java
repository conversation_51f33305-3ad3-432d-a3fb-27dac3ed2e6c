package cn.hxsy.api.user.model.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 微信小程序token请求体

 */
@Data
public class AppletTokenReq implements Serializable {

    /**
     * 填写 client_credential
     */
    @JsonProperty("grant_type")
    private String grantType;

    /**
     * 账号唯一凭证，即 AppID，可在「微信公众平台 - 设置 - 开发设置」页中获得。（需要已经成为开发者，且账号没有异常状态）
     */
    @JsonProperty("appid")
    private String appId;

    /**
     * 账号唯一凭证密钥，即 AppSecret，获取方式同 appid
     */
    private String secret;

    /**
     * 默认使用 false。
     * 1. force_refresh = false 时为普通调用模式，access_token 有效期内重复调用该接口不会更新 access_token；
     * 2. 当force_refresh = true 时为强制刷新模式，会导致上次获取的 access_token 失效，并返回新的 access_token
     */
    @JsonProperty("force_refresh")
    private boolean forceRefresh;
}
