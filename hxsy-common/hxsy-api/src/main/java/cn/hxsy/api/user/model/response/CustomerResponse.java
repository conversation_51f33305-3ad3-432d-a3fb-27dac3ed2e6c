package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户响应实体
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel("客户响应实体")
public class CustomerResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty(value = "客户头像URL")
    private String avatarUrl;

    @ApiModelProperty(value = "微信昵称")
    private String nickname;

    @ApiModelProperty("性别（0-未知 1-男 2-女）")
    private Integer gender;

    @ApiModelProperty(value = "最近活跃时间")
    private LocalDateTime lastActiveTime;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "用户类型")
    private String userType;

    @ApiModelProperty(value = "请求ID")
    private String unionId;

    @ApiModelProperty(value = "是否已完成注册 1-已完成")
    private String isRegister;
} 