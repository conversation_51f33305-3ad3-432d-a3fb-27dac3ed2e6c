package cn.hxsy.api.qy.feign.auth;

import cn.hxsy.api.qy.request.auth.QyWechatAuthReq;
import cn.hxsy.api.qy.response.QyAppResponse;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthSecretResponse;
import cn.hxsy.api.user.feign.vx.DisableLoadBalanceConfiguration;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
* @description: 企业-代理服务商-相关鉴权请求接口
* @author: xiaQL
* @date: 2025/6/16 10:52
*/
@FeignClient(
        url = "${feign.provider.qyWx.url}" + QyWxAuthClient.TK,
        name = "${feign.provider.qyWx.auth.id}",
        configuration = DisableLoadBalanceConfiguration.class
)
public interface QyWxAuthClient {

    String TK = "/cgi-bin";

    String SERVICE = "/service";

    String MINI = "/miniprogram";

    /**
     * 企微代开发小程序登录
     * @param suiteAccessToken 第三方应用凭证，获取方法见获取第三方应用凭证。要求必须由该小程序关联的第三方应用的secret获取
     * @param code 小程序wx.qy.login获取的code，5分钟有效
     * @param grantType 固定为"authorization_code"
     */
    @GetMapping(value = SERVICE + MINI + "/jscode2session", consumes = MediaType.ALL_VALUE)
    QyAppResponse jscode2session(@RequestParam(name = "suite_access_token") String suiteAccessToken,
                                 @RequestParam(name = "js_code") String code,
                                 @RequestParam(name = "grant_type") String grantType);

    /**
     * 获取服务商凭证
     * 所有的企微内部涉及接口，都需要此标识，默认有效期是2小时，看样子是大家都能一起公用的，直接缓存
     *  { "corpid":"xxxxx", "provider_secret":"xxx" }
     *  corpid 服务商的corpid
     *  provider_secret 服务商的secret，在服务商管理后台可见
     */
    @PostMapping(value = SERVICE + "/get_provider_token", consumes = MediaType.ALL_VALUE)
    QyWeChatAuthResponse getProviderToken(JSONObject json);

    /**
     * 获取代开发应用模板凭证
     * @param req 获取代开发应用模板凭证请求体
     */
    @PostMapping(value = SERVICE + "/get_suite_token", consumes = MediaType.ALL_VALUE)
    QyWeChatAuthResponse getSuiteToken(@RequestBody QyWechatAuthReq req);

    /**
     * 代开发授权应用secret的获取
     * 主要是为了返回对象中的permanent_code，等同于自建应用中的应用凭证密钥corpsecret，也需要落库存储咯
     * @param suiteAccessToken 代开发应用模板凭证
     * @param req 其实只要里面的auth_code
     */
    @PostMapping(value = SERVICE + "/v2/get_permanent_code", consumes = MediaType.ALL_VALUE)
    QyWeChatAuthSecretResponse getPermanentCode(@RequestParam(name = "suite_access_token") String suiteAccessToken,
                                                @RequestBody QyWechatAuthReq req);

}