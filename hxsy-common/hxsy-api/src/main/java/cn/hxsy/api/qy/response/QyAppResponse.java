package cn.hxsy.api.qy.response;

import cn.hxsy.api.qy.QyBaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
* @description: 企业内应用开发-自建小程序登录响应实体
* @author: xiaQL
* @date: 2025/6/17 11:22
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "企微内小程序登录响应实体", description = "企微内小程序登录响应实体")
@ToString
public class QyAppResponse extends QyBaseResponse {

    @ApiModelProperty(value = "访问令牌")
    String session_key;

    @ApiModelProperty(value = "用户在企业内的UserID，对应管理端的账号，企业内唯一。注意：如果用户所在企业并没有安装此小程序应用，则返回加密的userid")
    String userid;

    @ApiModelProperty(value = "全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节；同一用户，对于不同服务商open_userid是不同的")
    String open_userid;

    @ApiModelProperty(value = "用户所属企业的corpid")
    String corpid;


}
