package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel("客户查询请求")
public class CustomerBatchRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("客户ID集合")
    private List<Long> customerIds;

    @ApiModelProperty(value = "账号禁用状态（0-否/1-是）")
    private Integer forbiddenStatus;

    @ApiModelProperty(value = "红包禁用状态（0-否/1-是）")
    private Integer redPacketStatus;

} 