package cn.hxsy.api.qy.request.auth;

import lombok.Data;
import lombok.ToString;

/**
* @description: 获取代开发应用模板凭证请求体
* @author: xiaQL
* @date: 2025/6/28 16:13
*/
@Data
@ToString
public class QyWechatAuthReq {

    /*
     * 获取服务商凭证
     * 第三方应用id或者代开发应用模板id。第三方应用以ww或wx开头应用id（对应于旧的以tj开头的套件id）；代开发应用以dk开头
     */
    private String suite_id;

    /*
     * 获取服务商凭证
     * 第三方应用secret 或者代开发应用模板secret
     */
    private String suite_secret;

    /*
     * 获取服务商凭证
     * 企业微信后台推送的ticket
     */
    private String suite_ticket;

    /*
     * 代开发授权应用secret的获取
     * 临时授权码，会在授权成功时附加在redirect_uri中跳转回第三方服务商网站，或通过授权成功通知回调推送给服务商。长度为64至512个字节
     */
    private String auth_code;

    /*
     * 代开发授权应用secret的获取企业凭证
     * 授权方corpid
     */
    private String auth_corpid;

    /*
     * 代开发授权应用secret的获取企业凭证
     * 永久授权码，通过get_permanent_code获取
     */
    private String permanent_code;

}
