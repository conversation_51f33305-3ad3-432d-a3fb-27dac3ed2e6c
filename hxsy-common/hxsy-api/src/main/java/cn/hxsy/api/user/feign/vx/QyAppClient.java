package cn.hxsy.api.user.feign.vx;

import cn.hxsy.api.qy.request.QyUserReq;
import cn.hxsy.api.qy.request.auth.QyWechatAuthReq;
import cn.hxsy.api.qy.response.QyAppResponse;
import cn.hxsy.api.qy.response.auth.QyWeChatAuthResponse;
import cn.hxsy.api.qy.response.QyWeChatQueryDeptResponse;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import static cn.hxsy.api.user.feign.vx.QyAppClient.TK;

/**
* @description: 企业内应用开发-相关请求接口
* @author: xiaQL
* @date: 2025/6/16 10:52
*/
@FeignClient(
        url = "${feign.provider.qy.url}" + TK,
        name = "${feign.provider.qy.id}",
        contextId = "qy",
        configuration = DisableLoadBalanceConfiguration.class
)
public interface QyAppClient {

    String TK = "/cgi-bin";

    String SERVICE = "/service";

    String MINI = "/miniprogram";

    String DEPT = "/department";

    String USER = "/user";

    String EXTERNALCONTACT = "/externalcontact";

    String IDCONVERT = "/idconvert";



    /**
     * 企微内部请求权限头
     * 所有的企微内部涉及接口，都需要此标识，默认有效期是2小时，看样子是大家都能一起公用的，直接缓存
     * @param corpId 企业ID（每个企业都拥有唯一的corpid，获取此信息可在管理后台"我的企业"－"企业信息"下查看"企业ID"（需要有管理员权限））
     *               （企微内用户登录也会返回）
     * @param corpSecret 应用的凭证密钥，注意应用需要是启用状态（在管理后台->"应用管理"->"应用"->"自建"，点进某个应用，即可看到。）
     */
    @GetMapping(value = "/gettoken", consumes = MediaType.ALL_VALUE)
    QyWeChatAuthResponse getToken(@RequestParam(name = "corpid") String corpId,
                                  @RequestParam(name = "corpsecret") String corpSecret);

    /**
     * 获取企业凭证，第三方服务商在取得企业的永久授权码后，通过此接口可以获取到企业的access_token（等同于上面的企微内部自建应用获取accessToken）。
     * 企微内部通讯录相关接口，都需要此标识，默认有效期是2小时
     * @param suiteAccessToken 企业服务商预授权码
     * @param qyWechatAuthReq 只需要其中的auth_corpid、permanent_code
     */
    @GetMapping(value = SERVICE + "/get_corp_token", consumes = MediaType.ALL_VALUE)
    QyWeChatAuthResponse getCorpToken(@RequestParam(name = "suite_access_token") String suiteAccessToken,
                                  @RequestBody QyWechatAuthReq qyWechatAuthReq);

    /**
     * 企微客户端小程序登录
     * @param accessToken 企业微信应用对应的access_token，需要先通过上面的getToken获取
     * @param code 小程序wx.qy.login获取的code，5分钟有效
     * @param grantType 固定为"authorization_code"
     */
    @GetMapping(value = SERVICE + MINI + "/jscode2session", consumes = MediaType.ALL_VALUE)
    QyAppResponse jscode2session(@RequestParam(name = "access_token") String accessToken,
                                 @RequestParam(name = "js_code") String code,
                                 @RequestParam(name = "grant_type") String grantType);

    /**
     * 企微服务端获取部门列表
     * 原本list的接口从2022年8月15日不能再调用，需要换新的"/simplelist"
     * 返回的数据少了一点，不够没影响我只需要部门id集合来同步底下的企业成员
     * @param accessToken 企业微信应用对应的access_token，需要先通过上面的getToken获取
     * @param deptId 部门id。获取指定部门及其下的子部门
     */
    @GetMapping(value = DEPT + "/simplelist", consumes = MediaType.ALL_VALUE)
    QyWeChatQueryDeptResponse deptList(@RequestParam(name = "access_token") String accessToken,
                                       @RequestParam(name = "id", required = false) String deptId);

    /**
     * 企微服务端获取成员列表
     * 1、原本list的接口从2022年8月15日开始都不能再调用，现在直接返回员工id与对应部门id
     * 2、只能先同步了部门数据以后再把人员划分到对应部门下
     * @param accessToken 企业微信应用对应的access_token，需要先通过上面的getToken获取
     */
    @PostMapping(value = USER + "/list_id", consumes = MediaType.ALL_VALUE)
    QyWeChatQueryDeptResponse userList(@RequestParam(name = "access_token") String accessToken,
                                       @RequestBody QyUserReq qyUserReq);

    /**
     * description : 用于将企业主体的明文corpid转换为服务商主体的密文corpid。
     * <a href="https://developer.work.weixin.qq.com/document/path/97105">...</a>
     * @title: corpidToOpencorpid
     * @param: providerAccessToken 应用服务商的provider_access_token，获取方法参见服务商的凭证
     * @param: json  { "corpid":"xxxxx" }待获取的企业ID
     * <AUTHOR>
     * @date 2025/6/29 11:22
     * @return JSONObject ｛ "errcode":0, "errmsg":"ok", "open_corpid":"AAAAAA" ｝
     */
    @PostMapping(value = SERVICE + "/corpid_to_opencorpid", consumes = MediaType.ALL_VALUE)
    JSONObject corpidToOpencorpid(@RequestParam(name = "provider_access_token") String providerAccessToken,
                                  @RequestBody JSONObject json);



    /**
     * description : unionid转换为第三方external_userid
     * @title: unionidToExternalUserid
     * @param: accessToken
     * @param: json { "unionid":"oAAAAAAA", "openid":"oBBBB", "subject_type":1 }
     * unionid	是	微信客户的unionid
     * openid	是	微信客户的openid
     * subject_type	否	小程序或公众号的主体类型：
     * 0表示主体名称是企业的 (默认)，
     * 1表示主体名称是服务商的
     * <AUTHOR>
     * @date 2025/6/30 21:07
     * @return JSONObject
     */
    @PostMapping(value = IDCONVERT + "/unionid_to_external_userid", consumes = MediaType.ALL_VALUE)
    JSONObject unionidToExternalUserid(@RequestParam(name = "access_token") String accessToken,
                                       @RequestBody JSONObject json);

    /**
     * description : external_userid查询pending_id
     * @title: external_userid_to_pending_id
     * @param: accessToken
     * @param: json { "chat_id":"xxxxxx", "external_userid":["oAAAAAAA", "oBBBBB"] }
     * <AUTHOR>
     * @date 2025/7/3 23:41
     * @return JSONObject
     * ｛
     *  "errcode":0,
     *  "errmsg":"ok",
     *  "result":[{"external_userid":"oAAAAAAA","pending_id":"pAAAAA"},
     *            {"external_userid":"oBBBBB","pending_id":"pBBBBB"}
     *           ]
     * ｝
     */
    @PostMapping(value = IDCONVERT + "/batch/external_userid_to_pending_id", consumes = MediaType.ALL_VALUE)
    JSONObject externalUseridToPendingId(@RequestParam(name = "access_token") String accessToken,
                                             @RequestBody JSONObject json);

    /**
     * description : 将企业主体下的external_userid转换为服务商主体下的external_userid。
     * @title: getNewExternalUserid
     * @param: accessToken
     * @param: json { "external_userid_list":["xxxxx","yyyyyy"] }
     * <AUTHOR>
     * @date 2025/7/3 23:37
     * @return JSONObject
     * ｛"errcode":0,
     *  "errmsg":"ok",
     *  "items":[{"external_userid":"xxxxx","new_external_userid":"AAAA"},
     *          {"external_userid":"yyyyy","new_external_userid":"BBBB"}
     *          ]
     * ｝
     */
    @PostMapping(value = EXTERNALCONTACT + "/get_new_external_userid", consumes = MediaType.ALL_VALUE)
    JSONObject getNewExternalUserid(@RequestParam(name = "access_token") String accessToken,
                                    @RequestBody JSONObject json);

}