package cn.hxsy.api.qy.feign.contract;

import cn.hxsy.api.qy.request.QyUserReq;
import cn.hxsy.api.qy.response.contact.QyContactResponse;
import cn.hxsy.api.user.feign.vx.DisableLoadBalanceConfiguration;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
* @description: 企业-代理服务商-相关通讯录请求接口
* @author: xiaQL
* @date: 2025/6/16 10:52
*/
@FeignClient(
        url = "${feign.provider.qyWx.url}" + QyWxContactClient.TK,
        name = "${feign.provider.qyWx.contact.id}",
        configuration = DisableLoadBalanceConfiguration.class
)
public interface QyWxContactClient {

    String TK = "/cgi-bin";

    String DEPT = "/department";

    String USER = "/user";

    String BATCH = "/batch";

    String EXTERNALCONTACT = "/externalcontact";

    /**
     * 企微服务端获取子部门ID列表
     * @param accessToken 企业微信应用对应的access_token，需要先通过auth请求类下的getToken获取
     * @param deptId 部门id。获取指定部门及其下的子部门
     */
    @GetMapping(value = DEPT + "/simplelist", consumes = MediaType.ALL_VALUE)
    QyContactResponse deptList(@RequestParam(name = "access_token") String accessToken,
                               @RequestParam(name = "id", required = false) String deptId);

    /**
     * 企微服务端获取部门成员
     * @param accessToken 企业微信应用对应的access_token，需要先通过auth请求类下的getToken获取
     */
    @PostMapping(value = USER + "/simplelist", consumes = MediaType.ALL_VALUE)
    QyContactResponse userList(@RequestParam(name = "access_token") String accessToken,
                                       @RequestParam(name = "department_id") String deptId);

    /**
     * 企微服务端获取成员列表
     * 1、原本list的接口从2022年8月15日开始都不能再调用，现在直接返回员工id与对应部门id
     * 2、只能先同步了部门数据以后再把人员划分到对应部门下
     * @param accessToken 企业微信应用对应的access_token，需要先通过上面的getToken获取
     */
    @PostMapping(value = USER + "/list_id", consumes = MediaType.ALL_VALUE)
    QyContactResponse userListId(@RequestParam(name = "access_token") String accessToken,
                                       @RequestBody QyUserReq qyUserReq);

    /**
     * 企微服务商-openUserId转换为userId
     * @param accessToken 企业微信应用对应的access_token，需要先通过auth接口下的getToken获取
     */
    @PostMapping(value = BATCH + "/openuserid_to_userid", consumes = MediaType.ALL_VALUE)
    QyContactResponse openUserToUser(@RequestParam(name = "access_token") String accessToken,
                                 @RequestBody QyUserReq qyUserReq);

    /**
     * 企微服务商-openUserId转换为userId
     * @param accessToken 企业微信应用对应的access_token，需要先通过auth接口下的getToken获取
     */
    @PostMapping(value = BATCH + "/userid_to_openuserid", consumes = MediaType.ALL_VALUE)
    QyContactResponse userToOpenUser(@RequestParam(name = "access_token") String accessToken,
                                     @RequestBody QyUserReq qyUserReq);

    /**
     * description : 配置客户联系「联系我」方式 <a href="https://developer.work.weixin.qq.com/document/path/92228#%E9%85%8D%E7%BD%AE%E5%AE%A2%E6%88%B7%E8%81%94%E7%B3%BB%E3%80%8C%E8%81%94%E7%B3%BB%E6%88%91%E3%80%8D%E6%96%B9%E5%BC%8F">...</a>
     * @title: addContactWay
     * @param: accessToken
     * @param: jsonObject
     * {"type":1,"scene":1,"style":1,"remark":"渠道客户","skip_verify":true,"state":"teststate","user":["zhangsan","lisi","wangwu"],"party":[2,3],"is_temp":true,"expires_in":86400,"chat_expires_in":86400,"unionid":"oxTWIuGaIt6gTKsQRLau2M0AAAA","is_exclusive":true,"conclusions":{"text":{"content":"文本消息内容"},"image":{"media_id":"MEDIA_ID"},"link":{"title":"消息标题","picurl":"https://example.pic.com/path","desc":"消息描述","url":"https://example.link.com/path"},"miniprogram":{"title":"消息标题","pic_media_id":"MEDIA_ID","appid":"wx8bd80126147dfAAA","page":"/path/index.html"}}}
     * <AUTHOR>
     * @date 2025/7/15 0:02
     * @return JSONObject
     * {
     *    "errcode": 0,
     *    "errmsg": "ok",
     *    "config_id":"42b34949e138eb6e027c123cba77fAAA",
     *    "qr_code":"https://p.qpic.cn/wwhead/duc2TvpEgSdicZ9RrdUtBkv2UiaA/0"
     * }
     * errcode	返回码
     * errmsg	对返回码的文本描述内容
     * config_id	新增联系方式的配置id
     * qr_code	联系我二维码链接，仅在scene为2时返回
     */
    @PostMapping(value = EXTERNALCONTACT + "/add_contact_way", consumes = MediaType.ALL_VALUE)
    JSONObject addContactWay(@RequestParam(name = "access_token") String accessToken,
                             @RequestBody JSONObject jsonObject);

    /**
     * description : 获取企业已配置的「联系我」方式 <a href="https://developer.work.weixin.qq.com/document/path/92228#%E8%8E%B7%E5%8F%96%E4%BC%81%E4%B8%9A%E5%B7%B2%E9%85%8D%E7%BD%AE%E7%9A%84%E3%80%8C%E8%81%94%E7%B3%BB%E6%88%91%E3%80%8D%E6%96%B9%E5%BC%8F">...</a>
     * @title: getContactWay
     * @param: accessToken
     * @param: jsonObject
     * {
     *    "config_id":"42b34949e138eb6e027c123cba77fad7"
     * }
     * <AUTHOR>
     * @date 2025/7/15 0:05
     * @return JSONObject
     * {"errcode":0,"errmsg":"ok","contact_way":{"config_id":"42b34949e138eb6e027c123cba77fAAA","type":1,"scene":1,"style":2,"remark":"test remark","skip_verify":true,"state":"teststate","qr_code":"https://p.qpic.cn/wwhead/duc2TvpEgSdicZ9RrdUtBkv2UiaA/0","user":["zhangsan","lisi","wangwu"],"party":[2,3],"is_temp":true,"expires_in":86400,"chat_expires_in":86400,"unionid":"oxTWIuGaIt6gTKsQRLau2M0AAAA","conclusions":{"text":{"content":"文本消息内容"},"image":{"pic_url":"https://p.qpic.cn/pic_wework/XXXXX"},"link":{"title":"消息标题","picurl":"https://example.pic.com/path","desc":"消息描述","url":"https://example.link.com/path"},"miniprogram":{"title":"消息标题","pic_media_id":"MEDIA_ID","appid":"wx8bd80126147dfAAA","page":"/path/index"}}}}
     */
    @PostMapping(value = EXTERNALCONTACT + "/get_contact_way", consumes = MediaType.ALL_VALUE)
    JSONObject getContactWay(@RequestParam(name = "access_token") String accessToken,
                             @RequestBody JSONObject jsonObject);

    /**
     * description : 获取企业已配置的「联系我」列表 <a href="https://developer.work.weixin.qq.com/document/path/92228#%E8%8E%B7%E5%8F%96%E4%BC%81%E4%B8%9A%E5%B7%B2%E9%85%8D%E7%BD%AE%E7%9A%84%E3%80%8C%E8%81%94%E7%B3%BB%E6%88%91%E3%80%8D%E5%88%97%E8%A1%A8">...</a>
     * @title: listContactWay
     * @param: accessToken
     * @param: jsonObject
     * <AUTHOR>
     * @date 2025/7/15 0:07
     * @return JSONObject
     */
    @PostMapping(value = EXTERNALCONTACT + "/list_contact_way", consumes = MediaType.ALL_VALUE)
    JSONObject listContactWay(@RequestParam(name = "access_token") String accessToken,
                              @RequestBody JSONObject jsonObject);

    /**
     * description : 更新企业已配置的「联系我」方式 <a href="https://developer.work.weixin.qq.com/document/path/92228#%E6%9B%B4%E6%96%B0%E4%BC%81%E4%B8%9A%E5%B7%B2%E9%85%8D%E7%BD%AE%E7%9A%84%E3%80%8C%E8%81%94%E7%B3%BB%E6%88%91%E3%80%8D%E6%96%B9%E5%BC%8F">...</a>
     * @title: updateContactWay
     * @param: accessToken
     * @param: jsonObject
     * <AUTHOR>
     * @date 2025/7/15 0:08
     * @return JSONObject
     */
    @PostMapping(value = EXTERNALCONTACT + "/update_contact_way", consumes = MediaType.ALL_VALUE)
    JSONObject updateContactWay(@RequestParam(name = "access_token") String accessToken,
                                @RequestBody JSONObject jsonObject);

    /**
     * description : 删除企业已配置的「联系我」方式 <a href="https://developer.work.weixin.qq.com/document/path/92228#%E5%88%A0%E9%99%A4%E4%BC%81%E4%B8%9A%E5%B7%B2%E9%85%8D%E7%BD%AE%E7%9A%84%E3%80%8C%E8%81%94%E7%B3%BB%E6%88%91%E3%80%8D%E6%96%B9%E5%BC%8F">...</a>
     * @title: delContactWay
     * @param: accessToken
     * @param: jsonObject
     * {
     * 	"config_id":"42b34949e138eb6e027c123cba77fAAA"
     * }
     * <AUTHOR>
     * @date 2025/7/15 0:09
     * @return JSONObject
     * {
     *    "errcode": 0,
     *    "errmsg": "ok"
     * }
     */
    @PostMapping(value = EXTERNALCONTACT + "/del_contact_way", consumes = MediaType.ALL_VALUE)
    JSONObject delContactWay(@RequestParam(name = "access_token") String accessToken,
                             @RequestBody JSONObject jsonObject);


}