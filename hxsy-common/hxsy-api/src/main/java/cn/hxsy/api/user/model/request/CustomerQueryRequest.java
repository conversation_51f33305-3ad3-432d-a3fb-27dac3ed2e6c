package cn.hxsy.api.user.model.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("客户查询请求")
public class CustomerQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("企微添加状态（0未添加/1已添加/9已删除）")
    private Integer weworkStatus;
    
    @ApiModelProperty("手机号状态（0无手机号/1有手机号）")
    private Integer mobileStatus;
    
    @ApiModelProperty("活跃行为类型")
    private String behaviorType;
    
    @ApiModelProperty("活跃行为开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activeStartTime;
    
    @ApiModelProperty("活跃行为结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activeEndTime;
    
    @ApiModelProperty("微信备注")
    private String wechatRemark;
    
    @ApiModelProperty("微信昵称")
    private String nickname;
    
    @ApiModelProperty("昵称查询类型（1-模糊查询，2-精确查询）")
    private Integer nicknameQueryType;
    
    @ApiModelProperty("客户创建时间起始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createStartTime;
    
    @ApiModelProperty("客户创建结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createEndTime;
    
    @ApiModelProperty("手机号")
    private String mobile;
    
    @ApiModelProperty("标签ID")
    private Long tagId;
    
    @ApiModelProperty("营期ID")
    private Long campPeriodId;
    
    @ApiModelProperty("训练营（公司）ID")
    private Long companyId;

    @ApiModelProperty("栏目ID")
    private Long columnId;

    @ApiModelProperty("客户id")
    private String customerId;

    @ApiModelProperty("多小程序登录关联微信号")
    private String unionId;

    @ApiModelProperty("客户id集合，用于分页查询筛选")
    private List<Long> customerIds;

    @ApiModelProperty("销售id")
    private Long salesId;

    @ApiModelProperty(value = "销售组")
    private Integer salesGroupId;

    @ApiModelProperty(value = "账号禁用状态（0-否/1-是）")
    private Integer forbiddenStatus;

    @ApiModelProperty(value = "红包禁用状态（0-否/1-是）")
    private Integer redPacketStatus;

    @ApiModelProperty("可见栏目范围 栏目、公司、销售组管理员优先使用")
    private List<String> perColumnId;

    @ApiModelProperty("可见公司范围 栏目、公司、销售组管理员优先使用")
    private List<String> perCompanyId;

    @ApiModelProperty("可见销售组范围 栏目、公司、销售组管理员优先使用")
    private List<String> perSalesGroupId;

    @ApiModelProperty("是否需要连接销售表 没有用到栏目这些组织架构时，可以不连接销售表 不为空则表示需要连接")
    private String needJoinSales;
} 