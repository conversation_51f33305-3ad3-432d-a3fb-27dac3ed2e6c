package cn.hxsy.api.user.model.request;

import cn.hxsy.base.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 客户登录请求实体
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel("客户登录请求实体")
public class CustomerLoginRequest extends BaseRequest {

    @ApiModelProperty("客户主键id")
    private Long customerId;

    @ApiModelProperty("微信unionId")
    private String unionId;

    @ApiModelProperty("微信openid")
    private String openid;
    
    @ApiModelProperty("栏目ID")
    private Long columnId;

} 