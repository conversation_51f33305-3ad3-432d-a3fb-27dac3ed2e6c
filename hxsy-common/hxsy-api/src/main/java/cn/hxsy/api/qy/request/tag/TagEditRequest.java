package cn.hxsy.api.qy.request.tag;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 编辑企业客户标签请求
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagEditRequest {

    /**
     * 标签或标签组的id
     */
    private String id;

    /**
     * 新的标签或标签组名称，最长为30个字符
     */
    private String name;

    /**
     * 标签/标签组的次序值，非必填，默认为0，排序以此值升序排列
     */
    private Integer order;

    /**
     * 标签组id，如果要修改标签所属的标签组，需要填写此参数
     */
    private String group_id;

    /**
     * 是否是标签组，默认false
     */
    private Boolean is_group;
}
