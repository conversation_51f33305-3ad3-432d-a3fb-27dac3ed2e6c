package cn.hxsy.api.user.service;

import cn.hxsy.api.user.model.request.CustomerCourseVideoRequest;
import cn.hxsy.api.user.model.request.PcLoginRequest;
import cn.hxsy.api.user.model.request.UserInfoRequest;
import cn.hxsy.api.user.model.request.UserRegisterRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;

/**
 * <AUTHOR>
 */
public interface UserInfoRpcService {

    /**
     * 当缓存查询不到时，从数据库用户信息查询
     * 需要区分业务人员与客户
     * 查询完成后，同步结果写入redis
     * @param userQueryRequest
     * @return
     */
    Result<Object> query(UserInfoRequest userQueryRequest);

    /**
     * 用户注册
     * 需要区分业务人员与客户
     * 注册完成后，同步注册信息写入redis
     * @param userRegisterRequest
     * @return
     */
    Result<Object> register(UserRegisterRequest userRegisterRequest);

    /**
     * @description: Pc端登录
     * 需要对密码进行sm4解密
     * 别把密码响应出去了
     * @author: xiaQL
     * @date: 2025/4/19 11:03
     */
    Result<SystemUserResponse> PcLogin(PcLoginRequest pcLoginRequest);

}
