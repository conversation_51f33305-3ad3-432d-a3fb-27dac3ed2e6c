package cn.hxsy.api.qy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
* @description: 企业内应用开发-企微内相关api调用响应父类
* @author: xiaQL
* @date: 2025/6/17 11:22
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "企微内相关api调用响应父类", description = "企微内相关api调用响应父类")
public class QyBaseResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "错误码")
    String errcode;

    @ApiModelProperty(value = "错误信息")
    String errmsg;

    public void checkQyResponse() {
        if (null != errcode && !errcode.equals("0")) {
            throw new RuntimeException(errmsg);
        }
    }

}
