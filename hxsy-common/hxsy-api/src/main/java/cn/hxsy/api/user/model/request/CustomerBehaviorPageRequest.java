package cn.hxsy.api.user.model.request;

import cn.hxsy.base.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("客户分配请求")
public class CustomerBehaviorPageRequest extends BaseRequest {

    @NotNull(message = "客户ID不能为空")
    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "行为类型集合")
    private List<Integer> behaviorTypes;

    @NotNull(message = "栏目ID不能为空")
    @ApiModelProperty(value = "栏目ID")
    private Long columnId;
}
