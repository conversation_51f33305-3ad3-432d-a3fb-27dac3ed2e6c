package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CourseVideoResponse响应实体", description = "课程视频信息")
public class CourseVideoResponse extends BaseEntityResponse {


    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程分组")
    private Long groupId;

    @ApiModelProperty(value = "课程封面")
    private String coverPath;

    @ApiModelProperty(value = "课程封面id")
    private String coverId;

    @ApiModelProperty(value = "课程简介")
    private String courseIntroduction;

    @ApiModelProperty(value = "课程介绍")
    private String courseDescription;

    @ApiModelProperty(value = "课程内容")
    private String courseContent;

    @ApiModelProperty(value = "课程开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startTime;

    @ApiModelProperty(value = "视频前贴片")
    private String patchPath;

    @ApiModelProperty(value = "片头设置")
    private String openingSetting;

    @ApiModelProperty(value = "全屏观看设置")
    private String fullscreenSetting;

    @ApiModelProperty(value = "课程视频")
    private String videoPath;

    @ApiModelProperty(value = "课程视频")
    private Long videoId;

    @ApiModelProperty(value = "所属流量池")
    private List<String> coursePool;

    @ApiModelProperty(value = "项目")
    private String courseProject;

    @ApiModelProperty(value = "来源")
    private String courseSource;

    @ApiModelProperty(value = "标签")
    private List<String> courseTag;

    @ApiModelProperty(value = "课程价格")
    private BigDecimal coursePrice;

    @ApiModelProperty(value = "划线价格")
    private BigDecimal underlinedPrice;

    @ApiModelProperty(value = "上架设置")
    private String courseStatus;

    @ApiModelProperty(value = "课程考题")
    private String courseExam;

    @ApiModelProperty(value = "奖励形式")
    private String rewardForm;

    @ApiModelProperty(value = "现金形式")
    private String cashForm;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "奖励积分")
    private Integer rewardsPoints;

    @ApiModelProperty(value = "错误次数限制")
    private Integer errorLimit;

    @ApiModelProperty(value = "排序")
    private Integer orderNumber;

    @ApiModelProperty(value = "售卖方式")
    private String courseSalesmethod;

    @ApiModelProperty(value = "扩展字段1")
    private String field1;

    @ApiModelProperty(value = "扩展字段2")
    private String field2;

    @ApiModelProperty(value = "扩展字段3")
    private String field3;

    @ApiModelProperty(value = "扩展字段4")
    private String field4;

    @ApiModelProperty(value = "扩展字段5")
    private String field5;

    @ApiModelProperty(value = "活动信息")
    private String activityInfo;

    @ApiModelProperty(value = "营期红包开关：true-开启，false-关闭")
    private Boolean campperiodRedPack;

    @ApiModelProperty(value = "营期红包金额")
    private String campperiodRedPackAmount;

    @ApiModelProperty(value = " 课程状态（0未到课,1已到课,2已完课）")
    private Integer arrivalStatus;

    @ApiModelProperty(value = "到课时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime arrivalTime;

    @ApiModelProperty(value = "完播时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime completeTime;

    @ApiModelProperty(value = "播放时长")
    private Integer playProgress;

}
