package cn.hxsy.api.app.service;


import cn.hxsy.api.app.model.request.TencentWxInfoRequest;
import cn.hxsy.api.app.model.response.TencentWxInfoResponse;
import cn.hxsy.base.response.Result;

/**
 * <AUTHOR>
 */
public interface TencentWxInfoRpcService {

    /**
    * @description: 当缓存查询不到时，走数据库查询小程序配置
     * 查询完成后，同步结果写入redis
    * @author: <PERSON><PERSON><PERSON><PERSON>
    * @date: 2025/4/4 23:03
    */
    Result<TencentWxInfoResponse> queryWxConfig(TencentWxInfoRequest request);
}
