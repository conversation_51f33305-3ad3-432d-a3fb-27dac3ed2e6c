package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CampCoursePO对象", description = "营期课程信息表")
public class CampCoursePesponse extends BaseEntityResponse {

    @ApiModelProperty(value = "营期ID")
    private Long campId;

    @ApiModelProperty(value = "课程分组ID")
    private Long groupId;

    @ApiModelProperty(value = "外显名称")
    private String externalName;

    @ApiModelProperty(value = "课程分组下配置课程小节json")
    private String courseInfo;
}
