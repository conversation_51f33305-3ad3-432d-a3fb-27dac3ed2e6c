package cn.hxsy.api.user.service;

import cn.hxsy.api.user.model.response.*;
import cn.hxsy.base.response.Result;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface CampPeriodRpcService {

    /**
     * description : 根据营期id和课程信息查询营期课程详情
     * @title: queryCampPeriod
     * @param: campPeriodId
     * @param: courseId
     * <AUTHOR>
     * @date 2025/4/18 14:52
     * @return Result<Object>
     */
    Result<CampPeriodCourseResponse> queryCampPeriod(Long campPeriodId, Long courseId);

    Result<CampCourseVideoResponse> getCampTodayCourse(Long campPeriodId, LocalDate date);

    Result<List<CampPeriodResponse>> getCampPeriodsByIds(List<Long> campPeriodIds);

    Result<List<CampCoursePesponse>> getCampGroupAndCourses(Long campPeriodId);

    /**
     * description : 根据销售ID和营期ID查询 营期是否存在
     * @title: countCampPeriodBySalesId
     * @param: campPeriodId
     * @param: salesId
     * <AUTHOR>
     * @date 2025/7/5 13:46
     * @return int
     */
    int countCampPeriodBySalesGroupId(Long campPeriodId, Long salesGroupId);
}
