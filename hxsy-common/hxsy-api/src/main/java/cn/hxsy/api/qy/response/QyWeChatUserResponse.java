package cn.hxsy.api.qy.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
* @description: 企业内应用开发-企微内成员信息响应实体
* @author: xiaQL
* @date: 2025/6/17 11:22
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "企微内成员信息响应实体", description = "企微内成员信息响应实体")
public class QyWeChatUserResponse {

    @ApiModelProperty(value = "所属部门id")
    Integer department;

    @ApiModelProperty(value = "成员UserID。对应管理端的账号")
    String userid;

    @ApiModelProperty(value = "成员名称")
    String name;

}
