package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * external_userid_info项
 */
@Data
@ApiModel(value = "ExternalUseridInfo", description = "external_userid_info项")
public class ExternalUseridInfo implements Serializable {
    @ApiModelProperty(value = "企业ID")
    private String corpid;

    @ApiModelProperty(value = "external_userid")
    private String external_userid;
} 