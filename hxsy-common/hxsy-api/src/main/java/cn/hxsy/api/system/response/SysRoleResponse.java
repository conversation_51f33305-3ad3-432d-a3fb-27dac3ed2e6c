package cn.hxsy.api.system.response;

import cn.hxsy.base.response.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> XiaQL
 * @description : SysMenuResponse
 * @ClassName : SysMenuResponse
 * @date: 2025-05-02 11:24
 */
@Data
public class SysRoleResponse extends BaseResponse {

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色码值")
    private String roleCode;

    @ApiModelProperty("角色类型")
    private Integer roleType;

    @ApiModelProperty("所属类别 1-业务人员 2-客户")
    private Integer userFrom;


}
