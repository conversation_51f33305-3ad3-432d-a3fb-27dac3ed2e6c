package cn.hxsy.api.user.model.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("客户列表查询响应")
public class CustomerQueryResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty("客户ID")
    private Long customerId;

    @ApiModelProperty("unionId")
    private String unionId;
    
    @ApiModelProperty("微信昵称")
    private String nickname;
    
    @ApiModelProperty("头像URL")
    private String avatarUrl;
    
    @ApiModelProperty("手机号")
    private String mobile;
    
    @ApiModelProperty("手机号状态（0无手机号/1有手机号）")
    private Integer mobileStatus;
    
    @ApiModelProperty("企微添加状态（0未添加/1已添加/9已删除）")
    private Integer weworkStatus;
    
    @ApiModelProperty("微信备注")
    private String wechatRemark;

    @ApiModelProperty("最近活跃时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastActiveTime;
    
    @ApiModelProperty("来源渠道")
    private String source;
    
    @ApiModelProperty("客户创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime customerCreatedAt;

    @ApiModelProperty("标签列表")
    private List<CustomerTagsResponse> tags;
    
    @ApiModelProperty("活跃行为列表")
    private List<CustomerBehaviorResponse> behaviors;

    @ApiModelProperty(value = "账号禁用状态（0-否/1-是）")
    private Integer forbiddenStatus;

    @ApiModelProperty(value = "红包禁用状态（0-否/1-是）")
    private Integer redPacketStatus;
} 