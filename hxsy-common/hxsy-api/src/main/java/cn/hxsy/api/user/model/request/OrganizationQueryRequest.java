package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("组织架构查询请求")
public class OrganizationQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("总部id")
    private Integer headquartersId;
    
    @ApiModelProperty("营期ID")
    private String campPeriodId;

    @ApiModelProperty("训练营（公司）ID")
    private String companyId;

    @ApiModelProperty("栏目ID")
    private String columnId;

    @ApiModelProperty("销售组ID")
    private String salesGroup;

    @ApiModelProperty("联系人")
    private String relPerson;

    @ApiModelProperty("联系人手机号")
    private String phone;

    @ApiModelProperty("status")
    private Integer status;
}