package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 系统用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31 10:19:01
 */
@Data
@ApiModel(value = "客户、业务人员实体类查询公共response")
public class UserInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private SystemUserResponse systemUserResponse;

    private CustomerResponse customerResponse;
}
