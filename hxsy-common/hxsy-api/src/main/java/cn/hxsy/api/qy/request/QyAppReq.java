package cn.hxsy.api.qy.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 企微相关接口请求体
 * 一个公司可关联多个企微，所以首先需要校验当前操作用户是否拥有该公司的管理权限
 * 超管、普管、公司管理员（并且拥有该公司的管理权限）
 */
@Data
public class QyAppReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /*
     * 公司ID
     */
    private String companyId;

    /*
     * 企微ID
     */
    private String corpId;

    /*
     * 当前使用场景
     */
    private String queryScene;

    /*
     * 企微内部登录code
     */
    private String qyCode;

    /*
     * 游标，用户列表查询使用，这个游标是以open_userId为游标的，我们用户列表接口也获取不到，得前端存了然后下一次传过来，第一次查询可以不传
     */
    private String cursor;

    /*
     * 分页大小，用户列表查询使用
     */
    private String limit;

}
