
package cn.hxsy.api.qy.response.auth;

import cn.hxsy.api.qy.QyBaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* @description: 代开发授权应用secret的获取
* @author: xiaQL
* @date: 2025/6/29 1:53
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "代开发授权应用secret的获取响应实体", description = "代开发授权应用secret的获取响应实体")
public class QyWeChatAuthSecretResponse extends QyBaseResponse {

    @ApiModelProperty(value = "企业微信永久授权码,最长为512字节 即后续调用其他接口的应用的凭证密钥secret")
    private String permanent_code;

    @ApiModelProperty(value = "授权方企业信息")
    private AuthCorpInfo auth_corp_info;

    @ApiModelProperty(value = "授权管理员的信息，可能不返回")
    private AuthUserInfo auth_user_info;

    @ApiModelProperty(value = "推广二维码安装相关信息，扫推广二维码安装时返回。成员授权时暂不支持。（注：无论企业是否新注册，只要通过扫推广二维码安装，都会返回该字段）")
    private RegisterCodeInfo register_code_info;

    @ApiModelProperty(value = "安装应用时，扫码或者授权链接中带的state值。详见state说明")
    private String state;

    @ApiModelProperty(value = "访问令牌过期时间（秒）")
    private Integer expires_in;

}