package cn.hxsy.api.system.response;

import cn.hxsy.base.response.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> XiaQL
 * @description : SysMenuResponse
 * @ClassName : SysMenuResponse
 * @date: 2025-05-02 11:24
 */
@Data
public class SysMenuResponse extends BaseResponse {

    @ApiModelProperty("父菜单ID")
    private String parentId;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("路由地址")
    private String menuUrl;

    @ApiModelProperty("菜单类型（M目录 C菜单 F按钮 O操作）")
    private String menuType;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("显示顺序")
    private Integer sortOrder;

    @ApiModelProperty("所属应用 0-PC端 1-小程序")
    private Integer appFrom;


}
