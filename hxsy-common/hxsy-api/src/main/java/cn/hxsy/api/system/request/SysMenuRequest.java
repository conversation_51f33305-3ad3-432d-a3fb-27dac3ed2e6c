package cn.hxsy.api.system.request;

import cn.hxsy.base.request.BaseRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description : SysMenuResponse
 * @ClassName : SysMenuResponse
 * @date: 2025-05-02 11:24
 */
@Data
public class SysMenuRequest extends BaseRequest {

    @ApiModelProperty("菜单id")
    private Integer id;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("所属应用 0-PC端 1-小程序")
    private Integer appFrom;

    @ApiModelProperty("关联菜单id集合")
    private List<String> menuIds;

    @ApiModelProperty("父菜单ID")
    private Integer parentId;

    @ApiModelProperty("路由地址")
    private String menuUrl;

    @ApiModelProperty("菜单类型（M目录 C菜单 F按钮 O操作）")
    private String menuType;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("显示顺序")
    private Integer sortOrder;

    @ApiModelProperty("菜单状态（1正常 0停用）")
    private String status;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;
}
