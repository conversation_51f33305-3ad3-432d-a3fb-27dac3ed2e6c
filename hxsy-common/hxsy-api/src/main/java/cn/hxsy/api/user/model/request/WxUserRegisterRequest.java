package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


@Data
@ApiModel("销售用户注册请求")
public class WxUserRegisterRequest {
    
    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty("姓名")
    private String name;
    
    @NotBlank(message = "userId不能为空")
    @ApiModelProperty("userId")
    private String userId;

    @NotBlank(message = "accountId不能为空")
    @ApiModelProperty("accountId")
    private String accountId;

    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty("phone")
    private String phone;

    @NotBlank(message = "总部ID不能为空")
    @ApiModelProperty("总部ID")
    private Integer headquartersId;

    @NotBlank(message = "栏目ID不能为空")
    @ApiModelProperty("栏目ID")
    private Integer columnId;

    @NotBlank(message = "公司ID不能为空")
    @ApiModelProperty("公司ID")
    private Integer companyId;

    @NotBlank(message = "栏目ID不能为空")
    @ApiModelProperty("栏目ID")
    private Integer salesGroupId;
} 