package cn.hxsy.api.system.request;

import cn.hxsy.base.request.BaseRequestDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description :系统用户可查询权限范围请求类
 * @ClassName : SysUserSelectPermissionRequest
 * @date: 2025-05-02 11:24
 */
@Data
public class SysUserSelectPermissionRequest extends BaseRequestDTO {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("可见栏目范围")
    private List<String> perColumnId;

    @ApiModelProperty("可见公司范围")
    private List<String> perCompanyId;

    @ApiModelProperty("可见销售组范围")
    private List<String> perSalesGroupId;

}
