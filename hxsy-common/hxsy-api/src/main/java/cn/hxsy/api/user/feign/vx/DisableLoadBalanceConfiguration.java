package cn.hxsy.api.user.feign.vx;

import feign.Client;
import feign.codec.Decoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 */
public class DisableLoadBalanceConfiguration {

    @Bean
    public Client feignClient() {
        return new Client.Default(null, null);
    }

    @Bean
    public Decoder feignDecoder() {
        WeChatMessageConverter weChatMessageConverter = new WeChatMessageConverter();
        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(weChatMessageConverter);
        return new SpringDecoder(objectFactory);
    }
}
