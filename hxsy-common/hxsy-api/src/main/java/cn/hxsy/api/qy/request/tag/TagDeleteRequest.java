package cn.hxsy.api.qy.request.tag;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 删除企业客户标签请求
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TagDeleteRequest {

    /**
     * 标签的id列表
     */
    private List<String> tag_id;

    /**
     * 标签组的id列表
     */
    private List<String> group_id;

    /**
     * 删除标签组时是否连同标签一起删除，默认为true
     */
    private Boolean delete_tag_with_group;
}
