package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 客户标签响应实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerTagsResponse", description = "客户标签响应实体类")
public class CustomerTagsResponse extends BaseEntityResponse {

    @ApiModelProperty(value = "客户ID")
    private Long customerId;

    @ApiModelProperty(value = "营期ID")
    private Long campPeriodId;

    @ApiModelProperty(value = "营期名称")
    private String campPeriodName;

    @ApiModelProperty(value = "标签名称")
    private String tagsName;
} 