
package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "微信授权响应实体", description = "微信授权响应实体")
public class WeChatAuthResponse {

    @ApiModelProperty(value = "访问令牌")
    private String access_token;

    @ApiModelProperty(value = "访问令牌过期时间（秒）")
    private Integer expires_in;

    @ApiModelProperty(value = "刷新令牌")
    private String refresh_token;

    @ApiModelProperty(value = "用户唯一标识")
    private String openid;

    @ApiModelProperty(value = "授权范围")
    private String scope;

    @ApiModelProperty(value = "是否为快照用户")
    private Integer is_snapshotuser;

    @ApiModelProperty(value = "多应用内用户关联ID")
    private String unionid;
}