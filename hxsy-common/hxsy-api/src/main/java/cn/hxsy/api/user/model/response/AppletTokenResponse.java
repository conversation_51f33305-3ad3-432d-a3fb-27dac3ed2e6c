package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "小程序响应实体", description = "小程序响应实体")
public class AppletTokenResponse {

    @ApiModelProperty(value = "访问令牌")
    String access_token;

    @ApiModelProperty(value = "有效时间")
    Long expires_in;

    @ApiModelProperty(value = "错误码")
    String errcode;

    @ApiModelProperty(value = "错误信息")
    String errmsg;

    @ApiModelProperty(value = "请求ID")
    String rid;
}
