package cn.hxsy.api.system.request;

import cn.hxsy.base.request.BaseRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR> XiaQL
 * @description : SysMenuResponse
 * @ClassName : SysMenuResponse
 * @date: 2025-05-02 11:24
 */
@Data
public class SysRoleRequest extends BaseRequest {

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色码值")
    private String roleCode;

    @ApiModelProperty("角色类型")
    private Integer roleType;

    @ApiModelProperty("使用状态 0-禁用 1-启用")
    private Integer status;

    @ApiModelProperty("所属类别 1-业务人员 2-客户")
    private Integer userFrom;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    @ApiModelProperty("角色id，更新时必传")
    private String id;

}
