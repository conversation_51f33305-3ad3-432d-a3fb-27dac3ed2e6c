package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 企业微信第三方unionid转external_userid响应实体
 */
@Data
@ApiModel(value = "UnionidToExternalUserid3rdResponse", description = "企业微信第三方unionid转external_userid响应")
public class UnionidToExternalUserid3rdResponse implements Serializable {
    /**
     * 错误码
     */
    @ApiModelProperty(value = "错误码")
    private Integer errcode;
    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errmsg;
    /**
     * external_userid_info列表
     */
    @ApiModelProperty(value = "external_userid_info列表")
    private List<ExternalUseridInfo> external_userid_info;
} 