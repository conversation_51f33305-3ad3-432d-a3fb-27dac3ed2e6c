package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel(value = "CourseVideoResponse响应实体", description = "课程视频信息")
public class CampCourseVideoResponse extends BaseEntityResponse{

    @ApiModelProperty(value = "开课时间标志")
    private String startingFlag;

    @ApiModelProperty(value = "是否有看课权限")
    private Boolean hasCoursePermission;

    @ApiModelProperty(value = "领取红包是否需要获取用户手机号：true-需要，false-不需要")
    private Boolean needPhone;

    @ApiModelProperty(value = "营期新用户是否自动注册，不需要账号审核：true-需要，false-不需要")
    private Boolean autoRegister;

    // 如无课程权限，则返回该课程销售信息
    @ApiModelProperty(value = "客户当前所属销售")
    private String salesName;

    @ApiModelProperty(value = "客户当前所属销售ID")
    private String salesId;

    @ApiModelProperty(value = "本次链接的销售人员姓名")
    private String urlSalesName;

    @ApiModelProperty(value = "本次链接的销售人员ID")
    private String urlSalesId;

    @ApiModelProperty(value = "所有课程")
    private List<CourseVideoResponse> courseVideoList;

    @ApiModelProperty(value = "营期ID")
    private Long campPeriodId;

    @ApiModelProperty(value = "营期名称")
    private String campPeriodName;

    @ApiModelProperty(value = "营期外显名称")
    private String explicitName;

    @ApiModelProperty(value = "上架设置")
    private String campperiodStatus;

    @ApiModelProperty(value = "销售企微二维码")
    private String salesQrCode;


}
