package cn.hxsy.api.qy.response.contact;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 企业微信客户转移响应DTO
 */
@Data
@ApiModel(value = "CustomerTransferResponse", description = "企业微信客户转移响应")
public class CustomerTransferResponse {

    @ApiModelProperty("错误码，0表示成功")
    private Integer errcode;

    @ApiModelProperty("错误信息")
    private String errmsg;

    @ApiModelProperty("客户转移结果列表")
    @JsonProperty("customer_transfer_result")
    private List<CustomerTransferResult> customerTransferResult;

    /**
     * 客户转移结果
     */
    @Data
    public static class CustomerTransferResult {
        
        @ApiModelProperty("客户的external_userid")
        @JsonProperty("external_userid")
        private String externalUserId;
        
        @ApiModelProperty("对此客户进行分配的结果, 0表示成功，失败则返回相应的错误码")
        private Integer errcode;
        
        @ApiModelProperty("结果失败的原因")
        private String errmsg;
    }
}
