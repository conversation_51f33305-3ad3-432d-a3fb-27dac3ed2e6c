package cn.hxsy.api.qy.response.contact;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
* @description: 企业内应用开发-企微内成员信息响应实体
* @author: xiaQL
* @date: 2025/6/17 11:22
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "企微内成员信息响应实体", description = "企微内成员信息响应实体")
public class QyContactUserResponse {

    @ApiModelProperty(value = "所属部门id")
    List<Integer> department;

    @ApiModelProperty(value = "成员UserID。对应管理端的账号")
    String userid;

    @ApiModelProperty(value = "全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取")
    String open_userid;

    @ApiModelProperty(value = "成员名称")
    String name;

}
