
package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "微信用户信息响应实体", description = "微信用户信息响应实体")
public class WeChatUserInfoResponse {

    @ApiModelProperty(value = "用户唯一标识")
    private String openid;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "用户性别（1为男性，2为女性）")
    private Integer sex;

    @ApiModelProperty(value = "用户所在省份")
    private String province;

    @ApiModelProperty(value = "用户所在城市")
    private String city;

    @ApiModelProperty(value = "用户所在国家")
    private String country;

    @ApiModelProperty(value = "用户头像 URL")
    private String headimgurl;

    @ApiModelProperty(value = "用户特权信息列表")
    private List<String> privilege;

    @ApiModelProperty(value = "多应用内用户关联ID")
    private String unionid;
}