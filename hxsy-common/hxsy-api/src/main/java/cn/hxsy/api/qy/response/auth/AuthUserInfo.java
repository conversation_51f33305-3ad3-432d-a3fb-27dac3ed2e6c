package cn.hxsy.api.qy.response.auth;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> XiaQL
 * @description : 授权管理员的信息
 * @ClassName : authUserInfo
 * @date: 2025-06-29 01:57
 */
@Data
public class AuthUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "授权管理员的userid，可能为空")
    private String userid;

    @ApiModelProperty(value = "授权管理员的open_userid，可能为空")
    private String open_userid;

    @ApiModelProperty(value = "授权管理员的name，可能为空")
    private String name;

    @ApiModelProperty(value = "授权管理员的头像url，可能为空")
    private String avatar;
}
