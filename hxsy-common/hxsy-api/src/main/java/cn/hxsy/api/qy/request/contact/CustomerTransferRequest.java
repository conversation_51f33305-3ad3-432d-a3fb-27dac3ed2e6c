package cn.hxsy.api.qy.request.contact;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 企业微信客户转移请求DTO
 */
@Data
@ApiModel(value = "CustomerTransferRequest", description = "企业微信客户转移请求")
public class CustomerTransferRequest {

    @ApiModelProperty(value = "原跟进成员的userid", required = true)
    @NotBlank(message = "原跟进成员的userid不能为空")
    @JsonProperty("handover_userid")
    private String handoverUserId;

    @ApiModelProperty(value = "接替成员的userid", required = true)
    @NotBlank(message = "接替成员的userid不能为空")
    @JsonProperty("takeover_userid")
    private String takeoverUserId;

    @ApiModelProperty(value = "客户的external_userid列表", required = true)
    @NotEmpty(message = "客户的external_userid列表不能为空")
    @Size(max = 100, message = "一次最多转移100个客户")
    @JsonProperty("external_userid")
    private List<String> externalUserIds;

    @ApiModelProperty(value = "转移成功后发给客户的消息，最多200字符，不填则使用默认文案")
    @Size(max = 200, message = "转移成功后发给客户的消息最多200字符")
    @JsonProperty("transfer_success_msg")
    private String transferSuccessMsg;
}
