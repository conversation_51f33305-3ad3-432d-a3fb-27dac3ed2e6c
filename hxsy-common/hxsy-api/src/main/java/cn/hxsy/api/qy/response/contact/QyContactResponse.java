package cn.hxsy.api.qy.response.contact;

import cn.hxsy.api.qy.QyBaseResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
* @description: 企业-服务商代开发-企微内部门查询响应实体
* @author: xiaQL
* @date: 2025/6/17 11:22
*/
@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "企微内部门查询响应实体", description = "企微内部门查询响应实体")
public class QyContactResponse extends QyBaseResponse {

    @ApiModelProperty(value = "部门列表数据")
    List<QyContactDeptResponse> department_id;

    @ApiModelProperty(value = "成员列表数据")
    List<QyContactUserResponse> dept_user;

    @ApiModelProperty(value = "单部门下成员列表数据")
    List<QyContactUserResponse> userlist;

    @ApiModelProperty(value = "服务商open_user_id转换userid后成员列表数据")
    List<QyContactUserResponse> userid_list;

    @ApiModelProperty(value = "服务商userId转换open_user_id后成员列表数据")
    List<QyContactUserResponse> open_userid_list;

    @ApiModelProperty(value = "分页游标，下次请求时填写以获取之后分页的记录。如果该字段返回空则表示已没有更多数据")
    String next_cursor;

}
