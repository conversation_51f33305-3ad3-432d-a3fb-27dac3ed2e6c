package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "获取urlscheme请求类", description = "获取urlscheme请求类")
public class JumpWxa {

    @ApiModelProperty(value = "path")
    String path;

    @ApiModelProperty(value = "query")
    String query;

    @ApiModelProperty(value = "env_version")
    String env_version;
}
