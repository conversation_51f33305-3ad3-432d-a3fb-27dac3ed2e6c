package cn.hxsy.api.user.model.response;

import cn.hxsy.api.system.response.SysMenuResponse;
import cn.hxsy.api.system.response.SysRoleResponse;
import cn.hxsy.api.system.response.SysUserSelectPermissionResponse;
import cn.hxsy.base.response.BaseEntityResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "SystemUserPO", description = "系统用户")
public class SystemUserResponse extends BaseEntityResponse {

    @ApiModelProperty(value = "账号ID")
    private String accountId;

    @ApiModelProperty(value = "unionid")
    private String unionId;

    @ApiModelProperty(value = "账号类型")
    private Integer accountType;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "用户Id")
    private String userId;

    @ApiModelProperty(value = "所属总部")
    private Integer headquartersId;

    @ApiModelProperty(value = "栏目")
    private Integer columnId;

    @ApiModelProperty(value = "公司")
    private Integer companyId;

    @ApiModelProperty(value = "销售组")
    private Integer salesGroupId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "角色类型")
    private Integer roleType;

    @ApiModelProperty(value = "角色ID")
    private Integer roleId;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "出生日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate birthDate;

    @ApiModelProperty(value = "绑定企微ID")
    private Integer wechatBindId;

    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "用户类型")
    private String userType;

    @ApiModelProperty(value = "是否已完成注册 1-已完成")
    private String isRegister;

    @ApiModelProperty(value = "业务人员具备菜单权限")
    private List<SysMenuResponse> sysMenuResponses;

    @ApiModelProperty(value = "业务人员具备角色权限集合")
    private List<SysRoleResponse> sysRoleResponses;

    @ApiModelProperty(value = "业务人员具备角色权限")
    private SysRoleResponse sysRoleResponse;

    @ApiModelProperty(value = "业务人员具备查询组织架构范围")
    private SysUserSelectPermissionResponse sysUserSelectPermissionResponse;

    @ApiModelProperty(value = "业务人员在企微下的账号信息")
    private List<SystemUserQyRelationResponse> systemUserQyRelationResponses;

}
