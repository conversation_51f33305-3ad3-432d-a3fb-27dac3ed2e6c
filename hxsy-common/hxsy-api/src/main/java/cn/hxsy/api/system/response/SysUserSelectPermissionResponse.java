package cn.hxsy.api.system.response;

import cn.hxsy.base.response.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description : 系统用户可查询权限范围响应类
 * @ClassName : SysUserSelectPermissionResponse
 * @date: 2025-05-02 11:24
 */
@Data
public class SysUserSelectPermissionResponse extends BaseResponse {

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("可见栏目范围")
    private List<String> perColumnId;

    @ApiModelProperty("可见公司范围")
    private List<String> perCompanyId;

    @ApiModelProperty("可见销售组范围")
    private List<String> perSalesGroupId;

}
