package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel("系统用户分页查询请求")
public class SystemUserPageRequest {
    
    @ApiModelProperty("当前页码")
    private Integer current = 1;
    
    @ApiModelProperty("每页大小")
    private Integer size = 10;
    
    @ApiModelProperty("系统ID")
    private Long id;
    
    @ApiModelProperty("账号ID")
    private String accountId;

    @ApiModelProperty("用户名")
    private String username;
    
    @ApiModelProperty("手机号")
    private String phone;

    @ApiModelProperty("状态")
    private Integer status;
    
    @ApiModelProperty("审核状态")
    private Integer auditStatus;

    @ApiModelProperty("公司ID")
    private String companyId;
    
    @ApiModelProperty("销售组")
    private String salesGroupId;
    
    @ApiModelProperty("开始时间")
    private LocalDate startTime;
    
    @ApiModelProperty("结束时间")
    private LocalDate endTime;
    
    @ApiModelProperty("所属总部")
    private Integer headquartersId;
    
    @ApiModelProperty("栏目")
    private String columnId;
} 