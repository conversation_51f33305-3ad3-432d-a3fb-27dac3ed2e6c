package cn.hxsy.api.user.model.request;

import cn.hxsy.base.request.BaseRequest;
import lombok.*;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class UserRegisterRequest extends BaseRequest {

    private String unionId;

    private String userId;

    private String userType;

    /**
     * 单小程序wx关联openId
     */
    private String openid;

    /**
     * 小程序来源，区分三个端侧小程序
     */
    private String appId;

    /**
     * 分享栏目id
     */
    private Long columnId;

    /**
     * 营期ID
     */
    private Long campPeriodId;

    /**
     * 训练营ID
     */
    private Long companyId;

    /**
     * 销售组ID
     */
    private Long salesGroupId;

    /**
     * 销售人员姓名
     */
    private String salesName;

    /**
     * 销售ID
     */
    private Long salesId;

    /**
     * 客户头像URL
     */
    private String avatarUrl;

    /**
     * 微信昵称
     */
    private String nickname;

    /**
     * 性别（0-未知 1-男 2-女）
     */
    private Integer gender;

}
