package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标签实体类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Tag", description = "标签信息响应体")
public class TagResponse extends BaseEntityResponse {
    
    @ApiModelProperty(value = "标签组ID")
    private Long groupId;
    
    @ApiModelProperty(value = "标签名称")
    private String tagName;
    
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;
    
    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value = "使用状态（1-有效，0-无效）")
    private Integer status;
} 