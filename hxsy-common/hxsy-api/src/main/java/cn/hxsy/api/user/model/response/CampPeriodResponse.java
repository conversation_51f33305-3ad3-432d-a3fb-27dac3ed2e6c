package cn.hxsy.api.user.model.response;

import cn.hxsy.base.response.BaseEntityResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CampPeriodResponse对象", description = "营期信息响应")
public class CampPeriodResponse extends BaseEntityResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "训练营分组")
    private Long companyId;

    @ApiModelProperty(value = "营期名称")
    private String campperiodName;

    @ApiModelProperty(value = "营期简介")
    private String campperiodIntroduction;

    @ApiModelProperty(value = "营期封面")
    private String campperiodCoverpath;

    @ApiModelProperty(value = "营期内容")
    private String campperiodContent;

    @ApiModelProperty(value = "所属流量池")
    private List<String> campperiodPool;

    @ApiModelProperty(value = "项目")
    private String campperiodProject;

    @ApiModelProperty(value = "来源")
    private String campperiodSource;

    @ApiModelProperty(value = "标签")
    private List<String> campperiodTag;

    @ApiModelProperty(value = "可见范围")
    private List<String> visualRange;

    @ApiModelProperty(value = "营期可见设置")
    private List<String> visualList;

    @ApiModelProperty(value = "售卖方式")
    private String campperiodSalesmethod;

    @ApiModelProperty(value = "课程价格")
    private BigDecimal campperiodPrice;

    @ApiModelProperty(value = "划线价格")
    private BigDecimal underlinedPrice;

    @ApiModelProperty(value = "上架设置")
    private String campperiodStatus;

    @ApiModelProperty(value = "招生时间")
    private List<String> enrollmentTime;

    @ApiModelProperty(value = "开课时间标志")
    private String startingFlag;

    @ApiModelProperty(value = "开课时间")
    private List<String> startingTime;

    @ApiModelProperty(value = "仅招生时间可报名")
    private String registeredFlag;

    @ApiModelProperty(value = "课程目录模式")
    private String catalogMode;

    @ApiModelProperty(value = "关联系列课标志")
    private String seriesFlag;

    @ApiModelProperty(value = "关联系列课")
    private List<String> seriesCourses;

    @ApiModelProperty(value = "目录解锁模式")
    private String catalogUnlock;

    @ApiModelProperty(value = "目录解锁时间")
    private List<String> unlockTime;

}
