package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 客户分配请求实体
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
@ApiModel("客户分配请求")
public class CustomerAssignRequest {

    @NotNull(message = "旧栏目ID不能为空")
    @ApiModelProperty("旧栏目ID")
    private Long oldColumnId;

    @NotNull(message = "客户ID不能为空")
    @ApiModelProperty("客户ID")
    private List<Long> customerIds;

    @NotNull(message = "公司ID不能为空")
    @ApiModelProperty("公司ID")
    private Long companyId;

    @NotNull(message = "栏目ID不能为空")
    @ApiModelProperty("栏目ID")
    private Long columnId;

    @ApiModelProperty("营期ID")
    private Long campPeriodId;

    @NotNull(message = "销售组ID不能为空")
    @ApiModelProperty("销售组ID")
    private Long salesGroupId;

    @NotNull(message = "销售人员ID不能为空")
    @ApiModelProperty("销售人员ID")
    private Long salesId;

    @ApiModelProperty("销售人员ID-企微侧id")
    private String qyUserId;

    @NotNull(message = "销售人员姓名不能为空")
    @ApiModelProperty("销售人员姓名")
    private String salesName;

    @ApiModelProperty("分配类型（1-手动分配 2-自动分配）")
    private Integer assignType;

} 