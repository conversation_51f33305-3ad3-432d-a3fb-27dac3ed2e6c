package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/04/04
 */
@Data
@ApiModel("营期小程序链接请求体")
public class CampPeriodUrlRequest {

    @ApiModelProperty("小程序appId")
    private String appId;

    @ApiModelProperty("销售人员id")
    private String salesId;

    @ApiModelProperty("销售组id")
    private String salesGroupId;

    @ApiModelProperty("栏目id")
    private String columnId;

    @ApiModelProperty("公司id")
    private String companyId;

    @ApiModelProperty("营期id")
    private String campPeriodId;

    @ApiModelProperty("课程id")
    private String courseId;


}
