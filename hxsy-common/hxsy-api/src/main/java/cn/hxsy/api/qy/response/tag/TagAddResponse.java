package cn.hxsy.api.qy.response.tag;

import cn.hxsy.api.qy.QyBaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 添加企业客户标签响应
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TagAddResponse extends QyBaseResponse {

    /**
     * 标签组信息
     */
    private TagGroup tag_group;

    /**
     * 标签组信息
     */
    @Data
    public static class TagGroup {

        /**
         * 标签组id
         */
        private String group_id;

        /**
         * 标签组名称
         */
        private String group_name;

        /**
         * 标签组创建时间
         */
        private Long create_time;

        /**
         * 标签组排序的次序值，order值大的排序靠前
         */
        private Integer order;

        /**
         * 标签组内的标签列表
         */
        private List<Tag> tag;
    }

    /**
     * 标签信息
     */
    @Data
    public static class Tag {

        /**
         * 标签id
         */
        private String id;

        /**
         * 标签名称
         */
        private String name;

        /**
         * 标签创建时间
         */
        private Long create_time;

        /**
         * 标签排序的次序值，order值大的排序靠前
         */
        private Integer order;
    }
}
