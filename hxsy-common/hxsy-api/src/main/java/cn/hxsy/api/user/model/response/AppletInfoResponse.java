package cn.hxsy.api.user.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@SuppressWarnings("ALL")
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "小程序登录响应实体", description = "小程序登录响应实体")
public class AppletInfoResponse {

    @ApiModelProperty(value = "访问令牌")
    String session_key;

    @ApiModelProperty(value = "单小程序内wx号关联id")
    String openid;

    @ApiModelProperty(value = "错误码")
    String errcode;

    @ApiModelProperty(value = "错误信息")
    String errmsg;

    @ApiModelProperty(value = "多小程序内wx号关联id")
    String unionid;
}
