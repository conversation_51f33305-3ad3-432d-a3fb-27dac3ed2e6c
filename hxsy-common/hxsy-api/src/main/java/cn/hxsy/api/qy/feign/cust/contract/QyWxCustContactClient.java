package cn.hxsy.api.qy.feign.cust.contract;

import cn.hxsy.api.qy.request.contact.CustomerMarkTagRequest;
import cn.hxsy.api.qy.request.contact.CustomerTransferRequest;
import cn.hxsy.api.qy.request.tag.TagAddRequest;
import cn.hxsy.api.qy.request.tag.TagDeleteRequest;
import cn.hxsy.api.qy.request.tag.TagEditRequest;
import cn.hxsy.api.qy.request.tag.TagListRequest;
import cn.hxsy.api.qy.response.contact.CustomerMarkTagResponse;
import cn.hxsy.api.qy.response.contact.CustomerTransferResponse;
import cn.hxsy.api.qy.response.tag.TagAddResponse;
import cn.hxsy.api.qy.response.tag.TagCorpResponse;
import cn.hxsy.api.qy.QyBaseResponse;
import cn.hxsy.api.user.feign.vx.DisableLoadBalanceConfiguration;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;


/**
* @description: 企业-外部联系人-相关请求接口
* @author: xiaQL
* @date: 2025/6/16 10:52
*/
@FeignClient(
        url = "${feign.provider.qyWx.url}" + QyWxCustContactClient.TK,
        name = "${feign.provider.qyWx.contact.id}",
        configuration = DisableLoadBalanceConfiguration.class
)
public interface QyWxCustContactClient {

    String TK = "/cgi-bin";

    String EXTERNAL_CONTACT = "/externalcontact";

    /**
     * 分配在职成员的客户
     * 将在职员工的客户分配给在职员工
     * 
     * @param accessToken 企业微信访问令牌
     * @param request 客户转移请求
     * @return 客户转移响应
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/transfer_customer", consumes = MediaType.APPLICATION_JSON_VALUE)
    CustomerTransferResponse transferCustomer(@RequestParam(name = "access_token") String accessToken,
                                              @RequestBody CustomerTransferRequest request);
    
    /**
     * 分配离职成员的客户
     * 将离职员工的客户分配给在职员工
     * 
     * @param accessToken 企业微信访问令牌
     * @param request 客户转移请求
     * @return 客户转移响应
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/resigned/transfer_customer", consumes = MediaType.APPLICATION_JSON_VALUE)
    CustomerTransferResponse transferResignedCustomer(@RequestParam(name = "access_token") String accessToken,
                                                     @RequestBody CustomerTransferRequest request);
    
    /**
     * 编辑客户企业标签
     * 为指定客户添加或移除标签
     * 
     * @param accessToken 企业微信访问令牌
     * @param request 编辑客户标签请求
     * @return 编辑客户标签响应
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/mark_tag", consumes = MediaType.APPLICATION_JSON_VALUE)
    CustomerMarkTagResponse markCustomerTag(@RequestParam(name = "access_token") String accessToken,
                                           @RequestBody CustomerMarkTagRequest request);
    
    /**
     * 获取企业标签库
     * 获取企业设置的所有客户标签
     *
     * @param accessToken 企业微信访问令牌
     * @return 企业标签库响应
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/get_corp_tag_list")
    TagCorpResponse getCorpTagList(@RequestParam("access_token") String accessToken,
                                   @RequestBody TagListRequest request);
    
    /**
     * 添加企业客户标签
     * 添加新的标签或标签组
     *
     * @param accessToken 企业微信访问令牌
     * @param request 添加标签请求
     * @return 添加标签响应
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/add_corp_tag", consumes = MediaType.APPLICATION_JSON_VALUE)
    TagAddResponse addCorpTag(@RequestParam(name = "access_token") String accessToken,
                             @RequestBody TagAddRequest request);
    
    /**
     * 编辑企业客户标签
     * 修改标签或标签组的名称、次序值
     *
     * @param accessToken 企业微信访问令牌
     * @param request 编辑标签请求
     * @return 编辑标签响应
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/edit_corp_tag", consumes = MediaType.APPLICATION_JSON_VALUE)
    QyBaseResponse editCorpTag(@RequestParam(name = "access_token") String accessToken,
                               @RequestBody TagEditRequest request);
    
    /**
     * 删除企业客户标签
     * 删除标签或标签组
     *
     * @param accessToken 企业微信访问令牌
     * @param request 删除标签请求
     * @return 删除标签响应
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/del_corp_tag", consumes = MediaType.APPLICATION_JSON_VALUE)
    QyBaseResponse deleteCorpTag(@RequestParam(name = "access_token") String accessToken,
                                 @RequestBody TagDeleteRequest request);

    /**
     * description : 获取客户详情 <a href="https://developer.work.weixin.qq.com/document/path/96315">...</a>
     * @title: get
     * @param: accessToken 调用接口凭证
     * @param: externalUserId 外部联系人的userid，注意不是企业成员的账号
     * @param: cursor 上次请求返回的next_cursor
     * <AUTHOR>
     * @date 2025/7/26 0:38
     * @return JSONObject
     */
    @GetMapping(value = EXTERNAL_CONTACT + "/get")
    JSONObject get(@RequestParam(name = "access_token") String accessToken,
                   @RequestParam("external_userid") String externalUserId,
                   @RequestParam(name = "cursor", required = false) String cursor);

    /**
     * description : 修改客户备注信息 <a href="https://developer.work.weixin.qq.com/document/path/96317">...</a>
     * @title: remark
     * @param accessToken 调用接口凭证
     * @param jsonObject
     * {
     *    "userid":"zhangsan",
     *    "external_userid":"woAJ2GCAAAd1asdasdjO4wKmE8Aabj9AAA",
     *    "remark":"备注信息",
     *    "description":"描述信息",
     *    "remark_company":"腾讯科技",
     *    "remark_mobiles":[
     * 		"13800000001",
     * 		"13800000002"
     *    ],
     *    "remark_pic_mediaid":"MEDIAID"
     * }
     * @return
     */
    @PostMapping(value = EXTERNAL_CONTACT + "/remark", consumes = MediaType.APPLICATION_JSON_VALUE)
    JSONObject remark(@RequestParam(name = "access_token") String accessToken,
                      @RequestBody JSONObject jsonObject);

                                 
}