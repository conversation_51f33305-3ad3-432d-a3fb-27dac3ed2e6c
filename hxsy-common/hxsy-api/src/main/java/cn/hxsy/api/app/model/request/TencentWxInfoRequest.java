package cn.hxsy.api.app.model.request;

import cn.hxsy.base.request.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 小程序配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-04 21:59:42
 */
@Getter
@Setter
@ApiModel(value = "TencentWxInfo响应对象", description = "小程序配置响应对象")
public class TencentWxInfoRequest extends BaseRequest {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty(value = "小程序名称")
    private String name;

    @ApiModelProperty(value = "小程序AppID")
    private String appid;

    @ApiModelProperty(value = "小程序AppSecret")
    private String secret;

    @ApiModelProperty(value = "小程序状态（0:禁用, 1:启用）")
    private Integer status;


}
