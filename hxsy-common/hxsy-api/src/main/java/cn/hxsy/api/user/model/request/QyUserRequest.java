package cn.hxsy.api.user.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 业务人员账号与企微信息关联
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Getter
@Setter
@ApiModel(value = "SystemUserQyRelation对象", description = "业务人员账号与企微信息关联")
public class QyUserRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("业务人员id")
    private String systemUserId;

    @ApiModelProperty("业务人员名称")
    private String systemUserName;

    @ApiModelProperty("业务人员在企业内的UserID")
    private String qyUserId;

    @ApiModelProperty("业务人员所在企业ID")
    private String corpId;

    @ApiModelProperty("业务人员在企业的名称")
    private String qyName;

    @ApiModelProperty(value = "账号激活码(用于激活账号)")
    private String activeCode;

    @ApiModelProperty("账号类型 1-基础账号，2-互通账号")
    private Integer accountType;

    @ApiModelProperty("企微客户联系方式的配置id")
    private String configId;

    @ApiModelProperty("联系我二维码链接")
    private String qrCode;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("业务账号状态")
    private Integer systemUserStatus;

    @ApiModelProperty("企微帐号状态")
    private Integer qyStatus;

    @ApiModelProperty(value = "互通账号状态：0-未分配 1-已分配")
    private Integer accountStatus;

    @ApiModelProperty(value = "互通账号过期时间-开始")
    private LocalDateTime expireTimeStart;

    @ApiModelProperty(value = "互通账号过期时间-结束")
    private LocalDateTime expireTimeEnd;


}
