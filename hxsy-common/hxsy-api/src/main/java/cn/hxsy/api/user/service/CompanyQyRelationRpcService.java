package cn.hxsy.api.user.service;


import cn.hxsy.api.qy.request.QyAppReq;
import cn.hxsy.base.response.Result;

/**
* @description: 公司与企业账号关联rpc调用
* @author: xiaQL
* @date: 2025/6/17 21:53
*/
public interface CompanyQyRelationRpcService {

    /**
    * @description: 传入登录code，rpc获取对应的access_token，再发起登录流程
    * @author: xiaQL
    * @date: 2025/6/17 21:56
    */
    Result<Object> query(QyAppReq qyAppReq);


}
