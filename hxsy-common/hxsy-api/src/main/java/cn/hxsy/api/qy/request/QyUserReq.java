package cn.hxsy.api.qy.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description : QyUserReq
 * @ClassName : QyUserReq
 * @date: 2025-06-19 11:15
 */
@Data
public class QyUserReq {

    /*
     * 用于分页查询的游标，字符串类型，由上一次调用返回，首次调用不填
     */
    private String cursor;

    /*
     * 分页，预期请求的数据量，取值范围 1 ~ 10000
     */
    private String limit;

    /*
     * 企业id
     */
    private String corpId;

    /*
     * open_userid列表，最多不超过1000个。必须是source_agentid对应的应用所获取
     */
    private List<String> open_userid_list;

    /*
     * 获取到的成员ID列表，最多不超过1000个
     */
    private List<String> userid_list;

    /*
     * 企业授权的代开发自建应用或第三方应用的agentid
     */
    private String source_agentid;
}
