dubbo:
  consumer:
    timeout: 3000
    check: false
  protocol:
    name: dubbo
    port: -1
  registry:
    address: nacos://${cn.hxsy.nacos.server.url}
    parameters:
      namespace: ${cn.hxsy.dubbo.nacos.namespace} #dubbo单独命名空间
      group: ${cn.hxsy.dubbo.nacos.group}
      username: ${cn.hxsy.dubbo.nacos.username}
      password: ${cn.hxsy.dubbo.nacos.password}
  application:
    name: ${spring.application.name}
    qos-enable: false
    qos-accept-foreign-ip: false
    serialize-check-status: WARN
feign:
  compression: # 启用请求或响应GZIP压缩
    request:
      enabled: true
      mime-types: ${server.compression.mime-types}
      min-request-size: 2048
    response:
      enabled: true # 响应压缩
      useGzipDecoder: true # #响应解压
  client:
    config:
      default:
        read-timeout: 10000 #从服务器获取响应结果的最大时间
        connect-timeout: 5000 #feign客户端建立连接最大的时间
  provider:
    applet:
      id: 'applet'
      url: 'https://api.weixin.qq.com'