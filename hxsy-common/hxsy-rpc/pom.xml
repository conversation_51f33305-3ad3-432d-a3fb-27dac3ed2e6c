<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.hxsy</groupId>
        <artifactId>hxsy-common</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>hxsy-rpc</artifactId>
    <version>1.0-SNAPSHOT</version>
    <description>rpc配置模块</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- dubbo -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-registry-nacos</artifactId>
        </dependency>
        <!-- 公共依赖 -->
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-base</artifactId>
        </dependency>
    </dependencies>
</project>