//package cn.hxsy.config;
//
//import cn.dev33.satoken.dao.SaTokenDao;
//import cn.dev33.satoken.dao.SaTokenDaoForRedisTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
///**
// * sa-token的全局配置
// *
// * <AUTHOR>
// */
//@Configuration
//@Slf4j
//public class SaTokenConfigure {
//    @Bean
//    public SaTokenDao saTokenDao() {
//        return new SaTokenDaoForRedisTemplate();
//    }
//}
