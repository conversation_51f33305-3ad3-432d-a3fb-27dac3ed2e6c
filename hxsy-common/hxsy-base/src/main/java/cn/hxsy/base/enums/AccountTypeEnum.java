package cn.hxsy.base.enums;

/**
 * <AUTHOR>
 * @date 2025/04/04
 */
public enum AccountTypeEnum implements BaseEnum {
    SYSTEM_USER(1, "系统用户"),
    CUSTER(2, "客户");

    private final int code;
    private final String info;

    AccountTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
