package cn.hxsy.base.constant.user;

import cn.hxsy.base.enums.BaseEnum;

/**
* @description: 系统角色类型
* @author: xiaQL
* @date: 2025/5/14 23:00
*/
public enum UserRole implements BaseEnum {

    /**
     * 系统管理员（开发使用）
     */
    ADMIN(1, "admin"),

    /**
     * 普通管理员（总部人员使用）
     */
    COMMON_ADMIN(2, "common_admin"),

    /**
     * 栏目管理员
     */
    COL_ADMIN(3, "col_admin"),

    /**
     * 公司管理员
     */
    COM_ADMIN(4, "com_admin"),

    /**
     * 销售组管理员
     */
    SALE_ADMIN(5, "sale_admin"),
    /**
     * 销售
     */
    OTHER(6, "other"),
    ;

    private final int code;
    private final String info;

    UserRole(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
