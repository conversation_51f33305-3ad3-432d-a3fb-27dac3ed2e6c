package cn.hxsy.base.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/05/22
 */
@Data
public class CourseRedPacketRequest {

    @ApiModelProperty("栏目id")
    private String columnId;

    @ApiModelProperty("公司ID")
    private String companyId;

    @ApiModelProperty("营期id")
    private Long campPeriodId;

    @ApiModelProperty("课程id")
    private Long courseId;

    @ApiModelProperty("销售id")
    private String salesId;

    @ApiModelProperty("销售组id")
    private String salesGroupId;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("客户id")
    private Long customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("微信商户id")
    private String merchantId;

    @ApiModelProperty("转账状态")
    private String state;

    @ApiModelProperty("创建时间起始")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createStartTime;

    @ApiModelProperty("创建结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createEndTime;

}
