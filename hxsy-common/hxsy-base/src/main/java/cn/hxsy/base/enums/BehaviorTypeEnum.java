package cn.hxsy.base.enums;

/**
 * 行为类型枚举
 *
 * 1-客户注册
 * 2-训练营营期报名
 * 3-训练营视频课学习
 * 4-课后答题
 * 5-领取红包
 * 6-添加企微
 * 7-删除企微
 * 8-加入群聊
 */
public enum BehaviorTypeEnum implements BaseEnum {

    CUSTOMER_REGISTRATION(1, "客户注册"),
    CAMP_ENROLLMENT(2, "训练营营期报名"),
    CAMP_VIDEO_COURSE_LEARNING(3, "训练营视频课学习"),
    POST_COURSE_ANSWERING(4, "课后答题"),
    RECEIVE_RED_PACKET(5, "领取红包"),
    ADD_WECHAT_ENTERPRISE(6, "添加企微"),
    DELETE_WECHAT_ENTERPRISE(7, "删除企微"),
    JOIN_GROUP_CHAT(8, "加入群聊"),
    EXIT_GROUP_CHAT(9, "退出群聊")
    ;

    private final int code;
    private final String description;

    BehaviorTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return description;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}