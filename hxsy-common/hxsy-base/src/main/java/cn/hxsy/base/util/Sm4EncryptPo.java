package cn.hxsy.base.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.security.SecureRandom;

import static cn.hutool.crypto.Mode.CBC;
import static cn.hutool.crypto.Padding.ZeroPadding;

@Data
@NoArgsConstructor
@Slf4j
public class Sm4EncryptPo {

    /**
     * sm4加密对象
     */
    private SymmetricCrypto sm4;

    /**
     * sm4秘钥
     */
    private String encryptKey;

    /**
     * sm4初始向量
     */
    private String encryptInitIv;

    /**
     * sm4加密后的数据
     */
    private String encryptData;

    public Sm4EncryptPo(SymmetricCrypto sm4, String encryptKey, String encryptInitIv) {
        this.sm4 = sm4;
        this.encryptKey = encryptKey;
        this.encryptInitIv = encryptInitIv;
    }

    /**
     * sm4对象构造
     * 加密时自己生成秘钥与向量
     * 解密时由传入的秘钥与向量来生成sm4
     * @param encryptKey 秘钥
     * @param initIv 初始向量
     * @return
     */
    public Sm4EncryptPo sm4Build(String encryptKey, String initIv){
        log.info("初始秘钥: " + encryptKey);
        log.info("初始向量: " + initIv);
        SymmetricCrypto sm4 = new SM4(
                CBC, ZeroPadding,
                encryptKey.getBytes(),
                initIv.getBytes()
        );
        //构造返回对象
        Sm4EncryptPo sm4EncryptPo = new Sm4EncryptPo(sm4, encryptKey, initIv);
        return sm4EncryptPo;
    }

    /**
     * sm4解密，需要获取到加密方法时使用的秘钥与向量
     * @return
     */
    public String decrypt(){
        // 待解密数据
        String cipherTxt = this.encryptData;
        if(!cipherTxt.startsWith("{SM4}")){
            return cipherTxt;
        }
        cipherTxt = cipherTxt.substring(5);
        SymmetricCrypto sm4 = this.getSm4();
        String plainTxt = sm4.decryptStr(HexUtil.decodeHex(cipherTxt), CharsetUtil.CHARSET_UTF_8);
        // 移除所有非打印控制字符
        plainTxt = plainTxt.replaceAll("[\\p{Cntrl}\\p{Cc}]","");
        plainTxt = plainTxt.replaceAll("\\s+$", "");
        // 移除掉字符串的""，防止后续数据库查询带上
        plainTxt = plainTxt.replaceAll("\"", "");
        return plainTxt;
    }

    /**
     * sm4加密过程，需要自己生成秘钥与向量
     * @param plainTxt 待加密数据
     * @return
     */
    public Sm4EncryptPo encrypt(String plainTxt){
        //秘钥生成
        byte[] key = new byte[8];
        SecureRandom secureRandom = new SecureRandom();
        secureRandom.nextBytes(key);
        StringBuilder sbKey = new StringBuilder(16);
        for (int i = 0; i < key.length; i++) {
            sbKey.append(String.format("%02x", key[i]));
        }
        //初始向量生成
        byte[] iv = new byte[8];
        secureRandom.nextBytes(iv);
        StringBuilder sbIv = new StringBuilder(16);
        for (int i = 0; i < key.length; i++) {
            sbIv.append(String.format("%02x", iv[i]));
        }
        Sm4EncryptPo sm4EncryptPo = sm4Build(sbKey.toString(), sbIv.toString());
        SymmetricCrypto sm4 = sm4EncryptPo.getSm4();
        String cipherTxt = sm4.encryptHex(plainTxt);
        sm4EncryptPo.setEncryptData("{SM4}" + cipherTxt);
        return sm4EncryptPo;
    }
}
