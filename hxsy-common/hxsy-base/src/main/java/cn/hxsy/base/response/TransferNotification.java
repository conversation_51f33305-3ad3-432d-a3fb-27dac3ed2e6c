package cn.hxsy.base.response;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

@Data
public class TransferNotification {

    /*
     * 新版返回商单号
     */
    @SerializedName("transfer_bill_no")
    private String transferBillNo;
 
    @SerializedName("out_bill_no")
    private String outBillNo;

    /*
     * 旧版返回商单号
     */
    @SerializedName("batch_id")
    private String batchId;

    @SerializedName("out_batch_no")
    private String outBatchNo;
 
    /**
     * 【单据状态】 新版-商家转账订单状态
     * 可选取值
     * ACCEPTED: 转账已受理
     * PROCESSING: 转账锁定资金中。如果一直停留在该状态，建议检查账户余额是否足够，如余额不足，可充值后再原单重试。
     * WAIT_USER_CONFIRM: 待收款用户确认，可拉起微信收款确认页面进行收款确认
     * TRANSFERING: 转账中，可拉起微信收款确认页面再次重试确认收款
     * SUCCESS: 转账成功
     * FAIL: 转账失败
     * CANCELING: 商户撤销请求受理成功，该笔转账正在撤销中
     * CANCELLED: 转账撤销完成
     */
    @SerializedName("state")
    private String state;

    /**
     * 【单据状态】 旧版-商家转账订单状态
     *  【批次状态】ACCEPTED:已受理。批次已受理成功，即将进入转账中。若发起30分钟后仍处于该状态，可能原因是商户账户余额不足等。在24小时内充值充足资金将继续执行转账，请再次查单确认状态。
     *  PROCESSING:转账中。已开始处理批次内的转账明细单
     *  FINISHED:已完成。批次内的所有转账明细单都已处理完成
     *  CLOSED:已关闭。可查询具体的批次关闭原因确认
     */
    @SerializedName("batch_status")
    private String batchStatus;

    /*
     * [key_version】 0：旧版，1：新版
     */
    private Integer keyVersion;
 
    @SerializedName("mch_id")
    private String mchId;
 
    @SerializedName("transfer_amount")
    private Integer transferAmount;
 
    @SerializedName("openid")
    private String openid;
 
    @SerializedName("package_info")
    private String packageInfo;

    /**
     * 【失败原因】 订单已失败或者已退资金时，会返回订单失败原因
     * <a href="https://pay.weixin.qq.com/doc/v3/merchant/4013774966">...</a>
     */
    @SerializedName("fail_reason")
    private String failReason;
 
 
    /**
     * 【单据创建时间】遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，
     * yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss.表示时分秒，
     * TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35+08:00表示北京时间2015年05月20日13点29分35秒。
     */
    @SerializedName("create_time")
    private Date createTime;
 
    /**
     * 【最后一次状态变更时间】遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，
     * yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss.表示时分秒，
     * TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35+08:00表示北京时间2015年05月20日13点29分35秒。
     */
    @SerializedName("update_time")
    private Date updateTime;
}