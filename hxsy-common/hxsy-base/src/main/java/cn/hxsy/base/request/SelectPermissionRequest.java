package cn.hxsy.base.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> XiaQL
 * @description : 系统相关查询接口限制参数
 * 根据查询工具类中方法筛选，设置对应场景下用户具备角色应有查询条件
 * @ClassName : SelectPermissionRequest
 * @date: 2025-05-14 22:47
 */
@Data
public class SelectPermissionRequest {

    @ApiModelProperty(value = "所属总部")
    private Integer headquartersId;

    @ApiModelProperty(value = "栏目")
    private Integer columnId;

    @ApiModelProperty(value = "公司")
    private Integer companyId;

    @ApiModelProperty(value = "销售组")
    private Integer salesGroupId;

    @ApiModelProperty("可见栏目范围 栏目、公司、销售组管理员优先使用")
    private List<String> perColumnId;

    @ApiModelProperty("可见公司范围 栏目、公司、销售组管理员优先使用")
    private List<String> perCompanyId;

    @ApiModelProperty("可见销售组范围 栏目、公司、销售组管理员优先使用")
    private List<String> perSalesGroupId;

}
