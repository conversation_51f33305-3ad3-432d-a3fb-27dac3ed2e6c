package cn.hxsy.base.constant;

/**
 * <AUTHOR>
 */
public enum ResponseType {

    /**
     * 成功
     */
    Success(0, "成功"),

    /**
     * 失败
     */
    Failure(1, "失败");

    private Integer code;

    private String desc;

    ResponseType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getCodeToString() {
        return String.valueOf(code);
    }
}
