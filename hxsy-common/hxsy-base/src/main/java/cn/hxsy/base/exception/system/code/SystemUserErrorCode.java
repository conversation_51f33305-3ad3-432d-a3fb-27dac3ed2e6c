
package cn.hxsy.base.exception.system.code;

import cn.hxsy.base.exception.ErrorCode;

/**
 * 业务通用错误码
 *
 * <AUTHOR>
 */
public enum SystemUserErrorCode implements ErrorCode {

    /**
     * 业务人员权限不足
     */
    SYSTEM_USER_INFO_FAIL("SYSTEM_USER_INFO_FAIL", "业务人员信息已过期，请重新登录"),

    /**
     * 业务人员权限不足
     */
    OPERATION_NOT_PERMISSION("OPERATION_NOT_PERMISSION", "不具备对应操作权限，请联系管理员分配");



    private String code;


    private String message;

    SystemUserErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
