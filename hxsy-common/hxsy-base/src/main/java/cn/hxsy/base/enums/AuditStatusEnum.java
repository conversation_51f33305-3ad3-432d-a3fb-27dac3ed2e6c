package cn.hxsy.base.enums;

/**
 * <AUTHOR>
 * @date 2025/04/04
 */
public enum AuditStatusEnum implements BaseEnum {
    PENDING(0, "待审核"),
    APPROVED(1, "驳回"),
    REJECTED(2, "已通过");

    private final int code;
    private final String info;

    AuditStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
