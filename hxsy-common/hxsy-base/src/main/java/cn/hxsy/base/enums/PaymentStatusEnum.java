package cn.hxsy.base.enums;

/**
 * 支付状态枚举
 */
public enum PaymentStatusEnum implements BaseEnum {
    PENDING(0, "待支付"),
    PAID(1, "已支付"),
    CANCELLED(2, "已取消（未支付，订单已关闭）"),
    EXPIRED(3, "未支付、订单已过期"),
    REFUND_APPLYING(4, "申请退款中"),
    REFUND_SUCCESS(5, "退款成功"),
    REFUND_REJECTED(6, "退款被拒绝"),
    INVALID(7, "订单已失效");

    private final int code;
    private final String info;

    PaymentStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}