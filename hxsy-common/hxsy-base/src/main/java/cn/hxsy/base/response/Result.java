package cn.hxsy.base.response;

import cn.hxsy.base.constant.ResponseType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class Result<T> implements Serializable {
    private int code;
    private T data;
    private String msg;

    public Result(int code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    public static <T> Result<T> ok(T data){
        return new Result<T>(0, data, "ok");
    }

    public static <T> Result<T> ok(){
        return new Result<T>(0, null, "ok");
    }

    public static <T> Result<T> error(String msg){
        return new Result<T>(1, null, msg);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Boolean isSuccess(){
        return this.code == ResponseType.Success.getCode();
    }
}