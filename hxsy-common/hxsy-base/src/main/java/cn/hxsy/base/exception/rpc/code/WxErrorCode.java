
package cn.hxsy.base.exception.rpc.code;

import cn.hxsy.base.exception.ErrorCode;

/**
 * 业务通用错误码
 *
 * <AUTHOR>
 */
public enum WxErrorCode implements ErrorCode {

    request_failure("1", "小程序配置获取失败"),
    code_invalid("40029", "js_code无效"),
    api_minute("45011", "API调用太频繁，请稍候再试"),
    code_blocked("40226", "高风险等级用户，小程序登录拦截 。风险等级详见用户安全解方案"),
    system_error("-1", "系统繁忙，此时请开发者稍候再试");


    private String code;


    private String message;

    WxErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
