package cn.hxsy.base.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("栏目树形结构")
public class ColumnTreeResponse {
    
    @ApiModelProperty("栏目ID")
    private Long id;
    
    @ApiModelProperty("栏目名称")
    private String name;
    
    @ApiModelProperty("公司列表")
    private List<CompanyTreeResponse> companies;
}
