package cn.hxsy.base.enums;

/**
 * 标签层级枚举
 */
public enum TagLevelEnum implements BaseEnum {
    FIRST(1, "一级标签组"),
    SECOND(2, "二级标签组");

    private final int code;
    private final String info;

    TagLevelEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
} 