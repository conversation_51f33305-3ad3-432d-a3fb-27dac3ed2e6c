package cn.hxsy.base.enums;

/**
 * 企微添加状态枚举
 * 0-未添加 1-已添加 9-已删除
 */
public enum WeComAddStatusEnum implements BaseEnum {
    NOT_ADDED(0, "未添加"),
    ADDED(1, "已添加"),
    DELETED(9, "已删除");

    private final int code;
    private final String info;

    WeComAddStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}