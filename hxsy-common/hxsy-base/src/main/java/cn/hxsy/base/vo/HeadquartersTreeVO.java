package cn.hxsy.base.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("总部树形结构")
public class HeadquartersTreeVO {
    
    @ApiModelProperty("总部ID")
    private Long id;
    
    @ApiModelProperty("总部名称")
    private String name;
    
    @ApiModelProperty("栏目列表")
    private List<columnTreeVO> columns;
}

@Data
@ApiModel("栏目树形结构")
class columnTreeVO {
    
    @ApiModelProperty("栏目ID")
    private Long id;
    
    @ApiModelProperty("栏目名称")
    private String name;
    
    @ApiModelProperty("公司列表")
    private List<CompanyTreeVO> companies;
}

@Data
@ApiModel("公司树形结构")
class CompanyTreeVO {
    
    @ApiModelProperty("公司ID")
    private Long id;
    
    @ApiModelProperty("公司名称")
    private String name;
    
    @ApiModelProperty("销售组列表")
    private List<SalesGroupVO> salesGroups;
}

@Data
@ApiModel("销售组信息")
class SalesGroupVO {
    
    @ApiModelProperty("销售组ID")
    private Long id;
    
    @ApiModelProperty("销售组名称")
    private String name;
} 