package cn.hxsy.base.constant.qyWechat.secretApp;

/**
* @description: 企微账号对应配置类型-服务商代开发
 * 由于代开发的模版与具体应用对应的suitId与suitSecret不同，所以需要区分
* @author: xiaQL
* @date: 2025/6/19 10:14
*/
public enum QyWechatConfigType {

    /**
     * 客户方企微信息
     */
    CUSTOMER_CORP("0", "客户方企微信息"),

    /**
     * 服务商通讯录应用
     */
    PROVIDER_TEMPLATE("1", "服务商模版"),

    /**
     * 服务商通讯录应用
     */
    PROVIDER_CONTACT("2", "服务商通讯录应用"),

    ;

    private final String code;
    private final String info;

    QyWechatConfigType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}
