package cn.hxsy.base.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;

/**
 * <p>
 * 单条企微人员信息同步关联请求实体类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17 12:19:33
 */
@Getter
@Setter
@ApiModel(value = "SystemUserQyRelation对象", description = "业务人员账号与企微信息关联")
public class SystemUserQyRelationRequest extends BaseRequestDTO {

    @ApiModelProperty("业务人员id")
    private String systemUserId;

    @ApiModelProperty("业务人员在企业内的UserID")
    private String qyUserId;

    @ApiModelProperty("业务人员所在企业ID")
    private String corpId;


}
