package cn.hxsy.base.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.w3c.dom.Element;

import java.io.Serializable;

/**
 * description : 处理企微回调实体类
 */
@Data
public class QyCallBackRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "xml数据")
    private Element element;

    @ApiModelProperty(value = "公司ID")
    private String corpId;

    @ApiModelProperty("企微账号类型 （0-客户企微，1-服务商模版，2-服务商通讯录应用）")
    private String corpType;

    // 添加其它需要的字段


    @Override
    public String toString() {
        return "QyCallBackRequest{" +
                "element=" + element.getTextContent() +
                ", corpId='" + corpId + '\'' +
                ", corpType='" + corpType + '\'' +
                '}';
    }

}
