package cn.hxsy.base.enums;

public enum YesOrNoEnum implements BaseEnum{

    NO(0, "否"),
    YES(1, "是"),
    ;

    private final int code;
    private final String info;

    YesOrNoEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getInfo() {
        return this.info;
    }

    @Override
    public String getCodeToString() {
        return this.code  + "";
    }
}
