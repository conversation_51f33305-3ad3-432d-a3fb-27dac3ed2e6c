package cn.hxsy.base.util;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import lombok.extern.slf4j.Slf4j;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.StringReader;

@Slf4j
public class WecomXmlUtil {

    /**
     * description : 解析xml
     * @title: parseXml
     * @param: xml
     * <AUTHOR>
     * @date 2025/6/29 21:08
     * @return Element
     */
    public static Element parseXml(String xml) throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(xml)));
        return document.getDocumentElement();
    }

    /**
     * description : 获取节点内容
     * @title: getNodeText
     * @param: element
     * @param: tagName
     * <AUTHOR>
     * @date 2025/6/29 21:08
     * @return String
     */
    public static String getNodeText(Element element, String tagName) {
        NodeList nodes = element.getElementsByTagName(tagName);
        if (nodes != null && nodes.getLength() > 0) {
            return nodes.item(0).getTextContent().trim();
        }
        return null;
    }
}
