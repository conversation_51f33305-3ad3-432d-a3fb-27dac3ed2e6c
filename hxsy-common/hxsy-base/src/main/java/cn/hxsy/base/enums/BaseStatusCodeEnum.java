package cn.hxsy.base.enums;

public enum BaseStatusCodeEnum implements BaseEnum {

    MOBILE_PHONE_NUMBER_BOUND(40001, "手机号已绑定"),
    LOGIN_WAS_SUCCESSFUL(40002, "注册成功"),
    BINDING_SUCCEEDED(40003, "绑定成功"),
    INVALID_AUTHORIZATION_CODE(40004, "授权码无效"),
    ACCOUNT_IS_LOCKED(40005, "账户已锁定，请联系管理员"),

    INVALID_TOKENS_OR_TOKENS_EXPIRED(40301, "Token已过期，请重新登录"),
    WRONG_USER_NAME_OR_PASSWORD(40302, "用户名或密码错误"),

    NO_PERMISSION(40601, "暂无权限"),

    ;

    private final int code;
    private final String info;

    BaseStatusCodeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }


    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }

}
