package cn.hxsy.base.enums;

/**
 * 课程状态枚举
 */
public enum CourseStatusEnum implements BaseEnum {

    NOT_ATTENDED(0, "未到课"),
    ATTENDED(1, "已到课"),
    COMPLETED(2, "已完课");

    private final int code;
    private final String info;

    CourseStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
} 