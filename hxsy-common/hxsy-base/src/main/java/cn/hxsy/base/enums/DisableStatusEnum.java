package cn.hxsy.base.enums;
/**
 * description : 禁用状态枚举
 */
public enum DisableStatusEnum implements BaseEnum {
    /**
     * 启用
     **/
    ENABLE(0, "启用"),
    /**
     * 禁用
     **/
    DISABLE(1, "禁用"),
    /**
     * 待启用
     **/
    TO_BE_ENABLED(2, "待启用");
    private final int code;
    private final String info;

    DisableStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
