package cn.hxsy.base.enums;

/**
 * <AUTHOR>
 * @date 2025/04/04
 */
public enum WXTypeEnum implements BaseEnum {
    MINI_PROGRAM(1, "小程序"),
    OFFICAIAL(2, "公众号");

    private final int code;
    private final String info;

    WXTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
