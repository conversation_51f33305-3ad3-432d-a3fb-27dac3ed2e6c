package cn.hxsy.base.enums;

/**
 * 订单类型枚举
 */
public enum OrderTypeEnum implements BaseEnum {
    BUY_ACCOUNT(1, "购买账号"),
    RENEW_ACCOUNT(2, "续期账号"),
    HISTORICAL_ENTERPRISE_MIGRATION(5, "历史企业迁移订单"),
    HISTORICAL_CONTRACT_MIGRATION(6, "历史合同迁移订单"),
    /**
     * 多企业新购订单（只返回父订单，且仅当corpid不填时返回）
     */
    MULTI_ENTERPRISE_PURCHASE(8, "多企业新购订单");

    private final int code;
    private final String info;

    OrderTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}