package cn.hxsy.base.enums;

/**
 * 账号状态枚举
 */
public enum AccountStatusEnum implements BaseEnum {
    UNASSIGNED(0, "未分配"),
    ASSIGNED(1, "已分配");

    private final int code;
    private final String info;

    AccountStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}