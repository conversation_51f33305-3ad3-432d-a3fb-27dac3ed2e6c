package cn.hxsy.base.request;

import lombok.*;
/**
 * 发起商家转账参数
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class wxPayRequest extends BaseRequest{

    private String appId;
    private Long columnId;
    private Long companyId;
    private Long campPeriodId;
    private Long courseId;
    private Long salesId;
    private Long salesGroupId;
    private String courseName;
    private String amount;
    private Long customerId;
    private String customerName;
    private String openid;

}