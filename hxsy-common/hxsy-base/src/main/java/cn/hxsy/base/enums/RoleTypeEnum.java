package cn.hxsy.base.enums;

/**
 * 类名称：$CLASSNAME$
 * <p>
 * 描述：角色类型
 * 创建人: jinseyeon
 * 创建时间: 2025/04/04
 */

public enum RoleTypeEnum implements BaseEnum{

    SYSTEM_ADMIN(1, "系统超管"),
    COLUMN_ADMIN(2, "栏目管理员"),
    COM_ADMIN(3, "公司管理员"),
    SALES_USER(4, "销售人员"),
    ;

    private final int code;
    private final String info;

    RoleTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getInfo() {
        return this.info;
    }

    @Override
    public String getCodeToString() {
        return this.code  + "";
    }
}
