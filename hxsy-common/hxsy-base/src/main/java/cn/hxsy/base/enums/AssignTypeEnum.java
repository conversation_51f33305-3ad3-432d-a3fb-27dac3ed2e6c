package cn.hxsy.base.enums;

/**
 * 分配类型枚举
 *
 * 1-手动分配
 * 2-自动分配
 */
public enum AssignTypeEnum implements BaseEnum {

    MANUAL(1, "手动分配"),
    AUTOMATIC(2, "自动分配");
    
    private final int code;
    private final String description;
    
    AssignTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    @Override
    public int getCode() {
        return code;
    }
    
    @Override
    public String getInfo() {
        return description;
    }
    
    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
} 