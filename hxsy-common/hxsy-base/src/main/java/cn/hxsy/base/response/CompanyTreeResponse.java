package cn.hxsy.base.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("公司树形结构")
public class CompanyTreeResponse {
    
    @ApiModelProperty("公司ID")
    private Long id;
    
    @ApiModelProperty("公司名称")
    private String name;
    
    @ApiModelProperty("销售组列表")
    private List<SalesGroupResponse> salesGroups;
}
