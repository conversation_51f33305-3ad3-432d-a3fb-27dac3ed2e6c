package cn.hxsy.base.constant.qyWechat.auth;

/**
* @description: 企微服务商认证相关场景使用常量
* @author: xiaQL
* @date: 2025/6/19 10:14
*/
public enum QyWechatAuthType {

    /**
     * 服务商凭证secret
     */
    PROVIDER_TOKEN("1", "provider_token"),

    /**
     * 代开发应用模板凭证
     */
    SUITE_ACCESS_TOKEN("2", "suite_access_token"),

    /**
     * 企微对应access_token缓存前缀
     */
    QY_CORP_ACCESS_TOKEN("3", "qy:corp:access_token:"),

    ;

    private final String code;
    private final String info;

    QyWechatAuthType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}
