package cn.hxsy.base.constant.user;

import cn.hxsy.base.enums.BaseEnum;

/**
* @description: 用户查询权限限制接口
* @author: xiaQL
* @date: 2025/5/14 22:24
*/
public enum UserSelectType implements BaseEnum {

    /**
     * 组织架构_树形
     */
    organization(1, "organization"),

    /**
     * 客户管理
     */
    customer(2, "customer"),

    /**
     * 用户管理
     */
    userManage(3, "userManager"),

    /**
     * 训练营管理
     */
    train(4, "train"),

    /**
     * 栏目查询
     */
    column(5, "column"),

    /**
     * 公司查询
     */
    company(6, "company"),

    /**
     * 销售组查询
     */
    saleGroup(7, "saleGroup"),

    /**
     * 组织架构_小程序
     */
    organization_app(8, "organization_app"),


    /**
     * 系统用户管理
     */
    system_user(9, "system_user"),

    ;

    private final int code;
    private final String info;

    UserSelectType(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
