package cn.hxsy.base.enums;

import lombok.Getter;

/**
 * 转账状态枚举
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Getter
public enum TransferStateEnum {

    ACCEPTED(1, "转账已受理"),

    PROCESSING(2, "转账锁定资金中"),

    WAIT_USER_CONFIRM(3, "待收款用户确认"),

    TRANSFERING(4, "转账中"),

    SUCCESS(5, "转账成功"),

    FAIL(6, "转账失败"),

    CANCELING(7, "转账撤销中"),

    CANCELLED(8, "转账已撤销"),

    CLOSED(9, "转账已关闭"),
    ;

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    TransferStateEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据状态码获取枚举值
     *
     * @param code 状态码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static TransferStateEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TransferStateEnum state : TransferStateEnum.values()) {
            if (state.getCode().equals(code)) {
                return state;
            }
        }
        return null;
    }

    /**
     * 判断当前状态是否为最终状态
     *
     * @return true表示是最终状态（成功、失败、已撤销），false表示是中间状态
     */
    public boolean isFinalState() {
        return this == SUCCESS || this == FAIL || this == CANCELLED;
    }

    /**
     * 判断当前状态是否可以撤销
     *
     * @return true表示可以撤销，false表示不可撤销
     */
    public boolean canCancel() {
        return this == ACCEPTED || this == PROCESSING ||
               this == WAIT_USER_CONFIRM || this == TRANSFERING;
    }

    @Override
    public String toString() {
        return String.format("%s(%d, %s)", this.name(), this.code, this.description);
    }

    /**
     * 根据枚举名称获取对应的状态码
     *
     * @param name 枚举名称（如 "ACCEPTED"）
     * @return 对应的状态码，如果没找到返回null
     */
    public static Integer getCodeByName(String name) {
        if (name == null) {
            return null;
        }
        try {
            return valueOf(name).getCode();
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}