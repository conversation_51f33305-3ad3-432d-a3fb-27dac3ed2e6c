package cn.hxsy.base.enums;

/**
 * 手机号状态枚举
 */
public enum MobileStatusEnum implements BaseEnum {
    NONE(0, "无手机号"),
    HAS(1, "有手机号");

    private final int code;
    private final String info;

    MobileStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
} 