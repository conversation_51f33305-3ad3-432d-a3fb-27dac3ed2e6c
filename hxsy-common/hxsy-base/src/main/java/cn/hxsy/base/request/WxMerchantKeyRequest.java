package cn.hxsy.base.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 微信商户密钥请求DTO
 */
@Data
public class WxMerchantKeyRequest extends BaseRequest {

    /**
     * 公司ID
     */
    @NotNull(message = "公司ID不能为空")
    private Long companyId;

    /**
     * 销售组ID
     */
    private Long salesGroupId;

    /**
     * 商户ID
     */
    @NotBlank(message = "商户ID不能为空")
    private String merchantId;
    /**
     * 商户密钥
     */
    @NotBlank(message = "商户密钥不能为空")
    private String privateKey;

    /** 商户 API 私钥路径 */
    private String privateKeyPath;

    /** 微信支付平台公钥路径 */
    private String publicKeyPath;

    /** 微信支付平台公钥 ID */
    private String publicKeyId;

    /** 商户证书序列号 */
    private String merchantSerialNumber;

    /** 平台证书序列号 */
    private String platformSerialNumber;

    /**
     * 密钥版本（0：旧版，1：新版）
     */
    @NotNull(message = "密钥版本不能为空")
    private Integer keyVersion;

    /**
     * 模式（0：证书，1：公钥）
     */
    private Integer mode;

}
