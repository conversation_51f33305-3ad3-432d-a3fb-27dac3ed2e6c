package cn.hxsy.base.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("总部树形结构")
public class HeadquartersTreeResponse {
    
    @ApiModelProperty("总部ID")
    private Integer id;
    
    @ApiModelProperty("总部名称")
    private String name;
    
    @ApiModelProperty("栏目列表")
    private List<ColumnTreeResponse> columns;
}

