package cn.hxsy.base.enums;

/**
 * 用户操作类型
 * <AUTHOR>
 */
public enum UserOperateTypeEnum  implements BaseEnum {
    /**
     * 注册
     **/
    REGISTER(0, "register"),
    /**
     * 登录
     **/
    LOGIN(1, "login");
    private final int code;
    private final String info;

    UserOperateTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
