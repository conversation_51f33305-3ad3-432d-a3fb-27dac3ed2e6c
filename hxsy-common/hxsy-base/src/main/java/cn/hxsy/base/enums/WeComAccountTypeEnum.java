package cn.hxsy.base.enums;

/**
 * 企微账号类型枚举
 */
public enum WeComAccountTypeEnum implements BaseEnum {
    BASIC(1, "基础账号"),
    INTERCONNECTED(2, "互通账号");

    private final int code;
    private final String info;

    WeComAccountTypeEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}