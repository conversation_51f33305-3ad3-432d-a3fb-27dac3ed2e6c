package cn.hxsy.base.exception.system.code;

import cn.hxsy.base.exception.ErrorCode;

/**
 * 错误码
 *
 * <AUTHOR>
 */
public enum WePayErrorCode implements ErrorCode {
    /**
     * 存在分布式锁，红包正在发放
     */
    PROCESS_PAY("PROCESS_PAY", "正在发放，请稍后！"),

    /**
     * 存在数据库记录，红包已领取
     */
    SUCCESS_PAY("SUCCESS_PAY", "您已经领取过了！"),

    /**
     * 商家信息不存在
     */
    PAY_ACCOUNT_NO_EXIST("PAY_ACCOUNT_NO_EXIST", "商户信息获取失败！"),

    /**
     * 前端金额转换异常
     */
    PAY_AMOUNT_FAILED("PAY_AMOUNT_FAILED", "红包金额异常，请重试"),

    /**
     * 前端金额转换异常
     */
    CAMP_COURSE_NO_EXIST("CAMP_COURSE_NO_EXIST", "该课程不存在！"),

    /**
     * 红包领取超过次数
     */
    REDPACKET_LIMIT_MAX("REDPACKET_LIMIT_MAX", "红包领取超过次数！"),

    /**
     * 领取
     */
    PAY_ERROR("PAY_ERROR", "领取失败！"),

    ;

    private String code;

    private String message;

    WePayErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getMessage() {
        return this.message;
    }
}
