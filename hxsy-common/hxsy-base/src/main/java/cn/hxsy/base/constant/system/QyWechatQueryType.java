package cn.hxsy.base.constant.system;

/**
* @description: 企微相关查询场景
 * 不同功能下需要使用不同secret，正常登录就是用企微信息中的secret，而像通讯录等具体操作需要获取到对应功能下的secret
* @author: xiaQL
* @date: 2025/6/19 10:14
*/
public enum QyWechatQueryType {

    /**
     * 企业自建应用secret
     */
    COMMON("1", "企业自建应用secret"),

    /**
     * 通讯录
     */
    CONTACT("2", "通讯录管理"),

    /**
     * 客户管理
     */
    CUSTOMER("3", "客户管理"),

    ;

    private final String code;
    private final String info;

    QyWechatQueryType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

}
