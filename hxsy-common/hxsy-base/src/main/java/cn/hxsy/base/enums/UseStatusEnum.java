package cn.hxsy.base.enums;
public enum UseStatusEnum implements BaseEnum {
    /**
     * 无效-逻辑删除
     **/
    INVALID(0, "无效"),
    /**
     * 有效
     **/
    EFFECTIVE(1, "有效");
    private final int code;
    private final String info;

    UseStatusEnum(int code, String info) {
        this.code = code;
        this.info = info;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getInfo() {
        return info;
    }

    @Override
    public String getCodeToString() {
        return String.valueOf(code);
    }
}
