package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.CampPeriodPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 课程视频 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 20250401 03:07
 */
@Mapper
public interface CampPeriodMapper extends BaseMapper<CampPeriodPO> {

    List<CampPeriodPO> selectByCompanyAndSales(@Param("companyId") Long companyId,
                                               @Param("salesId") Integer salesId);

}
