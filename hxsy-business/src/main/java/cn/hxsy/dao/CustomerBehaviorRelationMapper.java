package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.*;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface CustomerBehaviorRelationMapper {

    // 1. 营期总会员人数
    @Select("SELECT COUNT(DISTINCT customer_id) FROM customer_behavior " +
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId} AND status = 1")
    Integer countDistinctCustomers(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId
    );
//    @Select({
//            "<script>",
//            "SELECT COUNT(DISTINCT customer_id) FROM customer_behavior_${tableSuffix}",
//            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId} AND status = 1",
//            "</script>"
//    })
//    Integer countDistinctCustomers(
//            @Param("companyId") Long companyId,
//            @Param("campPeriodId") Long campPeriodId,
//            @Param("tableSuffix") String tableSuffix
//    );

    // 2. 昨日营期总会员人数
    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT customer_id) FROM customer_behavior ",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId}",
            "AND status = 1 AND created_at BETWEEN #{startDate} AND #{endDate}",
            "</script>"
    })
    Integer countDistinctCustomersByDate(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    // 3. 昨日新增会员
    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT customer_id) FROM customer_behavior ",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId}",
            "AND status = 1 AND created_at BETWEEN #{startDate} AND #{endDate}",
            "</script>"
    })
    Integer countNewCustomers(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    // 4. 昨日上线人数
    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT customer_id) FROM customer_behavior ",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId}",
            "AND status = 1 AND created_at BETWEEN #{startDate} AND #{endDate}",
            "</script>"
    })
    Integer countActiveCustomers(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );
    // 5.今日新增会员
    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT customer_id) FROM customer_behavior ",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId}",
            "AND status = 1 AND created_at BETWEEN #{startDate} AND #{endDate}",
            "</script>"
    })
    Integer countTodayActiveCustomers(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select({
            "<script>",
            "SELECT COUNT(*) FROM course_red_packet",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId} AND status = 1 AND state = 5",
            "</script>"
    })
    Integer countRedPacket(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId);

    @Select({
            "<script>",
            "SELECT COALESCE(SUM(CAST(transfer_amount AS DECIMAL(10,2))), 0) FROM course_red_packet",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId} AND status = 1 AND state = 5",
            "</script>"
    })
    BigDecimal amountRedPacket(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId);

    // 解析JSON并查询课程信息
    @Select({
            "<script>",
            "SELECT",
            " course_info ",
            "FROM camp_course",
            "WHERE camp_id = #{campPeriodId}",
            "</script>"
    })
    List<String> findByCampId(@Param("campPeriodId") Long campPeriodId);


    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT customer_id) FROM customer_course_relation ",
            "WHERE course_id = #{courseId} AND camp_period_id = #{campPeriodId} AND status = 1",
            "</script>"
    })
    Integer countDistinctCustomersMembersById(
            @Param("courseId") Long courseId,
            @Param("campPeriodId") Long campPeriodId
    );

    @Select({
            "<script>",
            "SELECT COUNT(DISTINCT customer_id) FROM customer_course_relation ",
            "WHERE course_id = #{courseId} AND camp_period_id = #{campPeriodId} AND status = 1 AND arrival_status = 2",
            "</script>"
    })
    Integer countDistinctCustomersCompleteById(
            @Param("courseId") Long courseId,
            @Param("campPeriodId") Long campPeriodId
    );

    @Select({
            "<script>",
            "SELECT COUNT(*) FROM course_red_packet",
            "WHERE course_id = #{courseId} AND camp_period_id = #{campPeriodId} AND status = 1 AND state = 5",
            "</script>"
    })
    Integer countDistinctRedPacketById(
            @Param("courseId") Long courseId,
            @Param("campPeriodId") Long campPeriodId
    );

    @Select({
            "<script>",
            "SELECT COALESCE(SUM(CAST(transfer_amount AS DECIMAL(10,2))), 0) FROM course_red_packet",
            "WHERE course_id = #{courseId} AND camp_period_id = #{campPeriodId} AND status = 1 AND state = 5",
            "</script>"
    })
    BigDecimal countDistinctRedPacketAmountById(
            @Param("courseId") Long courseId,
            @Param("campPeriodId") Long campPeriodId
    );

    @Select({
            "<script>",
            "SELECT DISTINCT customer_id FROM customer_behavior_${tableSuffix}",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId} AND status = 1",
            "</script>"
    })
    List<String> countDistinctCustomersById(
            @Param("courseId") Long companyId,
            @Param("courseId") Long campPeriodId,
            @Param("tableSuffix") String tableSuffix
    );

    @Select({
            "<script>",
            "SELECT DISTINCT customer_id FROM customer_course_relation ",
            "WHERE camp_period_id = #{campPeriodId}",
            "AND status = 1 ",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "  <if test='isFlag != null'>",
            "    <if test='isFlag == 0'>",
            "      AND (arrival_status IS NULL OR arrival_status = 0)",
            "    </if>",
            "    <if test='isFlag == 1'>",
            "      AND (arrival_status = 1 OR arrival_status = 2)",
            "    </if>",
            "    <if test='isFlag == 2'>",
            "      AND arrival_status = 2",
            "    </if>",
            "    <if test='isFlag == 3'>",
            "      AND arrival_status = 1",
            "    </if>",
            "  </if>",
            "</script>"
    })
    List<Long> findCustomerIdsByCampPeriod(
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("courseId") Long courseId,
            @Param("isFlag") Integer isFlag
    );

    @Select({
            "<script>",
            "SELECT DISTINCT customer_id FROM customer_sales_relation ",
            "WHERE company_id = #{companyId}",
            "AND column_id = #{columnId}",
            "AND camp_period_id = #{campPeriodId}",
            "AND status = 1 ",
            "  <if test='salesGroupId != null'>",
            "    AND sales_group_id = #{salesGroupId}",
            "  </if>",
            "  <if test='salesId != null'>",
            "    AND sales_id = #{salesId}",
            "  </if>",
            "</script>"
    })
    List<Long> findCustomerIdsBySales(
            @Param("columnId") Long columnId,
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("salesGroupId") Long salesGroupId,
            @Param("salesId") Long salesId
    );

    @Select({
            "<script>",
            "SELECT id, avatar_url, nickname, mobile, mobile_status, gender, ",
            "wework_status, status, last_active_time, created_at",
            "FROM customer ",
            "WHERE id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            " ORDER BY created_at DESC",
            "</script>"
    })
    List<Customer> batchGetCustomers(
            @Param("customerIds") List<Long> customerIds
    );

    @Select({
            "<script>",
            "SELECT DISTINCT id ",
            "FROM customer ",
            "WHERE ",
            " last_active_time BETWEEN #{startDate} AND #{endDate} ",
            " ORDER BY last_active_time DESC",
            "</script>"
    })
    List<Long> findCustomerIdsByDate(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select({
            "<script>",
            "SELECT DISTINCT id ",
            "FROM customer ",
            "WHERE ",
            " created_at BETWEEN #{startDate} AND #{endDate} ",
            " ORDER BY created_at DESC",
            "</script>"
    })
    List<Long> findCustomerIdsByCreateDate(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select({
            "<script>",
            "SELECT customer_id, COUNT(*) as count FROM customer_behavior",
            "WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1",
            "AND camp_period_id = #{campPeriodId}",
            "AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "GROUP BY customer_id",
            "</script>"
            })
    List<VideoViewCountDTO> countVideoViews(
            @Param("customerIds") List<Long> customerIds,
            @Param("campPeriodId") Long campPeriodId,
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT COUNT(id) as count FROM customer_behavior",
            "WHERE behavior_type = 3 AND course_id IS NOT NULL AND status = 1",
            "AND camp_period_id = #{campPeriodId}",
            "AND company_id = #{companyId}",
            "AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "</script>"
    })
    Integer countCustomerVideoViews(
            @Param("customerIds") List<Long> customerIds,
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT customer_id, sales_name FROM customer_sales_relation",
            "WHERE customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            " AND column_id = #{columnId}",
            "</script>"
    })
    @Results({
            @Result(property = "customerId", column = "customer_id"),
            @Result(property = "salesMan", column = "sales_name")
    })
    List<CustomerSalesManDTO> getSalesMan(@Param("customerIds") List<Long> customerIds, @Param("columnId") Long columnId);
    @Select({
            "<script>",
            "SELECT ",
            "   ids.customer_id AS customer_id, ",
            "   COALESCE(COUNT(crp.customer_id), 0) AS count ",
            "FROM (",
            "   <foreach item='id' collection='customerIds' separator=' UNION ALL '>",
            "       SELECT #{id} AS customer_id",
            "   </foreach>",
            ") AS ids ",
            "LEFT JOIN course_red_packet crp ON ids.customer_id = crp.customer_id ",
            " AND status = 1 AND state = 5",
            " AND camp_period_id = #{campPeriodId}",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "GROUP BY ids.customer_id",
            "</script>"
    })
    @Results({
            @Result(property = "customerId", column = "customer_id"),
            @Result(property = "count", column = "count")
    })
    List<VideoViewCountDTO> getRedPacket(
            @Param("customerIds") List<Long> customerIds,
            @Param("campPeriodId") Long campPeriodId, // 分片键参数
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT customer_id, COUNT(*) as count FROM customer_course_relation",
            "WHERE status = 1 AND camp_period_id = #{campPeriodId}",
            "AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "GROUP BY customer_id",
            "</script>"
    })
    List<VideoViewCountDTO> countCustomersViewById(
            @Param("customerIds") List<Long> customerIds,
            @Param("campPeriodId") Long campPeriodId, // 分片键参数
            @Param("courseId") Long courseId
    );


    @Select({
            "<script>",
            "SELECT customer_id, play_progress FROM customer_course_relation",
            "WHERE status = 1 AND camp_period_id = #{campPeriodId}",
            "AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "</script>"
    })
    List<VideoViewCountDTO> countCustomersViewTimeById(
            @Param("customerIds") List<Long> customerIds,
            @Param("campPeriodId") Long campPeriodId, // 分片键参数
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT customer_id,COALESCE(COUNT(customer_id), 0) AS count FROM customer_course_relation ",
            "WHERE status = 1 AND camp_period_id = #{campPeriodId}",
            " AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            " AND arrival_status = 2 ",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "GROUP BY customer_id",
            "</script>"
    })
    List<VideoViewCountDTO> countCustomersCompleteById(
            @Param("customerIds") List<Long> customerIds,
            @Param("campPeriodId") Long campPeriodId, // 分片键参数
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT customer_id,complete_time FROM customer_course_relation ",
            "WHERE status = 1 AND camp_period_id = #{campPeriodId}",
            " AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            " AND arrival_status = 2 ",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            // 添加倒序排序
            " ORDER BY complete_time DESC",
            "</script>"
    })
    List<VideoViewCountDTO> countCustomersCompleteTimeById(
            @Param("customerIds") List<Long> customerIds,
            @Param("campPeriodId") Long campPeriodId, // 分片键参数
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT count(id) FROM customer_course_relation ",
            "WHERE status = 1 AND camp_period_id = #{campPeriodId}",
            " AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            " AND arrival_status = 2 ",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "</script>"
    })
    Integer countCustomerVideoCompleteById(
            @Param("customerIds") List<Long> customerIds,
            @Param("campPeriodId") Long campPeriodId, // 分片键参数
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT COUNT(*) FROM course_red_packet",
            "WHERE  company_id = #{companyId} AND camp_period_id = #{campPeriodId} AND status = 1 AND state = 5",
            " AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "</script>"
    })
    Integer countCustomerRedPacket(
            @Param("customerIds") List<Long> customerIds,
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("courseId") Long courseId
    );

    @Select({
            "<script>",
            "SELECT COALESCE(SUM(CAST(transfer_amount AS DECIMAL(10,2))), 0) FROM course_red_packet",
            "WHERE company_id = #{companyId} AND camp_period_id = #{campPeriodId} AND status = 1 AND state = 5",
            " AND customer_id IN ",
            "<foreach item='id' collection='customerIds' open='(' separator=',' close=')'>",
            "   #{id}",
            "</foreach>",
            "  <if test='courseId != null'>",
            "    AND course_id = #{courseId}",
            "  </if>",
            "</script>"
    })
    BigDecimal countCustomerRedPacketAmount(
            @Param("customerIds") List<Long> customerIds,
            @Param("companyId") Long companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("courseId") Long courseId
    );

}
