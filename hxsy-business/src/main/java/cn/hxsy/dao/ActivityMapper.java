package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.ActivityPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface ActivityMapper extends BaseMapper<ActivityPO> {

    @Update("UPDATE activity SET remaining_quantity = remaining_quantity - 1 " +
            "WHERE id = #{id} AND remaining_quantity > 0")
    int decrementStock(@Param("id") Long id);

    @Select("SELECT * FROM activity WHERE id = #{id}")
    ActivityPO selectActivityById(@Param("id") Long id);

}
