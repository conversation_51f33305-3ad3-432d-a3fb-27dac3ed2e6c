package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.CampCoursePO;
import cn.hxsy.datasource.model.entity.CampPeriodPO;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CampCourseMapper extends BaseMapper<CampCoursePO> {
    void batchInsert(@Param("list") List<CampCoursePO> list);

    @Select("SELECT COALESCE(MAX(order_number), 0) FROM camp_course WHERE camp_id = #{campId}")
    Integer selectMaxOrderNumber(@Param("campId") Long campId);

    List<CampCoursePO> selectFirstCourseByCampIds(@Param("campIds") List<Long> campIds);

    List<CampCoursePO> selectByCourseIds(@Param("courseIds") List<Long> courseIds);

}