package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.CourseVideoPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程视频 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 20250401 03:07
 */
@Mapper
public interface CourseVideoMapper extends BaseMapper<CourseVideoPO> {
    List<CourseVideoPO> selectByGroupIds(@Param("groupIds") List<Long> groupIds);

    List<CourseVideoPO> selectByCourseIds(@Param("courseIds") List<Long> courseIds);
}
