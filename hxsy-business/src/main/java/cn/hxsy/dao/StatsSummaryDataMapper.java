package cn.hxsy.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

@Mapper
public interface StatsSummaryDataMapper {

    /**
     * 统计所有分表中的总客户数
     */
    @Select("SELECT COUNT(*) FROM customer ")
    Long countTotalCustomers();

    @Select("SELECT COUNT(*) FROM company "+
            "WHERE status = 1 ")
    Long countTotalCompanies();

    @Select("SELECT COUNT(*) FROM system_user "+
            "WHERE role_id = 12 AND status = 1")
    Long countTotalSalesAccounts();

    @Select({"<script>",
            "SELECT COUNT(*) FROM system_user",
            "WHERE role_id = 12 AND status = 1",
            "<if test='id != null'>",
            "   AND company_id = #{id}",
            "</if>",
            "</script>"})
    Long countTotalSalesAccountsById(@Param("id") Long id);

    /**
     * 统计指定时间段内的新增客户数
     */
    @Select("SELECT COUNT(*) FROM customer WHERE created_at BETWEEN #{startDate} AND #{endDate}")
    Long countNewMembers(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select("SELECT COUNT(*) FROM customer_behavior " +
            "WHERE behavior_type = 3 AND course_id IS NOT NULL " +
            "AND status = 1 " +
            "AND created_at BETWEEN #{startDate} AND #{endDate}")
    Long countTodayViews(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select("SELECT COUNT(*) FROM customer_course_relation " +
            "WHERE status = 1 AND arrival_status = 2 " +
            "AND created_at BETWEEN #{startDate} AND #{endDate}")
    Long countTodayComplete(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select("SELECT COUNT(*) FROM course_red_packet "+
            "WHERE status = 1 AND state = 5 "+
            "AND created_at BETWEEN #{startDate} AND #{endDate}")
    Long countTodayRedPackets(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select("SELECT COUNT(*) FROM company "+
            "WHERE status = 1 "+
            "AND created_at BETWEEN #{startDate} AND #{endDate}")
    Long countMonthNewCompanies(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    @Select({"<script>",
            "SELECT COUNT(*) FROM system_user",
            "WHERE role_id = 12 AND status = 1",
            "AND created_at BETWEEN #{startDate} AND #{endDate}",
            "<if test='id != null'>",
            "   AND company_id = #{id}",
            "</if>",
            "</script>"})
    Long countMonthNewSalesAccountsById(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate,
            @Param("id") Long id
    );

    @Select("SELECT COUNT(*) FROM system_user "+
            "WHERE role_id = 12 AND status = 1 "+
            "AND created_at BETWEEN #{startDate} AND #{endDate}")
    Long countMonthNewSalesAccounts(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    // 统计截至某个时间点的总数
    @Select("SELECT COUNT(*) FROM customer WHERE created_at <= #{endDate}")
    Long countTotalCustomersAsOf(@Param("endDate") LocalDateTime endDate);

    @Select("SELECT COUNT(*) FROM company WHERE status = 1 AND created_at <= #{endDate}")
    Long countTotalCompaniesAsOf(@Param("endDate") LocalDateTime endDate);

    @Select("SELECT COUNT(*) FROM system_user WHERE role_id = 12 AND status = 1 AND created_at <= #{endDate}")
    Long countTotalSalesAccountsAsOf(@Param("endDate") LocalDateTime endDate);


}
