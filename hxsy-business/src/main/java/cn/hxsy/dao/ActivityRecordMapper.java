package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.ActivityPO;
import cn.hxsy.datasource.model.entity.ActivityRecordPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface ActivityRecordMapper extends BaseMapper<ActivityRecordPO> {

    @Select("SELECT COUNT(*) FROM activity_record " +
            "WHERE user_id = #{userId} AND activity_id = #{activityId} AND action_type = '1'")
    boolean existsReceivedRecord(
            @Param("userId") Long userId,
            @Param("activityId") Long activityId
    );

    @Select("SELECT COUNT(*) FROM activity_record " +
            "WHERE user_id = #{userId} AND activity_id = #{activityId} " +
            "AND action_type = '2'") // 答题动作
    boolean existsQuizRecord(@Param("userId") Long userId,
                             @Param("activityId") Long activityId);

}
