package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.VideoUploadPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 课程视频 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 20250401 03:07:26
 */
@Mapper
public interface VideoUploadMapper extends BaseMapper<VideoUploadPO> {

    int batchInsertVideoUpload(@Param("list") List<VideoUploadPO> list);

    int updateStatusById(Long id, Long updatedBy);

}
