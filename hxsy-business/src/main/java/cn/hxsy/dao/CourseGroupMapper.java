package cn.hxsy.dao;

import cn.hxsy.datasource.model.entity.CourseGroupPO;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CourseGroupMapper extends BaseMapper<CourseGroupPO> {

    List<Long> selectExistingGroupIds(@Param("ids") List<Long> ids);


}
