package cn.hxsy.datasource.mapper;

import cn.hxsy.datasource.model.entity.CourseRedPacket;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 课程红包记录Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CourseRedPacketMapper extends BaseMapper<CourseRedPacket> {

    /**
     * 根据条件查询红包总金额
     *
     * @param columnId 栏目ID
     * @param companyId 公司ID
     * @param campPeriodId 营期ID
     * @param salesGroupId 销售组ID
     * @param salesId 销售ID
     * @param courseId 课程ID
     * @param customerId 客户ID
     * @param state 状态
     * @param customerName 客户名称
     * @param courseName 课程名称
     * @param merchantId 商户ID
     * @param createStartTime 创建开始时间
     * @param createEndTime 创建结束时间
     * @return 红包总金额
     */
    BigDecimal getTotalAmountByConditions(
            @Param("columnId") Integer columnId,
            @Param("companyId") Integer companyId,
            @Param("campPeriodId") Long campPeriodId,
            @Param("salesGroupId") String salesGroupId,
            @Param("salesId") String salesId,
            @Param("courseId") Long courseId,
            @Param("customerId") Long customerId,
            @Param("state") String state,
            @Param("customerName") String customerName,
            @Param("courseName") String courseName,
            @Param("merchantId") String merchantId,
            @Param("createStartTime") LocalDateTime createStartTime,
            @Param("createEndTime") LocalDateTime createEndTime
    );
}
