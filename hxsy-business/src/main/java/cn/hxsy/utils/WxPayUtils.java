package cn.hxsy.utils;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import cn.hxsy.base.response.TransferNotification;
import cn.hxsy.datasource.model.entity.WxMerchantKeyPO;
import cn.hxsy.model.request.WxPayParam;
import com.alibaba.fastjson.JSONObject;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.PublicKeyVerifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.cert.CertificatesManager;
import com.wechat.pay.contrib.apache.httpclient.notification.Notification;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationHandler;
import com.wechat.pay.contrib.apache.httpclient.notification.NotificationRequest;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.RSAPublicKeyConfig;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.http.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
public class WxPayUtils {

    public static void main(String[] args) {
        // Example usage with WxPayParam object (assuming WxPayParam class exists and has appropriate constructor/getters)
//        WxPayParam param = new WxPayParam();
//        param.setOutBillNo("test8932479dasd23");
//        param.setOpenid("oZMlP64vOWVPtrTVl_DLtFWH4imI");
//        param.setUserName("");
//        param.setMerchantSerialNumber("4755846DE4D2D8DA72EBCC43DFE4B81E7CC4C3DF");
//        param.setAmount(30L);
//        param.setAppId("wxc3839ea2b48d63ea");
//        param.setNotifyUrl("https://www.xxx.com/wechat/notify/transferNotify");
//        createTransferBills(param);
//        String s = downloadPrivateKeyFromUrl("https://hxsy-dev-1351054319.cos.ap-guangzhou.myqcloud.com/wxPay/51020/private/apiclient_key.pem");
//        System.err.println(s);x
//        String key = "xiaotianshishequnxiangmubu666666";
//        byte[] bytes = key.getBytes();
//        // 平台证书管理器
//        CertificatesManager certificatesManager = CertificatesManager.getInstance();
//        certificatesManager.putMerchant(
//                "1691802853",
//                new WechatPay2Credentials(
//                        "1691802853",
//                        new PrivateKeySigner("2A679738584BE8BC4AFB74961DBBFF13BBAEC7AE", PemUtil.loadPrivateKey(s))
//                ),
//                bytes
//        );
//        // 从证书管理器中获取verifier
//        Verifier certificateVerifier = certificatesManager.getVerifier("1691802853");
//        System.err.println(certificateVerifier.getSerialNumber());
//        NotificationRequest request = new NotificationRequest.Builder().withSerialNumber("40D86FC270AC302D36D386A20514BE6A5DCDB299")
//                .withNonce("i3AYPPzIPyt03BV3j7I8iZq64gB35Sc4")
//                .withTimestamp("1747058984")
//                .withSignature("zHUlhs2CZpr/PEVeKqAPUZSg9N2ezn9+DisA6TdESZ8waES70mP/sCol+uZxjscIZwhE7fDjEfD02ESW5yWoypptYo+yGVYNz6WQIwUcmMNBQPIHj13RzwIz+CuHBDpYEunZkKnc0l6fI8XH290xGBxe0cRimTIx3jCByBIkObDqpIIgdBRZn4VUPLJq9rYT3Uq7n4DCYbJ1fCCvhxMDpIcL/6DIIrOVF37NYcsh9Pm8w14hzY8xXQXMAyMnxFmPQ4KR9ekqi69Yp19Y8G0gxu4EcW72Ed/EvUC1unZY1SJlkuTXYdooVBAb14HXZtN1Rtu1Mn8g6K7M5MYnqEKMPQ==")
//                .withBody("{\"id\":\"70de27cb-2ae6-5163-a027-4facc2f1f906\",\"create_time\":\"2025-05-12T22:09:40+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"MCHTRANSFER.BILL.FINISHED\",\"summary\":\"商家转账单据终态通知\",\"resource\":{\"original_type\":\"mch_payment\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"WKC5gpMh0rw+9gxwgcESu7obPh7+I5OucXVIoG3VaYQ3/ZP3HrdvRDUefLC1slWm1CUKwyZr7qx4tnOj77dq1H8pxR83lkFJN/Ic6OUvW3t5Mq6DZo7RDC7k8UMsqFu/tqofrsVPMCi0qZ2S4MhWFW0tix9KU8ZR4uJEssSlH7s/RpRRrjZQCOnB7CEtsulNfVtBCWMGKzQPwaq3AwKYFPpZkwGlTO+qtOSXSpIfHDNr5n3yqO7Tj1LOaGnqnWAnkN71am5UarOkALt+2Cq2+9XWm6dajgmtGSU9XyNRICguZ+fhryN6hpVk/jIx9E2AlJSR3bZ0GNk3iYktHKQUSHOsWl+6X1BZCqWV1d3j+oKvFqFY9hAdJJ2P31OAJ9n6xc1koUXjle/9WA9N9kXvEZxnpSd9kPMV6oQ=\",\"associated_data\":\"mch_payment\",\"nonce\":\"2cplQnkguHHI\"}}")
//                .build();
//
//        // 使用平台证书验签器：适用于尚未开始「平台证书」-->「微信支付公钥」迁移的旧商户
//        NotificationHandler handler = new NotificationHandler(certificateVerifier, key.getBytes(StandardCharsets.UTF_8));
//
//        // 验签和解析请求体
//        Notification notification = handler.parse(request);
//
//        System.err.println(">>>>>>>>>>>>"+notification.toString());
    }
    /**
     * 发起转账
     *
     * @param param WxPayParam object containing transfer parameters
     */
    public static TransferNotification createTransferBills(WxPayParam param) {
        OkHttpClient okHttpClient = new OkHttpClient();
        HttpClient httpClient = new DefaultHttpClientBuilder()
                .config(param.getMode()== 0 ? rsaAutoCertificateConfig(param) : rsaPublicKeyConfig(param))
                .okHttpClient(okHttpClient)
                .build();
        HttpHeaders headers = new HttpHeaders();
        headers.addHeader("Accept", MediaType.APPLICATION_JSON.getValue());
        headers.addHeader("Content-Type", MediaType.APPLICATION_JSON.getValue());
        headers.addHeader("Wechatpay-Serial", param.getMerchantSerialNumber());
        // 根据商家支付key版本，构造对应版本支付参数
        HashMap<Object, Object> map = buildWxPayParamByKeyVersion(param);

        JsonRequestBody build = new JsonRequestBody.Builder()
                .body(JSONUtil.toJsonStr(map))
                .build();
        String payUrl = "";
        if(param.getKeyVersion() == 1){
            payUrl = "https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills";
        }else {
            payUrl = "https://api.mch.weixin.qq.com/v3/transfer/batches";
        }
        HttpRequest executeSendGetHttpRequest = new HttpRequest.Builder()
                .httpMethod(HttpMethod.POST)
                .url(payUrl)
                .headers(headers)
                .body(build)
                .build();
        try {
            HttpResponse<TransferNotification> execute = httpClient.execute(executeSendGetHttpRequest, TransferNotification.class);
            return execute.getServiceResponse();
        } catch (ServiceException e) {
            // 捕获微信支付服务异常
            if (e.getHttpStatusCode() == 400 || e.getHttpStatusCode() == 403) {
                // 解析错误响应体
                String errorBody = e.getResponseBody();
                String errorMsg = "微信转账失败";
                try {
                    JSONObject errorJson = JSONObject.parseObject(errorBody);
                    errorMsg = errorJson.getString("message");
                    throw new RuntimeException("红包领取失败：" + errorMsg);
                } catch (Exception jsonEx) {
                    throw new RuntimeException("红包领取失败：" + errorMsg);
                }
            }
            log.error("微信支付服务异常：{}", e.getMessage(), e);
            throw new RuntimeException("微信支付服务异常：" + e.getMessage());
        } catch (Exception e) {
            log.error("系统异常：", e);
            throw new RuntimeException("微信支付转账失败：" + e.getMessage());
        }
    }

    /**
    * @description: 根据商家支付key版本，构造对应版本支付参数
     * 0-旧版 1-新版
    * @author: xiaQL
    * @date: 2025/5/29 14:44
    */
    private static HashMap<Object, Object> buildWxPayParamByKeyVersion(WxPayParam param) {
        // 公共参数
        HashMap<Object, Object> map = new HashMap<>();
        // 商户AppID
        map.put("appid", param.getAppId());
        // 回调通知地址
        map.put("notify_url", param.getNotifyUrl());
        if(param.getKeyVersion() == 1){
            // 新版请求参数
            // 商户单号
            map.put("out_bill_no", param.getOutBillNo());
            // 转账场景ID 新版现金营销对应1000
            map.put("transfer_scene_id", "1000");
            // 收款用户OpenID
            map.put("openid", param.getOpenid());
            // 转账金额（单位：分）
            map.put("transfer_amount", param.getAmount());
            // 转账备注
            map.put("transfer_remark", "看课红包");
            // 转账场景报备信息
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(new JSONObject().fluentPut("info_type", "活动名称").fluentPut("info_content", "活动奖励"));
            jsonArray.add(new JSONObject().fluentPut("info_type", "奖励说明").fluentPut("info_content", "活动奖励红包"));
            map.put("transfer_scene_report_infos", jsonArray);
        }else if (param.getKeyVersion() == 0){
            // 旧版请求参数
            // 商家批次单号 长度 1~32
            String outNo = param.getOutBillNo();
            map.put("out_batch_no", outNo);
            // 转账场景ID 新版现金营销对应1001
//            map.put("transfer_scene_id", "1001");
            // 该笔批量转账的名称
            map.put("batch_name", "活动奖励红包");
            // 转账说明，UTF8编码，最多允许32个字符
            map.put("batch_remark", "活动奖励");
            // 转账总笔数
            map.put("total_num", 1);
            // 转账金额（单位：分）
            map.put("total_amount", param.getAmount());
            List<Map<String, Object>> list = new ArrayList<>();
            Map<String, Object> subMap = new HashMap<>(4);
            //商家明细单号
            subMap.put("out_detail_no", outNo);
            //转账金额
            subMap.put("transfer_amount", param.getAmount());
            //转账备注
            subMap.put("transfer_remark", "活动奖励");
            //用户在直连商户应用下的用户标示
            subMap.put("openid", param.getOpenid());
//		    subMap.put("user_name", RsaCryptoUtil.encryptOAEP(userName, x509Certificate));
            list.add(subMap);
            map.put("transfer_detail_list", list);
        }else {
            log.error("商家支付key版本错误，商家id：{}", param.getMerchantId());
            throw new RuntimeException("网络异常，请稍后重试");
        }
        return map;
    }

    /**
     * API安全加密配置。PUB_KEY_ID_0116918028532025050500321832000200
     */
    private static RSAAutoCertificateConfig rsaAutoCertificateConfig(WxPayParam param) {
        // 证书模式
        String privateKeyContent = downloadPrivateKeyFromUrl(param.getPrivateKeyPath());
        return new RSAAutoCertificateConfig.Builder()
                // 商户号
                .merchantId(param.getMerchantId())
                // 直接从字符串加载私钥，而不是从文件路径
                .privateKey(privateKeyContent)
                // 商户API证书序列号
                .merchantSerialNumber(param.getMerchantSerialNumber())
                // APIv3密钥
                .apiV3Key(param.getApiV3Key())
                .build();//        return new RSAAutoCertificateConfig.Builder()
//                // 商户号
//                .merchantId("1691802853")
//                // 商户API证书私钥的存放路径
//                .privateKeyFromPath("/Users/<USER>/cert/1691802853_20250505_cert/apiclient_key.pem")
//                // 商户API证书序列号
//                .merchantSerialNumber("2A679738584BE8BC4AFB74961DBBFF13BBAEC7AE")
//                // APIv3密钥
//                .apiV3Key("xiaotianshishequnxiangmubu666666")
//                .build();
    }
    private static RSAPublicKeyConfig rsaPublicKeyConfig(WxPayParam param) {
        // 公钥模式
        String privateKeyContent = downloadPrivateKeyFromUrl(param.getPrivateKeyPath());
        String publicKeyContent = downloadPrivateKeyFromUrl(param.getPublicKeyPath());
        // 可以根据实际情况使用publicKeyFromPath或publicKey加载公钥
        return new RSAPublicKeyConfig.Builder()
                        .merchantId(param.getMerchantId()) //微信支付的商户号
                        .privateKeyFromPath(privateKeyContent) // 商户API证书私钥的存放路径
                        .publicKeyFromPath(publicKeyContent) //微信支付公钥的存放路径
                        .publicKeyId(param.getPublicKeyId()) //微信支付公钥ID
                        .merchantSerialNumber(param.getMerchantSerialNumber()) //商户API证书序列号
                        .apiV3Key(param.getApiV3Key()) //APIv3密钥
                        .build();
    }

    // 从URL下载
    private static String downloadPrivateKeyFromUrl(String privateKeyUrl){
        URL url = null;
        try {
            url = new URL(privateKeyUrl);
        } catch (MalformedURLException e) {
            throw new RuntimeException(e);
        }
        try (InputStream in = url.openStream();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            return new String(out.toByteArray(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            log.error("下载私钥失败", e);
        }
        return "";
    }    /**
     * 敏感信息加密
     */
    private String rsaEncryptOAEP(String message) {
        X509Certificate cert = getX509Certificate();
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/OAEPWithSHA-1AndMGF1Padding");
            cipher.init(Cipher.ENCRYPT_MODE, cert.getPublicKey());
            byte[] data = message.getBytes(StandardCharsets.UTF_8);
            byte[] cipherdata = cipher.doFinal(data);
            return Base64.getEncoder().encodeToString(cipherdata);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取 X509Certificate
     */
    private X509Certificate getX509Certificate() {
        ClassLoader classLoader = this.getClass().getClassLoader();
        try (InputStream in = classLoader.getResourceAsStream("v3/platform_cert.pem")) {
            if (in == null) {
                throw new IOException("Resource not found: v3/platform_cert.pem");
            }
            CertificateFactory cf = CertificateFactory.getInstance("X.509");
            return (X509Certificate) cf.generateCertificate(in);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 处理微信支付回调通知
     * @param nonce 随机串
     * @param timestamp 时间戳
     * @param signature 签名
     * @param body 请求体
     * @param merchantKey 商户配置信息
     * @return 解密后的通知数据，验签失败时返回null
     */
    public static JSONObject handlePayNotification(
            String nonce,
            String timestamp,
            String signature,
            String serialNumber,
            String body,
            WxMerchantKeyPO merchantKey
    ) {
        try {

            // 构建request，传入必要参数
            NotificationRequest request = new NotificationRequest.Builder().withSerialNumber(serialNumber)
                    .withNonce(nonce)
                    .withTimestamp(timestamp)
                    .withSignature(signature)
                    .withBody(body)
                    .build();

            NotificationHandler handler;
            if (merchantKey.getMode() == 0) {
                // 证书模式
                String privateKeyContent = downloadPrivateKeyFromUrl(merchantKey.getPrivateKeyPath());
                // 平台证书管理器
                CertificatesManager certificatesManager = CertificatesManager.getInstance();
                certificatesManager.putMerchant(
                        merchantKey.getMerchantId(),
                        new WechatPay2Credentials(
                                merchantKey.getMerchantId(),
                                new PrivateKeySigner(merchantKey.getMerchantSerialNumber(), PemUtil.loadPrivateKey(privateKeyContent))
                        ),
                        merchantKey.getPrivateKey().getBytes(StandardCharsets.UTF_8)
                );
                handler = new NotificationHandler(
                        certificatesManager.getVerifier(merchantKey.getMerchantId()),
                        merchantKey.getPrivateKey().getBytes(StandardCharsets.UTF_8)
                );
            } else {
                String publicKeyContent = downloadPrivateKeyFromUrl(merchantKey.getPublicKeyPath());
                PublicKey wechatPayPublicKey = PemUtil.loadPublicKey(publicKeyContent);
                // 公钥模式
                handler = new NotificationHandler(
                        new PublicKeyVerifier(merchantKey.getPublicKeyId(), wechatPayPublicKey),
                        merchantKey.getPrivateKey().getBytes(StandardCharsets.UTF_8)
                );
            }

            // 验签和解析请求体
            Notification notification = handler.parse(request);
            // 从notification中获取解密报文
            System.out.println(notification.getDecryptData());
            log.info("处理回调通知成功:{}", notification.getDecryptData());
            return JSONObject.parseObject(notification.getDecryptData());
        } catch (Exception e) {
            log.error("处理回调通知异常: {}", e.getMessage(), e);
            return null;
        }
    }
}
