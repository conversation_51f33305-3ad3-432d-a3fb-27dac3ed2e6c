package cn.hxsy.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @author: qinLuan
 * @Description: 线程池配置
 * @Data: 2025-06-08  23:12
 */
@Configuration
public class ThreadPoolConfig {
    /**
     * 核心线程池大小
     */
    @NacosValue(value = "${threadPool.corePoolSize}", autoRefreshed = true)
    public static final int corePoolSize = 8;
    /**
     * 最大可创建的线程数
     */
    @NacosValue(value = "${threadPool.maxPoolSize}", autoRefreshed = true)
    public static final int maxPoolSize = 10;
    /**
     * 队列最大长度
     */
    @NacosValue(value = "${threadPool.queueCapacity}", autoRefreshed = true)
    public static final int queueCapacity = 1000;
    /**
     * 线程池维护线程所允许的空闲时间
     */
    @NacosValue(value = "${threadPool.keepAliveSeconds}", autoRefreshed = true)
    public static final int keepAliveSeconds = 300;
    /**
     * 线程池中的线程的名称前缀
     */
    public static final String threadNamePrefix = "async-thread-";
    @Bean(name = "threadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 1: 创建核心线程数 cpu核数 -- 50
        executor.setCorePoolSize(corePoolSize);
        // 2：线程池维护线程的最大数量，只有在缓存队列满了之后才会申请超过核心线程数的线程
        executor.setMaxPoolSize(maxPoolSize);
        // 3：缓存队列 可以写大一点无非就浪费一点内存空间
        executor.setQueueCapacity(queueCapacity);
        // 4：线程的空闲事件，当超过了核心线程数之外的线程在达到指定的空闲时间会被销毁 200ms
        executor.setKeepAliveSeconds(keepAliveSeconds);
        // 5：线程池中的线程的名称前缀
        executor.setThreadNamePrefix(threadNamePrefix);
        /* 当线程的任务缓存队列已满并且线程池中的线程数量已经达到了最大连接数，如果还有任务来就会采取拒绝策略，
         * 通常有四种策略：
         *ThreadPoolExecutor.AbortPolicy：丢弃任务并抛出异常：RejectedExcutionException异常
         *ThreadPoolExecutor.DiscardPolicy：丢弃任务，但是不抛出异常
         *ThreadPoolExecutor.DiscardOldestPolicy: 丢弃队列最前面的任务，然后重新尝试执行任务（重复此过程）
         *ThreadPoolExecutor.CallerRunsPolicy：重试添加当前的任务，自动重复调用execute()方法，直到成功。
         *ThreadPoolExecutor. 扩展重试3次，如果3次都不充公在移除。
         * */
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
