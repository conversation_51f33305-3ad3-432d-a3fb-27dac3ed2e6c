package cn.hxsy.config;

import cn.hxsy.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

@Configuration
@EnableScheduling
public class ScheduledConfig {

    @Autowired
    private DashboardService dashboardService;

    // 每小时更新Redis数据
    @Scheduled(cron = "0 1 * * * *")
    public void refreshRedisStats() {
        dashboardService.refreshRedisStats();
    }

    // 每日凌晨0点10分持久化数据
    @Scheduled(cron = "0 10 0 * * *")
    public void persistToDatabase() {
        dashboardService.persistToDatabase();
    }
}
