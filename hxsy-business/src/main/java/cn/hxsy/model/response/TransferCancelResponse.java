package cn.hxsy.model.response;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

@Data
public class TransferCancelResponse {
 
    /**
     * 商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一
     */
    @SerializedName("out_bill_no")
    private String outBillNo;
 
    /**
     *  商家转账订单的主键，唯一定义此资源的标识
     */
    @SerializedName("transfer_bill_no")
    private String transferBillNo;
 
    /**
     * 【单据状态】 CANCELING: 撤销中；CANCELLED:已撤销
     */
    @SerializedName("state")
    private String state;
 
    /**
     * 【最后一次单据状态变更时间】 按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE
     */
    @SerializedName("update_time")
    private Date updateTime;
 
}