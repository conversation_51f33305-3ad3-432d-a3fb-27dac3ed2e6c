package cn.hxsy.model.response;

import com.alibaba.nacos.shaded.com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

@Data
public class TransferCreateResponse {
 
    @SerializedName("transfer_bill_no")
    private String transferBillNo;
 
    @SerializedName("out_bill_no")
    private String outBillNo;
 
    @SerializedName("create_time")
    private Date createTime;
 
    /**
     * 【单据状态】 商家转账订单状态
     * 可选取值
     * ACCEPTED: 转账已受理
     * PROCESSING: 转账锁定资金中。如果一直停留在该状态，建议检查账户余额是否足够，如余额不足，可充值后再原单重试。
     * WAIT_USER_CONFIRM: 待收款用户确认，可拉起微信收款确认页面进行收款确认
     * TRANSFERING: 转账中，可拉起微信收款确认页面再次重试确认收款
     * SUCCESS: 转账成功
     * FAIL: 转账失败
     * CANCELING: 商户撤销请求受理成功，该笔转账正在撤销中
     * CANCELLED: 转账撤销完成
     */
    @SerializedName("state")
    private String state;
 
    /**
     * 【失败原因】 订单已失败或者已退资金时，会返回订单失败原因
     * <a href="https://pay.weixin.qq.com/doc/v3/merchant/4013774966">...</a>
     */
    @SerializedName("fail_reason")
    private String failReason;
 
    /**
     * 【跳转领取页面的package信息】 跳转微信支付收款页的package信息，APP调起用户确认收款或者JSAPI调起用户确认收款 时需要使用的参数。
     * 单据创建后，用户24小时内不领取将过期关闭，建议拉起用户确认收款页面前，先查单据状态：如单据状态为待收款用户确认，可用之前的package信息拉起；单据到终态时需更换单号重新发起转账。
     */
    @SerializedName("package_info")
    private String packageInfo;
 
}