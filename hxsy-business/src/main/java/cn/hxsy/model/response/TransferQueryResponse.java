package cn.hxsy.model.response;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

@Data
public class TransferQueryResponse {
 
    @SerializedName("mch_id")
    private String mchid;
 
 
    @SerializedName("transfer_bill_no")
    private String transferBillNo;
 
    @SerializedName("out_bill_no")
    private String outBillNo;
 
    @SerializedName("appid")
    private String appid;
 
    /**
     * 【单据状态】 商家转账订单状态
     * 可选取值
     * ACCEPTED: 转账已受理
     * PROCESSING: 转账锁定资金中。如果一直停留在该状态，建议检查账户余额是否足够，如余额不足，可充值后再原单重试。
     * WAIT_USER_CONFIRM: 待收款用户确认，可拉起微信收款确认页面进行收款确认
     * TRANSFERING: 转账中，可拉起微信收款确认页面再次重试确认收款
     * SUCCESS: 转账成功
     * FAIL: 转账失败
     * CANCELING: 商户撤销请求受理成功，该笔转账正在撤销中
     * CANCELLED: 转账撤销完成
     */
    @SerializedName("state")
    private String state;
 
    @SerializedName("transfer_amount")
    private Integer transferAmount;
 
    @SerializedName("transfer_remark")
    private String transferRemark;
 
    @SerializedName("fail_reason")
    private String failReason;
 
    @SerializedName("openid")
    private String openid;
 
    @SerializedName("user_name")
    private String userName;
 
    /**
     * 【单据创建时间】遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，
     * yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss.表示时分秒，
     * TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35+08:00表示北京时间2015年05月20日13点29分35秒。
     */
    @SerializedName("create_time")
    private Date createTime;
 
    /**
     * 【最后一次状态变更时间】遵循rfc3339标准格式，格式为yyyy-MM-DDTHH:mm:ss+TIMEZONE，
     * yyyy-MM-DD表示年月日，T出现在字符串中，表示time元素的开头，HH:mm:ss.表示时分秒，
     * TIMEZONE表示时区（+08:00表示东八区时间，领先UTC 8小时，即北京时间）。
     * 例如：2015-05-20T13:29:35+08:00表示北京时间2015年05月20日13点29分35秒。
     */
    @SerializedName("update_time")
    private Date updateTime;
}