package cn.hxsy.model.response;

import cn.hxsy.datasource.model.entity.CourseRedPacket;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 课程红包分页响应，包含分页数据和红包总金额
 *
 * <AUTHOR>
 * @date 2024-06-10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CourseRedPacketPageResponse {

    /**
     * 红包分页数据
     */
    private Page<CourseRedPacket> pageData;

    /**
     * 红包总金额
     */
    private BigDecimal totalAmount;
}
