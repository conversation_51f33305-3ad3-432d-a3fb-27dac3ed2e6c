package cn.hxsy.model.response;


import cn.hxsy.datasource.model.entity.CourseRedPacket;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 客户红包汇总信息响应类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel(value = "CustomerRedPacketSummaryResponse", description = "客户红包汇总信息响应")
public class CustomerRedPacketSummaryResponse {

    @ApiModelProperty(value = "已领取红包总金额")
    private String totalReceivedAmount;

    @ApiModelProperty(value = "未领取红包列表")
    private List<CourseRedPacket> unreceivedRedPackets;
}
