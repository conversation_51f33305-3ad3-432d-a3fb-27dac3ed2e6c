package cn.hxsy.model.request;

import cn.hxsy.base.request.BaseRequestDTO;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
* @description: 所有id字段都转为string类型，防止json存储后响应前端，前端有长度问题
* @author: xiaQL
* @date: 2025/4/24 22:10
*/
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ApiModel(value = "CourseVideoPO对象", description = "课程视频信息表")
public class CourseVideoJson extends BaseRequestDTO {

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "课程分组")
    private String groupId;

    @ApiModelProperty(value = "课程封面")
    private String coverPath;

    @ApiModelProperty(value = "课程封面id")
    private String coverId;

    @ApiModelProperty(value = "课程简介")
    private String courseIntroduction;

    @ApiModelProperty(value = "课程介绍")
    private String courseDescription;

    @ApiModelProperty(value = "课程内容")
    private String courseContent;

    @ApiModelProperty(value = "课程开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd")
    private LocalDate startTime;

    @ApiModelProperty(value = "视频前贴片")
    private String patchPath;

    @ApiModelProperty(value = "片头设置")
    private String openingSetting;

    @ApiModelProperty(value = "全屏观看设置")
    private String fullscreenSetting;

    @ApiModelProperty(value = "课程视频")
    private String videoPath;

    @ApiModelProperty(value = "课程视频")
    private String videoId;

    @ApiModelProperty(value = "所属流量池")
    private List<String> coursePool;

    @ApiModelProperty(value = "项目")
    private String courseProject;

    @ApiModelProperty(value = "来源")
    private String courseSource;

    @ApiModelProperty(value = "标签")
    private List<String> courseTag;

    @ApiModelProperty(value = "课程价格")
    private BigDecimal coursePrice;

    @ApiModelProperty(value = "划线价格")
    private BigDecimal underlinedPrice;

    @ApiModelProperty(value = "上架设置")
    private String courseStatus;
    
    @ApiModelProperty(value = "课程考题")
    private String courseExam;

    @ApiModelProperty(value = "奖励形式")
    private String rewardForm;

    @ApiModelProperty(value = "现金形式")
    private String cashForm;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal rewardAmount;

    @ApiModelProperty(value = "奖励积分")
    private Integer rewardsPoints;

    @ApiModelProperty(value = "错误次数限制")
    private Integer errorLimit;

    @ApiModelProperty(value = "排序")
    private Integer orderNumber;

    @ApiModelProperty(value = "售卖方式")
    private String courseSalesmethod;

    @ApiModelProperty(value = "扩展字段1")
    private String field1;

    @ApiModelProperty(value = "扩展字段2")
    private String field2;

    @ApiModelProperty(value = "扩展字段3")
    private String field3;

    @ApiModelProperty(value = "扩展字段4")
    private String field4;

    @ApiModelProperty(value = "扩展字段5")
    private String field5;

    @ApiModelProperty(value = "活动信息")
    private String activityInfo;

    @ApiModelProperty(value = "营期红包开关：true-开启，false-关闭")
    private Boolean campperiodRedPack;

    @ApiModelProperty(value = "营期红包金额")
    private String campperiodRedPackAmount;

}
