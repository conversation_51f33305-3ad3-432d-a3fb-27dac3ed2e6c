package cn.hxsy.model.request;

import com.google.gson.annotations.SerializedName;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 转账场景报备信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferSceneReportInfo {
 
    /**
     * 【信息类型】 不能超过15个字符，商户所属转账场景下的信息类型，此字段内容为固定值，需严格按照转账场景报备信息字段说明传参。
     */
    @SerializedName("info_type")
    private String infoType;
 
    /**
     * 【信息内容】 不能超过32个字符，商户所属转账场景下的信息内容，商户可按实际业务场景自定义传参，需严格按照转账场景报备信息字段说明传参。
     */
    @SerializedName("info_content")
    private String infoContent;
 
    @Override
    public String toString() {
        return GsonUtil.getGson().toJson(this);
    }
}