package cn.hxsy.model.request;

import cn.hxsy.datasource.model.entity.CourseVideoPO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

/**
* @description: 营期下课程分组，对应分组下单节课程查询请求实体类
* @author: xiaQL
* @date: 2025/4/20 21:59
*/
@Data
public class CampCourseRequest {
    @ApiModelProperty(value = "营期ID", required = true)
    private String campId;

    @ApiModelProperty(value = "课程分组ID", required = true)
    private Long groupId;

    @ApiModelProperty(value = "课程小节ID", required = true)
    private Long courseId;

    @ApiModelProperty(value = "是否需要更新营期下关联该课程数据")
    private Boolean updateFlag;

    @ApiModelProperty(value = "需要更新时，传入待更新的课程小节信息")
    private CourseVideoPO courseVideo;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "课程开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate startTime;

}
