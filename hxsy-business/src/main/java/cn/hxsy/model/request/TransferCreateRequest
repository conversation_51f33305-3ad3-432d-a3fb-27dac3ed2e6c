package cn.hxsy.model.request;

import com.google.gson.annotations.SerializedName;
import com.wechat.pay.java.core.util.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 发起商家转账参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransferCreateRequest {

    @SerializedName("appid")
    private String appid;

    @SerializedName("out_bill_no")
    private String outBillNo;

    @SerializedName("transfer_scene_id")
    private String transferSceneId;

    @SerializedName("openid")
    private String openid;

    @SerializedName("user_name")
    private String userName;

    @SerializedName("transfer_remark")
    private String transferRemark;

    @SerializedName("transfer_amount")
    private Integer transferAmount;

    @SerializedName("notify_url")
    private String notifyUrl;

    /**
     * 【用户收款感知】
     * 用户收款时感知到的收款原因将根据转账场景自动展示默认内容。
     * 如有其他展示需求，可在本字段传入。
     * 各场景展示的默认内容和支持传入的内容，可查看产品文档了解。
     */
    @SerializedName("user_recv_perception")
    private String userRecvPerception;

    /**
     * 【转账场景报备信息】 各转账场景下需报备的内容，商户需要按照所属转账场景规则传参，详见转账场景报备信息字段说明。
     */
    @SerializedName("transfer_scene_report_infos")
    private List<TransferSceneReportInfo> transferSceneReportInfos;

    @Override
    public String toString() {
        return GsonUtil.getGson().toJson(this);
    }

}