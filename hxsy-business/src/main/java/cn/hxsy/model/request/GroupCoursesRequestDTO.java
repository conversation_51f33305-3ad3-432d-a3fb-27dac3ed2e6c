package cn.hxsy.model.request;

import cn.hxsy.datasource.model.entity.CourseVideoPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* @description: 课程分组下具体课程请求实体类
* @author: xiaQL
* @date: 2025/4/20 21:59
*/
@Data
public class GroupCoursesRequestDTO {

    @ApiModelProperty(value = "课程分组id", required = true)
    private Long groupId;

    @ApiModelProperty(value = "具体课程配置信息", required = true)
    private List<CourseVideoJson> courseVideoDTOS;

}
