package cn.hxsy.model.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
* @description: 营期下课程分组，对应分组下具体课程请求实体类
* @author: xiaQL
* @date: 2025/4/20 21:59
*/
@Data
public class CampCourseRequestDTO {
    @ApiModelProperty(value = "营期ID", required = true)
    private Long campId;

    @ApiModelProperty(value = "课程分组下具体课程小节配置", required = true)
    private List<GroupCoursesRequestDTO> groupCoursesRequestDTOS;


}
