package cn.hxsy.model.request;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 微信支付参数实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxPayParam {

    /** 商户订单号 */
    private String outBillNo;

    /** 收款人 openid */
    private String openid;

    /** 收款人姓名（敏感信息需加密） */
    private String userName;

    /** 转账金额（单位：分） */
    private Long amount;

    /** 微信回调通知地址 */
    private String notifyUrl;

    /** 商户 AppID */
    private String appId;

    /** 商户号 */
    private String merchantId;

    /** 商户 API 私钥路径 */
    private String privateKeyPath;

    /** 微信支付平台公钥路径 */
    private String publicKeyPath;

    /** 微信支付平台公钥 ID */
    private String publicKeyId;

    /** 商户证书序列号 */
    private String merchantSerialNumber;

    /** API v3 密钥 */
    private String apiV3Key;

    /**
     * 密钥版本（0：旧版，1：新版）
     */
    private Integer keyVersion;

    /**
     * 模式（0：证书，1：公钥）
     */
    private Integer mode;

}