package cn.hxsy.rpc.user;

import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.response.*;
import cn.hxsy.api.user.service.CampPeriodRpcService;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.model.user.UserRegisterCacheModel;
import cn.hxsy.datasource.model.entity.CampCoursePO;
import cn.hxsy.datasource.model.entity.CampPeriodPO;
import cn.hxsy.model.request.CampCourseRequest;
import cn.hxsy.model.request.CourseVideoDTO;
import cn.hxsy.model.request.CourseVideoJson;
import cn.hxsy.service.CampCourseService;
import cn.hxsy.service.CampPeriodService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static cn.hxsy.base.constant.ResponseType.Success;

@DubboService(version = "1.0.0")
@Component
@Slf4j
public class CampPeriodRpcServiceImpl implements CampPeriodRpcService {

    @Resource
    private CampPeriodService campPeriodService;
    @Resource
    private CampCourseService campCourseService;

    @Override
    public Result<CampPeriodCourseResponse>
    queryCampPeriod(Long campPeriodId, Long courseId) {
        log.info("campPeriodId:{}", campPeriodId);
        CampPeriodPO campPeriodPO = campPeriodService.getById(campPeriodId);
        CampCourseRequest campCourseRequest = new CampCourseRequest();
        campCourseRequest.setCampId(String.valueOf(campPeriodId));
        campCourseRequest.setCourseId(courseId);
        Result<CampCourseVideoResponse> campGroupAndCourses = campCourseService.getCampTodayCourse(campCourseRequest);
        if (campGroupAndCourses.getCode() != Success.getCode() ) {
            new RuntimeException("查询营期下课程失败：" + campGroupAndCourses.getMsg());
        }
        List<CourseVideoResponse> videoList = campGroupAndCourses.getData().getCourseVideoList();
        CourseVideoResponse courseVideoDTO = videoList.stream()
            .filter(video -> Objects.equals(courseId, video.getId()))
            .findFirst()
            .orElseThrow(() -> new RuntimeException("未找到匹配的课程"));

        if(ObjectUtil.isAllEmpty(campPeriodPO, courseVideoDTO)){
            new RuntimeException("该课程不存在");
        }
        CampPeriodCourseResponse campPeriodCourseResponse = new CampPeriodCourseResponse();
        BeanUtils.copyProperties(campPeriodPO, campPeriodCourseResponse);
        campPeriodCourseResponse.setOrderNumber(courseVideoDTO.getOrderNumber());
        campPeriodCourseResponse.setCourseName(courseVideoDTO.getCourseName());
        return Result.ok(campPeriodCourseResponse);
    }

    @Override
    public Result<CampCourseVideoResponse> getCampTodayCourse(Long campPeriodId, LocalDate date) {
        CampCourseRequest campCourseRequest = new CampCourseRequest();
        campCourseRequest.setCampId(String.valueOf(campPeriodId));
        campCourseRequest.setStartTime(date);
        Result<CampCourseVideoResponse> campTodayCourse = campCourseService.getCampTodayCourse(campCourseRequest);
        if (campTodayCourse.getCode() != Success.getCode() ) {
            new RuntimeException("查询营期下课程失败：" + campTodayCourse.getMsg());
        }
        CampCourseVideoResponse campCourseVideoResponse = campTodayCourse.getData();
        return Result.ok(campCourseVideoResponse);
    }

    @Override
    public Result<List<CampPeriodResponse>> getCampPeriodsByIds(List<Long> campPeriodIds) {
        List<CampPeriodPO> campPeriodsByIds = campPeriodService.getCampPeriodsByIds(campPeriodIds);
        List<CampPeriodResponse> campPeriodResponseList = new ArrayList<>();
        campPeriodsByIds.forEach(campPeriodPO -> {
            CampPeriodResponse campPeriodResponse = new CampPeriodResponse();
            BeanUtils.copyProperties(campPeriodPO, campPeriodResponse);
            campPeriodResponseList.add(campPeriodResponse);
        });
        return Result.ok(campPeriodResponseList);
    }

    @Override
    public Result<List<CampCoursePesponse>> getCampGroupAndCourses(Long campPeriodId) {
        LambdaQueryWrapper<CampCoursePO> campPeriodPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        campPeriodPOLambdaQueryWrapper.eq(CampCoursePO::getCampId, campPeriodId);
        List<CampCoursePO> campCoursePOList = campCourseService.getBaseMapper().selectList(campPeriodPOLambdaQueryWrapper);
        List<CampCoursePesponse> campCoursePesponseList = new ArrayList<>();
        campCoursePOList.forEach(campCoursePO -> {
            CampCoursePesponse campCoursePesponse = new CampCoursePesponse();
            BeanUtils.copyProperties(campCoursePO, campCoursePesponse);
            System.out.println("campCoursePO" + campCoursePO);
            campCoursePesponseList.add(campCoursePesponse);
        });
        return Result.ok(campCoursePesponseList);
    }


    /**
     * description : 根据销售ID和营期ID查询 营期是否存在
     * @title: countCampPeriodBySalesId
     * @param: campPeriodId
     * @param: salesId
     * <AUTHOR>
     * @date 2025/7/5 13:46
     * @return int
     */
    public int countCampPeriodBySalesGroupId(Long campPeriodId, Long salesGroupId) {
        LambdaQueryWrapper<CampPeriodPO> campPeriodPOLambdaQueryWrapper = Wrappers.lambdaQuery(CampPeriodPO.class);
        campPeriodPOLambdaQueryWrapper.eq(CampPeriodPO::getId, campPeriodId)
                .like(CampPeriodPO::getSalesGroupId, salesGroupId);
        return campPeriodService.getBaseMapper().selectCount(campPeriodPOLambdaQueryWrapper);
    }





}
