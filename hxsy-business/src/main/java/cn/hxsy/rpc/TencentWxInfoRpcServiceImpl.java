package cn.hxsy.rpc;

import cn.hxsy.api.app.model.request.TencentWxInfoRequest;
import cn.hxsy.api.app.model.response.TencentWxInfoResponse;
import cn.hxsy.api.app.service.TencentWxInfoRpcService;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.datasource.model.entity.MiniProgram;
import cn.hxsy.service.MiniProgramService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cn.hxsy.cache.constant.user.CacheConstant.APP_CONFIG_KEY_PREFIX;

/**
 * <AUTHOR> <PERSON>a<PERSON>
 * @ClassName : TencentWxInfoRpcServiceImpl
 * @description : TencentWxInfoRpcServiceImpl
 * @date: 2025-04-04 23:08
 */
@DubboService(version = "1.0.0")
@Component
public class TencentWxInfoRpcServiceImpl implements TencentWxInfoRpcService {

    @Autowired
    private MiniProgramService tencentWxInfoService;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Override
    public Result<TencentWxInfoResponse> queryWxConfig(TencentWxInfoRequest request) {
        LambdaQueryWrapper<MiniProgram> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniProgram::getAppid, request.getAppid());
        MiniProgram miniProgram = tencentWxInfoService.getBaseMapper().selectOne(queryWrapper);
        if (miniProgram == null) {
            return Result.error("小程序不存在");
        }
        TencentWxInfoResponse tencentWxInfoResponse = new TencentWxInfoResponse();
        BeanUtils.copyProperties(miniProgram, tencentWxInfoResponse);
        // 将数据写入redis，可永久存储
        String appCacheKey = APP_CONFIG_KEY_PREFIX + tencentWxInfoResponse.getId();
        redisJsonUtils.set(appCacheKey, tencentWxInfoResponse);
        return Result.ok(tencentWxInfoResponse);
    }
}
