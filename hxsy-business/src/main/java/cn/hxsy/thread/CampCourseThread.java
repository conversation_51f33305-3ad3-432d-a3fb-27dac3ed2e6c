package cn.hxsy.thread;

import cn.hxsy.anno.DisableShardingLog;
import cn.hxsy.dao.CampCourseMapper;
import cn.hxsy.datasource.model.entity.CampCoursePO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @author: qinLuan
 * @Description: 营期下课程小车批量更新异步线程
 * @Data: 2025-06-08  23:22
 */
@Component
public class CampCourseThread {

    @Autowired
    private CampCourseMapper campCourseMapper;

    @DisableShardingLog
    @Async("threadPoolTaskExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void updateCampCourse(List<CampCoursePO> updateList) {
        updateList.stream().forEach(campCoursePO -> {
            campCourseMapper.updateById(campCoursePO);
        });
    }
}
