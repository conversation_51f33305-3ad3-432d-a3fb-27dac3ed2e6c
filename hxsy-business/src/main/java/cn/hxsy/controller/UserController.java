package cn.hxsy.controller;

import cn.hxsy.datasource.model.entity.User;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.UserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * <p>
 * 用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13 16:22:51
 */
@RestController
@RequestMapping(UserController.API_PREFIX)
public class UserController {

    public static final String API_PREFIX = "/api/v1/user";

    @Resource
    private UserService userService;

    @PostMapping("/login")
    public Result login(@RequestBody User user) {
        return userService.login(user);
    }

    @PostMapping("/login-auth")
    public Result loginAuth(@RequestBody User user) {
        return userService.loginAuth(user);
    }
}
