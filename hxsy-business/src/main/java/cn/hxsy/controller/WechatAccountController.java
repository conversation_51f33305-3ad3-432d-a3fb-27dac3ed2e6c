package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.ActivityGroupPO;
import cn.hxsy.datasource.model.entity.WechatAccountPO;
import cn.hxsy.service.WechatAccountService;
import cn.hxsy.utils.UserCacheUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;

@RestController
@RequestMapping("/api/v1/wechat-account")
@Api(tags = "企业微信账号管理")
public class WechatAccountController {

    @Autowired
    private WechatAccountService accountService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @PostMapping
    @ApiOperation("创建账号")
    public Result<WechatAccountPO> create(@RequestBody WechatAccountPO po) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        po.setCreatedBy(systemUserSelfInfo.getUsername());
        po.setCreatedAt(now);

        return accountService.save(po) ?
                Result.ok(po) : Result.error("创建失败");
    }

    @PostMapping("/deleteById")
    @ApiOperation("删除账号")
    public Result<Void> delete(@RequestBody WechatAccountPO po) {
        return accountService.removeById(po.getId()) ?
                Result.ok() : Result.error("删除失败");
    }

    @PutMapping
    @ApiOperation("更新账号")
    public Result<WechatAccountPO> update(@RequestBody WechatAccountPO po) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        po.setUpdatedBy(systemUserSelfInfo.getUsername());
        po.setUpdatedAt(now);

        return accountService.updateById(po) ?
                Result.ok(po) : Result.error("更新失败");
    }

    @GetMapping("/{id}")
    @ApiOperation("获取详情")
    public Result<WechatAccountPO> detail(@PathVariable Long id) {
        return Result.ok(accountService.getById(id));
    }

    @GetMapping("/page")
    @ApiOperation("分页查询账号列表")
    public Result<Page<WechatAccountPO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String corpName,
            @RequestParam(required = false) String corpId,
            @RequestParam(required = false) Integer status) {

        Page<WechatAccountPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<WechatAccountPO> wrapper = new LambdaQueryWrapper<>();

        // 动态条件构造（参考网页4的带条件分页）
        wrapper.like(StringUtils.isNotBlank(corpName), WechatAccountPO::getCorpName, corpName)
                .eq(StringUtils.isNotBlank(corpId), WechatAccountPO::getCorpId, corpId)
                .eq(status != null, WechatAccountPO::getStatus, status)
                .orderByDesc(WechatAccountPO::getCreatedAt);

        Page<WechatAccountPO> resultPage = accountService.page(page, wrapper);
        return Result.ok(resultPage);
    }
}