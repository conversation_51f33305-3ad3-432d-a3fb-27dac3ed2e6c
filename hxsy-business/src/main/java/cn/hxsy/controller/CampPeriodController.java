package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CampPeriodPO;
import cn.hxsy.datasource.model.entity.CampPeriodResponseDTO;
import cn.hxsy.datasource.model.entity.CampPeriodVO;
import cn.hxsy.service.CampPeriodService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/v1/camp-period")
@Api(tags = "营期管理接口")
public class CampPeriodController {

    @Autowired
    private CampPeriodService campPeriodService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    // 新增营期
    @PostMapping
    @ApiOperation("创建营期")
    public Result<CampPeriodPO> create(@RequestBody CampPeriodPO campPeriod) {
        //System.out.println("收到的 companyId：" + campPeriod.getCompanyId());

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        campPeriod.setCreatedBy(systemUserSelfInfo.getUsername());
        campPeriod.setCreatedAt(now);

        boolean success = campPeriodService.save(campPeriod);
        return success ? Result.ok(campPeriod) : Result.error("创建失败");
    }

    // 删除营期
    @PostMapping("/deleteById")
    @ApiOperation("删除营期")
    public Result<Void> delete(@RequestBody CampPeriodPO campPeriod) {
        boolean success = campPeriodService.removeById(campPeriod.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    // 更新营期
    @PostMapping("/updateById")
    @ApiOperation("更新营期")
    public Result<CampPeriodPO> update(@RequestBody CampPeriodPO campPeriod) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        campPeriod.setUpdatedBy(systemUserSelfInfo.getUsername());
        campPeriod.setUpdatedAt(now);

        boolean success = campPeriodService.updateById(campPeriod);
        return success ? Result.ok(campPeriod) : Result.error("更新失败");
    }

    // 查询营期详情
    @GetMapping("/{id}")
    @ApiOperation("获取营期详情")
    public Result<CampPeriodPO> getById(@PathVariable Long id) {
        CampPeriodPO campPeriod = campPeriodService.getById(id);
        return campPeriod != null ? Result.ok(campPeriod) : Result.error("数据不存在");
    }

    // 分页查询营期列表
    @GetMapping("/page")
    @ApiOperation("分页查询营期列表")
    public Result<Page<CampPeriodPO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String id,
            @RequestParam(required = false) String name) {

        Page<CampPeriodPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CampPeriodPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(name)) {
            wrapper.like(CampPeriodPO::getCampperiodName, name);
        }
        if (StringUtils.isNotBlank(id)) {
            wrapper.like(CampPeriodPO::getCompanyId, id);
        }

        // 执行分页查询
        Page<CampPeriodPO> resultPage = campPeriodService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    /**
     *根据companyId和salesId查询课程视频
     * @param companyId 公司ID
     * @param salesGroupId 销售组ID
     * @return
     */
    @GetMapping("/getCourseByCompanyIdANDSalesId")
    public Result<List<CampPeriodResponseDTO>> getCamps(
            @RequestParam Long companyId,
            @RequestParam(required = false) Long salesGroupId) {
        return Result.ok(campPeriodService.getCampPeriodsWithCourses(companyId, salesGroupId));
    }

    /**
     * description : 根据公司查询营期
     * @title: Result
     * @param:
     * <AUTHOR>
     * @date 2025/4/21 16:01
     * @return null
     */
    @ApiOperation(value = "根据公司查询营期")
    @GetMapping("/getCampPeriodsByCompanyId")
    public Result<List<CampPeriodPO>> getCampPeriodsByCompanyId(@RequestParam Long companyId) {
        return Result.ok(campPeriodService.getCampPeriodsByCompanyId(companyId));
    }

    @ApiOperation(value = "设置营期红包金额")
    @GetMapping("/setCampPeriodRedPacketAmount")
    public Result<Boolean> setCampPeriodRedPacketAmount(@RequestParam String id, @RequestParam String redPacketAmount)  {
        boolean success = campPeriodService.setCampPeriodRedPacketAmount(id, redPacketAmount);
        return success ? Result.ok() : Result.error("设置失败");
    }

    // 根据营期id集合查询营期信息
    @GetMapping("/getCampPeriodsByIds")
    public Result<List<CampPeriodPO>> getCampPeriodsByIds(@RequestParam List<Long> campPeriodIds) {
        List<CampPeriodPO> campPeriods = campPeriodService.getCampPeriodsByIds(campPeriodIds);
        return Result.ok(campPeriods);
    }

}
