package cn.hxsy.controller;

import cn.hxsy.base.request.WxMerchantKeyRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.WxMerchantKeyPO;
import cn.hxsy.service.WxMerchantKeyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;

import javax.validation.Valid;

/**
 * 微信商户密钥管理接口
 */
@RestController
@RequestMapping("/api/v1/wx/merchant-key")
@Api(tags = "微信商户密钥管理")
public class WxMerchantKeyController {

    @Autowired
    private WxMerchantKeyService wxMerchantKeyService;

    @PostMapping("/bind")
    @ApiOperation("绑定商户密钥")
    public Result<String> bindMerchantKey(@Valid @RequestBody WxMerchantKeyRequest request) {
        WxMerchantKeyPO wxMerchantKeyPO = new WxMerchantKeyPO();
        BeanUtils.copyProperties(request, wxMerchantKeyPO);
        boolean success = wxMerchantKeyService.bindMerchantKey(wxMerchantKeyPO);
        return success ? Result.ok("绑定成功") : Result.error("绑定失败");
    }

    @PutMapping("/update")
    @ApiOperation("更新商户密钥")
    public Result<String> updateMerchantKey(@Valid @RequestBody WxMerchantKeyRequest request) {
        WxMerchantKeyPO wxMerchantKeyPO = new WxMerchantKeyPO();
        BeanUtils.copyProperties(request, wxMerchantKeyPO);
        boolean success = wxMerchantKeyService.updateMerchantKey(wxMerchantKeyPO);
        return success ? Result.ok("更新成功") : Result.error("更新失败");
    }

    @GetMapping("/company/{companyId}")
    @ApiOperation("查询商户密钥")
    public Result<WxMerchantKeyPO> getMerchantKey(@PathVariable Long companyId) {
        WxMerchantKeyPO byCompanyId = wxMerchantKeyService.getByCompanyId(companyId);
        return Result.ok(byCompanyId);
    }

    @DeleteMapping("/unbind/{companyId}")
    @ApiOperation("解绑商户密钥")
    public Result<String> unbindMerchantKey(@PathVariable Long companyId) {
        boolean success = wxMerchantKeyService.unbindMerchantKey(companyId);
        return success ? Result.ok("解绑成功") : Result.error("解绑失败");

    }
}
