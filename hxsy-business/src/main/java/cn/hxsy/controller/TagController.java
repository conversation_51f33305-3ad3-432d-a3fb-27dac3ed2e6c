package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.QywxTag;
import cn.hxsy.datasource.model.entity.TagBatchDTO;
import cn.hxsy.service.TagService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/tags")
@RequiredArgsConstructor
public class TagController {

    private final TagService tagService;

    // 单条操作 -------------------------------------------------
    @PostMapping("/create")
    public Result<QywxTag> createTag(@RequestBody QywxTag tag) {
        return tagService.createTag(tag);
    }

    @PostMapping("/update")
    public Result<QywxTag> updateTag(@RequestBody QywxTag tag) {
        return tagService.updateTag(tag);
    }

    @GetMapping("/delete")
    public Result<Void> deleteTag(@RequestParam Long tagId) {
        return tagService.deleteTag(tagId);
    }

    @GetMapping("/list")
    public Result<List<QywxTag>> getAllTags() {
        return tagService.getAllTags();
    }

    // 批量操作 -------------------------------------------------
    @PostMapping("/batch-process")
    public Result<Map<String, Object>> batchProcess(@RequestBody TagBatchDTO batchDTO) {
        return tagService.batchProcess(batchDTO);
    }

    @PostMapping("/batch")
    public Result<Map<String, Object>> batchOperation(
            @RequestParam String action,
            @RequestBody List<QywxTag> tags) {
        return tagService.batchOperation(action, tags);
    }
}
