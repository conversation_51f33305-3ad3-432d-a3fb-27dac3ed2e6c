package cn.hxsy.controller;

import cn.hxsy.base.request.CourseRedPacketRequest;
import cn.hxsy.model.response.CourseRedPacketPageResponse;
import cn.hxsy.model.response.CustomerRedPacketSummaryResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.CourseRedPacketService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/course-redpacket")
@Api(tags = "用户红包记录接口")
public class CourseRedPacketController {

    @Autowired
    private CourseRedPacketService courseRedPacketService;

    @PostMapping("/courseRedPacketList")
    @ApiOperation("分页查询红包领取记录")
    public Result<CourseRedPacketPageResponse> courseRedPacketList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestBody CourseRedPacketRequest request) {

        // 返回分页结果
        return Result.ok(courseRedPacketService.courseRedPacketList(pageNum, pageSize, request));
    }

    @GetMapping("/summary/{customerId}")
    @ApiOperation("获取客户红包汇总信息")
    public Result<CustomerRedPacketSummaryResponse> getCustomerRedPacketSummary(
            @ApiParam(value = "客户ID", required = true) 
            @PathVariable Long customerId) {
        
        if (customerId == null) {
            return Result.error("客户ID不能为空");
        }
        
        return Result.ok(courseRedPacketService.getCustomerRedPacketSummary(customerId));
    }

}
