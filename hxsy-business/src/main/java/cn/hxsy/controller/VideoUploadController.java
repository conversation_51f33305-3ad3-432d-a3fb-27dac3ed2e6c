package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import cn.hxsy.datasource.model.entity.FileUploadRequest;
import cn.hxsy.datasource.model.entity.ImageInfoPO;
import cn.hxsy.datasource.model.entity.VideoUploadPO;
import cn.hxsy.service.ImageInfoService;
import cn.hxsy.service.VideoUploadService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.sql.DataSource;
import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping("/api/v1/video_upload")
@Api(tags = "视频批量上传接口")
public class VideoUploadController {

    @Autowired
    private VideoUploadService videoUploadService;

    @Autowired
    private ImageInfoService imageInfoService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    /**
     * 1. 批量初始化分片上传
     * @param request
     * @return
     */
    @PostMapping("/initiateBatch")
    public Result<Map<String, String>> initiateBatchUpload(@RequestBody Map<String, List<String>> request) {
        return videoUploadService.initiateBatchUpload(request);
    }

    /**
     * 2. 批量上传分片
     * @param fileNames 文件名称
     * @param partNumbers 分片号
     * @param files 文件
     * @param groupIds 视频分组ID
     * @return
     * @throws Exception
     */
    @PostMapping("/uploadPartsBatch")
    public Result<String> uploadPartsBatch(@RequestParam("fileNames") List<String> fileNames,
                                           @RequestParam("partNumbers") List<Integer> partNumbers,
                                           @RequestParam("files") List<MultipartFile> files,
                                           @RequestParam("groupIds") List<String> groupIds) throws Exception {
        return videoUploadService.uploadPartsBatch(fileNames, partNumbers, files,groupIds);
    }

    /**
     * 3. 批量合并分片
     * @param request
     * @return
     */
    @PostMapping("/completeBatch")
    public Result<List<Map<String, String>>> completeBatchUpload(@RequestBody Map<String, List<String>> request) {
        return videoUploadService.completeBatchUpload(request);
    }

    /**
     * 简单文件上传
     * @param file
     * @return
     * @throws IOException
     */
    @PostMapping("/upload")
    public Result<String> simpleUpload(@RequestParam MultipartFile file,String path) throws IOException {
        return videoUploadService.simpleUpload(file,path);
    }

    @PostMapping("/uploadCourseImage")
    public Result<String> uploadCourseImage(@RequestParam MultipartFile file,String path) throws IOException {
        return videoUploadService.uploadCourseImage(file,path);
    }

    @PostMapping("/batchUploadParts")
    public Result<String> batchUploadParts(
            @ModelAttribute FileUploadRequest fileUploadRequest,
            @RequestPart("files") MultipartFile files) throws Exception {
        return videoUploadService.batchUploadParts(fileUploadRequest, files);
    }

    @GetMapping("/page")
    @ApiOperation("分页查询课程视频列表")
    public Result<Page<VideoUploadPO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String id,
            @RequestParam(required = false) String videoName,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        Page<VideoUploadPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<VideoUploadPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VideoUploadPO::getStatus,"1");

        // 设置查询条件
        if (StringUtils.isNotBlank(id) && !"ALL".equalsIgnoreCase(id.trim())) {
            wrapper.eq(VideoUploadPO::getVideoGroupid, id);
        }

        if (StringUtils.isNotBlank(videoName)) {
            wrapper.like(VideoUploadPO::getVideoName, videoName);
        }

        // 设置时间区间查询条件
        if (StringUtils.isNotBlank(startDate)) {
            LocalDate start = LocalDate.parse(startDate);
            // 大于 日期
            wrapper.ge(VideoUploadPO::getCreatedAt, start.atStartOfDay());
        }
        if (StringUtils.isNotBlank(endDate)) {
            LocalDate end = LocalDate.parse(endDate);
            // 小于 日期
            wrapper.le(VideoUploadPO::getCreatedAt, end.atTime(LocalTime.MAX));
        }

        // 执行分页查询
        Page<VideoUploadPO> resultPage = videoUploadService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    @GetMapping("/imageList")
    @ApiOperation("分页查询课程封面列表")
    public Result<Page<ImageInfoPO>> imageList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String id) {

        Page<ImageInfoPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ImageInfoPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(id) && !"ALL".equalsIgnoreCase(id.trim())) {
            wrapper.like(ImageInfoPO::getId, id);
        }

        // 执行分页查询
        Page<ImageInfoPO> resultPage = imageInfoService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    @PostMapping("/deleteById")
    public Result<String> deleteById(@RequestBody VideoUploadPO videoUploadPO) {
        return videoUploadService.deleteById(videoUploadPO.getId());
    }

    @PostMapping("/save")
    public Result<Boolean> save(@RequestBody VideoUploadPO videoUploadPO) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        videoUploadPO.setCreatedBy(systemUserSelfInfo.getUsername());
        videoUploadPO.setCreatedAt(now);

        boolean save = videoUploadService.save(videoUploadPO);
        return save ? Result.ok(save) : Result.error("保存失败");

    }

    @GetMapping("/getVodSignature")
    public Result<String> getVodSignature() {
        return videoUploadService.getVodSignature();
    }


//    @Autowired
//    private DataSource dataSource;
//    @GetMapping("/testConnection")
//    public String testConnection() throws SQLException {
//        try (Connection conn = dataSource.getConnection()) {
//            Statement stmt = conn.createStatement();
//            stmt.executeUpdate("UPDATE video_upload SET status = '0' WHERE id = 1911370090894016514");
//            return "Update success";
//        }
//    }

}

