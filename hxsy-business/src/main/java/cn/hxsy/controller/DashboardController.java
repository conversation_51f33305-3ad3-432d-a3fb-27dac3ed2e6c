package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.dao.StatsSummaryMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.service.DashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    private static final String REDIS_KEY = "dashboard:stats";

    @Autowired
    private StatsSummaryMapper statsSummaryMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @PostMapping("/stats")
    public Result<StatsSummary> getDashboardStats() {
        return Result.ok(dashboardService.getDashboardStats());
    }

    @PostMapping("/companySales")
    public Result<CompanySalesRsp> getCompanySales(@RequestBody CompanyPO company) {
        return Result.ok(dashboardService.getCompanySales(company.getId()));
    }

    @GetMapping("/saveToDB")
    public Result<Integer> saveDashboardStats() {
       return Result.ok(dashboardService.persistToDatabase() > 0 ? 1 : 0);
    }

    @GetMapping("/delete")
    public Result<String> deleteDashboardStats() {
        try {
            // 检查 Redis 中是否存在该 key
            Boolean exists = redisTemplate.hasKey(REDIS_KEY);

            if (exists != null && exists) {
                // 删除 Redis 数据
                Boolean deleted = redisTemplate.delete(REDIS_KEY);

                if (deleted != null && deleted) {
                    return Result.ok("Redis 数据已成功清除");
                } else {
                    return Result.error("删除操作失败，请重试");
                }
            } else {
                return Result.error("Redis 中不存在统计数据");
            }
        } catch (Exception e) {
            // 记录详细错误日志
            return Result.error("清除数据时发生错误: " + e.getMessage());
        }
    }

//    @GetMapping("/getUpdateDashboardStats")
//    public Result<StatsSummary> getUpdateDashboardStats() {
//        return Result.ok(dashboardService.getUpdateDashboardStats());
//    }

    @PostMapping("/viewChart")
    public Result<List<ViewChartRsp>> getViewChartData(@RequestBody ViewChartReq req) {

        List<StatsSummaryDO> records = statsSummaryMapper.selectByDateRange(req.getStartDate(), req.getEndDate());

        List<ViewChartRsp> result = records.stream()
                .map(record -> new ViewChartRsp(
                        record.getRecordDate().toString(),
                        record.getTodayViews(),
                        record.getTodayComplete(),
                        record.getTodayRedPackets(),
                        record.getTodayNewMembers()
                ))
                .collect(Collectors.toList());

        return Result.ok(result);
    }


}
