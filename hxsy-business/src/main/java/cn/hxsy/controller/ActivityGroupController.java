package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.ActivityGroupPO;
import cn.hxsy.datasource.model.entity.ActivityPO;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import cn.hxsy.service.ActivityGroupService;
import cn.hxsy.service.ActivityService;
import cn.hxsy.utils.UserCacheUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;


@RestController
@RequestMapping("/api/v1/activity-group")
@Api(tags = "营销分组管理接口")
public class ActivityGroupController {

    @Autowired
    private ActivityGroupService activityGroupService;

    @Autowired
    private ActivityService activityService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    // 新增视频课分组
    @PostMapping
    @ApiOperation("创建视频课分组")
    public Result<ActivityGroupPO> create(@RequestBody ActivityGroupPO activityGroup) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        activityGroup.setCreatedBy(systemUserSelfInfo.getUsername());
        activityGroup.setCreatedAt(now);

        boolean success = activityGroupService.save(activityGroup);
        return success ? Result.ok(activityGroup) : Result.error("创建失败");
    }

    // 删除视频课分组
    @PostMapping("/deleteById")
    @ApiOperation("删除视频课分组")
    public Result<Void> delete(@RequestBody ActivityGroupPO activityGroup) {
        boolean success = activityGroupService.removeById(activityGroup.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    // 更新视频课分组
    @PostMapping("/updateById")
    @ApiOperation("更新视频课分组")
    public Result<ActivityGroupPO> updateGroupName(@RequestBody ActivityGroupPO activityGroupPO) {
        // 校验输入参数
        if (activityGroupPO.getId() == null || StringUtils.isEmpty(activityGroupPO.getGroupName())) {
            return Result.error("id 或 groupName 不能为空");
        }

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        // 只更新 groupName 字段
        LambdaUpdateWrapper<ActivityGroupPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ActivityGroupPO::getId, activityGroupPO.getId())
                .set(ActivityGroupPO::getGroupName, activityGroupPO.getGroupName())
                .set(ActivityGroupPO::getUpdatedBy,systemUserSelfInfo.getUsername())
                .set(ActivityGroupPO::getUpdatedAt,now);

        boolean success = activityGroupService.update(updateWrapper);
        return success ? Result.ok(activityGroupPO) : Result.error("更新失败");
    }

    // 查询视频课分组详情
    @GetMapping("/{id}")
    @ApiOperation("获取视频课分组详情")
    public Result<ActivityGroupPO> getById(@PathVariable Long id) {
        ActivityGroupPO activityGroup = activityGroupService.getById(id);
        return activityGroup != null ? Result.ok(activityGroup) : Result.error("数据不存在");
    }

    // 分页查询视频课分组列表
    @GetMapping("/page")
    @ApiOperation("分页查询视频课分组列表")
    public Result<Page<ActivityGroupPO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String groupName) {

        Page<ActivityGroupPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ActivityGroupPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(groupName)) {
            wrapper.like(ActivityGroupPO::getGroupName, groupName);
        }

        // 执行分页查询
        Page<ActivityGroupPO> resultPage = activityGroupService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    @GetMapping("/list")
    @ApiOperation("查询所有课程视频分组列表（不分页）")
    public Result<List<ActivityGroupPO>> list(@RequestParam(required = false) String groupName) {
        LambdaQueryWrapper<ActivityGroupPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件（可选）
//        if (StringUtils.isNotBlank(groupName)) {
//            wrapper.like(ActivityGroupPO::getGroupName, groupName);
//        }

        List<ActivityGroupPO> list = activityGroupService.list(wrapper);
        return Result.ok(list);
    }

    @GetMapping("/activityList")
    @ApiOperation("分页查询课程视频列表")
    public Result<Page<ActivityPO>> activityList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = true) String id,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String title) {

        Page<ActivityPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ActivityPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(id) && !"ALL".equalsIgnoreCase(id.trim())) {
            wrapper.eq(ActivityPO::getGroupId, id);
        }

        if (StringUtils.isNotBlank(type)) {
            wrapper.eq(ActivityPO::getActivityType, type);
        }

        if (StringUtils.isNotBlank(title)) {
            wrapper.like(ActivityPO::getTitle, title);
        }

        // 执行分页查询
        Page<ActivityPO> resultPage = activityService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }
    
}