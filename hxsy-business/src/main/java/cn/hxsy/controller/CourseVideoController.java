package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.CustomerCourseVideoRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.CourseVideoService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/v1/course-video")
@Api(tags = "课程视频管理接口")
public class CourseVideoController {

    @Autowired
    private CourseVideoService courseVideoService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    // 新增课程视频
    @PostMapping
    @ApiOperation("创建课程视频")
    public Result<CourseVideoPO> create(@RequestBody CourseVideoPO courseVideo) {
        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        courseVideo.setCreatedBy(systemUserSelfInfo.getUsername());
        courseVideo.setCreatedAt(now);

        boolean success = courseVideoService.save(courseVideo);
        return success ? Result.ok(courseVideo) : Result.error("创建失败");
    }

    // 删除课程视频
    @PostMapping("/deleteById")
    @ApiOperation("删除课程视频")
    public Result<Void> delete(@RequestBody CourseVideoPO courseVideo) {
        boolean success = courseVideoService.removeById(courseVideo.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    // 更新课程视频
    @PostMapping("/updateById")
    @ApiOperation("更新课程视频")
    public Result<CourseVideoPO> update(@RequestBody CourseVideoPO courseVideo) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        courseVideo.setUpdatedBy(systemUserSelfInfo.getUsername());
        courseVideo.setUpdatedAt(now);

        boolean success = courseVideoService.updateById(courseVideo);
        return success ? Result.ok(courseVideo) : Result.error("更新失败");
    }


    @PostMapping("/removeShellById")
    @ApiOperation("下架课程视频")
    public Result<CourseVideoPO> removeShellById(@RequestBody CourseVideoPO courseVideo) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        courseVideo.setUpdatedBy(systemUserSelfInfo.getUsername());
        courseVideo.setUpdatedAt(now);

        boolean success = courseVideoService.removeShellById(courseVideo.getId());
        return success ? Result.ok(courseVideo) : Result.error("下架失败");
    }

    // 查询课程视频详情
    @GetMapping("/{id}")
    @ApiOperation("获取课程视频详情")
    public Result<CourseVideoPO> getById(@PathVariable Long id) {
        CourseVideoPO courseVideo = courseVideoService.getById(id);
        return courseVideo != null ? Result.ok(courseVideo) : Result.error("数据不存在");
    }

    // 分页查询课程视频列表
    @GetMapping("/page")
    @ApiOperation("分页查询课程视频列表")
    public Result<Page<CourseVideoPO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String courseName,
            @RequestParam(required = false) String courseStatus,
            @RequestParam(required = false) List<String> searchTime) {

        Page<CourseVideoPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CourseVideoPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(courseName)) {
            wrapper.like(CourseVideoPO::getCourseName, courseName);
        }

        if (StringUtils.isNotBlank(courseStatus)) {
            wrapper.eq(CourseVideoPO::getCourseStatus, courseStatus);
        }

        // 设置时间区间查询条件
        if (searchTime != null && !searchTime.isEmpty()) {
            if (searchTime.size() == 1) {
                // 只选择了开始时间
                String startTime = searchTime.get(0);
                LocalDate startDate = LocalDate.parse(startTime);
                LocalDateTime startDateTime = startDate.atStartOfDay();
                wrapper.ge(CourseVideoPO::getCreatedAt, startDateTime);
            } else if (searchTime.size() == 2) {
                String startTime = searchTime.get(0);
                String endTime = searchTime.get(1);

                if (StringUtils.isNotBlank(startTime) && StringUtils.isBlank(endTime)) {
                    // 只选择了开始时间
                    LocalDate startDate = LocalDate.parse(startTime);
                    LocalDateTime startDateTime = startDate.atStartOfDay();
                    wrapper.ge(CourseVideoPO::getCreatedAt, startDateTime);
                } else if (StringUtils.isBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                    // 只选择了结束时间
                    LocalDate endDate = LocalDate.parse(endTime);
                    LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
                    wrapper.le(CourseVideoPO::getCreatedAt, endDateTime);
                } else if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
                    // 同时选择了开始时间和结束时间
                    LocalDate startDate = LocalDate.parse(startTime);
                    LocalDate endDate = LocalDate.parse(endTime);
                    LocalDateTime startDateTime = startDate.atStartOfDay();
                    LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
                    wrapper.between(CourseVideoPO::getCreatedAt, startDateTime, endDateTime);
                }
            }
        }
        wrapper.orderByAsc(CourseVideoPO::getField1); // 排序序号

        // 执行分页查询
        Page<CourseVideoPO> resultPage = courseVideoService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

//    @GetMapping("/page")
//    @ApiOperation("分页查询课程视频列表")
//    public Result<Page<CourseVideoPO>> page(
//            @RequestParam(defaultValue = "1") Integer pageNum,
//            @RequestParam(defaultValue = "10") Integer pageSize,
//            @RequestParam(required = false) String courseName) {
//
//        Page<CourseVideoPO> page = new Page<>(pageNum, pageSize);
//        LambdaQueryWrapper<CourseVideoPO> wrapper = new LambdaQueryWrapper<>();
//        if (StringUtils.isNotBlank(courseName)) {
//            wrapper.like(CourseVideoPO::getCourseName, courseName);
//        }
//        courseVideoService.page(page, wrapper);
//        return Result.ok(page);
//    }

    @PostMapping("/batch")
    @ApiOperation("批量创建课程视频")
    public Result<String> createBatch(
            @RequestBody @Valid List<CourseVideoPO> courseVideos
    ) {
        if (courseVideos == null || courseVideos.isEmpty()) {
            return Result.error("数据不能为空");
        }

        boolean success = courseVideoService.saveBatchCourseVideos(courseVideos);
        return success ?
                Result.ok("成功插入" + courseVideos.size() + "条记录") :
                Result.error("批量插入失败");
    }

}
