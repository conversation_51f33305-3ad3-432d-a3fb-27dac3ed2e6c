package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.CampCourseVideoResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CampCoursePO;
import cn.hxsy.model.request.CampCourseRequest;
import cn.hxsy.model.request.CampCourseRequestDTO;
import cn.hxsy.datasource.model.entity.CourseOrderDTO;
import cn.hxsy.model.request.CourseVideoJson;
import cn.hxsy.service.CampCourseService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/v1/camp-course")
@Api(tags = "营期课程管理接口")
public class CampCourseController {

    @Autowired
    private CampCourseService campCourseService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    // 新增营期课程
    @PostMapping
    @ApiOperation("创建营期课程")
    public Result<CampCoursePO> create(@RequestBody CampCoursePO campCourse) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        campCourse.setCreatedBy(systemUserSelfInfo.getUsername());
        campCourse.setCreatedAt(now);

        boolean success = campCourseService.save(campCourse);
        return success ? Result.ok(campCourse) : Result.error("创建失败");
    }

    // 新增批量接口
    @PostMapping("/batchCreate")
    @ApiOperation("批量创建营期课程")
    public Result<List<CampCoursePO>> createBatch(@RequestBody List<CampCoursePO> campCourses) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        // 遍历设置创建人和创建时间
        campCourses.forEach(campCourse -> {
            campCourse.setCreatedBy(systemUserSelfInfo.getUsername());
            campCourse.setCreatedAt(now);
        });

        boolean success = campCourseService.saveBatch(campCourses);
        return success ? Result.ok(campCourses) : Result.error("批量创建失败");
    }

    // 删除营期课程
    @PostMapping("/deleteById")
    @ApiOperation("删除营期课程")
    public Result<Void> delete(@RequestBody CampCoursePO campCourse) {
        boolean success = campCourseService.removeById(campCourse.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    // 更新营期课程
    @PostMapping("/updateById")
    @ApiOperation("更新营期课程")
    public Result<CampCoursePO> update(@RequestBody CampCoursePO campCourse) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        campCourse.setUpdatedBy(systemUserSelfInfo.getUsername());
        campCourse.setUpdatedAt(now);

        boolean success = campCourseService.updateById(campCourse);
        return success ? Result.ok(campCourse) : Result.error("更新失败");
    }

    // 查询营期课程详情
    @GetMapping("/{id}")
    @ApiOperation("获取营期课程详情")
    public Result<CampCoursePO> getById(@PathVariable Long id) {
        CampCoursePO campCourse = campCourseService.getById(id);
        return campCourse != null ? Result.ok(campCourse) : Result.error("数据不存在");
    }

    // 分页查询营期课程列表
    @GetMapping("/page")
    @ApiOperation("分页查询营期课程列表")
    public Result<Page<CampCoursePO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Long campId) {

        Page<CampCoursePO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CampCoursePO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (campId != null) {
            wrapper.eq(CampCoursePO::getCampId, campId);
        }

        // 设置排序规则：按照 order_number 字段从小到大排序
//        wrapper.orderByAsc(CampCoursePO::getOrderNumber);

        // 执行分页查询
        Page<CampCoursePO> resultPage = campCourseService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    // 查询所有营期课程列表（不分页）
    @GetMapping("/list")
    @ApiOperation("查询所有营期课程列表（不分页）")
    public Result<List<CampCoursePO>> list(@RequestParam(required = false) Long campId) {

        LambdaQueryWrapper<CampCoursePO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件（可选）
        if (campId != null) {
            wrapper.eq(CampCoursePO::getCampId, campId);
        }

        List<CampCoursePO> list = campCourseService.list(wrapper);
        return Result.ok(list);
    }

    @PostMapping("/{id}/order")
    @ApiOperation("更新课程顺序并自动调整其他顺序")
    public Result<Void> updateOrderNumber(
            @PathVariable Long id,
            @RequestParam Integer newOrderNumber
    ) {
        boolean success = campCourseService.updateOrderNumber(id, newOrderNumber);
        return success ? Result.ok() : Result.error("更新排序失败");
    }

    @PostMapping("/sort")
    @ApiOperation("批量更新课程顺序")
    public Result<Void> sortCourses(@RequestBody List<CourseOrderDTO> courseOrderList) {
        boolean success = campCourseService.sortCourses(courseOrderList);
        return success ? Result.ok() : Result.error("批量排序失败");
    }

//    @PostMapping("/batch")
//    @ApiOperation("根据groupId批量插入营期课程")
//    public Result<Void> batchInsertCampCourses(@RequestBody CampCourseRequestDTO request) {
//        campCourseService.batchInsertByGroupIds(request.getCampId(), request.getGroupIds());
//        return Result.ok();
//    }

    @PostMapping("/save-by-camp-courses")
    @ApiOperation("批量插入营期下配置的课程分组下的分组信息")
    public Result<Void> saveCampGroupAndCourses(@RequestBody CampCourseRequestDTO request) {
        Boolean b = campCourseService.saveCampGroupAndCourses(request);
        if (!b) {
            return Result.error("插入失败");
        }
        return Result.ok();
    }

    @PostMapping("/get-camp-courses")
    @ApiOperation("查询营期下配置的课程分组下的分组信息")
    public Result<Object> getCampGroupAndCourses(@RequestBody CampCourseRequest request) {
        return campCourseService.getCampGroupAndCourses(request);
    }

    @PostMapping("/get-camp-today-course")
    @ApiOperation("查询营期下当天正在发布的课程小节")
    public Result<CampCourseVideoResponse> getCampTodayCourse(@RequestBody CampCourseRequest request) {
        return campCourseService.getCampTodayCourse(request);
    }

}
