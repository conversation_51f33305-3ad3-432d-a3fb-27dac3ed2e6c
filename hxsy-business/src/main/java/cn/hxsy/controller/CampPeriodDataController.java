package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.service.CampCourseService;
import cn.hxsy.service.CampPeriodDataService;
import cn.hxsy.service.CampPeriodService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.list.CursorableLinkedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/camp-period-data")
@Api(tags = "营期数据接口")
public class CampPeriodDataController {
    @Autowired
    private CampPeriodDataService dataService;

    @PostMapping("/getCampPeriodData")
    @ApiOperation("获取营期数据详情")
    public Result<List<CampPeriodDataRsp>> getCampPeriodDataList(@RequestBody CampPeriodDataReq req) {


        CampPeriodDataRsp rsp = dataService.buildCampPeriodData(
                req.getCompanyId(),req.getCampPeriodId()
        );
        // 返回成功结果
        return Result.ok(Collections.singletonList(rsp));
    }

    @PostMapping("/getCampCourseList")
    @ApiOperation("获取营期数据详情")
    public Result<List<CampCourseListDataRsp>> getCampCourseList(@RequestBody CampCourseDataReq req) {

        // 获取所有课程小节
        List<CampCourseListDataRsp> allCourseSections = dataService.buildCampCourseListData(
                req.getCompanyId(), req.getCampPeriodId()
        );

        return Result.ok(allCourseSections);

    }

    @PostMapping("/getCampCourseData")
    @ApiOperation("获取营期数据详情")
    public Result<MyPageData<CampCourseDataRsp>> getCampCourseDataList(@RequestBody CampCourseDataReq req) {

        // 处理分页参数（从请求中获取或使用默认值）
        int pageNum =  req.getPageNum() != null && req.getPageNum() > 0 ? req.getPageNum() : 1;
        int pageSize = req.getPageSize() != null && req.getPageSize() > 0 ? req.getPageSize() : 10;
        // 获取所有课程小节
        List<CampCourseDataRsp> allCourseSections = dataService.buildCampCourseData(
                req.getCompanyId(), req.getCampPeriodId(), req.getCourseId()
        );

        List<CampCourseDataRsp> filteredSections = allCourseSections;
        if (req.getCourseId() != null) {
            filteredSections = allCourseSections.stream()
                    .filter(rsp -> req.getCourseId().toString().equals(rsp.getCourseId()))
                    .collect(Collectors.toList());
            pageNum = 1;
        }

        // 计算总条数（每个课程小节才算一条数据）
        int total = filteredSections.size();

        // 正确分页
        List<CampCourseDataRsp> paginatedList = filteredSections.stream()
                .skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());

        // 构建响应（确保图片中的分页信息正确）
        MyPageData<CampCourseDataRsp> page = new MyPageData<>();
        page.setRecords(paginatedList);
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(total);

        return Result.ok(page);

    }

    Set<Long> getIntersectionSkipEmpty(Set<Long>... sets) {
        return Arrays.stream(sets)
                .filter(set -> set != null && !set.isEmpty()) // 过滤空集
                .reduce((set1, set2) -> {
                    Set<Long> intersection = new HashSet<>(set1);
                    intersection.retainAll(set2);
                    return intersection;
                })
                .orElse(Collections.emptySet()); // 全空时返回空集
    }

    private Set<Long> getIntersection(Set<Long> set1, Set<Long> set2) {
        Set<Long> result = new HashSet<>(set1);
        result.retainAll(set2);
        return result;
    }

    @PostMapping("/getCustomerData")
    @ApiOperation("获取营期客户数据详情")
    public Result<MyPageData<CustomerInfoRsp>> getCustomerData(@RequestBody CustomerInfoReq req) {

        Set<Long> customerIds = Collections.emptySet();

        Set<Long> customerIds1 = Optional.ofNullable(dataService.getCustomerIdsBySales(
                req.getColumnId(),
                req.getCompanyId(),
                req.getCampPeriodId(),
                req.getSalesGroupId(),
                req.getSalesId())).orElse(Collections.emptySet());

        Set<Long> customerIds2 = Optional.ofNullable(dataService.getCustomerIdsByCampPeriod(
                req.getCompanyId(),
                req.getCampPeriodId(),
                req.getCourseId(),
                req.getIsFlag())).orElse(Collections.emptySet());

        Set<Long> customerIds3 = Optional.ofNullable(dataService.batchGetCustomersId(
                req.getStartDate(),
                req.getEndDate())).orElse(Collections.emptySet());

        //Set<Long> customerIds = getIntersectionSkipEmpty(customerIds1, customerIds2, customerIds3);

        // 条件1：课程ID不为空
        if (req.getCourseId() != null) {
            if (customerIds1.isEmpty() || customerIds1 == null || customerIds2.isEmpty() || customerIds2 == null){
                customerIds = Collections.emptySet();
            } else {
                customerIds = getIntersection(customerIds1, customerIds2);
            }
        }
        // 条件2：开始结束时间存在
        else if (req.getStartDate() != null && req.getEndDate() != null) {
            if (customerIds1.isEmpty() || customerIds1 == null || customerIds3.isEmpty() || customerIds3 == null){
                customerIds = Collections.emptySet();
            } else if(req.getIsFlag() != null){
                customerIds = getIntersectionSkipEmpty(customerIds1, customerIds2, customerIds3);
            }else{
                customerIds = getIntersection(customerIds1, customerIds3);
            }
        }

        //批量查询客户信息
        Map<Long, Customer> customerMap = dataService.batchGetCustomers(customerIds);
        //获取经销商
        Map<Long, String> salesManMap = dataService.batchGetSalesMan(customerIds, req.getColumnId());
        //获取观看次数
        Map<Long, Integer> videoViewCountMap = dataService.batchGetVideoViewCounts(customerIds,req.getCampPeriodId(),req.getCourseId());
        //观看课节+完播次数
        Map<Long, Integer> viewCourseCountMap = dataService.batchGetViewCourseCounts(customerIds,req.getCampPeriodId(),req.getCourseId());
        Map<Long, Integer> completeCourseCountMap = dataService.batchGetCompleteCourseCounts(customerIds,req.getCampPeriodId(),req.getCourseId());
        //领取红包次数
        Map<Long, Integer> redPacketCountMap = dataService.batchGetRedPacketCounts(customerIds,req.getCampPeriodId(),req.getCourseId());
        //播放时间+完播时间
        Map<Long, String> viewCourseTimeMap = dataService.batchGetViewCourseTime(customerIds,req.getCampPeriodId(),req.getCourseId());
        Map<Long, String> completeCourseTimeMap = dataService.batchGetCompleteCourseTime(customerIds,req.getCampPeriodId(),req.getCourseId());
        // 3. 构建响应数据
        List<CustomerInfoRsp> customerDataList = customerIds.stream()
                .map(id -> {
                    Customer cust = customerMap.get(id);
                    CustomerInfoRsp rsp = new CustomerInfoRsp();
                    if (cust != null) {
                        rsp.setMemberId(cust.getId());
                        rsp.setAvatarUrl(cust.getAvatarUrl());
                        rsp.setNickname(cust.getNickname());
                        rsp.setMobile(cust.getMobile());
                        rsp.setStatusDesc(cust.getStatus());
                        rsp.setCreatedAt(cust.getCreatedAt());
                        rsp.setLastActiveTime(cust.getLastActiveTime());
                    } else {
                        // 基础信息填充（即使客户信息缺失）
                        rsp.setMemberId(id);
                        rsp.setNickname("未知用户");
                        rsp.setStatusDesc(0); // 标记为无效
                    }

                    rsp.setVideoViewCount(videoViewCountMap.getOrDefault(id, 0));
                    rsp.setSalesMan(salesManMap.getOrDefault(id, "无"));
                    rsp.setRedPacketCount(redPacketCountMap.getOrDefault(id, 0));
                    rsp.setViewCourseCount(viewCourseCountMap.getOrDefault(id, 0));
                    rsp.setCompleteCourseCount(completeCourseCountMap.getOrDefault(id, 0));

                    if(req.getCourseId() != null){
                        rsp.setViewCourseTime(viewCourseTimeMap.getOrDefault(id, "00:00"));
                        rsp.setCompleteCourseTime(completeCourseTimeMap.getOrDefault(id, "00:00:00"));
                    }

                    return rsp;
                })
                .collect(Collectors.toList());

        // --- 新增排序逻辑：按 活跃时间 降序（最新在前）---
        customerDataList.sort(
                Comparator.comparing(
                        CustomerInfoRsp::getLastActiveTime,
                        Comparator.nullsLast(Comparator.reverseOrder())
                )
        );

        // 4. 分页处理
        int pageNum = req.getPageNum() != null && req.getPageNum() > 0 ? req.getPageNum() : 1;
        int pageSize = req.getPageSize() != null && req.getPageSize() > 0 ? req.getPageSize() : 10;

        // 使用内存分页（适合中小数据量）
        List<CustomerInfoRsp> paginatedList = customerDataList.stream()
                .skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());

        // 5. 构建分页响应
        MyPageData<CustomerInfoRsp> pageData = new MyPageData<>();
        pageData.setRecords(paginatedList);
        pageData.setPageNum(pageNum);
        pageData.setPageSize(pageSize);
        pageData.setTotal(customerDataList.size());
        //pageData.setPages((int) Math.ceil((double) customerDataList.size() / pageSize));

        return Result.ok(pageData);
    }

    @PostMapping("/getCustomerCourseList")
    @ApiOperation("获取营期数据详情")
    public Result<List<CustomerCourseListDataRsp>> getCustomerCourseList(@RequestBody CustomerInfoReq req) {

        List<CustomerCourseListDataRsp> result = new ArrayList<>(); // 修复：初始化List而不是null
        Set<Long> customerIds = Collections.emptySet();

        Set<Long> customerIds1 = Optional.ofNullable(dataService.getCustomerIdsBySales(
                req.getColumnId(),
                req.getCompanyId(),
                req.getCampPeriodId(),
                req.getSalesGroupId(),
                req.getSalesId())).orElse(Collections.emptySet());

        Set<Long> customerIds2 = Optional.ofNullable(dataService.getCustomerIdsByCampPeriod(
                req.getCompanyId(),
                req.getCampPeriodId(),
                req.getCourseId(),
                req.getIsFlag())).orElse(Collections.emptySet());

        Set<Long> customerIds3 = Optional.ofNullable(dataService.batchGetCustomersId(
                req.getStartDate(),
                req.getEndDate())).orElse(Collections.emptySet());

        //Set<Long> customerIds = getIntersectionSkipEmpty(customerIds1, customerIds2, customerIds3);

        // 条件1：课程ID不为空
        if (req.getCourseId() != null) {
            if (customerIds1.isEmpty() || customerIds1 == null || customerIds2.isEmpty() || customerIds2 == null){
                customerIds = Collections.emptySet();
            } else {
                customerIds = getIntersection(customerIds1, customerIds2);
            }
        }
        // 条件2：开始结束时间存在
        else if (req.getStartDate() != null && req.getEndDate() != null) {
            if (customerIds1.isEmpty() || customerIds1 == null || customerIds3.isEmpty() || customerIds3 == null){
                customerIds = Collections.emptySet();
            } else if(req.getIsFlag() != null){
                customerIds = getIntersectionSkipEmpty(customerIds1, customerIds2, customerIds3);
            }else{
                customerIds = getIntersection(customerIds1, customerIds3);
            }
        }

        Integer videoViewCount = dataService.batchGetCustomerVideoViewCounts(customerIds,req.getCompanyId(),req.getCampPeriodId(),req.getCourseId());
        Integer videoViewComplete = dataService.batchGetCustomersVideoCompleteById(customerIds,req.getCompanyId(),req.getCampPeriodId(),req.getCourseId());
        Integer videoViewRedPacketCount = dataService.batchGetCustomersRedPacketById(customerIds,req.getCompanyId(),req.getCampPeriodId(),req.getCourseId());
        BigDecimal videoViewRedPacketAmount = dataService.batchGetCustomersRedPacketAmountById(customerIds,req.getCompanyId(),req.getCampPeriodId(),req.getCourseId());

        CustomerCourseListDataRsp rsp = new CustomerCourseListDataRsp();
        rsp.setCountCustomerCourse(videoViewCount);
        rsp.setCountCustomerComplete(videoViewComplete);
        rsp.setCountCustomerRedPacket(videoViewRedPacketCount);
        rsp.setAmountCustomerRedPacket(videoViewRedPacketAmount);

        result.add(rsp);

        return Result.ok(result);

    }

}
