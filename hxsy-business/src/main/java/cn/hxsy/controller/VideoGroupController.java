package cn.hxsy.controller;

import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.VideoGroupPO;
import cn.hxsy.service.VideoGroupService;
import cn.hxsy.utils.UserCacheUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/v1/video-group")
@Api(tags = "视频课分组管理接口")
public class VideoGroupController {

    @Autowired
    private VideoGroupService videoGroupService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    // 新增视频课分组
    @PostMapping
    @ApiOperation("创建视频课分组")
    public Result<VideoGroupPO> create(@RequestBody VideoGroupPO videoGroup) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        videoGroup.setCreatedBy(systemUserSelfInfo.getUsername());
        videoGroup.setCreatedAt(now);

        boolean success = videoGroupService.save(videoGroup);
        return success ? Result.ok(videoGroup) : Result.error("创建失败");
    }

    // 删除视频课分组
    @PostMapping("/deleteById")
    @ApiOperation("删除视频课分组")
    public Result<Void> delete(@RequestBody VideoGroupPO videoGroup) {
        boolean success = videoGroupService.removeById(videoGroup.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    // 更新视频课分组
    @PostMapping("/updateById")
    @ApiOperation("更新视频课分组")
    public Result<VideoGroupPO> updateGroupName(@RequestBody VideoGroupPO videoGroup) {
        // 校验输入参数
        if (videoGroup.getId() == null || StringUtils.isEmpty(videoGroup.getGroupName())) {
            return Result.error("id 或 groupName 不能为空");
        }

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        // 只更新 groupName 字段
        LambdaUpdateWrapper<VideoGroupPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(VideoGroupPO::getId, videoGroup.getId())
                .set(VideoGroupPO::getGroupName, videoGroup.getGroupName())
                .set(VideoGroupPO::getUpdatedBy,systemUserSelfInfo.getUsername())
                .set(VideoGroupPO::getUpdatedAt,now);


        boolean success = videoGroupService.update(updateWrapper);
        return success ? Result.ok(videoGroup) : Result.error("更新失败");
    }
//    @PostMapping("/updateById")
//    @ApiOperation("更新视频课分组")
//    public Result<VideoGroupPO> update(@RequestBody VideoGroupPO videoGroup) {
//        boolean success = videoGroupService.updateById(videoGroup);
//        return success ? Result.ok(videoGroup) : Result.error("更新失败");
//    }

    // 查询视频课分组详情
    @GetMapping("/{id}")
    @ApiOperation("获取视频课分组详情")
    public Result<VideoGroupPO> getById(@PathVariable Long id) {
        VideoGroupPO videoGroup = videoGroupService.getById(id);
        return videoGroup != null ? Result.ok(videoGroup) : Result.error("数据不存在");
    }

    // 分页查询视频课分组列表
    @GetMapping("/page")
    @ApiOperation("分页查询视频课分组列表")
    public Result<Page<VideoGroupPO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String groupName) {

        Page<VideoGroupPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<VideoGroupPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(groupName)) {
            wrapper.like(VideoGroupPO::getGroupName, groupName);
        }

        // 执行分页查询
        Page<VideoGroupPO> resultPage = videoGroupService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    @GetMapping("/list")
    @ApiOperation("查询所有视频分组列表（不分页）")
    public Result<List<VideoGroupPO>> list(@RequestParam(required = false) String groupName) {
        LambdaQueryWrapper<VideoGroupPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件（可选）
//        if (StringUtils.isNotBlank(groupName)) {
//            wrapper.like(VideoGroupPO::getGroupName, groupName);
//        }

        List<VideoGroupPO> list = videoGroupService.list(wrapper);
        return Result.ok(list);
    }

}
