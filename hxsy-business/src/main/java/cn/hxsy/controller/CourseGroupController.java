package cn.hxsy.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CourseGroupPO;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import cn.hxsy.service.CourseGroupService;
import cn.hxsy.service.CourseVideoService;
import cn.hxsy.utils.UserCacheUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.List;


@RestController
@RequestMapping("/api/v1/course-group")
@Api(tags = "视频课分组管理接口")
public class CourseGroupController {

    @Autowired
    private CourseGroupService courseGroupService;
    @Autowired
    private CourseVideoService courseVideoService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Value("${course.public.id}")
    private String publicCourseGroupId;

    // 新增视频课分组
    @PostMapping
    @ApiOperation("创建视频课分组")
    public Result<CourseGroupPO> create(@RequestBody CourseGroupPO courseGroup) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        courseGroup.setCreatedBy(systemUserSelfInfo.getUsername());
        courseGroup.setCreatedAt(now);

        boolean success = courseGroupService.save(courseGroup);
        return success ? Result.ok(courseGroup) : Result.error("创建失败");
    }

    // 删除视频课分组
    @PostMapping("/deleteById")
    @ApiOperation("删除视频课分组")
    public Result<Void> delete(@RequestBody CourseGroupPO courseGroup) {
        boolean success = courseGroupService.removeById(courseGroup.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    // 更新视频课分组
    @PostMapping("/updateById")
    @ApiOperation("更新视频课分组")
    public Result<CourseGroupPO> updateGroupName(@RequestBody CourseGroupPO courseGroupPO) {
        // 校验输入参数
        if (courseGroupPO.getId() == null || StringUtils.isEmpty(courseGroupPO.getGroupName())) {
            return Result.error("id 或 groupName 不能为空");
        }

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        // 只更新 groupName 字段
        LambdaUpdateWrapper<CourseGroupPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CourseGroupPO::getId, courseGroupPO.getId())
                .set(CourseGroupPO::getGroupName, courseGroupPO.getGroupName())
                .set(CourseGroupPO::getUpdatedBy,systemUserSelfInfo.getUsername())
                .set(CourseGroupPO::getUpdatedAt,now);

        boolean success = courseGroupService.update(updateWrapper);
        return success ? Result.ok(courseGroupPO) : Result.error("更新失败");
    }
//    @PostMapping("/updateById")
//    @ApiOperation("更新视频课分组")
//    public Result<CourseGroupPO> update(@RequestBody CourseGroupPO courseGroup) {
//        boolean success = courseGroupService.updateById(courseGroup);
//        return success ? Result.ok(courseGroup) : Result.error("更新失败");
//    }

    // 查询视频课分组详情
    @GetMapping("/{id}")
    @ApiOperation("获取视频课分组详情")
    public Result<CourseGroupPO> getById(@PathVariable Long id) {
        CourseGroupPO courseGroup = courseGroupService.getById(id);
        return courseGroup != null ? Result.ok(courseGroup) : Result.error("数据不存在");
    }

    // 分页查询视频课分组列表
    @GetMapping("/page")
    @ApiOperation("分页查询视频课分组列表")
    public Result<Page<CourseGroupPO>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String groupName) {

        Page<CourseGroupPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CourseGroupPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(groupName)) {
            wrapper.like(CourseGroupPO::getGroupName, groupName);
        }

        // 执行分页查询
        Page<CourseGroupPO> resultPage = courseGroupService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    @GetMapping("/list")
    @ApiOperation("查询所有课程视频分组列表（不分页）")
    public Result<List<CourseGroupPO>> list(@RequestParam(required = false) String groupName) {
        LambdaQueryWrapper<CourseGroupPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件（可选）
//        if (StringUtils.isNotBlank(groupName)) {
//            wrapper.like(CourseGroupPO::getGroupName, groupName);
//        }

        List<CourseGroupPO> list = courseGroupService.list(wrapper);
        return Result.ok(list);
    }

    @GetMapping("/courseVideoList")
    @ApiOperation("分页查询课程视频列表")
    public Result<Page<CourseVideoPO>> courseVideoList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = true) String id,
            @RequestParam(required = false) String courseName,
            @RequestParam(required = false) String courseStatus,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        Page<CourseVideoPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<CourseVideoPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        // 新增逻辑：当courseGroupId非空且不是"null"字符串时，才添加查询条件
        if (StringUtils.isNotBlank(id) && !"ALL".equalsIgnoreCase(id.trim())) {
            wrapper.eq(CourseVideoPO::getGroupId, id);
        }
        if (StringUtils.isNotBlank(courseName)) {
            wrapper.like(CourseVideoPO::getCourseName, courseName);
        }
        if (StringUtils.isNotBlank(courseStatus)) {
            wrapper.eq(CourseVideoPO::getCourseStatus, courseStatus);
        }
        if (StringUtils.isNotBlank(startDate)) {
            LocalDate start = LocalDate.parse(startDate);
            // 大于 日期
            wrapper.ge(CourseVideoPO::getCreatedAt, start.atStartOfDay());
        }
        if (StringUtils.isNotBlank(endDate)) {
            LocalDate end = LocalDate.parse(endDate);
            // 小于 日期
            wrapper.le(CourseVideoPO::getCreatedAt, end.atTime(LocalTime.MAX));
        }


        // 升序排序 field1是字符串
        wrapper.last("ORDER BY CAST(field1 AS UNSIGNED) ASC");
        // 执行分页查询
        Page<CourseVideoPO> resultPage = courseVideoService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }
    @GetMapping("/publicCourseVideoList")
    @ApiOperation("公开课课程视频列表")
    public Result<List<CourseVideoPO>> courseVideoList() {
        LambdaQueryWrapper<CourseVideoPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CourseVideoPO::getGroupId, publicCourseGroupId);
        List<CourseVideoPO> list = courseVideoService.list(wrapper);
        return Result.ok(list);
    }

}