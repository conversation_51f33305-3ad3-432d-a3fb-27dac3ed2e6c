package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.service.ActivityRecordService;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v1/activity-records")
@Api(tags = "活动记录管理")
public class ActivityRecordController {

    @Autowired
    private ActivityRecordService recordService;

    @PostMapping("/video-completed")
    @ApiOperation("视频完播触发活动记录")
    public Result<VideoCompletedResponse> handleVideoCompleted(
            @RequestBody @Valid VideoCompletedRequest request) {

        VideoCompletedResponse response = recordService.processActivities(request);
        return Result.ok(response);
    }

    @GetMapping("/activityRecordList")
    @ApiOperation("分页查询列表")
    public Result<Page<ActivityRecordPO>> activityRecordList(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String type) {

        Page<ActivityRecordPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ActivityRecordPO> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
//        if (StringUtils.isNotBlank(name)) {
//            wrapper.eq(ActivityRecordPO::getActivityType, type);
//        }

        if (StringUtils.isNotBlank(type)) {
            wrapper.eq(ActivityRecordPO::getActivityType, type);
        }

        // 执行分页查询
        Page<ActivityRecordPO> resultPage = recordService.page(page, wrapper);

        // 返回分页结果
        return Result.ok(resultPage);
    }

    @GetMapping("/list")
    @ApiOperation("查询所有课程视频分组列表（不分页）")
    public Result<List<ActivityRecordPO>> list() {
        LambdaQueryWrapper<ActivityRecordPO> wrapper = new LambdaQueryWrapper<>();

        List<ActivityRecordPO> list = recordService.list(wrapper);
        return Result.ok(list);
    }

}