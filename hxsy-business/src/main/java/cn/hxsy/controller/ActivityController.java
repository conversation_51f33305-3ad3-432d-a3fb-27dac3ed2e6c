package cn.hxsy.controller;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.ActivityPO;
import cn.hxsy.datasource.model.entity.CampCoursePO;
import cn.hxsy.service.ActivityService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v1/activities")
@Api(tags = "营销活动管理")
public class ActivityController {

    @Autowired
    private ActivityService activityService;

    @PostMapping
    @ApiOperation("创建活动")
    public Result<ActivityPO> createActivity(@RequestBody ActivityPO activityPO) {
        if(!StringUtils.contains(activityPO.getActivityType(), "5")){
            activityPO.setAnswerOptions("[]");
            activityPO.setCorrectAnswer("[]");
        }

        boolean success = activityService.save(activityPO);
        return success ? Result.ok(activityPO) : Result.error("创建失败");
    }

    @PostMapping("/deleteById")
    @ApiOperation("删除活动")
    public Result<Void> deleteActivity(@RequestBody ActivityPO activityPO) {
        boolean success = activityService.removeById(activityPO.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    @PostMapping("/updateById")
    @ApiOperation("更新活动")
    public Result<ActivityPO> updateActivity(@RequestBody ActivityPO activityPO) {
        boolean success = activityService.updateById(activityPO);
        return success ? Result.ok(activityPO) : Result.error("更新失败");
    }

    @GetMapping("/{id}")
    @ApiOperation("查询活动详情")
    public Result<ActivityPO> getActivity(@PathVariable Long id) {
        ActivityPO activityPO = activityService.getById(id);
        return activityPO != null ? Result.ok(activityPO) : Result.error("数据不存在");
    }

    @GetMapping("/page")
    @ApiOperation("分页查询活动列表")
    public Result<Page<ActivityPO>> listPage(
            @RequestParam(required = false) String activityType,
            @RequestParam(required = false) String title,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {

        Page<ActivityPO> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ActivityPO> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotBlank(activityType)) {
            wrapper.eq(ActivityPO::getActivityType, activityType);
        }

        if (StringUtils.isNotBlank(title)) {
            wrapper.like(ActivityPO::getTitle, title);
        }

        // 执行分页查询
        Page<ActivityPO> resultPage = activityService.page(page, wrapper);

        return Result.ok(resultPage);
    }

    @GetMapping
    @ApiOperation("查询活动列表（不分页）")
    public  Result<List<ActivityPO>> list(@RequestParam(required = false) String activityType) {
        List<ActivityPO> list = activityService.listByType(activityType);
        return Result.ok(list);
    }
}
