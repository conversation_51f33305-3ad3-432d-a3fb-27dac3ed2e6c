package cn.hxsy.controller;

import cn.hxsy.api.user.model.request.CampPeriodUrlRequest;
import cn.hxsy.api.user.model.request.CompanyUrlRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.request.wxPayRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.base.response.TransferNotification;
import cn.hxsy.datasource.model.entity.MiniProgram;
import cn.hxsy.service.MiniProgramService;
import cn.hxsy.service.WxPayService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

/**
 * 小程序管理接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@RestController
@RequestMapping("/api/v1/mini-program")
@Api(tags = "小程序管理接口")
public class MiniProgramController {

    @Autowired
    private MiniProgramService miniProgramService;

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @PostMapping("/add")
    @ApiOperation("创建小程序")
    public Result<MiniProgram> create(@RequestBody MiniProgram miniProgram) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        miniProgram.setCreatedBy(systemUserSelfInfo.getUsername());
        miniProgram.setCreatedAt(now);

        boolean success = miniProgramService.save(miniProgram);
        return success ? Result.ok(miniProgram) : Result.error("创建失败");
    }

    @PostMapping("/delete")
    @ApiOperation("删除小程序")
    public Result<Void> delete(@RequestBody MiniProgram miniProgram) {
        boolean success = miniProgramService.removeById(miniProgram.getId());
        return success ? Result.ok() : Result.error("删除失败");
    }

    @PutMapping
    @ApiOperation("更新小程序")
    public Result<MiniProgram> update(@RequestBody MiniProgram miniProgram) {

        LocalDateTime now = LocalDateTime.now();
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();

        miniProgram.setUpdatedBy(systemUserSelfInfo.getUsername());
        miniProgram.setUpdatedAt(now);

        boolean success = miniProgramService.updateById(miniProgram);
        return success ? Result.ok(miniProgram) : Result.error("更新失败");
    }

    @GetMapping("/getById")
    @ApiOperation("获取小程序详情")
    public Result<MiniProgram> getById(@PathVariable Long id) {
        MiniProgram miniProgram = miniProgramService.getById(id);
        return miniProgram != null ? Result.ok(miniProgram) : Result.error("数据不存在");
    }

    @GetMapping("/page")
    @ApiOperation("分页查询小程序列表")
    public Result<Page<MiniProgram>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String type
    ) {

        Page<MiniProgram> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<MiniProgram> wrapper = new LambdaQueryWrapper<>();

        // 设置查询条件
        if (StringUtils.isNotBlank(name)) {
            wrapper.like(MiniProgram::getName, name);
        }
        if (status != null) {
            wrapper.eq(MiniProgram::getStatus, status);
        }

        if (StringUtils.isNotBlank(type)) {
            wrapper.eq(MiniProgram::getType, type);
        }

        // 执行分页查询
        Page<MiniProgram> resultPage = miniProgramService.page(page, wrapper);
        page.setTotal(miniProgramService.count(wrapper));
        // 返回分页结果
        return Result.ok(resultPage);
    }

    @ApiOperation(value = "获取小程序邀请销售注册小程序码", notes = "获取小程序邀请销售注册小程序码", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/get-sell-url", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<String> getSellUrl(@RequestBody CompanyUrlRequest companyUrlRequest) {
        return miniProgramService.getSellUrl(companyUrlRequest);
    }

    @ApiOperation(value = "获取营期分享链接", notes = "获取营期分享链接", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/get-camp-period-url", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<String>  getCampPeriodUrl(@RequestBody CampPeriodUrlRequest campPeriodUrlRequest) {
        return miniProgramService.getCampPeriodUrl(campPeriodUrlRequest);
    }

    @ApiOperation(value = "获取课程分享链接", notes = "获取课程分享链接", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/get-course-url", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<String>  getCourseUrl(@RequestBody CampPeriodUrlRequest campPeriodUrlRequest) {
        return miniProgramService.getCourseUrl(campPeriodUrlRequest);
    }
    @ApiOperation(value = "发起微信转账", notes = "发起微信转账", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/wx-pay", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<TransferNotification>  toPay(@RequestBody wxPayRequest wxPayRequest) {
        return wxPayService.toPay(wxPayRequest);
    }

    @ApiOperation(value = "新增商户微信转账测试", notes = "新增商户微信转账测试", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/wx-pay-test", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<TransferNotification>  toPayTest(@RequestBody wxPayRequest wxPayRequest) {
        return wxPayService.toPayTest(wxPayRequest);
    }

    @ApiOperation(value = "微信支付回调接口", notes = "处理微信支付结果通知", consumes = MediaType.ALL_VALUE)
    @RequestMapping(value = "/wx-pay-callback", method = RequestMethod.POST,
            consumes = MediaType.ALL_VALUE)
    public Result<String> wxPayCallback(HttpServletRequest request) {
        return wxPayService.handlePayCallback(request);
    }
} 