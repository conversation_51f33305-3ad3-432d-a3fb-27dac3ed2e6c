package cn.hxsy;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = {
        "cn.hxsy"})
@EnableFeignClients(basePackages = {"cn.hxsy.api.user.feign"})
@EnableDubbo
@EnableAsync
public class HxsyBusinessApplication {

    public static void main(String[] args) {
        SpringApplication.run(HxsyBusinessApplication.class, args);
    }

}
