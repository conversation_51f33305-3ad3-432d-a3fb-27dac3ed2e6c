package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.WxMerchantKeyPO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 微信商户密钥Service接口
 */
public interface WxMerchantKeyService extends IService<WxMerchantKeyPO> {

    /**
     * 绑定商户密钥
     *
     * @param wxMerchantKeyPO 商户密钥信息
     * @return 是否绑定成功
     */
    boolean bindMerchantKey(WxMerchantKeyPO wxMerchantKeyPO);

    /**
     * 更新商户密钥
     *
     * @param wxMerchantKeyPO 商户密钥信息
     * @return 是否更新成功
     */
    boolean updateMerchantKey(WxMerchantKeyPO wxMerchantKeyPO);

    /**
     * 根据公司ID查询商户密钥
     *
     * @param companyId 公司ID
     * @return 商户密钥信息
     */
    WxMerchantKeyPO getByCompanyId(Long companyId);

    /**
     * 解绑商户密钥
     *
     * @param companyId 公司ID
     * @return 是否解绑成功
     */
    boolean unbindMerchantKey(Long companyId);
}
