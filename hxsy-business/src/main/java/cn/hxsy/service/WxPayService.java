package cn.hxsy.service;

import cn.hxsy.base.request.wxPayRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.base.response.TransferNotification;

import javax.servlet.http.HttpServletRequest;

/**
 * 微信支付服务接口
 */
public interface WxPayService {

    /**
     * 发起支付请求
     * @param wxPayRequest 支付请求参数
     * @return 支付结果
     */
    Result<TransferNotification> toPay(wxPayRequest wxPayRequest);

    /**
     * @param wxPayRequest 商户转账测试
     * 用于新增商户号测试能否转账，不记录转账信息
     * @return 支付结果
     */
    Result<TransferNotification> toPayTest(wxPayRequest wxPayRequest);

    /**
     * 处理微信支付回调
     * @param request HTTP请求
     * @return 处理结果
     */
    Result<String> handlePayCallback(HttpServletRequest request);
}