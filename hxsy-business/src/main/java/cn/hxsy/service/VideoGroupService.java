package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.VideoGroupPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface VideoGroupService extends IService<VideoGroupPO> {

    // 保存视频课分组
    boolean save(VideoGroupPO videoGroup);

    // 删除视频课分组
    boolean removeById(Long id);

    // 更新视频课分组
    boolean updateById(VideoGroupPO videoGroup);

    // 查询视频课分组详情
    VideoGroupPO getById(Long id);

    // 分页查询视频课分组列表
    Page<VideoGroupPO> page(Page<VideoGroupPO> page, LambdaQueryWrapper<VideoGroupPO> wrapper);
}
