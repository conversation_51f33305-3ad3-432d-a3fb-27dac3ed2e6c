package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.CompanySalesRsp;
import cn.hxsy.datasource.model.entity.StatsSummary;

public interface DashboardService {

    // 获取统计数据
    StatsSummary getDashboardStats();

    // 每小时更新Redis数据
    void refreshRedisStats();

    // 每日凌晨持久化数据到数据库
    int persistToDatabase();

    StatsSummary getUpdateDashboardStats();

    CompanySalesRsp getCompanySales(Long id);
}
