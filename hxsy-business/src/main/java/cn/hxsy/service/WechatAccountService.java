package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.ActivityGroupPO;
import cn.hxsy.datasource.model.entity.WechatAccountPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface WechatAccountService extends IService<WechatAccountPO> {

    // 保存
    boolean save(WechatAccountPO po);

    // 删除
    boolean removeById(Long id);

    // 更新
    boolean updateById(WechatAccountPO po);

    // 查询详情
    WechatAccountPO getById(Long id);

    Page<WechatAccountPO> page(Page<WechatAccountPO> page, LambdaQueryWrapper<WechatAccountPO> wrapper);

}
