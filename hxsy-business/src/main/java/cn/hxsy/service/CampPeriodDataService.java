package cn.hxsy.service;

import cn.hxsy.dao.CustomerBehaviorRelationMapper;
import cn.hxsy.datasource.model.entity.*;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.flogger.Flogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class CampPeriodDataService {
    @Autowired
    private CustomerBehaviorRelationMapper relationMapper;

    public CampPeriodDataRsp buildCampPeriodData(Long companyId, Long campPeriodId) {

        //计算昨日时间范围
        LocalDateTime yesterdayStart = LocalDateTime.now().minusDays(1).with(LocalTime.MIN);
        LocalDateTime yesterdayEnd = LocalDateTime.now().minusDays(1).with(LocalTime.MAX);
        LocalDateTime todayStart = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.now().with(LocalTime.MAX).withNano(0);;
        // 1. 统计总会员数
        Integer totalMembers = relationMapper.countDistinctCustomers(companyId, campPeriodId);
        // 2. 截止日期的会员统计
        Integer yesterdayTotalMembers = relationMapper.countDistinctCustomersByDate(
                companyId, campPeriodId, yesterdayStart, yesterdayEnd
        );
        // 3. 新增会员统计
        Integer yesterdayNewMembers = relationMapper.countNewCustomers(
                companyId, campPeriodId, yesterdayStart, yesterdayEnd
        );
        // 4. 活跃会员统计
        Integer yesterdayActiveMembers = relationMapper.countActiveCustomers(
                companyId, campPeriodId, yesterdayStart, yesterdayEnd
        );
        // 5. 今日会员统计
//        Integer todayActiveMembers = relationMapper.countTodayActiveCustomers(
//                companyId, campPeriodId, todayStart, todayEnd
//        );

        Set<Long> customerIds1 = Optional.ofNullable(findCustomerIdsByCreateDate(todayStart, todayEnd)).orElse(Collections.emptySet());
        Set<Long> customerIds2 = Optional.ofNullable(getCustomerIdsByCampPeriod(companyId, campPeriodId,null,null)).orElse(Collections.emptySet());
        Set<Long> customerIds = getIntersection(customerIds1, customerIds2);
        Integer todayActiveMembers = customerIds.size();

        //昨日上线率
        String loginRate = "0.00%";
        if (yesterdayTotalMembers != null && yesterdayTotalMembers > 0) {
            double rate = (yesterdayActiveMembers * 100.0) / yesterdayTotalMembers;
            loginRate = String.format("%.2f", rate);
        }

        // 2. 构建响应对象
        CampPeriodDataRsp rsp = new CampPeriodDataRsp();
        rsp.setCampPeriodTotalMembers(totalMembers + "");
        rsp.setCampPeriodLastDayMembers(yesterdayTotalMembers + "");
        rsp.setCampPeriodLastDayNewMembers(yesterdayNewMembers + "");
        rsp.setCampPeriodLastDayLoginMembers(yesterdayActiveMembers + "");
        rsp.setCampPeriodLastDayLoginPercentMembers(loginRate);
        rsp.setCampPeriodToDayNewMembers(todayActiveMembers + "");

        //其他字段
        Integer totalRedPacket = relationMapper.countRedPacket(companyId, campPeriodId);
        BigDecimal amountRedPacket = relationMapper.amountRedPacket(companyId, campPeriodId);
        rsp.setCampPeriodTotalRedPacket(totalRedPacket + "");
        rsp.setCampPeriodAmountRedPacket(amountRedPacket + "");

        return rsp;
    }

    private Set<Long> getIntersection(Set<Long> set1, Set<Long> set2) {
        Set<Long> result = new HashSet<>(set1);
        result.retainAll(set2);
        return result;
    }

    public List<CampCourseDataRsp> buildCampCourseData(Long companyId, Long campPeriodId, Long courseId) {
        // 1. 获取 JSON 数据
        List<String> jsonList = relationMapper.findByCampId(campPeriodId);

        // 2. 解析并提取课程小节
        List<CampCourseDataRsp> response = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();

        try {
            // 没有记录时返回"暂无数据"
            if (jsonList == null || jsonList.isEmpty()) {
                response.add(createEmptyDataRow());
                return response;
            }

            // 遍历每个营期记录（一个营期可能对应多个课程）
            for (String json : jsonList) {
                if (json == null || json.isEmpty()) continue;

                // 解析为 JSON 数组
                JsonNode root = mapper.readTree(json);
                if (!root.isArray()) continue;

                // 提取每个课程小节
                for (JsonNode courseSection : root) {
                    CampCourseDataRsp rsp = parseCourseSection(courseSection,companyId,campPeriodId,courseId);
                    if (rsp != null) {
                        response.add(rsp);
                    }
                }
            }
        } catch (Exception e) {
            //log.error("JSON解析失败", e);
        }

        // 如果没有任何课程小节，返回"暂无数据"
        if (response.isEmpty()) {
            response.add(createEmptyDataRow());
        }

        return response;
    }

    public List<CampCourseListDataRsp> buildCampCourseListData(Long companyId, Long campPeriodId) {
        // 1. 获取 JSON 数据
        List<String> jsonList = relationMapper.findByCampId(campPeriodId);

        // 2. 解析并提取课程小节
        List<CampCourseListDataRsp> response = new ArrayList<>();
        ObjectMapper mapper = new ObjectMapper();

        try {
            // 遍历每个营期记录（一个营期可能对应多个课程）
            for (String json : jsonList) {
                if (json == null || json.isEmpty()) continue;

                // 解析为 JSON 数组
                JsonNode root = mapper.readTree(json);
                if (!root.isArray()) continue;

                // 提取每个课程小节
                for (JsonNode courseSection : root) {
                    CampCourseListDataRsp rsp = parseCourseListSection(courseSection);
                    if (rsp != null) {
                        response.add(rsp);
                    }
                }
            }
        } catch (Exception e) {
            //log.error("JSON解析失败", e);
        }

        return response;
    }

    // 创建"暂无数据"行
    private CampCourseDataRsp createEmptyDataRow() {
        CampCourseDataRsp rsp = new CampCourseDataRsp();
        rsp.setCourseId("-");
        rsp.setCourseName("暂无数据");
        rsp.setCourseTime("");
        rsp.setViewCount(0);
        rsp.setCompleteCount(0);
        rsp.setCompletionRate("0.00");
        rsp.setRedPacketCount(0);
        rsp.setRedPacketAmount(BigDecimal.ZERO);
        return rsp;
    }

    // 解析单个课程小节
    private CampCourseDataRsp parseCourseSection(JsonNode sectionNode,Long companyId,Long campPeriodId, Long courseId) {
        // 关键字段提取 - 与您提供的JSON结构匹配
        String id = getNodeValue(sectionNode, "id");
        String courseName = getNodeValue(sectionNode, "courseName");
        String startTime = getNodeValue(sectionNode, "startTime");

        // 缺少必要字段时跳过
        if (StringUtils.isBlank(id) || StringUtils.isBlank(courseName)) {
            return null;
        }

        CampCourseDataRsp rsp = new CampCourseDataRsp();
        rsp.setCourseId(id);
        rsp.setCourseName(courseName);
        rsp.setCourseTime(startTime);


        //统计观看人数、观看完人数、红包数量、红包金额
        Integer viewMembers = relationMapper.countDistinctCustomersMembersById(Long.valueOf(id),campPeriodId);
        Integer viewComplete = relationMapper.countDistinctCustomersCompleteById(Long.valueOf(id),campPeriodId);
        Integer viewRedPacketCount = relationMapper.countDistinctRedPacketById(Long.valueOf(id),campPeriodId);
        BigDecimal viewRedPacketAmount = relationMapper.countDistinctRedPacketAmountById(Long.valueOf(id),campPeriodId);
        // 设置默认统计值
        String loginRate = "0.00%";
        if (viewMembers != null && viewMembers > 0) {
            double rate = (viewComplete * 100.0) / viewMembers;
            loginRate = String.format("%.2f%%", rate);
        }
        rsp.setViewCount(viewMembers);
        rsp.setCompleteCount(viewComplete);
        rsp.setCompletionRate(loginRate);
        rsp.setRedPacketCount(viewRedPacketCount);
        rsp.setRedPacketAmount(viewRedPacketAmount);

        return rsp;
    }

    private CampCourseListDataRsp parseCourseListSection(JsonNode sectionNode) {
        // 关键字段提取 - 与您提供的JSON结构匹配
        String id = getNodeValue(sectionNode, "id");
        String courseName = getNodeValue(sectionNode, "courseName");

        // 缺少必要字段时跳过
        if (StringUtils.isBlank(id) || StringUtils.isBlank(courseName)) {
            return null;
        }

        CampCourseListDataRsp rsp = new CampCourseListDataRsp();
        rsp.setValue(id);
        rsp.setLabel(courseName);

        return rsp;
    }

    // 安全获取JSON节点值
    private String getNodeValue(JsonNode node, String fieldName) {
        if (node.hasNonNull(fieldName)) {
            JsonNode valueNode = node.get(fieldName);
            if (valueNode != null) {
                return valueNode.isTextual() ? valueNode.asText() : valueNode.toString();
            }
        }
        return "";
    }

    public Set<Long> getCustomerIdsByCampPeriod(Long companyId, Long campPeriodId,Long courseId,Integer isFlag) {
        Set<Long> customerIds = new HashSet<>();
        List<Long> ids = relationMapper.findCustomerIdsByCampPeriod(companyId, campPeriodId, courseId, isFlag);

        customerIds.addAll(ids);
        return customerIds;
    }

    public Set<Long> getCustomerIdsBySales(Long columnId, Long companyId, Long campPeriodId,Long salesGroupId,Long salesId) {
        Set<Long> customerIds = new HashSet<>();
        List<Long> ids = relationMapper.findCustomerIdsBySales(columnId, companyId, campPeriodId, salesGroupId, salesId);
        customerIds.addAll(ids);
        return customerIds;
    }

    public Set<Long> batchGetCustomersId(LocalDateTime startDate,LocalDateTime endDate) {
        Set<Long> customerIds = new HashSet<>();
        List<Long> ids = relationMapper.findCustomerIdsByDate(startDate, endDate);
        customerIds.addAll(ids);
        return customerIds;
    }

    public Set<Long> findCustomerIdsByCreateDate(LocalDateTime startDate,LocalDateTime endDate) {
        Set<Long> customerIds = new HashSet<>();
        List<Long> ids = relationMapper.findCustomerIdsByCreateDate(startDate, endDate);
        customerIds.addAll(ids);
        return customerIds;
    }

    public Map<Long, Customer> batchGetCustomers(Set<Long> customerIds) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, Customer> resultMap = new HashMap(customerIds.size() * 2);

        // 分批查询（每批500个ID）
        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            List<Customer> batchCustomers = relationMapper.batchGetCustomers(batchIds);
            // 3. 使用 EntrySet 遍历
            for (Customer customer : batchCustomers) {
                resultMap.put(customer.getId(), customer);
            }
        }

        return resultMap;
    }

    public Map<Long, Integer> batchGetVideoViewCounts(Set<Long> customerIds,Long campPeriodId,Long courseId) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, Integer> resultMap = new HashMap(customerIds.size() * 2);

        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            List<VideoViewCountDTO> batchResult = relationMapper.countVideoViews(batchIds,campPeriodId,courseId);
            // 3. 使用 EntrySet 遍历
            for (VideoViewCountDTO dto : batchResult) {
                resultMap.put(dto.getCustomerId(), dto.getCount());
            }
        }
        return resultMap;
    }

    public Integer batchGetCustomerVideoViewCounts(Set<Long> customerIds,Long companyId,Long campPeriodId,Long courseId) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        int totalCount = 0;
        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            Integer batchResult = relationMapper.countCustomerVideoViews(batchIds,companyId,campPeriodId,courseId);
            totalCount += batchResult;
        }
        return totalCount;
    }

    public Integer batchGetCustomersVideoCompleteById(Set<Long> customerIds,Long companyId,Long campPeriodId,Long courseId) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        int totalCount = 0;
        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            Integer batchResult = relationMapper.countCustomerVideoCompleteById(batchIds,campPeriodId,courseId);
            totalCount += batchResult;
        }
        return totalCount;
    }

    public Integer batchGetCustomersRedPacketById(Set<Long> customerIds,Long companyId,Long campPeriodId,Long courseId) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        int totalCount = 0;
        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            Integer batchResult = relationMapper.countCustomerRedPacket(batchIds,companyId,campPeriodId,courseId);
            totalCount += batchResult;
        }
        return totalCount;
    }

    public BigDecimal batchGetCustomersRedPacketAmountById(Set<Long> customerIds,Long companyId,Long campPeriodId,Long courseId) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        BigDecimal totalCount = BigDecimal.ZERO;
        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            BigDecimal batchResult = relationMapper.countCustomerRedPacketAmount(batchIds,companyId,campPeriodId,courseId);
            if (batchResult != null) {
                totalCount = totalCount.add(batchResult);
            }
        }
        return totalCount;
    }

    public Map<Long, String> batchGetSalesMan(Set<Long> customerIds, Long columnId) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, String> resultMap = new HashMap<>(customerIds.size() * 2); // 初始化容量防扩容

        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            List<CustomerSalesManDTO> batchResult = relationMapper.getSalesMan(batchIds, columnId);
            // 3. 使用 EntrySet 遍历
            for (CustomerSalesManDTO dto : batchResult) {
                resultMap.put(dto.getCustomerId(), dto.getSalesMan());
            }
        }
        return resultMap;
    }

    public Map<Long, Integer> batchGetRedPacketCounts(Set<Long> customerIds,Long campPeriodId,Long courseId) {
        // 1. 分批查询防 SQL 过长（每批 1000 个 ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, Integer> resultMap = new HashMap<>(customerIds.size() * 2);

        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            List<VideoViewCountDTO> batchResult = relationMapper.getRedPacket(batchIds,campPeriodId,courseId);
            // 3. 使用 EntrySet 遍历
            for (VideoViewCountDTO dto : batchResult) {
                resultMap.put(dto.getCustomerId(), dto.getCount());
            }
        }
        return resultMap;
    }

    public Map<Long, Integer> batchGetViewCourseCounts(Set<Long> customerIds,Long campPeriodId, Long courseId) {
        // 1. 分批查询防SQL过长（每批1000个ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, Integer> resultMap = new HashMap<>(customerIds.size() * 2);

        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            List<VideoViewCountDTO> batchResult = relationMapper.countCustomersViewById(batchIds,campPeriodId,courseId);
            // 3. 使用 EntrySet 遍历
            for (VideoViewCountDTO dto : batchResult) {
                resultMap.put(dto.getCustomerId(), dto.getCount());
            }
        }
        return resultMap;
    }

    public Map<Long, String> batchGetViewCourseTime(Set<Long> customerIds,Long campPeriodId,Long courseId) {
        // 1. 分批查询防SQL过长（每批1000个ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, String> resultMap = new HashMap<>(customerIds.size() * 2);

        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));
            // 2. 单次批量查询
            List<VideoViewCountDTO> batchResult = relationMapper.countCustomersViewTimeById(batchIds,campPeriodId,courseId);
            // 3. 使用 EntrySet 遍历
            for (VideoViewCountDTO dto : batchResult) {
                resultMap.put(dto.getCustomerId(), dto.getPlayProgress());
            }
        }
        return resultMap;
    }

    public Map<Long, Integer> batchGetCompleteCourseCounts(Set<Long> customerIds, Long campPeriodId,Long courseId) {
        // 1. 分批查询防SQL过长（每批1000个ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, Integer> resultMap = new HashMap<>(customerIds.size() * 4 / 3 + 1);

        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));

            // 2. 单次批量查询
            List<VideoViewCountDTO> batchResult = relationMapper.countCustomersCompleteById(batchIds,campPeriodId,courseId);

            // 3. 使用 EntrySet 遍历
            for (VideoViewCountDTO dto : batchResult) {
                resultMap.put(dto.getCustomerId(), dto.getCount());
            }
        }
        return resultMap;
    }

    public Map<Long, String> batchGetCompleteCourseTime(Set<Long> customerIds, Long campPeriodId, Long courseId) {
        // 1. 分批查询防SQL过长（每批1000个ID）
        int batchSize = 1000;
        List<Long> idList = new ArrayList<>(customerIds);
        Map<Long, String> resultMap = new HashMap<>(customerIds.size() * 4 / 3 + 1);

        for (int i = 0; i < idList.size(); i += batchSize) {
            List<Long> batchIds = idList.subList(i, Math.min(i + batchSize, idList.size()));

            // 2. 单次批量查询
            List<VideoViewCountDTO> batchResult = relationMapper.countCustomersCompleteTimeById(batchIds,campPeriodId,courseId);

            // 3. 使用 EntrySet 遍历
            for (VideoViewCountDTO dto : batchResult) {
                resultMap.put(dto.getCustomerId(), dto.getCompleteTime());
            }
        }
        return resultMap;
    }

}
