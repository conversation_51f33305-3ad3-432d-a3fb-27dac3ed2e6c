package cn.hxsy.service;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.FileUploadRequest;
import cn.hxsy.datasource.model.entity.VideoUploadPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-02 14:37:26
 */
public interface VideoUploadService extends IService<VideoUploadPO> {

    Result<Map<String, String>> initiateBatchUpload(Map<String, List<String>> request);

    Result<String> uploadPartsBatch(List<String> fileNames, List<Integer> partNumbers, List<MultipartFile> files,List<String> groupIds) throws Exception;

    Result<List<Map<String, String>>> completeBatchUpload(Map<String, List<String>> request);

    Result<String> simpleUpload(MultipartFile file,String path) throws IOException;

    Result<String> uploadCourseImage(MultipartFile file,String path) throws IOException;

    /**
     * @description: 分片上传视频文件
     * 1、断点续传逻辑
     * 1.1、有上传记录缓存时，需要先确定当前请求上传的分片是否已经上传过，如果上传过则直接返回成功
     * 1.2、如果上传过，并且是最后一个分片，就需要直接开启合并碎片，而不是直接返回
     * 1.3、如果当前没有上传记录，则执行后续的分片上传逻辑
     * 2、批量上传时成功传完的文件合并逻辑
     * 2.1、每次分片上传时，如果当前上传的是最后一个分片，则利用redis记录当前文件已经成功
     * 2.2、每次分片上传时，还需要匹配当前上传列表的所有缓存，是否有文件的上传状态为成功，成功则开启合并任务
     * 2.3、合并时，利用redis记录当前文件合并操作的分布式锁，防止有第二个文件的全部分片也上传成功后，第一个文件还没合并完成，对应分片缓存信息还未删除，重复发起了合并请求
     * @author: qinLuan
     * @date: 2025/5/19 17:11
     * @param: [fileUploadRequest, files]
     **/
    Result<String> batchUploadParts(FileUploadRequest fileUploadRequest,MultipartFile files) throws Exception;

    Page<VideoUploadPO> page(Page<VideoUploadPO> page, LambdaQueryWrapper<VideoUploadPO> wrapper);

    Result<String> deleteById(Long id);

    Result<String> getVodSignature();
}
