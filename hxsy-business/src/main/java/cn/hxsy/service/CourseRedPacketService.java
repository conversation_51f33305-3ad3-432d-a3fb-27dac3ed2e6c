package cn.hxsy.service;

import cn.hxsy.base.request.CourseRedPacketRequest;
import cn.hxsy.datasource.model.entity.CourseRedPacket;
import cn.hxsy.model.response.CourseRedPacketPageResponse;
import cn.hxsy.model.response.CustomerRedPacketSummaryResponse;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 课程红包记录服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface CourseRedPacketService extends IService<CourseRedPacket> {

    /**
     * 分页查询课程红包记录，并返回符合条件的红包总金额
     *
     * @param current 当前页
     * @param size 每页大小
     * @param request 查询条件
     * @return 包含分页数据和红包总金额的响应对象
     */
    CourseRedPacketPageResponse courseRedPacketList(long current, long size, CourseRedPacketRequest request);

    /**
     * 获取客户红包汇总信息
     *
     * @param customerId 客户ID
     * @return 红包汇总信息，包含已领取总金额和未领取红包列表
     */
    CustomerRedPacketSummaryResponse getCustomerRedPacketSummary(Long customerId);
}