package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CampPeriodPO;
import cn.hxsy.datasource.model.entity.CampPeriodResponseDTO;
import cn.hxsy.datasource.model.entity.CampPeriodVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface CampPeriodService extends IService<CampPeriodPO> {

    // 保存课程视频
    boolean save(CampPeriodPO campPeriod);

    // 删除课程视频
    boolean removeById(Long id);

    // 更新课程视频
    boolean updateById(CampPeriodPO campPeriod);

    // 查询课程视频详情
    CampPeriodPO getById(Long id);

    // 分页查询课程视频列表
    Page<CampPeriodPO> page(Page<CampPeriodPO> page, LambdaQueryWrapper<CampPeriodPO> wrapper);

    List<CampPeriodResponseDTO> getCampPeriodsWithCourses(Long companyId, Long salesId);

    List<CampPeriodPO> getCampPeriodsByCompanyId(Long companyId);

    boolean setCampPeriodRedPacketAmount(String id, String redPacketAmount);

    List<CampPeriodPO> getCampPeriodsByIds(List<Long> campPeriodIds);
}
