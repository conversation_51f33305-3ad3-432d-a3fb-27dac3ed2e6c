package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.ActivityGroupPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ActivityGroupService extends IService<ActivityGroupPO> {

    // 保存视频课分组
    boolean save(ActivityGroupPO activityGroup);

    // 删除视频课分组
    boolean removeById(Long id);

    // 更新视频课分组
    boolean updateById(ActivityGroupPO activityGroup);

    // 查询视频课分组详情
    ActivityGroupPO getById(Long id);

    // 分页查询视频课分组列表
    Page<ActivityGroupPO> page(Page<ActivityGroupPO> page, LambdaQueryWrapper<ActivityGroupPO> wrapper);
}