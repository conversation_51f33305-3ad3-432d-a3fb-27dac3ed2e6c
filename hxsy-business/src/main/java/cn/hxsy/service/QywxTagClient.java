package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.QywxTag;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class QywxTagClient {
    private final Map<Long, QywxTag> tagStore = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1000);

    public boolean tagExists(String tagName) {
        return tagStore.values().stream()
                .anyMatch(t -> t.getTagname().equalsIgnoreCase(tagName));
    }

    public boolean tagExists(Long tagId) {
        return tagStore.containsKey(tagId);
    }

    public QywxTag createTag(QywxTag tag) {
        synchronized (tagStore) {
            if (tag.getTagid() == null) {
                tag.setTagid(idGenerator.getAndIncrement());
            }
            tagStore.put(tag.getTagid(), tag);
            return tag;
        }
    }

    public QywxTag updateTag(QywxTag tag) {
        if (!tagStore.containsKey(tag.getTagid())) {
            throw new RuntimeException("标签不存在");
        }
        tagStore.put(tag.getTagid(), tag);
        return tag;
    }

    public void safeDeleteTag(Long tagId) {
        if (!tagStore.containsKey(tagId)) {
            throw new RuntimeException("标签不存在");
        }
        tagStore.remove(tagId);
    }

    public List<QywxTag> listTags() {
        return new ArrayList<>(tagStore.values());
    }
}
