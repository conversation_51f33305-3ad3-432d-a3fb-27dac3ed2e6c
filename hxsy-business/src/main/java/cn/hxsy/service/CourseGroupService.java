package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.CourseGroupPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface CourseGroupService extends IService<CourseGroupPO> {

    // 保存视频课分组
    boolean save(CourseGroupPO courseGroup);

    // 删除视频课分组
    boolean removeById(Long id);

    // 更新视频课分组
    boolean updateById(CourseGroupPO courseGroup);

    // 查询视频课分组详情
    CourseGroupPO getById(Long id);

    // 分页查询视频课分组列表
    Page<CourseGroupPO> page(Page<CourseGroupPO> page, LambdaQueryWrapper<CourseGroupPO> wrapper);
}