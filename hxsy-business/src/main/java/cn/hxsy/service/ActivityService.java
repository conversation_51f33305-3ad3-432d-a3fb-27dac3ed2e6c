package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.ActivityPO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ActivityService extends IService<ActivityPO> {

    /**
     * 分页查询活动列表（按活动类型过滤）
     */
    Page<ActivityPO> listByTypePage(String activityType, String title,Integer pageNum, Integer pageSize);

    /**
     * 查询活动列表（不分页，按活动类型过滤）
     */
    List<ActivityPO> listByType(String activityType);
}
