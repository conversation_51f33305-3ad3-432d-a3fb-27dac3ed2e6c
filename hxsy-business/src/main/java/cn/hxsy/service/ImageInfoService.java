package cn.hxsy.service;

import cn.hxsy.datasource.model.entity.ImageInfoPO;
import cn.hxsy.datasource.model.entity.VideoUploadPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ImageInfoService extends IService<ImageInfoPO> {

    Page<ImageInfoPO> page(Page<ImageInfoPO> page, LambdaQueryWrapper<ImageInfoPO> wrapper);

}
