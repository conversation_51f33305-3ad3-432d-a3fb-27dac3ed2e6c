package cn.hxsy.service;

import cn.hxsy.api.user.model.response.CampCourseVideoResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.CampCoursePO;
import cn.hxsy.datasource.model.entity.CourseOrderDTO;
import cn.hxsy.model.request.CampCourseRequest;
import cn.hxsy.model.request.CampCourseRequestDTO;
import cn.hxsy.model.request.CourseVideoJson;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface CampCourseService extends IService<CampCoursePO> {

    // 保存营期课程
    boolean save(CampCoursePO campCourse);

    boolean saveBatch(List<CampCoursePO> campCourses);

    // 删除营期课程
    boolean removeById(Long id);

    // 更新营期课程
    boolean updateById(CampCoursePO campCourse);

    // 查询营期课程详情
    CampCoursePO getById(Long id);

    // 分页查询营期课程列表
    Page<CampCoursePO> page(Page<CampCoursePO> page, LambdaQueryWrapper<CampCoursePO> wrapper);

    boolean updateOrderNumber(Long id, Integer newOrderNumber);

    boolean sortCourses(List<CourseOrderDTO> courseOrderList);

    void batchInsertByGroupIds(Long campId, List<Long> groupIds);

    //void fixOrderNumberByCampId(Long campId);

    CampCoursePO getByCampCourseId(Long campPeriodId,Long courseId);


    /**
    * @description: 批量插入营期下配置的课程分组下的分组信息
     * 拆分获取配置的课程分组，插入其下对应的具体课程小节配置
     * 每次调用此接口，都默认删除之前关联的数据，重新关联新一批次分组下课程
    * @author: xiaQL
    * @date: 2025/4/20 22:08
    */
    Boolean saveCampGroupAndCourses(CampCourseRequestDTO requestDTO);

    /**
     * @description: 查询营期下配置的课程分组下的课程信息
     * 传入campId：返回营期下配置的所有课程分组下的具体课程小节信息
     * @author: xiaQL
     * @date: 2025/4/20 22:08
     */
    Result<Object> getCampGroupAndCourses(CampCourseRequest request);

    /**
     * @description: 查询配置了传入课程分组下的单个课程小节的营期
     * 传入groupId、课程小节id：返回选择了该课程分组，并且配置了此课程小节的营期
     * 传入updateFlag：true：直接更新营期下配置了此课程小节的营期数据
     * @author: xiaQL
     * @date: 2025/4/20 22:08
     */
    List<CampCoursePO> getCampByCourse(CampCourseRequest request);

    /**
     * @description: 查询营期下当天正在发布的课程小节
     * 固定传入campId、courseId或startTime只有一个参数可能传入
     * 先从redis获取，key为campId，看是否有该营期当天正在播放的课程小节；没有的话就走数据库
     * 如果关联了多个课程分组，只能全部分组下的课程小节聚合在一起，根据courseId或startTime去过滤
     * 此结果查询后存入redis到当天24点，即当天该营期下当天发布的课程小节详情，防止每次查询消耗
     * @author: xiaQL
     * @date: 2025/4/20 22:08
     */
    Result<CampCourseVideoResponse> getCampTodayCourse(CampCourseRequest request);

}
