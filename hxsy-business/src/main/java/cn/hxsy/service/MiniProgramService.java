package cn.hxsy.service;

import cn.hxsy.api.user.model.request.CampPeriodUrlRequest;
import cn.hxsy.api.user.model.request.CompanyUrlRequest;
import cn.hxsy.api.user.model.request.CourseUrlRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.MiniProgram;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 小程序服务接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface MiniProgramService extends IService<MiniProgram> {

    Result<String> getSellUrl(CompanyUrlRequest companyUrlRequest);

    Result<String>  getCampPeriodUrl(CampPeriodUrlRequest campPeriodUrlRequest);

    Result<String>  getCourseUrl(CampPeriodUrlRequest campPeriodUrlRequest);
} 