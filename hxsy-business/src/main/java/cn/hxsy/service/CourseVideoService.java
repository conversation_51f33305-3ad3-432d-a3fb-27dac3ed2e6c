package cn.hxsy.service;

import cn.hxsy.api.user.model.request.CustomerCourseVideoRequest;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import cn.hxsy.model.request.CourseVideoDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface CourseVideoService extends IService<CourseVideoPO> {

    // 保存课程视频
    boolean save(CourseVideoPO courseVideo);

    // 删除课程视频
    boolean removeById(Long id);


    // 更新课程视频
    boolean updateById(CourseVideoPO courseVideo);

    boolean removeShellById(Long id);

    // 查询课程视频详情
    CourseVideoPO getById(Long id);

    // 分页查询课程视频列表
    Page<CourseVideoPO> page(Page<CourseVideoPO> page, LambdaQueryWrapper<CourseVideoPO> wrapper);

    // 新增批量插入方法
    boolean saveBatchCourseVideos(List<CourseVideoPO> entities);

}
