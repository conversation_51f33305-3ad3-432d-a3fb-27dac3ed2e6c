package cn.hxsy.service;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.QywxTag;
import cn.hxsy.datasource.model.entity.TagBatchDTO;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;

import java.util.List;
import java.util.Map;

public interface TagService {
    Result<QywxTag> createTag(QywxTag tag);
    Result<QywxTag> updateTag(QywxTag tag);
    Result<Void> deleteTag(Long tagId); // 移除 confirm 参数
    Result<List<QywxTag>> getAllTags();
    Result<Map<String, Object>> batchProcess(TagBatchDTO batchDTO);
    Result<Map<String, Object>> batchOperation(String action, List<QywxTag> tags);
}
