package cn.hxsy.service;

import cn.hxsy.base.response.Result;
import cn.hxsy.controller.ActivityRecordController;
import cn.hxsy.datasource.model.entity.ActivityPO;
import cn.hxsy.datasource.model.entity.ActivityRecordPO;
import cn.hxsy.datasource.model.entity.VideoCompletedRequest;
import cn.hxsy.datasource.model.entity.VideoCompletedResponse;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;


public interface ActivityRecordService extends IService<ActivityRecordPO> {

    VideoCompletedResponse processActivities(VideoCompletedRequest request);

}
