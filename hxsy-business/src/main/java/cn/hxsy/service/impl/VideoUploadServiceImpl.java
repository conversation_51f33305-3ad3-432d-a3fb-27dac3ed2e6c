package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.constant.ResponseType;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.model.cache.VideoUploadCacheModel;
import cn.hxsy.dao.ImageInfoMapper;
import cn.hxsy.dao.VideoUploadMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.service.VideoGroupService;
import cn.hxsy.service.VideoUploadService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.VodSignatureUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.model.*;
import com.qcloud.cos.model.ciModel.mediaInfo.MediaInfoRequest;
import com.qcloud.cos.model.ciModel.mediaInfo.MediaInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

import static cn.hxsy.cache.constant.video.VideoUploadConstant.*;

@Service
@Slf4j
public class VideoUploadServiceImpl extends ServiceImpl<VideoUploadMapper, VideoUploadPO> implements VideoUploadService {
    @Autowired
    private COSClient cosClient;

    @Autowired
    private VideoUploadMapper videoUploadMapper; // 注入MyBatis-Plus Mapper

    @Autowired
    private VideoGroupService videoGroupService;

    @Autowired
    private ImageInfoMapper imageInfoMapper;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Value("${system.cosAddr}")
    private String cosAddr;
    @Value("${cos.bucketName}")
    private String bucketName;
    @Value("${cos.region}")
    private String region;
    @Value("${cos.secretId}")
    private String secretId;

    @Value("${cos.secretKey}")
    private String secretKey;

    private static final int SIGN_EXPIRE_SECONDS = 3600 * 24 * 2;
    private final Map<String, String> uploadIdMap = new ConcurrentHashMap<>();
    private final Map<String, List<PartETag>> uploadedParts = new ConcurrentHashMap<>();
    private final long minChunkSize = 10 * 1024 * 1024; // 10MB

    /**
     * 1. 批量初始化分片上传
     * @param request
     * @return
     */
    @Override
    public Result<Map<String, String>> initiateBatchUpload(Map<String, List<String>> request) {
        List<String> fileNames = request.get("fileNames");
        List<String> groupIds = request.get("groupIds");
        if (fileNames == null || fileNames.isEmpty()) {
            return Result.error("文件名列表不能为空");
        }
        if (groupIds == null || groupIds.isEmpty()) {
            return Result.error("groupIds不能为空");
        }
        if (fileNames.size() != groupIds.size()) {
            return Result.error("fileNames与groupIds数量不匹配");
        }

        // 获取公共GroupID（假设所有文件共享同一ID）
        String commonGroupId = groupIds.get(0);

        //根据commonGroupId从表video_group获取分组名称（group_name）
        VideoGroupPO videoGroupInfo = videoGroupService.getById(commonGroupId);

        if (videoGroupInfo == null) {
            return Result.error("分组ID不存在: " + commonGroupId);
        }

        String groupName = videoGroupInfo.getGroupName();

        Map<String, String> uploadIds = new HashMap<>();
        for (String fileName : fileNames) {
            String key = groupName.trim() + "/" + fileName;
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(bucketName, key);
            String uploadId = cosClient.initiateMultipartUpload(initRequest).getUploadId();

            uploadIdMap.put(fileName, uploadId);
            uploadedParts.put(fileName, new ArrayList<>());
            uploadIds.put(fileName, uploadId);
        }

        return Result.ok(uploadIds);
    }

    /**
     * 2. 批量上传分片
     * @param fileNames
     * @param partNumbers
     * @param files
     * @param groupIds
     * @return
     * @throws Exception
     */
    @Override
    public Result<String> uploadPartsBatch(List<String> fileNames, List<Integer> partNumbers, List<MultipartFile> files,List<String> groupIds) throws Exception {
        if (fileNames.size() != files.size() || fileNames.size() != partNumbers.size()) {
            return Result.error("参数数量不匹配");
        }
        if (groupIds == null || groupIds.isEmpty()) {
            return Result.error("groupIds不能为空");
        }

        // 获取公共GroupID（假设所有文件共享同一ID）
        String commonGroupId = groupIds.get(0);

        //根据commonGroupId从表video_group获取分组名称（group_name）
        VideoGroupPO videoGroupInfo = videoGroupService.getById(commonGroupId);

        if (videoGroupInfo == null) {
            return Result.error("分组ID不存在: " + commonGroupId);
        }

        String groupName = videoGroupInfo.getGroupName();

        for (int i = 0; i < fileNames.size(); i++) {
            String fileName = fileNames.get(i);
            int partNumber = partNumbers.get(i);
            MultipartFile file = files.get(i);

            String uploadId = uploadIdMap.get(fileName);
            if (uploadId == null) {
                return Result.error("Upload ID 未找到: " + fileName);
            }

            long fileSize = file.getSize();
            List<PartETag> parts = uploadedParts.get(fileName);

            boolean isFinalPart = (partNumber == (parts.size() + 1));
            if (fileSize < minChunkSize && !isFinalPart) {
                return Result.error("错误: 非最后一个分片过小! 最小分片大小为 10MB");
            }

            String key = groupName.trim() + "/" + fileName;
            File tempFile = File.createTempFile("part", null);
            file.transferTo(tempFile);

            UploadPartRequest uploadPartRequest = new UploadPartRequest()
                    .withBucketName(bucketName)
                    .withKey(key)
                    .withUploadId(uploadId)
                    .withPartNumber(partNumber)
                    .withFile(tempFile)
                    .withPartSize(fileSize);

            try {
                UploadPartResult uploadPartResult = cosClient.uploadPart(uploadPartRequest);
                PartETag partETag = uploadPartResult.getPartETag();
                parts.add(partETag);
            } catch (CosServiceException e) {
                return Result.error("上传失败: 服务器错误 - " + e.getMessage());
            }
        }

        return Result.ok("所有分片上传成功");
    }

    /**
     * 3. 批量合并分片
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Map<String, String>>> completeBatchUpload(Map<String, List<String>> request) {
        List<String> fileNames = request.get("fileNames");
        List<String> groupIds = request.get("groupIds");

        if (fileNames == null || fileNames.isEmpty()) {
            return Result.error("文件名列表不能为空");
        }
        if (groupIds == null || groupIds.isEmpty()) {
            return Result.error("groupIds不能为空");
        }
        if (fileNames.size() != groupIds.size()) {
            return Result.error("fileNames与groupIds数量不匹配");
        }
        // 获取公共GroupID（假设所有文件共享同一ID）
        String commonGroupId = groupIds.get(0);
        List<VideoUploadPO> videoList = new ArrayList<>();
        List<Map<String, String>> records = new ArrayList<>();
        //根据commonGroupId从表video_group获取分组名称（group_name）
        VideoGroupPO videoGroupInfo = videoGroupService.getById(commonGroupId);
        if (videoGroupInfo == null) {
            return Result.error("分组ID不存在: " + commonGroupId);
        }
        String groupName = videoGroupInfo.getGroupName();
        try {
            for (String fileName : fileNames) {
                String uploadId = uploadIdMap.get(fileName);
                if (uploadId == null) {
                    return Result.error("Upload ID 未找到: " + fileName);
                }
                String key = groupName.trim() + "/" + fileName;
                //String urlEncodedStr = URLEncoder.encode(groupName, "UTF_8");
                List<PartETag> partETags = uploadedParts.get(fileName);
                partETags.sort(Comparator.comparingInt(PartETag::getPartNumber));
                CompleteMultipartUploadRequest completeRequest =
                        new CompleteMultipartUploadRequest(bucketName, key, uploadId, partETags);
                try {
                    cosClient.completeMultipartUpload(completeRequest);
                } catch (CosServiceException e) {
                    return Result.error("合并失败: " + e.getMessage());
                }
                // 获取视频信息
                String videoUrl = cosAddr + "/" + key.trim();
                //1.创建媒体信息请求对象
                MediaInfoRequest mediaInfoRequest = new MediaInfoRequest();
                //2.添加请求参数 参数详情请见 API 接口文档
                mediaInfoRequest.setBucketName(bucketName);
                mediaInfoRequest.getInput().setObject(key);
                log.info("mediaInfoRequest:" + mediaInfoRequest);
                double videoSize = 0.00;
                LocalTime localTime = LocalTime.parse("00:00:00");
                try {
                    //3.调用接口,获取媒体信息响应对象
                    MediaInfoResponse mediaInfoResponse = cosClient.generateMediainfo(mediaInfoRequest);
                    // 获取视频大小（字节）
                    String sizeBytes = mediaInfoResponse.getMediaInfo().getFormat().getSize();
                    // 转换为更友好单位（如 MB）
                    double sizeMB = Double.parseDouble(sizeBytes) / (1024.0 * 1024.0);
                    DecimalFormat df = new DecimalFormat("#0.00");
                    videoSize = Double.parseDouble(df.format(sizeMB));

                    // 获取视频总时长（秒）
                    String durationSecondsStr = mediaInfoResponse.getMediaInfo().getFormat().getDuration();
                    double durationSeconds = Double.parseDouble(durationSecondsStr); // 转换为 double 类型
                    localTime = formatDuration(durationSeconds);

                    log.info("mediaInfoResponse:" + mediaInfoResponse);
                } catch (CosServiceException e) {
                    return Result.error("文件无效: " + e.getMessage());
                }
                // 构建入库实体
                VideoUploadPO video = new VideoUploadPO();
                video.setVideoGroupid(Long.parseLong(commonGroupId)); // GroupID转为Long
                video.setVideoName(fileName);
                video.setVideoUrl(videoUrl);
                video.setTransStatus("1"); // 默认转码状态
                video.setStreamStatus("1"); // 默认推流状态
                video.setVideoSource("1"); // 来源标识
                video.setVideoDuration(localTime);
                video.setVideoSize(videoSize);
                videoList.add(video);
                // 组装返回数据
                Map<String, String> record = new HashMap<>();
                record.put("videoName", fileName);
                record.put("videoUrl", videoUrl);
                records.add(record);
            }
            // 批量入库
            videoUploadMapper.batchInsertVideoUpload(videoList);
            return Result.ok(records);
        } catch (Exception e) {
            // 异常时自动回滚事务（数据库操作）
            throw new RuntimeException("操作失败: " + e.getMessage(), e);
        } finally {
            // 清理临时数据
            fileNames.forEach(fileName -> {
                uploadIdMap.remove(fileName);
                uploadedParts.remove(fileName);
            });
        }
    }

    // 工具方法：秒转 00:00:00
    public static LocalTime formatDuration(double totalSeconds) {
        int total = (int) Math.round(totalSeconds);
        int hours = total / 3600;
        int remainingSeconds = total % 3600;
        int minutes = remainingSeconds / 60;
        int seconds = remainingSeconds % 60;
        // LocalTime 只能表示 00:00:00 到 23:59:59
        hours %= 24;  // 强制限制小时在 24 以内
        return LocalTime.of(hours, minutes, seconds);
    }

    /**
     * 简单上传小文件（< 5MB）
     * @param file
     * @return
     * @throws IOException
     */
    @Override
    public Result<String> simpleUpload(MultipartFile file,String path) throws IOException {
        if (file.isEmpty()) {
            return Result.error("File is empty.");
        }
        //String contentType = file.getContentType();
        String originalFilename = file.getOriginalFilename();
//        String folder;
//        if (contentType != null) {
//            if (contentType.startsWith("image/")) {
//                folder = "images/";
//            } else if (contentType.startsWith("video/")) {
//                folder = "videos/";
//            } else if (contentType.startsWith("application/pdf") || contentType.startsWith("application/msword")
//                    || contentType.startsWith("application/vnd.openxmlformats-officedocument.wordprocessingml.document")) {
//                folder = "documents/";
//            } else {
//                folder = "others/";
//            }
//        } else {
//            folder = "others/";
//        }
        String key = path + "/" + originalFilename;
        if (file.getSize() < minChunkSize) {
            cosClient.putObject(bucketName, key, file.getInputStream(), new ObjectMetadata());
            String fileUrl = cosAddr + "/" + key.trim();
            return Result.ok(fileUrl);
        } else {
            return Result.error("文件太大，无法直接上传");
        }
    }

    @Override
    public Result<String> uploadCourseImage(MultipartFile file, String path) throws IOException {
        if (file.isEmpty()) {
            return Result.error("File is empty.");
        }
        //String contentType = file.getContentType();
        String originalFilename = file.getOriginalFilename();
        String key = path + "/" + originalFilename;
        if (file.getSize() < minChunkSize) {
            cosClient.putObject(bucketName, key, file.getInputStream(), new ObjectMetadata());
            String fileUrl = cosAddr + "/" + key.trim();
            double fileSizeMB = file.getSize() / (1024.0 * 1024.0); // 精确计算
            fileSizeMB = Math.round(fileSizeMB * 100.0) / 100.0;
            ImageInfoPO imageInfoPO = new ImageInfoPO();
            imageInfoPO.setImageName(originalFilename);
            imageInfoPO.setImagePath(key);
            imageInfoPO.setImageSize(fileSizeMB);
            imageInfoPO.setImageUrl(fileUrl);
            imageInfoMapper.insert(imageInfoPO);
            return Result.ok(fileUrl);
        } else {
            return Result.error("文件太大，无法直接上传");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> batchUploadParts(FileUploadRequest fileUploadRequest, MultipartFile multipartFile) throws Exception {
        List<String> fileNamesArray = fileUploadRequest.getFileNamesArray();
        String groupId = fileUploadRequest.getGroupId();
        String fileName = fileUploadRequest.getFileNames(); // 当前分片所属文件
        int partNumber = Integer.parseInt(fileUploadRequest.getPartNumbers());
        int totalParts = Integer.parseInt(fileUploadRequest.getTotalParts());
        // 获取 上传group 信息
        VideoGroupPO group = videoGroupService.getById(groupId);
        if (group == null) return Result.error("分组ID无效");
        String groupName = group.getGroupName();
        // 1、从redis获取当前上传任务，是否有对应分片任务执行计划，没有则初始化
        String uploadInRedisKey = VIDEO_UPLOAD_PREFIX + fileName;
        VideoUploadCacheModel uploadCacheResult = redisJsonUtils.get(uploadInRedisKey, VideoUploadCacheModel.class);
        // 由于前端一次发送一个文件的分片，可以只针对此文件判断，文件数组只用在最后聚合考虑
        String key = groupName + "/" + fileName;
        if (uploadCacheResult == null) {
            InitiateMultipartUploadRequest initReq = new InitiateMultipartUploadRequest(bucketName, key);
            String newUploadId = cosClient.initiateMultipartUpload(initReq).getUploadId();
            // 利用redis存储一个文件分片上传任务id，分片信息，对应总分片数；
            uploadCacheResult = new VideoUploadCacheModel();
            uploadCacheResult.setUploadId(newUploadId);
            uploadCacheResult.setParts(new ArrayList<>());
            uploadCacheResult.setTotalSize(totalParts);
            redisJsonUtils.set(uploadInRedisKey, uploadCacheResult, VIDEO_UPLOAD_SAVE_TIME, TimeUnit.DAYS);
        }else {
            // 2、断点续传
            log.info("当前上传文件:{}，缓存获取到对应已执行上传数：{}，前端正在上传第{}分片", uploadInRedisKey, uploadCacheResult.getParts().size(), partNumber);
            for (PartETag part : uploadCacheResult.getParts()) {
                // 已经有上传记录缓存时，需要先确定当前请求上传的分片是否已经上传过，如果上传过则直接返回成功
                if (part.getPartNumber() == partNumber && partNumber != totalParts) {
                    return Result.ok("分片上传成功");
                }else if (part.getPartNumber() == partNumber && partNumber == totalParts) {
                    // 如果上传成功，并且是最后一个分片，就需要直接开启合并碎片，而不是直接返回
                    return this.mergeMediainfo(fileNamesArray, groupName, groupId);
                }else {
                    // 如果当前没有上传记录，继续往后判断，直到已经上传完的分片都不匹配，就执行后续的分片上传逻辑
                    continue;
                }
            }
        }
        // 上面已经处理了上传任务，直接使用
        String uploadId = uploadCacheResult.getUploadId();
        List<PartETag> partList = uploadCacheResult.getParts();
        // 上传分片
        UploadPartRequest uploadPartRequest = new UploadPartRequest()
                .withBucketName(bucketName)
                .withKey(key)
                .withUploadId(uploadId)
                .withPartNumber(partNumber)
                .withInputStream(multipartFile.getInputStream())
                .withPartSize(multipartFile.getSize());
        UploadPartResult uploadResult = cosClient.uploadPart(uploadPartRequest);
        partList.add(uploadResult.getPartETag());
        log.info("当前上传任务id：{}，正在上传第{}分片", uploadId, partList.size());
        // 当前分片上传完，判断是否为此文件的最后一个分片，是则置为完成状态后更新到redis，用于后面判断是否全部上传完成
        if(partList.size() == totalParts) {
            uploadCacheResult.setUploadState(ResponseType.Success.getCode());
        }
        redisJsonUtils.set(uploadInRedisKey, uploadCacheResult, VIDEO_UPLOAD_SAVE_TIME, TimeUnit.DAYS);
        // === 多个文件上传时，只要有一个上传成功的，都应该先直接合并，再考虑其他 ===
        boolean anyFilesUploaded = fileNamesArray.stream().anyMatch(name -> {
            String redisKey = VIDEO_UPLOAD_PREFIX + name;
            VideoUploadCacheModel uploadModel = redisJsonUtils.get(redisKey, VideoUploadCacheModel.class);
            if(uploadModel == null){
                return false;
            }
            // 获取到对应分片信息时，并且已经上全部上传成功的标志
            return Objects.equals(ResponseType.Success.getCode(), uploadModel.getUploadState());
        });
        if (anyFilesUploaded) {
            return this.mergeMediainfo(fileNamesArray, groupName, groupId);
        }
        return Result.ok("分片上传成功");
    }

    @Override
    public Page<VideoUploadPO> page(Page<VideoUploadPO> page, LambdaQueryWrapper<VideoUploadPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteById(Long id) {
        // 记录操作人员信息
        SystemUserResponse systemUserResponse = userCacheUtil.getSystemUserInfo(StpUtil.getTokenValue());
        int rows = videoUploadMapper.updateStatusById(id, systemUserResponse.getId());
        log.info("影响行数：" + rows);
        return rows > 0 ? Result.ok() : Result.error("删除失败");
    }

    /**
    * @description: 合并分片
    * @author: xiaQL
    * @date: 2025/5/9 15:56
    */
    private Result<String> mergeMediainfo(List<String> fileNamesArray, String groupName, String groupId){
        StringBuilder returnResult = new StringBuilder("文件：");
        for (String file : fileNamesArray) {
            String redisKey = VIDEO_UPLOAD_PREFIX + file;
            VideoUploadCacheModel uploadModel = redisJsonUtils.get(redisKey, VideoUploadCacheModel.class);
            // 分片缓存为空，即代表当前文件已经上传并合并成功，跳过
            if(uploadModel == null){
                continue;
            }else {
                // 不为空，则判断是否是全部分片上传成功的文件，不是则跳过
                if(!Objects.equals(ResponseType.Success.getCode(), uploadModel.getUploadState())){
                    continue;
                }
                // 有可能在之前的请求中已经向cos桶发起了合并请求，但是还未合并完成导致此文件分片信息未删除，结合分布式锁判断一下是否有正在进行的合并操作
                Boolean ifAbsent = redisTemplate.opsForValue().setIfAbsent(VIDEO_UPLOAD_MERGE_PREFIX + file, "1", 10, TimeUnit.MINUTES);
                log.info("文件：{}，是否正在合并中：{}", file, ifAbsent);
                if(Boolean.FALSE.equals(ifAbsent)){
                    continue;
                }
            }
            // 开始获取全部分片并上传
            log.info("文件：{}，上传完全部分片，开始合并", file);
            String mergeKey = groupName + "/" + file;
            String mergeUploadId = uploadModel.getUploadId();
            List<PartETag> mergeParts = uploadModel.getParts();
            mergeParts.sort(Comparator.comparingInt(PartETag::getPartNumber));
            CompleteMultipartUploadRequest completeReq =
                    new CompleteMultipartUploadRequest(bucketName, mergeKey, mergeUploadId, mergeParts);
            cosClient.completeMultipartUpload(completeReq);
            returnResult.append(file).append("、");
            // 获取视频信息
            String videoUrl = cosAddr + "/" + mergeKey;
            MediaInfoRequest infoReq = new MediaInfoRequest();
            infoReq.setBucketName(bucketName);
            infoReq.getInput().setObject(mergeKey);
            MediaInfoResponse infoResp = cosClient.generateMediainfo(infoReq);
            double sizeMB = Double.parseDouble(infoResp.getMediaInfo().getFormat().getSize()) / (1024.0 * 1024.0);
            double durationSec = Double.parseDouble(infoResp.getMediaInfo().getFormat().getDuration());
            LocalTime duration = formatDuration(durationSec);
            // 入库
            VideoUploadPO video = new VideoUploadPO();
            video.setVideoGroupid(Long.parseLong(groupId));
            video.setVideoName(file);
            video.setVideoUrl(videoUrl);
            video.setTransStatus("1");
            video.setStreamStatus("1");
            video.setVideoSource("1");
            video.setVideoDuration(duration);
            video.setVideoSize(sizeMB);
            video.setCreatedAt(LocalDateTime.now());
            video.setStatus(1);
            videoUploadMapper.insert(video);
            // 合并完成，清除分片缓存与分布式锁
            redisJsonUtils.delete(redisKey);
            redisJsonUtils.delete(VIDEO_UPLOAD_MERGE_PREFIX + file);
        }
        return Result.ok(String.valueOf(returnResult.append("上传并合并成功")));
    }

    @Override
    public Result<String> getVodSignature() {
    if(StringUtils.isBlank(secretId) || StringUtils.isBlank(secretKey)) {
        log.warn("VOD密钥未正确配置");
        return Result.error("服务配置错误");
    }
        try {
            VodSignatureUtil sign = new VodSignatureUtil();
            // 设置 App 的云 API 密钥
            sign.setSecretId(secretId);
            sign.setSecretKey(secretKey);
            sign.setCurrentTime(System.currentTimeMillis() / 1000);
            sign.setRandom(ThreadLocalRandom.current().nextInt(Integer.MAX_VALUE));
            sign.setSignValidDuration(SIGN_EXPIRE_SECONDS); // 签名有效期：2天

            String signature = sign.getUploadSignature();
            return Result.ok(signature);
        } catch (Exception e) {
            log.error("获取VOD签名失败: {}", e.getMessage(), e);
            return Result.error("获取视频上传签名失败");
        }
    }
    }
