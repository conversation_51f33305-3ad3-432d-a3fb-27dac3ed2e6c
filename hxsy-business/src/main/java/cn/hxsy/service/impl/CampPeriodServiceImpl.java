package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.CampCourseMapper;
import cn.hxsy.dao.CampPeriodMapper;
import cn.hxsy.dao.CourseVideoMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.model.request.CourseVideoDTO;
import cn.hxsy.service.CampCourseService;
import cn.hxsy.service.CampPeriodService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hxsy.base.constant.user.UserSelectType.organization_app;
import static cn.hxsy.base.constant.user.UserSelectType.saleGroup;

@Service
@Slf4j
public class CampPeriodServiceImpl extends ServiceImpl<CampPeriodMapper, CampPeriodPO> implements CampPeriodService {

    @Autowired
    private CampPeriodMapper campPeriodMapper;

    @Autowired
    private CampCourseMapper campCourseMapper;

    @Autowired
    private CourseVideoMapper courseVideoMapper;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Autowired
    private UserSelectUtil userSelectUtil;

    @Override
    public boolean save(CampPeriodPO campPeriod) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        campPeriod.setCreatedAt(LocalDateTime.now());
        //campPeriod.setCreatedBy(systemUserPO.getAccountId());

        return super.save(campPeriod); // 使用 ServiceImpl 提供的 save 方法
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id); // 使用 ServiceImpl 提供的 removeById 方法
    }


    @Override
    public boolean updateById(CampPeriodPO campPeriod) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        campPeriod.setUpdatedAt(LocalDateTime.now());
        //campPeriod.setUpdatedBy(systemUserPO.getAccountId());

        return super.updateById(campPeriod); // 使用 ServiceImpl 提供的 updateById 方法
    }

    @Override
    public CampPeriodPO getById(Long id) {
        return super.getById(id); // 使用 ServiceImpl 提供的 getById 方法
    }

    @Override
    public Page<CampPeriodPO> page(Page<CampPeriodPO> page, LambdaQueryWrapper<CampPeriodPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }

    @Override
    public List<CampPeriodResponseDTO> getCampPeriodsWithCourses(Long companyId, Long salesId) {
        // 1、首先获取用户缓存信息
        SystemUserResponse systemUserInfo = userCacheUtil.getSystemUserSelfInfo();
        // 2、获取用户角色对应查询权限
        OrganizationQueryRequest request = new OrganizationQueryRequest();
        request.setCompanyId(String.valueOf(companyId));
        request.setSalesGroup(salesId == null ? null : String.valueOf(salesId));
        SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserInfo, saleGroup, request);
        // 3. 查询营期列表
        List<CampPeriodPO> campPeriodPOS = campPeriodMapper.selectByCompanyAndSales(companyId, selectPermission.getSalesGroupId());
        if (CollectionUtils.isEmpty(campPeriodPOS)) {
            log.warn("未找到companyId={}, salesId={}的营期", companyId, salesId);
            return Collections.emptyList();
        }
        // 2. 获取所有营期ID
        List<Long> campIds = campPeriodPOS.stream()
                .map(CampPeriodPO::getId)
                .collect(Collectors.toList());
        // 3. 获取营期详情
        LambdaQueryWrapper<CampPeriodPO> campPeriodPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        campPeriodPOLambdaQueryWrapper.in(CampPeriodPO::getId, campIds);
        campPeriodPOS = this.getBaseMapper().selectList(campPeriodPOLambdaQueryWrapper);
        if(CollectionUtils.isEmpty(campPeriodPOS)){
            return Collections.emptyList();
        }
        // 4. 获取营期下所有课程分组-课程小节关联信息
        LambdaQueryWrapper<CampCoursePO> campCoursePOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        campCoursePOLambdaQueryWrapper.in(CampCoursePO::getCampId, campIds);
        List<CampCoursePO> campCoursePOS = campCourseMapper.selectList(campCoursePOLambdaQueryWrapper);
        // 5. 数据组装
        ArrayList<CampPeriodResponseDTO> allCourses = new ArrayList<>();
        if(!CollectionUtils.isEmpty(campCoursePOS)){
            // 营期下已有关联课程
            campPeriodPOS.forEach(campPeriodPO -> {
                //收集单条营期信息
                CampPeriodResponseDTO campPeriodResponseDTO = new CampPeriodResponseDTO();
                Long campId = campPeriodPO.getId();
                campPeriodResponseDTO.setId(campId);
                campPeriodResponseDTO.setName(campPeriodPO.getCampperiodName());
                campPeriodResponseDTO.setExplicitName(campPeriodPO.getField1());
                campPeriodResponseDTO.setStartingFlag(campPeriodPO.getStartingFlag());
                campPeriodResponseDTO.setCampperiodRedPack(campPeriodPO.getCampperiodRedPack());
                campPeriodResponseDTO.setCampperiodRedPackAmount(campPeriodPO.getCampperiodRedPackAmount());
                campPeriodResponseDTO.setRedPackRange(campPeriodPO.getRedPackRange());
                //收集该营期下所有课程小节
                ArrayList<CourseDTO> coursesResult = new ArrayList<>();
                campCoursePOS.stream().filter(campCoursePO -> campId.equals(campCoursePO.getCampId()))
                                .forEach(campCoursePO -> {
                                    String courseInfo = campCoursePO.getCourseInfo();
                                    if(StringUtils.isNotEmpty(courseInfo)){
                                        List<CourseVideoDTO> courseVideoDTOS = JSON.parseArray(courseInfo, CourseVideoDTO.class);
                                        List<CourseDTO> courseDTOS = courseVideoDTOS.stream().map(courseVideoDTO -> {
                                            CourseDTO courseDTO = new CourseDTO();
                                            courseDTO.setId(courseVideoDTO.getId());
                                            courseDTO.setTitle(courseVideoDTO.getCourseName());
                                            courseDTO.setDesc(courseVideoDTO.getCourseDescription());
                                            courseDTO.setCover(courseVideoDTO.getCoverPath());
                                            courseDTO.setVideoPath(courseVideoDTO.getVideoPath());
                                            courseDTO.setPrice(courseVideoDTO.getCoursePrice());
                                            courseDTO.setStartTime(courseVideoDTO.getStartTime());
                                            courseDTO.setActivityInfo(courseVideoDTO.getActivityInfo());
                                            return courseDTO;
                                        }).collect(Collectors.toList());
                                        coursesResult.addAll(courseDTOS);
                                    }
                                });
                campPeriodResponseDTO.setCourses(coursesResult);
                allCourses.add(campPeriodResponseDTO);
            });
        }else {
            // 营期下还未关联课程
            campPeriodPOS.forEach(campPeriodPO -> {
                CampPeriodResponseDTO campPeriodResponseDTO = new CampPeriodResponseDTO();
                Long campId = campPeriodPO.getId();
                campPeriodResponseDTO.setId(campId);
                campPeriodResponseDTO.setName(campPeriodPO.getCampperiodName());
                campPeriodResponseDTO.setExplicitName(campPeriodPO.getField1());
                campPeriodResponseDTO.setCampperiodRedPack(campPeriodPO.getCampperiodRedPack());
                campPeriodResponseDTO.setCampperiodRedPackAmount(campPeriodPO.getCampperiodRedPackAmount());
                campPeriodResponseDTO.setRedPackRange(campPeriodPO.getRedPackRange());
                campPeriodResponseDTO.setStartingFlag(campPeriodPO.getStartingFlag());
                allCourses.add(campPeriodResponseDTO);
            });
        }
        return allCourses;
    }

    @Override
    public List<CampPeriodPO> getCampPeriodsByCompanyId(Long companyId) {
        LambdaQueryWrapper<CampPeriodPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CampPeriodPO::getCompanyId, companyId);
        return list(wrapper);
    }

    @Override
    public boolean setCampPeriodRedPacketAmount(String id, String redPacketAmount) {
        CampPeriodPO campPeriodPO = new CampPeriodPO();
        campPeriodPO.setId(Long.valueOf(id));
        campPeriodPO.setCampperiodRedPackAmount(redPacketAmount);
        return updateById(campPeriodPO);
    }
    @Override
    public List<CampPeriodPO> getCampPeriodsByIds(List<Long> campPeriodIds) {
        LambdaQueryWrapper<CampPeriodPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CampPeriodPO::getId, campPeriodIds);
        return list(wrapper);
    }

}