package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hxsy.api.user.model.response.CampCourseVideoResponse;
import cn.hxsy.api.user.model.response.CourseVideoResponse;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.CampCourseMapper;
import cn.hxsy.dao.CourseGroupMapper;
import cn.hxsy.dao.CourseVideoMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.model.request.*;
import cn.hxsy.service.CampCourseService;
import cn.hxsy.service.CampPeriodService;
import cn.hxsy.thread.CampCourseThread;
import cn.hxsy.utils.BeanCopyUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hxsy.cache.constant.course.CampCourseCacheConstant.CAMP_TODAY_COURSE;
import static cn.hxsy.cache.constant.user.CacheConstant.SYS_USER_LOGIN_TOKEN;

@Service
@Slf4j
public class CampCourseServiceImpl extends ServiceImpl<CampCourseMapper, CampCoursePO> implements CampCourseService {

    @Autowired
    private CampCourseMapper campCourseMapper;
    @Autowired
    private CourseGroupMapper courseGroupMapper;
    @Autowired
    private CourseVideoMapper courseVideoMapper;

    @Autowired
    private CampPeriodService campPeriodService;

    @Autowired
    private CampCourseThread campCourseThread;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Override
    public boolean save(CampCoursePO campCourse) {
        return super.save(campCourse); // 使用 ServiceImpl 提供的 save 方法
    }

    @Override
    @Transactional
    public boolean saveBatch(List<CampCoursePO> campCourses) {
        return super.saveBatch(campCourses);
    }

    @Override
    public boolean updateById(CampCoursePO campCourse) {
        return super.updateById(campCourse); // 使用 ServiceImpl 提供的 updateById 方法
    }

    @Override
    public CampCoursePO getById(Long id) {
        return super.getById(id); // 使用 ServiceImpl 提供的 getById 方法
    }

    @Override
    public Page<CampCoursePO> page(Page<CampCoursePO> page, LambdaQueryWrapper<CampCoursePO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }

    @Override
    @Transactional
    public boolean updateOrderNumber(Long id, Integer newOrderNumber) {
        CampCoursePO current = this.getById(id);
        if (current == null) return false;

        Long campId = current.getCampId();

        // 获取该 camp 下所有课程，按顺序排序
        List<CampCoursePO> courses = this.lambdaQuery()
                .eq(CampCoursePO::getCampId, campId)
//                .orderByAsc(CampCoursePO::getOrderNumber)
                .list();

        // 移除当前课程
        courses.removeIf(course -> course.getId().equals(id));

        // 计算插入位置
        int insertIndex = Math.max(0, Math.min(newOrderNumber - 1, courses.size()));
        courses.add(insertIndex, current);

        // 重新设置 order_number
        for (int i = 0; i < courses.size(); i++) {
            CampCoursePO course = courses.get(i);
//            course.setOrderNumber(i + 1);
            this.updateById(course);
        }

        return true;
    }

    @Override
    @Transactional
    public boolean removeById(Long id) {
        CampCoursePO current = this.getById(id);
        if (current == null) return false;

        boolean deleted = super.removeById(id);
        if (!deleted) return false;

        reorderCourses(current.getCampId());
        return true;
    }

    private void reorderCourses(Long campId) {
        List<CampCoursePO> courses = this.lambdaQuery()
                .eq(CampCoursePO::getCampId, campId)
//                .orderByAsc(CampCoursePO::getOrderNumber)
                .list();

        for (int i = 0; i < courses.size(); i++) {
            CampCoursePO course = courses.get(i);
//            course.setOrderNumber(i + 1);
            this.updateById(course);
        }
    }

    @Override
    @Transactional
    public boolean sortCourses(List<CourseOrderDTO> courseOrderList) {
        if (CollectionUtils.isEmpty(courseOrderList)) return false;

        // 校验所有课程是否存在，并属于同一个 campId
        List<Long> ids = courseOrderList.stream().map(CourseOrderDTO::getId).collect(Collectors.toList());
        List<CampCoursePO> existingCourses = this.listByIds(ids);

        if (existingCourses.size() != courseOrderList.size()) return false;

        Long campId = existingCourses.get(0).getCampId();
        boolean allSameCamp = existingCourses.stream().allMatch(course -> course.getCampId().equals(campId));
        if (!allSameCamp) return false;

        // 更新每个课程的 order_number
        Map<Long, Integer> idToOrderMap = courseOrderList.stream()
                .collect(Collectors.toMap(CourseOrderDTO::getId, CourseOrderDTO::getOrderNumber));

        for (CampCoursePO course : existingCourses) {
            Integer newOrder = idToOrderMap.get(course.getId());
//            course.setOrderNumber(newOrder);
            this.updateById(course);
        }

        // 最后统一再排一次顺序，确保从 1 开始连续
        reorderCourses(campId);
        return true;
    }

    @Override
    @Transactional
    public void batchInsertByGroupIds(Long campId, List<Long> groupIds) {
        // 校验 groupIds 是否存在
        List<Long> existingGroupIds = courseGroupMapper.selectExistingGroupIds(groupIds);
        if (existingGroupIds.size() != groupIds.size()) {
            throw new RuntimeException("存在无效的 groupId");
        }

        // 查找课程视频
        List<CourseVideoPO> videos = courseVideoMapper.selectByGroupIds(groupIds);
        if (CollectionUtils.isEmpty(videos)) {
            return;
        }

        List<CampCoursePO> insertList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 获取当前营期下最大的 order_number
        Integer maxOrder = campCourseMapper.selectMaxOrderNumber(campId);
        if (maxOrder == null) {
            maxOrder = 0;
        }

        for (int i = 0; i < videos.size(); i++) {
            CourseVideoPO video = videos.get(i);
            CampCoursePO course = new CampCoursePO();
            course.setId(IdUtil.getSnowflakeNextId());
            course.setCampId(campId);
//            course.setCourseId(video.getId());
            System.out.println("插入顺序号: " + (maxOrder + i + 1));
//            course.setOrderNumber(maxOrder + i + 1); // 设置排序号
            course.setCreatedAt(now);
            course.setUpdatedAt(now);
            course.setCreatedBy("system");
            course.setUpdatedBy("system");
            insertList.add(course);
        }

//        for (CourseVideoPO video : videos) {
//            CampCoursePO course = new CampCoursePO();
//            course.setId(IdUtil.getSnowflakeNextId());
//            course.setCampId(campId);
//            course.setCourseId(video.getId());
//            course.setCreatedAt(now);
//            course.setUpdatedAt(now);
//            course.setCreatedBy("system");
//            course.setUpdatedBy("system");
//            insertList.add(course);
//        }

        if (!insertList.isEmpty()) {
            campCourseMapper.batchInsert(insertList);
        }
    }

    @Override
    public CampCoursePO getByCampCourseId(Long campPeriodId, Long courseId) {
        // 创建查询条件
        LambdaQueryWrapper<CampCoursePO> queryWrapper = new LambdaQueryWrapper<>();

        // 设置查询条件：campPeriodId 和 courseId 都必须匹配
        queryWrapper.eq(CampCoursePO::getCampId, campPeriodId);
//                .eq(CampCoursePO::getCourseId, courseId);

        // 执行查询，返回单个对象或 null
        return baseMapper.selectOne(queryWrapper);
    }

//    @Override
//    @Transactional
//    public void fixOrderNumberByCampId(Long campId) {
//        List<CampCoursePO> list = this.lambdaQuery()
//                .eq(CampCoursePO::getCampId, campId)
//                .orderByAsc(CampCoursePO::getCreatedAt)
//                .list();
//
//        for (int i = 0; i < list.size(); i++) {
//            CampCoursePO po = list.get(i);
//            po.setOrderNumber(i + 1); // 从1开始排序
//        }
//
//        this.updateBatchById(list); // 批量更新
//    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCampGroupAndCourses(CampCourseRequestDTO requestDTO) {
        // 获取对应业务人员缓存信息
        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
        if (systemUserPO == null) {
            throw new RuntimeException("获取业务人员信息失败");
        }
        // 判断营期是否存在
        Long campId = requestDTO.getCampId();
        CampPeriodPO campPeriodPO = campPeriodService.getById(campId);
        if (campPeriodPO == null) {
            throw new RuntimeException("对应营期不存在");
        }
        // 先把之前的配置都删除，等待后续统一新增
        LambdaQueryWrapper<CampCoursePO> campPeriodPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        campPeriodPOLambdaQueryWrapper.eq(CampCoursePO::getCampId, campId);
        this.getBaseMapper().delete(campPeriodPOLambdaQueryWrapper);
        // 获取配置的课程分组信息
        List<GroupCoursesRequestDTO> groupCoursesRequestDTOS = requestDTO.getGroupCoursesRequestDTOS();
        if (CollectionUtils.isEmpty(groupCoursesRequestDTOS)) {
            throw new RuntimeException("课程分组下课程小节配置不能为空");
        }
        ArrayList<CampCoursePO> campCoursePOS = new ArrayList<>();
        for (GroupCoursesRequestDTO groupCoursesRequestDTO : groupCoursesRequestDTOS) {
            Long groupId = groupCoursesRequestDTO.getGroupId();
            // 设置营期下课程分组基本信息
            CampCoursePO campCoursePO = new CampCoursePO();
            campCoursePO.setCampId(campId);
            campCoursePO.setGroupId(groupId);
            // 课程分组下的课程小节配置转为json存储
            List<CourseVideoJson> courseVideoDTOS = groupCoursesRequestDTO.getCourseVideoDTOS();
            String courseVideoJson = JSON.toJSONString(courseVideoDTOS);
            campCoursePO.setCourseInfo(courseVideoJson);
            campCoursePO.setCreatedAt(LocalDateTime.now());
            campCoursePO.setCreatedBy(systemUserPO.getAccountId());
            campCoursePOS.add(campCoursePO);
        }
        // 删除当天的营期课程缓存
        String redisKey = CAMP_TODAY_COURSE + campId;
        redisJsonUtils.delete(redisKey);
        // 批量插入营期-课程分组-分组下小节课程
        return this.saveBatch(campCoursePOS);
    }

    @Override
    public Result<Object> getCampGroupAndCourses(CampCourseRequest request) {
        Long campId = Long.valueOf(request.getCampId());
        LambdaQueryWrapper<CampCoursePO> campPeriodPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        campPeriodPOLambdaQueryWrapper.eq(CampCoursePO::getCampId, campId);
        return Result.ok(this.getBaseMapper().selectList(campPeriodPOLambdaQueryWrapper));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CampCoursePO> getCampByCourse(CampCourseRequest request) {
        Long groupId = request.getGroupId();
        LambdaQueryWrapper<CampCoursePO> campPeriodPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        campPeriodPOLambdaQueryWrapper.eq(CampCoursePO::getGroupId, groupId);
        List<CampCoursePO> campCoursePOS = this.getBaseMapper().selectList(campPeriodPOLambdaQueryWrapper);
        // 将配置的课程小节列表都转为json串，匹配是否有当前配置的课程小节id
        if(CollectionUtil.isEmpty(campCoursePOS)){
            return null;
        }
        // 流式过滤
        List<CampCoursePO> collect = campCoursePOS.stream().filter(campCoursePO -> {
            List<CourseVideoResponse> courseVideoResponses = JSON.parseArray(campCoursePO.getCourseInfo(), CourseVideoResponse.class);
            if (CollectionUtil.isNotEmpty(courseVideoResponses)) {
                CourseVideoResponse videoResponse = courseVideoResponses.stream().filter(courseVideoResponse ->
                        request.getCourseId().equals(courseVideoResponse.getId())).findFirst().orElse(null);
//                log.info("匹配到课程小节: {}", videoResponse);
                if (ObjectUtils.isNotEmpty(videoResponse)) {
                    // 判断调用处是否需要更新营期下数据，是则需要更新当前的课程小节数据
                    if(request.getUpdateFlag()){
                        // 这里用copy会把相同字段全部覆盖，但是他妈空字段也覆盖上了，所以就导致有的数据被置为空了我草
//                        BeanUtils.copyProperties(request.getCourseVideo(), videoResponse);
                        BeanCopyUtils.copyNonNullProperties(request.getCourseVideo(), videoResponse);
                        campCoursePO.setCourseInfo(JSON.toJSONString(courseVideoResponses));
                    }
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
        if(request.getUpdateFlag()){
            // 防止批量更新到其他不必要字段，可以只留下id和courseInfo两个字段
//            log.info("待更新营期数据: {}", collect);
            List<CampCoursePO> updateList = collect.stream().map(campCoursePO -> {
                CampCoursePO campCourse = new CampCoursePO();
                campCourse.setId(campCoursePO.getId());
                campCourse.setCourseInfo(campCoursePO.getCourseInfo());
                campCourse.setUpdatedAt(LocalDateTime.now());
                campCourse.setUpdatedBy(request.getUpdatedBy());
                return campCourse;
            }).collect(Collectors.toList());
//            log.info("构造后营期更新数据: {}", updateList);
            // 异步进行批量更新，直接返回
            if(CollectionUtil.isNotEmpty(updateList)){
                campCourseThread.updateCampCourse(updateList);
            }
        }
        return collect;
    }

    @Override
    public Result<CampCourseVideoResponse> getCampTodayCourse(CampCourseRequest request) {
        CampCourseVideoResponse campCourseVideoResponse = new CampCourseVideoResponse();

        Long campId = Long.valueOf(request.getCampId());
        // 查询营期数据
        CampPeriodPO campPeriodPO = campPeriodService.getById(campId);
        campCourseVideoResponse.setStartingFlag(campPeriodPO.getStartingFlag());
        campCourseVideoResponse.setCampPeriodId(campPeriodPO.getId());
        campCourseVideoResponse.setCampPeriodName(campPeriodPO.getCampperiodName());
        campCourseVideoResponse.setExplicitName(campPeriodPO.getField1());
        campCourseVideoResponse.setNeedPhone(campPeriodPO.getNeedPhone());
        campCourseVideoResponse.setAutoRegister(campPeriodPO.getAutoRegister());
        campCourseVideoResponse.setCampperiodStatus(campPeriodPO.getCampperiodStatus());
        // 1、redis获取是否有当天该营期的播放课程小节
        String redisKey = CAMP_TODAY_COURSE + campId;
        List<CourseVideoResponse> courseVideoCache = redisJsonUtils.getList(redisKey, CourseVideoResponse.class);
        if (CollectionUtil.isNotEmpty(courseVideoCache)) {
            // 3、缓存有当天该营期的播放课程小节
            campCourseVideoResponse.setCourseVideoList(courseVideoCache);
            return Result.ok(campCourseVideoResponse);
        }
        // 2、缓存没有，从数据库获取该营期下的所有分组下的所有课程
        LambdaQueryWrapper<CampCoursePO> campPeriodPOLambdaQueryWrapper = new LambdaQueryWrapper<>();
        campPeriodPOLambdaQueryWrapper.eq(CampCoursePO::getCampId, campId);
        List<CampCoursePO> campCoursePOS = this.getBaseMapper().selectList(campPeriodPOLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(campCoursePOS)) {
            return Result.error("未查询到营期下当天配置课程信息");
        }
        List<CourseVideoResponse> courseVideosFromDb = new ArrayList<>();
        campCoursePOS.forEach(campCoursePO -> {
            String courseInfo = campCoursePO.getCourseInfo();
            // 为空表示当前关联的分组未配置课程信息，跳过此条分组
            if(StringUtils.isEmpty(courseInfo)) {
                return;
            }
            List<CourseVideoResponse> courseVideoDTOS = JSON.parseArray(courseInfo, CourseVideoResponse.class);
            courseVideosFromDb.addAll(courseVideoDTOS);
        });
        List<CourseVideoResponse> matchedCourses = new ArrayList<>();
        if (StringUtils.equals(campPeriodPO.getStartingFlag(), "1")) {
            Long courseId = request.getCourseId();
            LocalDate startTime = request.getStartTime();
            matchedCourses = courseVideosFromDb.stream()
                    .filter(courseVideoDTO -> {
                        if (courseId != null) {
                            return courseVideoDTO.getId().equals(String.valueOf(courseId));
                        } else if (startTime != null) {
                            return startTime.equals(courseVideoDTO.getStartTime());
                        }
                        return false;
                    }).collect(Collectors.toList());
            if (matchedCourses.isEmpty()) {
                throw new RuntimeException("未查询到营期下当天配置课程信息");
            }
        } else {
            matchedCourses = courseVideosFromDb;
        }
        // 营期红包
        if (Boolean.TRUE.equals(campPeriodPO.getCampperiodRedPack())) {
            matchedCourses.forEach(course -> {
                course.setCampperiodRedPack(campPeriodPO.getCampperiodRedPack());
                course.setCampperiodRedPackAmount(campPeriodPO.getCampperiodRedPackAmount());
            });
        }
        campCourseVideoResponse.setCourseVideoList(matchedCourses);
        // 4、缓存当天营期课程信息
        Duration duration = Duration.between(LocalDateTime.now(), LocalDate.now().plusDays(1).atTime(0, 0, 0));
        redisJsonUtils.set(redisKey, matchedCourses, duration.toMinutes(), TimeUnit.MINUTES);
        log.info("缓存营期当天课程信息成功，营期id为：{}, 名称为：{},课程数据为：{}", campPeriodPO.getId(), campPeriodPO.getCampperiodName(), matchedCourses);
        return Result.ok(campCourseVideoResponse);

    }


}