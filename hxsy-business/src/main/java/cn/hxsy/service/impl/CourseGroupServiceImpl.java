package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.CourseGroupMapper;
import cn.hxsy.datasource.model.entity.CourseGroupPO;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.service.CourseGroupService;
import cn.hxsy.service.VideoGroupService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static cn.hxsy.cache.constant.user.CacheConstant.SYS_USER_LOGIN_TOKEN;

@Service
@Slf4j
public class CourseGroupServiceImpl extends ServiceImpl<CourseGroupMapper, CourseGroupPO> implements CourseGroupService {

    @Autowired
    private CourseGroupMapper courseGroupMapper;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Override
    public boolean save(CourseGroupPO courseGroup) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        courseGroup.setCreatedAt(LocalDateTime.now());
        //courseGroup.setCreatedBy(systemUserPO.getAccountId());

        return super.save(courseGroup); // 使用 ServiceImpl 提供的 save 方法
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id); // 使用 ServiceImpl 提供的 removeById 方法
    }


    @Override
    public boolean updateById(CourseGroupPO courseGroup) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        courseGroup.setUpdatedAt(LocalDateTime.now());
        //courseGroup.setUpdatedBy(systemUserPO.getAccountId());

        return super.updateById(courseGroup); // 使用 ServiceImpl 提供的 updateById 方法
    }

    @Override
    public CourseGroupPO getById(Long id) {
        return super.getById(id); // 使用 ServiceImpl 提供的 getById 方法
    }

    @Override
    public Page<CourseGroupPO> page(Page<CourseGroupPO> page, LambdaQueryWrapper<CourseGroupPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }
}