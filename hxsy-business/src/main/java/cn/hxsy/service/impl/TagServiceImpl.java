package cn.hxsy.service.impl;

import cn.hxsy.base.response.Result;
import cn.hxsy.datasource.model.entity.QywxTag;
import cn.hxsy.datasource.model.entity.TagBatchDTO;
import cn.hxsy.service.QywxTagClient;
import cn.hxsy.service.TagService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@RequiredArgsConstructor
public class TagServiceImpl implements TagService {

    private final QywxTagClient qywxClient; // 确保你的项目中实际存在该Bean
    private final ExecutorService batchExecutor = Executors.newWorkStealingPool(5);

    //------------------------ 单条操作 ------------------------
    @Override
    public Result<QywxTag> createTag(QywxTag tag) {
        try {
            if (qywxClient.tagExists(tag.getTagname())) {
                return Result.error("标签已存在");
            }
            return Result.ok(qywxClient.createTag(tag));
        } catch (Exception e) {
            log.error("创建标签失败 | tagName:{}", tag.getTagname(), e);
            return Result.error("标签创建失败");
        }
    }

    @Override
    public Result<QywxTag> updateTag(QywxTag tag) {
        try {
            if (!qywxClient.tagExists(tag.getTagid())) {
                return Result.error( "标签不存在");
            }
            return Result.ok(qywxClient.updateTag(tag));
        } catch (Exception e) {
            log.error("更新标签失败 | tagId:{}", tag.getTagid(), e);
            return Result.error("标签更新失败");
        }
    }

    @Override
    public Result<Void> deleteTag(Long tagId) { // 移除 confirm 参数
        try {
            qywxClient.safeDeleteTag(tagId);
            return Result.ok(null);
        } catch (Exception e) {
            log.error("删除标签失败 | tagId:{}", tagId, e);
            return Result.error("标签删除失败");
        }
    }

    //------------------------ 批量操作 ------------------------
    @Override
    public Result<List<QywxTag>> getAllTags() {
        try {
            return Result.ok(qywxClient.listTags());
        } catch (Exception e) {
            log.error("获取标签列表失败", e);
            return Result.error("获取标签失败");
        }
    }

    @Override
    public Result<Map<String, Object>> batchProcess(TagBatchDTO batchDTO) {
        try {
            Map<String, Object> result = new ConcurrentHashMap<>();

            CompletableFuture<Integer> createFuture = CompletableFuture.supplyAsync(
                    () -> processBatch(qywxClient::createTag, batchDTO.getCreateTags()),
                    batchExecutor
            );

            CompletableFuture<Integer> updateFuture = CompletableFuture.supplyAsync(
                    () -> processBatch(qywxClient::updateTag, batchDTO.getUpdateTags()),
                    batchExecutor
            );

            CompletableFuture<Integer> deleteFuture = CompletableFuture.supplyAsync(
                    () -> processDeleteBatch(batchDTO.getDeleteTagIds()),
                    batchExecutor
            );

            CompletableFuture.allOf(createFuture, updateFuture, deleteFuture).join();

            result.put("created", createFuture.get());
            result.put("updated", updateFuture.get());
            result.put("deleted", deleteFuture.get());

            return Result.ok(result);
        } catch (Exception e) {
            log.error("批量处理失败", e);
            return Result.error("批量操作失败");
        }
    }

    @Override
    public Result<Map<String, Object>> batchOperation(String action, List<QywxTag> tags) {
        try {
            int totalCount = tags.size();
            int successCount;

            switch (action.toLowerCase()) {
                case "create":
                    successCount = processBatch(qywxClient::createTag, tags);
                    break;
                case "update":
                    successCount = processBatch(qywxClient::updateTag, tags);
                    break;
                case "delete":
                    successCount = processDeleteBatch(
                            tags.stream()
                                    .map(QywxTag::getTagid)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toList())
                    );
                    break;
                default:
                    throw new IllegalArgumentException("无效操作类型");
            }

            return Result.ok(Stream.of(
                    new AbstractMap.SimpleEntry<>("action", action),
                    new AbstractMap.SimpleEntry<>("successCount", successCount),
                    new AbstractMap.SimpleEntry<>("totalCount", totalCount)
            ).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));

        } catch (Exception e) {
            log.error("批量{}操作失败", action, e);
            return Result.error("批量" + action + "失败");
        }
    }

    //批量同步（增删）工具方法
    private int processBatch(Consumer<QywxTag> action, List<QywxTag> tags) {
        if (CollectionUtils.isEmpty(tags)) return 0;

        return (int) tags.parallelStream()
                .filter(tag -> {
                    try {
                        action.accept(tag);
                        return true;
                    } catch (Exception e) {
                        log.error("处理标签失败 | tagName:{}", tag.getTagname(), e);
                        return false;
                    }
                }).count();
    }

    private int processDeleteBatch(List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) return 0;

        return (int) tagIds.parallelStream()
                .filter(id -> {
                    try {
                        qywxClient.safeDeleteTag(id);
                        return true;
                    } catch (Exception e) {
                        log.error("删除标签失败 | tagId:{}", id, e);
                        return false;
                    }
                }).count();
    }

    @PreDestroy
    public void shutdownExecutor() {
        batchExecutor.shutdown();
    }
}
