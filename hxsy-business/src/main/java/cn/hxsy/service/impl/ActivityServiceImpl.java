package cn.hxsy.service.impl;

import cn.hxsy.dao.ActivityMapper;
import cn.hxsy.datasource.model.entity.ActivityPO;
import cn.hxsy.service.ActivityService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, ActivityPO> implements ActivityService {

    @Override
    public Page<ActivityPO> listByTypePage(String activityType, String title,Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<ActivityPO> wrapper = new LambdaQueryWrapper<>();
        if (activityType != null) {
            wrapper.eq(ActivityPO::getActivityType, activityType);
        }
        if (title != null) {
            wrapper.like(ActivityPO::getTitle, title);
        }
        return this.page(new Page<>(pageNum, pageSize), wrapper);
    }

    @Override
    public List<ActivityPO> listByType(String activityType) {
        LambdaQueryWrapper<ActivityPO> wrapper = new LambdaQueryWrapper<>();
        if (activityType != null) {
            wrapper.eq(ActivityPO::getActivityType, activityType);
        }
        return this.list(wrapper);
    }
}
