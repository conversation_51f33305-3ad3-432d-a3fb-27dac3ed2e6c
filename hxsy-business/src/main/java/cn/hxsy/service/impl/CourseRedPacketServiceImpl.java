package cn.hxsy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hxsy.api.user.model.request.OrganizationQueryRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.base.constant.user.UserSelectType;
import cn.hxsy.base.enums.TransferStateEnum;
import cn.hxsy.base.request.CourseRedPacketRequest;
import cn.hxsy.base.request.SelectPermissionRequest;
import cn.hxsy.dao.CustomerBehaviorRelationMapper;
import cn.hxsy.model.response.CourseRedPacketPageResponse;
import cn.hxsy.model.response.CustomerRedPacketSummaryResponse;
import cn.hxsy.datasource.mapper.CourseRedPacketMapper;
import cn.hxsy.datasource.model.entity.CourseRedPacket;
import cn.hxsy.service.CourseRedPacketService;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.UserSelectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程红包记录服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Slf4j
@Service
public class CourseRedPacketServiceImpl extends ServiceImpl<CourseRedPacketMapper, CourseRedPacket> implements CourseRedPacketService {

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Autowired
    private UserSelectUtil userSelectUtil;
    
    @Autowired
    private CustomerBehaviorRelationMapper customerBehaviorRelationMapper;


    @Override
    public CourseRedPacketPageResponse courseRedPacketList(long current, long size, CourseRedPacketRequest request) {

        // 首先获取用户缓存信息
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        // 获取用户角色对应查询权限，先构造查询参数
        OrganizationQueryRequest selectRequest = new OrganizationQueryRequest();
        BeanUtil.copyProperties(request, selectRequest);
        selectRequest.setSalesGroup(StringUtils.isEmpty(request.getSalesGroupId()) ? request.getSalesGroupId() : String.valueOf(request.getSalesGroupId()));
        SelectPermissionRequest selectPermission = userSelectUtil.getSelectPermission(systemUserSelfInfo, UserSelectType.system_user, selectRequest);

        // 创建分页对象
        Page<CourseRedPacket> page = new Page<>(current, size);
        
        // 创建查询条件
        LambdaQueryWrapper<CourseRedPacket> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件，优先使用请求参数，如果为空则使用权限中的值
        // 按照栏目查询
        queryWrapper.eq(selectPermission.getColumnId() != null, CourseRedPacket::getColumnId, selectPermission.getColumnId());
        // 按照用户公司查询
        queryWrapper.eq(selectPermission.getCompanyId() != null, CourseRedPacket::getCompanyId, selectPermission.getCompanyId());
        // 按照用户销售组查询
        queryWrapper.eq(selectPermission.getSalesGroupId()!= null, CourseRedPacket::getSalesGroupId, selectPermission.getSalesGroupId());

        if (ObjectUtils.isNotEmpty(request.getCampPeriodId())) {
            queryWrapper.eq(CourseRedPacket::getCampPeriodId, request.getCampPeriodId());
        }
        if (ObjectUtils.isNotEmpty(request.getCourseId())) {
            queryWrapper.eq(CourseRedPacket::getCourseId, request.getCourseId());
        }
        if (StringUtils.isNotBlank(request.getSalesId())) {
            queryWrapper.eq(CourseRedPacket::getSalesId, request.getSalesId());
        }
        if (ObjectUtils.isNotEmpty(request.getCustomerId())) {
            queryWrapper.eq(CourseRedPacket::getCustomerId, request.getCustomerId());
        }
        if (StringUtils.isNotBlank(request.getState())) {
            queryWrapper.eq(CourseRedPacket::getState, request.getState());
        }
        if (StringUtils.isNotBlank(request.getCustomerName())) {
            queryWrapper.like(CourseRedPacket::getCustomerName, request.getCustomerName());
        }
        if (StringUtils.isNotBlank(request.getCourseName())) {
            queryWrapper.like(CourseRedPacket::getCourseName, request.getCourseName());
        }
        if (StringUtils.isNotBlank(request.getMerchantId())) {
            queryWrapper.eq(CourseRedPacket::getMerchantId, request.getMerchantId());
        }

        // 添加创建时间范围查询
        if (request.getCreateStartTime() != null) {
            queryWrapper.ge(CourseRedPacket::getCreatedAt, request.getCreateStartTime());
        }
        if (request.getCreateEndTime() != null) {
            queryWrapper.le(CourseRedPacket::getCreatedAt, request.getCreateEndTime());
        }
        
        // 添加默认排序：按创建时间降序
        queryWrapper.orderByDesc(CourseRedPacket::getCreatedAt);
        
        // 执行分页查询
        Page<CourseRedPacket> pageResult = baseMapper.selectPage(page, queryWrapper);
        
        // 使用相同的条件查询红包总金额
        BigDecimal totalAmount = baseMapper.getTotalAmountByConditions(
                selectPermission.getColumnId(),
                selectPermission.getCompanyId(),
                request.getCampPeriodId(),
                request.getSalesGroupId(),
                request.getSalesId(),
                request.getCourseId(),
                request.getCustomerId(),
                request.getState(),
                request.getCustomerName(),
                request.getCourseName(),
                request.getMerchantId(),
                request.getCreateStartTime(),
                request.getCreateEndTime()
        );
        
        // 如果总金额为null，设置为0
        if (totalAmount == null) {
            totalAmount = BigDecimal.ZERO;
        }
        
        // 构造并返回包含分页数据和总金额的响应对象
        return new CourseRedPacketPageResponse(pageResult, totalAmount);
    }

    @Override
    public CustomerRedPacketSummaryResponse getCustomerRedPacketSummary(Long customerId) {
        CustomerRedPacketSummaryResponse response = new CustomerRedPacketSummaryResponse();
        
        // 查询已领取的红包（状态为5表示转账成功）
        LambdaQueryWrapper<CourseRedPacket> receivedQuery = new LambdaQueryWrapper<>();
        receivedQuery.eq(CourseRedPacket::getCustomerId, customerId)
                    .eq(CourseRedPacket::getState, TransferStateEnum.SUCCESS.getCode());
        List<CourseRedPacket> receivedRedPackets = this.list(receivedQuery);
        
        // 计算已领取红包总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CourseRedPacket redPacket : receivedRedPackets) {
            try {
                if (StringUtils.isNotBlank(redPacket.getTransferAmount())) {
                    BigDecimal amount = new BigDecimal(redPacket.getTransferAmount());
                    totalAmount = totalAmount.add(amount);
                }
            } catch (NumberFormatException e) {
                // 忽略无法解析的金额
            }
        }
        
        // 查询未领取的红包且创建时间不超过24小时
        LambdaQueryWrapper<CourseRedPacket> unreceivedQuery = new LambdaQueryWrapper<>();
        unreceivedQuery.eq(CourseRedPacket::getCustomerId, customerId)
                      .eq(CourseRedPacket::getState, TransferStateEnum.WAIT_USER_CONFIRM.getCode())
                      .ge(CourseRedPacket::getCreatedAt, new java.util.Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000));
        List<CourseRedPacket> unreceivedRedPackets = this.list(unreceivedQuery);
        // 设置响应数据
        response.setTotalReceivedAmount(totalAmount.toString());
        response.setUnreceivedRedPackets(unreceivedRedPackets);
        
        return response;
    }
}