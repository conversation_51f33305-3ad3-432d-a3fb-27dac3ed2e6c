package cn.hxsy.service.impl;

import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.WechatAccountMapper;
import cn.hxsy.datasource.model.entity.WechatAccountPO;
import cn.hxsy.service.WechatAccountService;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@Slf4j
public class WechatAccountServiceImpl extends ServiceImpl<WechatAccountMapper, WechatAccountPO>
        implements WechatAccountService {

    @Autowired
    private RedisJsonUtils redisUtils;

    @Override
    @Transactional
    public boolean save(WechatAccountPO entity) {
        // 自动填充创建信息
        entity.setCreatedAt(LocalDateTime.now());
        entity.setCreatedBy(getCurrentUser());
        return super.save(entity);
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id);
    }

    @Override
    @Transactional
    public boolean updateById(WechatAccountPO entity) {
        // 自动填充更新信息
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setUpdatedBy(getCurrentUser());
        return super.updateById(entity);
    }

    @Override
    public WechatAccountPO getById(Long id) {
        return super.getById(id);
    }


    private String getCurrentUser() {
        // 从Redis获取当前用户（参考网页8的鉴权实现）
        // return redisUtils.get(...).getAccountId();
        return "system";
    }

    @Override
    public Page<WechatAccountPO> page(Page<WechatAccountPO> page, LambdaQueryWrapper<WechatAccountPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }
}
