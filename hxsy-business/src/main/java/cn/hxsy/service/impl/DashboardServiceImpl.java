package cn.hxsy.service.impl;

import cn.hxsy.dao.StatsSummaryDataMapper;
import cn.hxsy.dao.StatsSummaryMapper;
import cn.hxsy.datasource.model.entity.CompanyPO;
import cn.hxsy.datasource.model.entity.CompanySalesRsp;
import cn.hxsy.datasource.model.entity.StatsSummary;
import cn.hxsy.datasource.model.entity.StatsSummaryDO;
import cn.hxsy.service.DashboardService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.concurrent.TimeUnit;

import static com.mysql.cj.util.TimeUtil.DATE_FORMATTER;
import static me.ahoo.cosid.snowflake.SecondSnowflakeIdStateParser.DATE_TIME_FORMATTER;

@Service
public class DashboardServiceImpl implements DashboardService {

    private static final String REDIS_KEY = "dashboard:stats";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private StatsSummaryMapper statsSummaryMapper;

    @Autowired
    private StatsSummaryDataMapper statsSummaryDataMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public StatsSummary getDashboardStats() {
        // 从Redis获取数据
//        Object data = redisTemplate.opsForValue().get(REDIS_KEY);
//
//        if (data instanceof StatsSummary) {
//            return (StatsSummary) data;
//        }
        try {
            Object data = redisTemplate.opsForValue().get(REDIS_KEY);

            if (data instanceof StatsSummary) {
                return (StatsSummary) data;
            }

            if (data != null) {
                redisTemplate.delete(REDIS_KEY);
            }
        } catch (Exception e) {

        }

        // 重新获取全量数据
        StatsSummary stats = fetchFullStats();
        redisTemplate.opsForValue().set(REDIS_KEY, stats, 1, TimeUnit.HOURS);
        return stats;
    }

    // 获取全量统计数据
    private StatsSummary fetchFullStats() {
        // 统计总客户数
        Long totalMembers = statsSummaryDataMapper.countTotalCustomers();
        Long totalCompanies = statsSummaryDataMapper.countTotalCompanies();
        Long totalSalesAccounts = statsSummaryDataMapper.countTotalSalesAccounts();

        // 统计今日新增数
        LocalDateTime todayStart = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
        // 统计当月新增数
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).with(LocalTime.MIN);
        LocalDateTime monthEnd = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);

        Long todayNewMembers = statsSummaryDataMapper.countNewMembers(todayStart, todayEnd);
        Long todayViews = statsSummaryDataMapper.countTodayViews(todayStart, todayEnd);
        Long todayComplete = statsSummaryDataMapper.countTodayComplete(todayStart, todayEnd);
        Long todayRedPackets = statsSummaryDataMapper.countTodayRedPackets(todayStart, todayEnd);
        Long monthNewCompanies = statsSummaryDataMapper.countMonthNewCompanies(monthStart, monthEnd);
        Long monthNewSalesAccounts = statsSummaryDataMapper.countMonthNewSalesAccounts(monthStart, monthEnd);

        // 数据查询
        return new StatsSummary(
                totalMembers,                    // 会员总数
                todayNewMembers,                 // 今日新增会员
                todayViews,                      // 今日观看
                todayComplete,
                todayRedPackets,                 // 今日发放红包
                totalCompanies,          // 总公司数
                monthNewCompanies,            // 本月新增公司数
                totalSalesAccounts,         // 总销售账号数
                monthNewSalesAccounts,           // 本月新增销售账号数
                LocalDate.now().format(DATE_FORMATTER),     // 记录日期
                StatsSummary.getCurrentHour()               // 刷新时间
        );
    }

    @Override
    public void refreshRedisStats() {
        StatsSummary currentStats = getCurrentRedisStats();
        StatsSummary updatedStats;

        if (currentStats == null) {
            // Redis 中没有数据，获取全量数据
            updatedStats = fetchFullStats();
        } else {
            // Redis 中有数据，执行增量更新
            updatedStats = updateStatsIncrementally(currentStats);
        }
        // 设置刷新时间并存入 Redis
        updatedStats.setRefreshDate(StatsSummary.getCurrentHour());
        redisTemplate.opsForValue().set(REDIS_KEY, updatedStats, 1, TimeUnit.HOURS);
    }

    // 获取当前 Redis 中的统计数据
    private StatsSummary getCurrentRedisStats() {
        try {
            Object data = redisTemplate.opsForValue().get(REDIS_KEY);
            return (data instanceof StatsSummary) ? (StatsSummary) data : null;
        } catch (Exception e) {
            return null;
        }
    }

    // 增量更新统计数据
    private StatsSummary updateStatsIncrementally(StatsSummary currentStats) {
        // 1. 检查日期是否变化
        String currentDate = LocalDate.now().format(DATE_FORMATTER);
        boolean isNewDay = !currentDate.equals(currentStats.getRecordDate());

        // 2. 获取最近一小时的新增会员数
        LocalDateTime lastRefreshTime = parseRefreshTime(currentStats.getRefreshDate());
        LocalDateTime currentRefreshTime = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS);
        // 确保查询范围不超过一小时
        LocalDateTime actualStartTime = lastRefreshTime;
        if (Duration.between(lastRefreshTime, currentRefreshTime).toHours() > 1) {
            actualStartTime = currentRefreshTime.minusHours(1);
        }

        Long incrementalMembers = statsSummaryDataMapper.countNewMembers(actualStartTime, currentRefreshTime);



        // 3. 创建更新后的统计数据
        StatsSummary updatedStats = new StatsSummary();
        BeanUtils.copyProperties(currentStats, updatedStats);

        // 4. 更新会员相关数据
        if (isNewDay) {
            // 新的一天，重置今日新增会员数
            updatedStats.setRecordDate(currentDate);
            updatedStats.setTodayNewMembers(incrementalMembers);
        } else {
            // 同一天，累加今日新增会员数
            updatedStats.setTodayNewMembers(currentStats.getTodayNewMembers() + incrementalMembers);
        }

        // 更新总会员数
        updatedStats.setTotalMembers(currentStats.getTotalMembers() + incrementalMembers);

        // 5. 更新其他指标（全量获取）
        StatsSummary otherStats = fetchOtherStats();
        updatedStats.setTodayViews(otherStats.getTodayViews());
        updatedStats.setTodayComplete(otherStats.getTodayComplete());
        updatedStats.setTodayRedPackets(otherStats.getTodayRedPackets());
        updatedStats.setTotalCompanies(otherStats.getTotalCompanies());
        updatedStats.setMonthNewCompanies(otherStats.getMonthNewCompanies());
        updatedStats.setTotalSalesAccounts(otherStats.getTotalSalesAccounts());
        updatedStats.setMonthNewSalesAccounts(otherStats.getMonthNewSalesAccounts());

        return updatedStats;
    }

    // 解析刷新时间字符串
    private LocalDateTime parseRefreshTime(String refreshTimeStr) {
        try {
            return LocalDateTime.parse(refreshTimeStr, DATE_TIME_FORMATTER);
        } catch (Exception e) {
            return LocalDateTime.now().minusHours(1).truncatedTo(ChronoUnit.HOURS);
        }
    }

    private StatsSummary fetchOtherStats() {

        Long totalCompanies = statsSummaryDataMapper.countTotalCompanies();
        Long totalSalesAccounts = statsSummaryDataMapper.countTotalSalesAccounts();

        // 统计当日
        LocalDateTime todayStart = LocalDateTime.now().with(LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.now().with(LocalTime.MAX).withNano(0);
        // 统计当月
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).with(LocalTime.MIN);
        LocalDateTime monthEnd = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);

        Long monthNewCompanies = statsSummaryDataMapper.countMonthNewCompanies(monthStart, monthEnd);
        Long monthNewSalesAccounts = statsSummaryDataMapper.countMonthNewSalesAccounts(monthStart, monthEnd);
        Long todayViews = statsSummaryDataMapper.countTodayViews(todayStart, todayEnd);
        Long todayComplete = statsSummaryDataMapper.countTodayComplete(todayStart, todayEnd);
        Long todayRedPackets = statsSummaryDataMapper.countTodayRedPackets(todayStart, todayEnd);

        // 数据库查询
        return new StatsSummary(
                null,  // 会员总数不更新
                null,  // 今日新增会员不更新
                todayViews,      // 今日观看
                todayComplete,
                todayRedPackets,       // 今日发放红包
                totalCompanies,          // 总公司数
                monthNewCompanies,            // 本月新增公司数
                totalSalesAccounts,         // 总销售账号数
                monthNewSalesAccounts,           // 本月新增销售账号数
                LocalDate.now().format(DATE_FORMATTER),     // 记录日期
                StatsSummary.getCurrentHour()               // 刷新时间
        );
    }

    @Override
    @Transactional
    public int persistToDatabase() {
        // 获取当前日期（昨天）
        LocalDate yesterday = LocalDate.now().minusDays(1);

        // 统计昨天完整数据（00:00:00 - 23:59:59）
        StatsSummary stats = fetchFullStatsForDate(yesterday);

        // 转换为数据库实体
        StatsSummaryDO statsDO = new StatsSummaryDO();
        BeanUtils.copyProperties(stats, statsDO, "refreshDate");
        statsDO.setRecordDate(yesterday);

        // 保存到数据库
        return statsSummaryMapper.insert(statsDO);
    }

    // 新增方法：统计指定日期的完整数据
    private StatsSummary fetchFullStatsForDate(LocalDate date) {
        // 统计时间段
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.atTime(23, 59, 59);

        // 统计当月时间段
        LocalDateTime monthStart = date.withDayOfMonth(1).atStartOfDay();
        LocalDateTime monthEnd = date.with(TemporalAdjusters.lastDayOfMonth()).atTime(23, 59, 59);

        // 统计数据
        Long totalMembers = statsSummaryDataMapper.countTotalCustomersAsOf(end);
        Long totalCompanies = statsSummaryDataMapper.countTotalCompaniesAsOf(end);
        Long totalSalesAccounts = statsSummaryDataMapper.countTotalSalesAccountsAsOf(end);

        Long todayNewMembers = statsSummaryDataMapper.countNewMembers(start, end);
        Long todayViews = statsSummaryDataMapper.countTodayViews(start, end);
        Long todayComplete = statsSummaryDataMapper.countTodayComplete(start, end);
        Long todayRedPackets = statsSummaryDataMapper.countTodayRedPackets(start, end);
        Long monthNewCompanies = statsSummaryDataMapper.countMonthNewCompanies(monthStart, monthEnd);
        Long monthNewSalesAccounts = statsSummaryDataMapper.countMonthNewSalesAccounts(monthStart, monthEnd);

        return new StatsSummary(
                totalMembers,
                todayNewMembers,
                todayViews,
                todayComplete,
                todayRedPackets,
                totalCompanies,
                monthNewCompanies,
                totalSalesAccounts,
                monthNewSalesAccounts,
                date.format(DATE_FORMATTER),
                end.format(DATE_TIME_FORMATTER)  // 使用当天结束时间作为刷新时间
        );
    }

    @Override
    public StatsSummary getUpdateDashboardStats() {
        StatsSummary currentStats = getCurrentRedisStats();
        if (currentStats == null) {
            // 如果Redis没有数据，获取全量数据
            return fetchFullStats();
        }
        return updateStatsIncrementally(currentStats);
    }

    @Override
    public CompanySalesRsp getCompanySales(Long id){

        Long totalSalesAccounts = statsSummaryDataMapper.countTotalSalesAccountsById(id);
        // 统计当月新增数
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).with(LocalTime.MIN);
        LocalDateTime monthEnd = LocalDateTime.now().with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
        Long monthNewSalesAccounts = statsSummaryDataMapper.countMonthNewSalesAccountsById(monthStart, monthEnd, id);

        // 数据查询
        return new CompanySalesRsp(
                totalSalesAccounts,         // 总销售账号数
                monthNewSalesAccounts           // 本月新增销售账号数
        );
    }
}
