package cn.hxsy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.hxsy.datasource.model.entity.User;
import cn.hxsy.dao.UserMapper;
import cn.hxsy.base.response.Result;
import cn.hxsy.service.UserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-13 16:22:51
 */
@Service
@Slf4j
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

//    @Resource
//    Oauth2PwdClient oauth2PwdClient;

    @Override
    public Result login(User user) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getUserCode, user.getUserCode())
                .eq(User::getPassword, user.getPassword());
        List<User> users = this.getBaseMapper().selectList(wrapper);
        if(CollectionUtils.isEmpty(users)){
            return Result.error("用户名或密码错误");
        }
        return Result.ok(users.get(0));
    }

    @Override
    public Result loginAuth(User user) {
//        Oauth2PwdLoginReq req = new Oauth2PwdLoginReq();
//        req.setAdCode(user.getUserCode());
//        req.setPassword(user.getPassword());
//        Oauth2PwdLoginRes login = oauth2PwdClient.login(req);
//        log.info("单点登录响应：{}", JSON.toJSONString(login));
//        return Result.ok(login);
        return Result.ok();
    }
}
