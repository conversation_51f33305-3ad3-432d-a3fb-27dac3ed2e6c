package cn.hxsy.service.impl;

import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.ActivityGroupMapper;
import cn.hxsy.datasource.model.entity.ActivityGroupPO;
import cn.hxsy.service.ActivityGroupService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
@Slf4j
public class ActivityGroupServiceImpl extends ServiceImpl<ActivityGroupMapper, ActivityGroupPO> implements ActivityGroupService {

    @Autowired
    private ActivityGroupMapper activityGroupMapper;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Override
    public boolean save(ActivityGroupPO courseGroup) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        courseGroup.setCreatedAt(LocalDateTime.now());
        //courseGroup.setCreatedBy(systemUserPO.getAccountId());

        return super.save(courseGroup); // 使用 ServiceImpl 提供的 save 方法
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id); // 使用 ServiceImpl 提供的 removeById 方法
    }


    @Override
    public boolean updateById(ActivityGroupPO courseGroup) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        courseGroup.setUpdatedAt(LocalDateTime.now());
        //courseGroup.setUpdatedBy(systemUserPO.getAccountId());

        return super.updateById(courseGroup); // 使用 ServiceImpl 提供的 updateById 方法
    }

    @Override
    public ActivityGroupPO getById(Long id) {
        return super.getById(id); // 使用 ServiceImpl 提供的 getById 方法
    }

    @Override
    public Page<ActivityGroupPO> page(Page<ActivityGroupPO> page, LambdaQueryWrapper<ActivityGroupPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }
}