package cn.hxsy.service.impl;

import cn.hxsy.dao.WxMerchantKeyMapper;
import cn.hxsy.datasource.model.entity.WxMerchantKeyPO;
import cn.hxsy.service.WxMerchantKeyService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 微信商户密钥Service实现类
 */
@Service
public class WxMerchantKeyServiceImpl extends ServiceImpl<WxMerchantKeyMapper, WxMerchantKeyPO> implements WxMerchantKeyService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindMerchantKey(WxMerchantKeyPO wxMerchantKeyPO) {
        Assert.notNull(wxMerchantKeyPO.getCompanyId(), "公司ID不能为空");
        Assert.hasText(wxMerchantKeyPO.getMerchantId(), "商户ID不能为空");
        Assert.hasText(wxMerchantKeyPO.getPrivateKey(), "商户密钥不能为空");
        Assert.notNull(wxMerchantKeyPO.getKeyVersion(), "密钥版本不能为空");

        // 检查是否已存在绑定关系
        WxMerchantKeyPO existingKey = this.getByCompanyId(wxMerchantKeyPO.getCompanyId());
        if (existingKey != null) {
            throw new IllegalStateException("该公司已绑定商户密钥");
        }

        return this.save(wxMerchantKeyPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMerchantKey(WxMerchantKeyPO wxMerchantKeyPO) {
        Assert.notNull(wxMerchantKeyPO.getCompanyId(), "公司ID不能为空");
        Assert.hasText(wxMerchantKeyPO.getPrivateKey(), "商户密钥不能为空");

        // 检查是否存在绑定关系
        WxMerchantKeyPO existingKey = this.getByCompanyId(wxMerchantKeyPO.getCompanyId());
        if (existingKey == null) {
            throw new IllegalStateException("该公司未绑定商户密钥");
        }

        // 更新密钥信息
        wxMerchantKeyPO.setId(existingKey.getId());
        return this.updateById(wxMerchantKeyPO);
    }

    @Override
    public WxMerchantKeyPO getByCompanyId(Long companyId) {
        Assert.notNull(companyId, "公司ID不能为空");

        LambdaQueryWrapper<WxMerchantKeyPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxMerchantKeyPO::getCompanyId, companyId);
        return this.getOne(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindMerchantKey(Long companyId) {
        Assert.notNull(companyId, "公司ID不能为空");

        LambdaQueryWrapper<WxMerchantKeyPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WxMerchantKeyPO::getCompanyId, companyId);
        return this.remove(wrapper);
    }
}
