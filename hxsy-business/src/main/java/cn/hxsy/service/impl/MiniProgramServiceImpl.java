package cn.hxsy.service.impl;

import cn.hutool.core.lang.Pair;
import cn.hxsy.api.user.feign.vx.AppletClient;
import cn.hxsy.api.user.model.request.*;
import cn.hxsy.api.user.model.response.AppletTokenResponse;
import cn.hxsy.api.user.service.CompWxCodeRpcService;
import cn.hxsy.base.enums.UseStatusEnum;
import cn.hxsy.base.enums.WXTypeEnum;
import cn.hxsy.base.response.Result;
import cn.hxsy.dao.MiniProgramMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 小程序服务实现类
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
@Slf4j
public class MiniProgramServiceImpl extends ServiceImpl<MiniProgramMapper, MiniProgram> implements MiniProgramService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private AppletClient appletClient;

    @Value("${vx.jump.env_version:develop}")
    private String jumpWxaEnvVersion;

    @Value("${vx.jump.regPath:pages/index/register}")
    private String jumpRegWxaPath;

    @Value("${vx.jump.coursePath:pages/course/course}")
    private String jumpCourseWxaPath;

    public static final String WX_ACCESS_TOKEN = "CUST_WX_ACCESS_TOKEN";

//    @Autowired
//    private CustomerService customerService;

    @Autowired
    private CampCourseService campCourseService;

    @Autowired
    private CampPeriodService campPeriodService;

    @Autowired
    private CourseVideoService courseVideoService;

    @Autowired
    private VideoUploadService videoUploadService;

    @DubboReference(version = "1.0.0")
    private CompWxCodeRpcService compWxCodeRpcService;


    /**
     * @param companyUrlRequest
     * @return {@link Result }<{@link String }>
     * <AUTHOR>
     * @date 2025/04/08
     */

    @Override
    public Result<String> getSellUrl(CompanyUrlRequest companyUrlRequest) {
        // 从小程序信息表查询数据
        LambdaQueryWrapper<MiniProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MiniProgram::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .eq(MiniProgram::getType, WXTypeEnum.MINI_PROGRAM.getCode())
                .eq(MiniProgram::getAppid, companyUrlRequest.getAppId());
        MiniProgram miniProgram = baseMapper.selectOne(queryWrapper);

        if (miniProgram == null) {
            log.error("未找到对应的小程序配置信息");
            return Result.error("未找到对应的小程序配置信息");
        }

        String accessToken = getAccessToken(miniProgram);

        // 构建请求参数
        QRCodeRequest request = new QRCodeRequest();
        request.setPath(jumpRegWxaPath +
                "?headquartersId=" + companyUrlRequest.getHeadquartersId() +
                "&columnId=" + companyUrlRequest.getColumnId() +
                "&companyId=" + companyUrlRequest.getCompanyId());
        request.setWidth(500);
        request.setLineColor(new QRCodeRequest.LineColor("0,0,0"));
        request.setEnvVersion(jumpWxaEnvVersion);

        // cos路径
        String reginUrlPath = "register/" + companyUrlRequest.getCompanyId() + "/" + companyUrlRequest.getAppId();
        // 调用接口生成二维码
        Result<String> codePath = null;
        try {
            byte[] qrCodeBytes = appletClient.getQRCode(accessToken, request);
            log.info("成功获取二维码，大小: {} bytes", qrCodeBytes.length);

            String fileName = companyUrlRequest.getCompanyId() + "-" + companyUrlRequest.getAppId() + "Code.png";
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                "image/png",
                qrCodeBytes
            );

        codePath = videoUploadService.simpleUpload(multipartFile, reginUrlPath);
        log.info("二维码上传成功: {}", codePath.getData());
        // 保存二维码到公司
        compWxCodeRpcService.saveCompWxCode(
            companyUrlRequest.getCompanyId(),
            companyUrlRequest.getAppId(),
            codePath.getData()
        );
        } catch (Exception e) {
            log.error("邀请二维码生成失败", e);
            throw new RuntimeException("邀请二维码生成失败", e);
        }
    return Result.ok(codePath.getData());
    }


    /**
     * @param campPeriodUrlRequest
     * @return {@link Result }<{@link String }>
     * <AUTHOR>
     * @date 2025/04/04
     */

    @Override
    public Result<String>  getCampPeriodUrl(CampPeriodUrlRequest campPeriodUrlRequest) {
        // 从小程序信息表查询数据
        LambdaQueryWrapper<MiniProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MiniProgram::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .eq(MiniProgram::getType, WXTypeEnum.MINI_PROGRAM.getCode())
                .eq(MiniProgram::getAppid, campPeriodUrlRequest.getAppId());
        MiniProgram miniProgram = baseMapper.selectOne(queryWrapper);

        String accessToken = getAccessToken(miniProgram);

        StringBuilder queryBuilder = builderQuery(campPeriodUrlRequest);

        JSONObject json = new JSONObject();
        JumpWxa jumpWxa = new JumpWxa();
        jumpWxa.setEnv_version(jumpWxaEnvVersion);
        jumpWxa.setPath(jumpCourseWxaPath);
        jumpWxa.setQuery(queryBuilder.toString());
        json.put("jump_wxa", jumpWxa);
        String scheme = appletClient.urlScheme(accessToken, json, false, 1L, 365L);
        JSONObject urlSchemaJson = JSONObject.parseObject(scheme);
        String openLink = urlSchemaJson.getString("openlink");
        return Result.ok(openLink);
    }

    @Override
    public Result<String> getCourseUrl(CampPeriodUrlRequest campPeriodUrlRequest) {
        // 从小程序信息表查询数据
        LambdaQueryWrapper<MiniProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MiniProgram::getStatus, UseStatusEnum.EFFECTIVE.getCode())
                .eq(MiniProgram::getType, WXTypeEnum.MINI_PROGRAM.getCode())
                .eq(MiniProgram::getAppid, campPeriodUrlRequest.getAppId());
        MiniProgram miniProgram = baseMapper.selectOne(queryWrapper);

        String accessToken = getAccessToken(miniProgram);

        StringBuilder queryBuilder = builderQuery(campPeriodUrlRequest);

        JSONObject json = new JSONObject();
        JumpWxa jumpWxa = new JumpWxa();
        jumpWxa.setEnv_version(jumpWxaEnvVersion);
        jumpWxa.setPath(jumpCourseWxaPath);
        jumpWxa.setQuery(queryBuilder.toString());
        json.put("jump_wxa", jumpWxa);
        String scheme = appletClient.urlScheme(accessToken, json, false, 1L, 365L);
        System.err.println(scheme);


        // 如果生成 URL Scheme 成功，则执行数据库操作
        if (scheme != null && !scheme.isEmpty()) {
            //数据库查询
            CampCoursePO campCourseInfo = campCourseService.getByCampCourseId(Long.parseLong(campPeriodUrlRequest.getCampPeriodId().trim()),Long.parseLong(campPeriodUrlRequest.getCourseId().trim()));
            CampPeriodPO campPeriodInfo = campPeriodService.getById(Long.parseLong(campPeriodUrlRequest.getCampPeriodId().trim()));
            CourseVideoPO courseVideoInfo = courseVideoService.getById(Long.parseLong(campPeriodUrlRequest.getCourseId().trim()));
            if(campCourseInfo != null && campPeriodInfo != null && courseVideoInfo !=null){
                //创建实体类存储
                CustomerCourseRelation customerCourseRelation = new CustomerCourseRelation();
                customerCourseRelation.setCampPeriodId(campCourseInfo.getId());
                customerCourseRelation.setCampPeriodId(campCourseInfo.getId());
                customerCourseRelation.setCourseId(Long.parseLong(campPeriodUrlRequest.getCourseId().trim()));
                customerCourseRelation.setCompanyId(campPeriodInfo.getCompanyId());
//                customerCourseRelation.setArrivalStatus(Integer.valueOf(courseVideoInfo.getCourseStatus()));
                //插入数据库
//                customerService.saveCourseCustomerRel(customerCourseRelation);
            }
            return Result.error("数据库操作失败");
        }

        return Result.ok(scheme);

    }

    /**
     * @param campPeriodUrlRequest
     * @return {@link StringBuilder }
     * <AUTHOR>
     * @date 2025/04/04
     */

    private static StringBuilder builderQuery(CampPeriodUrlRequest campPeriodUrlRequest) {
        // 创建参数构建器
        StringBuilder queryBuilder = new StringBuilder();

        // 定义需要拼接的字段列表（按业务需求排序）
        List<Pair<String, Object>> params = Arrays.asList(
                Pair.of("salesId", campPeriodUrlRequest.getSalesId()),
                Pair.of("salesGroupId", campPeriodUrlRequest.getSalesGroupId()),
                Pair.of("columnId", campPeriodUrlRequest.getColumnId()),
                Pair.of("campPeriodId", campPeriodUrlRequest.getCampPeriodId()),
                Pair.of("courseId", campPeriodUrlRequest.getCourseId()),
                Pair.of("companyId", campPeriodUrlRequest.getCompanyId())
        );

        params.stream()
                .filter(p -> {
                    Object value = p.getValue();
                    if (value instanceof CharSequence) {
                        return StringUtils.isNotBlank((CharSequence) value);
                    } else if (value instanceof Number) {
                        return ((Number) value).intValue() > 0;
                    }
                    return value != null;
                })
                .forEach(p -> {
                    Object paramValue = p.getValue();
                    String encodedValue = null;
                    try {
                        encodedValue = URLEncoder.encode(
                                paramValue.toString(),
                                String.valueOf(StandardCharsets.UTF_8)
                        );
                    } catch (UnsupportedEncodingException e) {
                        throw new RuntimeException(e);
                    }

                    if (queryBuilder.length() > 0) {
                        queryBuilder.append("&");
                    }
                    queryBuilder.append(p.getKey())
                            .append("=")
                            .append(encodedValue);
                });
        return queryBuilder;
    }

    /**
     * 获取小程序access_token
     */
    private String getAccessToken(MiniProgram miniProgram) {
        String tokenCacheKey = WX_ACCESS_TOKEN + ":" + miniProgram.getAppid();
        String str = (String) redisTemplate.boundValueOps(tokenCacheKey).get();
        if (str == null || str.isEmpty()) {
            // 下一行代码中常量请不要加"S"，腾讯要求为CLIENT_CREDENTIAL
            AppletTokenReq tokenReq = new AppletTokenReq();
            tokenReq.setGrantType("client_credential");
            tokenReq.setAppId(miniProgram.getAppid());
            tokenReq.setSecret(miniProgram.getSecret());
            AppletTokenResponse token = appletClient.stableAccessToken(tokenReq);
            str = token.getAccess_token();
            log.info("获取access_token，access_token={}", token);
            redisTemplate.boundValueOps(tokenCacheKey).set(str, token.getExpires_in() - 300, TimeUnit.SECONDS);
        }
        return str;
    }


} 