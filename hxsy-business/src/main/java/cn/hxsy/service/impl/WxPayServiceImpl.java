package cn.hxsy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.response.CourseVideoResponse;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.api.user.service.CustomerRpcService;
import cn.hxsy.base.enums.DisableStatusEnum;
import cn.hxsy.base.enums.TransferStateEnum;
import cn.hxsy.base.exception.system.BizException;
import cn.hxsy.base.request.wxPayRequest;
import cn.hxsy.base.response.Result;
import cn.hxsy.base.response.TransferNotification;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.cache.config.snowId.SnowflakeIdWorker;
import cn.hxsy.datasource.model.entity.CourseRedPacket;
import cn.hxsy.datasource.model.entity.WxMerchantKeyPO;
import cn.hxsy.model.request.WxPayParam;
import cn.hxsy.service.*;
import cn.hxsy.utils.UserCacheUtil;
import cn.hxsy.utils.WxPayUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hxsy.base.constant.ResponseType.Success;
import static cn.hxsy.base.exception.system.code.WePayErrorCode.*;
import static cn.hxsy.cache.constant.course.CampCourseCacheConstant.CAMP_TODAY_COURSE;
import static cn.hxsy.cache.constant.course.CampCourseCacheConstant.TODAY_COURSE_PAK_USER;
import static cn.hxsy.cache.constant.course.CampCourseCacheConstant.USER_REDPACKET_LIMIT;


/**
 * 微信支付服务实现类
 */
@Slf4j
@Service
public class WxPayServiceImpl implements WxPayService {

    @Autowired
    private WxMerchantKeyService merchantKeyService;

    @Autowired
    private CourseRedPacketService courseRedPacketService;

    @Autowired
    private SnowflakeIdWorker idWorker;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @DubboReference(version = "1.0.0")
    private CustomerRpcService customerRpcService;

    @Value("${system.domain}")
    private String domain;

    private String notifyUrl = "/gateway/hxsy-business/api/v1/mini-program/wx-pay-callback";

    /**
     * 校验支付请求参数
     * @param wxPayRequest 支付请求对象
     * @return 如果校验通过返回null，否则返回包含错误信息的Result对象
     */
    private Result<String> validatePayRequest(wxPayRequest wxPayRequest) {
        if (wxPayRequest == null) {
            log.warn("支付请求参数为空");
            return Result.error("请求参数不能为空");
        }
        
        // 检查必要参数
        if (wxPayRequest.getCustomerId() == null) {
            log.warn("支付请求缺少参数: customerId");
            return Result.error("参数缺失");
        }
        log.warn("微信支付请求参数，客户ID:{}" , wxPayRequest.getCustomerId());
        if (wxPayRequest.getCampPeriodId() == null) {
            log.warn("支付请求缺少参数: campPeriodId");
            return Result.error("参数缺失");
        }
        if (wxPayRequest.getCourseId() == null) {
            log.warn("支付请求缺少参数: courseId");
            return Result.error("参数缺失");
        }
        if (wxPayRequest.getCompanyId() == null) {
            log.warn("支付请求缺少参数: companyId");
            return Result.error("参数缺失");
        }
        if (StringUtils.isBlank(wxPayRequest.getOpenid())) {
            log.warn("支付请求缺少参数: openid");
            return Result.error("参数缺失");
        }
        if (StringUtils.isBlank(wxPayRequest.getAmount())) {
            log.warn("支付请求缺少参数: amount");
            return Result.error("参数缺失");
        }
        if (StringUtils.isBlank(wxPayRequest.getAppId())) {
            log.warn("支付请求缺少参数: appId");
            return Result.error("参数缺失");
        }
        return null;
    }

    @Override
    public Result<TransferNotification> toPay(wxPayRequest wxPayRequest) {
        // 参数校验
        Result<String> validationResult = validatePayRequest(wxPayRequest);
        if (validationResult != null) {
            return Result.error(validationResult.getMsg());
        }
        
        // 合并检查用户红包状态和课程完成状态
        Result<Integer> result = customerRpcService.getRedPacketStatus(wxPayRequest);
        if (result.getCode() != Success.getCode()) {
            return Result.error(result.getMsg());
        }
        Integer redPacketStatus = result.getData();
        if (ObjectUtil.notEqual(redPacketStatus, DisableStatusEnum.ENABLE.getCode())) {
            return Result.error("您暂时无法领取红包");
        }
        String lockKey = TODAY_COURSE_PAK_USER + wxPayRequest.getCampPeriodId() + ":" + wxPayRequest.getCourseId() + ":" + wxPayRequest.getCustomerId();
        try {
            // 先对相关参数进行校验，有返回代表可以正常发起红包
            long amount = verifyPayRequest(wxPayRequest, lockKey);
            
            // 检查用户是否已达到红包领取限制
            checkRedPacketLimit(wxPayRequest);

            // 查询商户密钥信息
            WxMerchantKeyPO merchantKey = merchantKeyService.getByCompanyId(wxPayRequest.getCompanyId());
            if (merchantKey == null) {
                throw new BizException(PAY_ACCOUNT_NO_EXIST);
            }
            WxPayParam param = new WxPayParam();
            String outBillNo = idWorker.nextIdString();
            param.setOutBillNo(outBillNo);
            param.setOpenid(wxPayRequest.getOpenid());
            param.setAmount(amount);
            param.setAppId(wxPayRequest.getAppId());
            param.setNotifyUrl(domain + notifyUrl);
            param.setMerchantId(merchantKey.getMerchantId());
            param.setApiV3Key(merchantKey.getPrivateKey());
            param.setKeyVersion(merchantKey.getKeyVersion());
            param.setMerchantSerialNumber(merchantKey.getMerchantSerialNumber());
            param.setPrivateKeyPath(merchantKey.getPrivateKeyPath());
            param.setPublicKeyId(merchantKey.getPublicKeyId());
            param.setPublicKeyPath(merchantKey.getPublicKeyPath());
            param.setMode(merchantKey.getMode());
            log.info("微信支付请求参数: {}", JSONObject.toJSONString(param));
            // 调用工具类进行支付请求
            TransferNotification transferInfo = WxPayUtils.createTransferBills(param);
            log.info("发起转账返回：{}", JSONObject.toJSONString(transferInfo));
            transferInfo.setMchId(merchantKey.getMerchantId());
            // 返回前端商户支付秘钥版本信息，用于前端处理不同版本的领取方式
            transferInfo.setKeyVersion(merchantKey.getKeyVersion());
            // 记录支付信息
            recordTransfer(wxPayRequest, outBillNo, transferInfo);
            // 增加用户一小时内领取红包次数
            incrRedPacketCount(wxPayRequest);
            // 红包记录插入成功后，删除对应用户领取记录的分布式锁
            redisJsonUtils.delete(lockKey);
            return Result.ok(transferInfo);
        } catch (Exception e) {
            log.error("微信支付异常:", e);
            // 只有不是分布式锁异常，都需要删一下此次发起红包用户的分布式锁，防止没领取到红包又被限制了重复领取
            if (e instanceof BizException && ((BizException) e).getErrorCode() == PROCESS_PAY) {
                return Result.error(e.getMessage());
            }
            redisJsonUtils.delete(lockKey);
            return Result.error("红包领取失败: " + e.getMessage());
        }
    }

    @Override
    public Result<TransferNotification> toPayTest(wxPayRequest wxPayRequest) {
        // 限制超管使用
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        userCacheUtil.checkUserAdmin(systemUserSelfInfo);
        // 查询商户密钥信息
        WxMerchantKeyPO merchantKey = merchantKeyService.getByCompanyId(wxPayRequest.getCompanyId());
        if (merchantKey == null) {
            throw new BizException(PAY_ACCOUNT_NO_EXIST);
        }
        try {
            WxPayParam param = new WxPayParam();
            String outBillNo = idWorker.nextIdString();
            param.setOutBillNo(outBillNo);
            param.setOpenid("oZMlP66oTKrz7c2KNaGRXHU8Hvdg");
            param.setAmount(10L);
            param.setAppId("wxa7e62e6efbab8d35");
            param.setNotifyUrl(domain + notifyUrl);
            param.setMerchantId(merchantKey.getMerchantId());
            param.setApiV3Key(merchantKey.getPrivateKey());
            param.setKeyVersion(merchantKey.getKeyVersion());
            param.setMerchantSerialNumber(merchantKey.getMerchantSerialNumber());
            param.setPrivateKeyPath(merchantKey.getPrivateKeyPath());
            param.setPublicKeyId(merchantKey.getPublicKeyId());
            param.setPublicKeyPath(merchantKey.getPublicKeyPath());
            param.setMode(merchantKey.getMode());
            log.info("微信支付测试: {}", JSONObject.toJSONString(param));
            // 调用工具类进行支付请求
            TransferNotification transferInfo = WxPayUtils.createTransferBills(param);
            log.info("微信支付测试返回：{}", JSONObject.toJSONString(transferInfo));
            transferInfo.setMchId(merchantKey.getMerchantId());
            return Result.ok(transferInfo);
        } catch (Exception e) {
            log.error("微信支付测试异常:", e);
            return Result.error("微信支付测试失败: " + e.getMessage());
        }
    }

    /**
    * @description: 发红包前对应情况校验
     * @param lockKey 以当前发起课程号，领取用户id进行一下分布式锁校验，防止红包记录还未入库时，重复领取红包
     * @return 返回前端发送的红包金额，如果这里有抛出异常即不返回
    * @author: xiaQL
    * @date: 2025/6/7 14:38
    */
    private long verifyPayRequest(wxPayRequest wxPayRequest, String lockKey) {
        // 红包金额校验
        long amount = new BigDecimal(wxPayRequest.getAmount())
                .multiply(new BigDecimal("100"))
                .longValue();
        // 金额不能超过1块（100分）
        if (amount > 100 || amount == 0 || amount < 0) {
            log.info("前端转换后金额异常：{}", wxPayRequest.getAmount());
            throw new BizException(PAY_AMOUNT_FAILED);
        }
        // 5分钟的锁过期时间，调用到存储如果超过5分钟应该是系统崩了
        Boolean lock = redisJsonUtils.getLock(lockKey, 300L);
        if(!lock){
            throw new BizException(PROCESS_PAY);
        }
        // 校验该用户在该课程下是否已经有领取记录
        CourseRedPacket one = courseRedPacketService.getOne(new LambdaQueryWrapper<CourseRedPacket>()
                .eq(CourseRedPacket::getCampPeriodId, wxPayRequest.getCampPeriodId())
                .eq(CourseRedPacket::getCourseId, wxPayRequest.getCourseId())
                .eq(CourseRedPacket::getCustomerId, wxPayRequest.getCustomerId()));
        if (one != null) {
            throw new BizException(SUCCESS_PAY);
        }
        // 校验该课程是否是今天播放课程（存在redis）
        String redisKey = CAMP_TODAY_COURSE + wxPayRequest.getCampPeriodId();
        List<CourseVideoResponse> courseVideoCache = redisJsonUtils.getList(redisKey, CourseVideoResponse.class);
        if (CollectionUtil.isNotEmpty(courseVideoCache)) {
            List<CourseVideoResponse> collect = courseVideoCache.stream()
                    .filter(courseVideoResponse -> wxPayRequest.getCourseId().equals(courseVideoResponse.getId())).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(collect)) {
                throw new BizException(CAMP_COURSE_NO_EXIST);
            }
        }else {
            throw new BizException(CAMP_COURSE_NO_EXIST);
        }
        return amount;
    }


    /**
    * @description: 记录支付信息
    * @author: xiaQL
    * @date: 2025/5/29 21:39
    */
    private void recordTransfer(wxPayRequest wxPayRequest, String outBillNo, TransferNotification transferInfo) {
        CourseRedPacket courseRedPacket = new CourseRedPacket();
        courseRedPacket.setColumnId(wxPayRequest.getColumnId());
        courseRedPacket.setCompanyId(wxPayRequest.getCompanyId());
        courseRedPacket.setCampPeriodId(wxPayRequest.getCampPeriodId());
        courseRedPacket.setCourseId(wxPayRequest.getCourseId());
        courseRedPacket.setSalesId(wxPayRequest.getSalesId());
        courseRedPacket.setSalesGroupId(wxPayRequest.getSalesGroupId());
        courseRedPacket.setCourseName(wxPayRequest.getCourseName());
        courseRedPacket.setCustomerId(wxPayRequest.getCustomerId());
        courseRedPacket.setCustomerName(wxPayRequest.getCustomerName());
        courseRedPacket.setTransferAmount(wxPayRequest.getAmount());
        courseRedPacket.setOutBillNo(outBillNo);
        // 处理新、旧版发起转账后响应参数区别
        if(transferInfo.getKeyVersion() == 1){
            courseRedPacket.setTransferBillNo(transferInfo.getTransferBillNo());
            courseRedPacket.setState(TransferStateEnum.getCodeByName(transferInfo.getState()));
            courseRedPacket.setPackageInfo(transferInfo.getPackageInfo());
        }else {
            courseRedPacket.setTransferBillNo(transferInfo.getBatchId());
            if("FINISHED".equals(transferInfo.getBatchStatus())){
                courseRedPacket.setState(TransferStateEnum.getCodeByName("SUCCESS"));
            }else {
                courseRedPacket.setState(TransferStateEnum.getCodeByName(transferInfo.getBatchStatus()));
            }
        }
        courseRedPacket.setMerchantId(transferInfo.getMchId());
        courseRedPacketService.save(courseRedPacket);
    }
    
    /**
     * 检查用户是否已达到红包领取限制
     * @param wxPayRequest 支付请求对象，包含用户ID等信息
     */
    private void checkRedPacketLimit(wxPayRequest wxPayRequest) {
        Long customerId = wxPayRequest.getCustomerId();
        String hourLimitKey = USER_REDPACKET_LIMIT + customerId;
        
        // 检查当前计数，防止快速点击导致计数超限
        String countStr = stringRedisTemplate.opsForValue().get(hourLimitKey);
        if (countStr != null) {
            long currentCount = Long.parseLong(countStr);
            if (currentCount >= 2) {
                log.info("用户[{}]一小时内已领取2次红包，超过限制（预检查）", customerId);
                customerRpcService.updateRedPacketAndUseStatus(wxPayRequest);
                throw new BizException(REDPACKET_LIMIT_MAX);
            }
        }
    }
    
    /**
     * 增加用户的红包领取次数
     * @param wxPayRequest 支付请求对象，包含用户ID等信息
     * @return 增加后的次数
     */
    private Long incrRedPacketCount(wxPayRequest wxPayRequest) {
        Long customerId = wxPayRequest.getCustomerId();
        String hourLimitKey = USER_REDPACKET_LIMIT + customerId;
        
        // 使用Redis的原子操作递增计数
        Long newCount = stringRedisTemplate.opsForValue().increment(hourLimitKey);
        
        // 设置过期时间（如果key是新创建的）
        if (newCount == 1) {
            stringRedisTemplate.expire(hourLimitKey, 3600, TimeUnit.SECONDS);
        }
        
        // 如果新计数超过限制，递减计数并抛出异常
        if (newCount > 2) {
            // 回退计数
            stringRedisTemplate.opsForValue().decrement(hourLimitKey);
            log.info("用户[{}]一小时内已领取2次红包，超过限制", customerId);
            customerRpcService.updateRedPacketAndUseStatus(wxPayRequest);
            throw new BizException(REDPACKET_LIMIT_MAX);
        }
        
        log.info("用户[{}]领取红包次数已更新，当前次数：{}", customerId, newCount);
        return newCount;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<String> handlePayCallback(HttpServletRequest request) {
        String requestData = null;
        try {
            // 读取回调数据
            requestData = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
            log.info("收到微信支付回调请求");
            // 获取请求头中的关键信息
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String signature = request.getHeader("Wechatpay-Signature");
            String serialNumber = request.getHeader("Wechatpay-Serial");
            log.info("--------------回调请求头数据START-----------");
            log.info("回调请求头: {}", requestData);
            log.info("Wechatpay-Serial: {}", serialNumber);
            log.info("Wechatpay-signature: {}", signature);
            log.info("Wechatpay-nonce: {}", nonce);
            log.info("Wechatpay-timestamp: {}", timestamp);
            log.info("--------------回调请求头数据END-----------");
            // 验证必要的请求头
            if (StringUtils.isAnyBlank(timestamp, nonce, signature, serialNumber)) {
                log.error("回调请求缺少必要的请求头");
                return Result.error("缺少必要的请求头");
            }
            // 验证时间戳，防止重放攻击（5分钟内的请求有效）
            long currentTime = System.currentTimeMillis() / 1000;
            long timestampLong = Long.parseLong(timestamp);
            if (Math.abs(currentTime - timestampLong) > 300) {
                log.error("回调请求已过期，当前时间: {}, 请求时间: {}", currentTime, timestamp);
                return Result.error("请求已过期");
            }
            LambdaQueryWrapper<WxMerchantKeyPO> queryWrapper = new LambdaQueryWrapper<WxMerchantKeyPO>()
                    .eq(WxMerchantKeyPO::getPlatformSerialNumber, serialNumber);
            // 查询商户密钥信息
            List<WxMerchantKeyPO> wxMerchantKeyPOS = merchantKeyService.getBaseMapper().selectList(queryWrapper);
            if (CollectionUtils.isEmpty(wxMerchantKeyPOS)) {
                log.error("商户密钥信息不存在");
                return Result.error("商户配置不存在");
            }
            WxMerchantKeyPO wxMerchantKeyPO = wxMerchantKeyPOS.get(0);
            // 验签并解密通知数据
            JSONObject notifyData = WxPayUtils.handlePayNotification(
                nonce,
                timestamp,
                signature,
                serialNumber,
                requestData,
                    wxMerchantKeyPO
            );
            if (notifyData == null) {
                log.error("回调通知验签失败或解密失败");
                return Result.error("回调通知验证失败");
            }
            log.info("回调通知数据: {}", notifyData.toJSONString());
            // 根据支付key版本，处理不同版本支付结果
            String state = "";
            String transferBillNo = "";
            String outBillNo = "";
            if(wxMerchantKeyPO.getKeyVersion() == 1){
                // 新版请求参数
                state = notifyData.getString("state");
                transferBillNo = notifyData.getString("transfer_bill_no");
                outBillNo = notifyData.getString("out_bill_no");
            }else if (wxMerchantKeyPO.getKeyVersion() == 0){
                // 旧版请求参数
                state = notifyData.getString("batch_status");
                transferBillNo = notifyData.getString("batch_id");
                outBillNo = notifyData.getString("out_batch_no");
            }
            if (!("SUCCESS".equals(state) || "FINISHED".equals(state))) {
                log.error("outBillNo: {}, 支付结果不是成功状态, state: {}", outBillNo, state);
                return Result.error("支付未成功");
            }
            // 查询支付记录
            CourseRedPacket redPacket = courseRedPacketService.getOne(
                    new LambdaQueryWrapper<CourseRedPacket>()
                            .eq(CourseRedPacket::getOutBillNo, outBillNo)
            );
            if (redPacket == null) {
                log.error("支付记录不存在, outBillNo: {}", outBillNo);
                return Result.error("支付记录不存在");
            }
            redPacket.setState(TransferStateEnum.SUCCESS.getCode());
            redPacket.setUpdatedAt(LocalDateTime.now());
            // 更新支付记录状态
            int i = courseRedPacketService.getBaseMapper().updateById(redPacket);
            if (i <= 0)  {
                log.error("更新支付状态失败, outBillNo: {}, transferBillNo: {}",
                         outBillNo, transferBillNo);
                throw new RuntimeException("更新支付状态失败");
            }
            // 更新领取红包行为轨迹
            Result result = customerRpcService.saveReceiveRedPacket(redPacket.getCustomerId(), redPacket.getCompanyId(), redPacket.getCampPeriodId(), redPacket.getCourseId(), redPacket.getTransferAmount(), 1);
            if (!(result.getCode() == Success.getCode())) {
                log.error("更新领取红包行为轨迹失败, outBillNo: {}, transferBillNo: {}, customerId: {}, courseId: {}",
                         outBillNo, transferBillNo, redPacket.getCustomerId(), redPacket.getCourseId());
            }
            log.info("支付回调处理成功，outBillNo: {}, transferBillNo: {}",
                     outBillNo, transferBillNo);
            return Result.ok("{\"code\": \"SUCCESS\",\"message\": \"成功\"}");

        } catch (Exception e) {
            log.error("处理支付回调时发生异常，requestData: {}", requestData, e);
            throw new RuntimeException("处理支付回调异常", e);
        }
    }

    /**
     * @description: 根据商家支付key版本，构造对应版本支付回调响应
     * 0-旧版 1-新版
     * @author: xiaQL
     * @date: 2025/5/29 14:44
     */
//    private CourseRedPacket buildWxPayRecordByKeyVersion(CourseRedPacket redPacket, WxMerchantKeyPO wxMerchantKeyPO, JSONObject notifyData) {
//        // 公共参数
//        String state = notifyData.getString("state");
//        String transferBillNo = notifyData.getString("transfer_bill_no");
//        String outBillNo = notifyData.getString("out_bill_no");
//        if(wxMerchantKeyPO.getKeyVersion() == 1){
//            // 新版请求参数
//
//        }else if (wxMerchantKeyPO.getKeyVersion() == 0){
//            // 旧版请求参数
//
//        }else {
//            log.error("回调通知，商家支付key版本错误，商家id：{}", wxMerchantKeyPO.getMerchantId());
//            throw new RuntimeException("网络异常，请稍后重试");
//        }
//        return redPacket;
//    }
}