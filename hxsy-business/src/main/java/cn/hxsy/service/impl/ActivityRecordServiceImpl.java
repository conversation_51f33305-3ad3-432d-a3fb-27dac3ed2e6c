package cn.hxsy.service.impl;

import cn.hxsy.base.response.Result;
import cn.hxsy.controller.ActivityRecordController;
import cn.hxsy.dao.ActivityMapper;
import cn.hxsy.dao.ActivityRecordMapper;
import cn.hxsy.datasource.model.entity.*;
import cn.hxsy.service.ActivityRecordService;
import cn.hxsy.service.ActivityService;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ActivityRecordServiceImpl extends ServiceImpl<ActivityRecordMapper, ActivityRecordPO> implements ActivityRecordService {

    @Autowired
    private  ActivityService activityService;
    @Autowired
    private  ActivityRecordMapper activityRecordMapper;
    @Autowired
    private  ActivityMapper activityMapper;

    @Override
    public VideoCompletedResponse processActivities(VideoCompletedRequest request) {
        List<Long> successIds = new ArrayList<>();
        List<FailedActivity> failedList = new ArrayList<>();

        request.getActivityIds().forEach(activityId -> {
            try {
                // 获取用户答案
                List<Integer> userAnswers = request.getAnswers().getOrDefault(activityId, Collections.emptyList());
                processSingleActivity(request.getUserId(), activityId, userAnswers);
                successIds.add(activityId);
            } catch (RuntimeException e) {
                log.error("活动处理失败 | activityId: {} | 原因: {}", activityId, e.getMessage());
                failedList.add(new FailedActivity(activityId, e.getMessage()));
            }
        });

        return new VideoCompletedResponse(successIds, failedList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void processSingleActivity(Long userId, Long activityId, List<Integer> userAnswers) {
        // 1. 验证活动存在性及时效
        ActivityPO activity = activityService.getById(activityId);
        //ActivityPO activity = activityMapper.selectActivityById(activityId);
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        validateActivityTime(activity);

        // 2. 幂等性校验
        if (activityRecordMapper.existsReceivedRecord(userId, activityId)) {
            throw new RuntimeException("请勿重复领取");
        }

        // 3. 根据类型处理
        switch (activity.getActivityType()) {
            case "1":
                handleRedPacketActivity(userId, activity);
                break;
            case "2":
                handleCouponActivity(userId, activity);
                break;
            case "3":
                handleGiftActivity(userId, activity);
                break;
            case "4":
                handlePointsActivity(userId, activity);
                break;
            case "5":
                handleQuizRedPacketActivity(userId, activity, userAnswers);
                break;
            case "6":
                handleRedPacketActivity(userId, activity);
                break;
            default:
                throw new UnsupportedOperationException("不支持的活动类型: " + activity.getActivityType());
        }
    }

    // 公共校验逻辑
    private void validateActivityTime(ActivityPO activity) {
        LocalDateTime now = LocalDateTime.now();
        // 检查开始时间
        if (now.isBefore(activity.getStartTime())) {
            throw new RuntimeException("活动未开始");
        }
        // 检查结束时间（允许无结束时间）
        if (activity.getEndTime() != null && now.isAfter(activity.getEndTime())) {
            throw new RuntimeException("活动已结束");
        }
    }

    private void handleRedPacketActivity(Long userId, ActivityPO activity) {
        // 4. 解析配置
        JSONObject config = JSON.parseObject(activity.getConfig());
        String type = config.getString("type");
        BigDecimal amount;

        // 5. 计算金额
        if ("FIXED".equals(type)) {
            amount = config.getBigDecimal("amount");
        } else if ("RANDOM".equals(type)) {
            BigDecimal min = config.getBigDecimal("minAmount");
            BigDecimal max = config.getBigDecimal("maxAmount");
            amount = generateRandomAmount(min, max);
        } else {
            throw new RuntimeException("无效的红包类型");
        }

        // 6. 扣减库存（原子操作）
        if (activityMapper.decrementStock(activity.getId()) <= 0) {
            throw new RuntimeException("红包已领完");
        }

        // 7. 保存记录
        ActivityRecordPO record = buildRedPacketRecord(userId, activity, amount, config);
        activityRecordMapper.insert(record);
    }

    private BigDecimal generateRandomAmount(BigDecimal min, BigDecimal max) {
        return BigDecimal.valueOf(
                min.doubleValue() + Math.random() * (max.doubleValue() - min.doubleValue())
        ).setScale(2, RoundingMode.HALF_UP);
    }

    private ActivityRecordPO buildRedPacketRecord(Long userId, ActivityPO activity,
                                         BigDecimal amount, JSONObject config) {
        JSONObject detail = new JSONObject();
        detail.put("amount", amount);
        detail.put("type", config.getString("type"));
        if ("RANDOM".equals(config.getString("type"))) {
            detail.put("minAmount", config.getBigDecimal("minAmount"));
            detail.put("maxAmount", config.getBigDecimal("maxAmount"));
        }

        return ActivityRecordPO.builder()
                .userId(userId)
                .activityId(activity.getId())
                .activityType(activity.getActivityType())
                .actionType("1")
                .detail(detail.toString())
                .build();
    }

    private void handleCouponActivity(Long userId, ActivityPO activity) {
        // 1. 解析配置
        JSONObject config = JSON.parseObject(activity.getConfig());
        String discountType = config.getString("discountType");
        BigDecimal discountValue = config.getBigDecimal("discountValue");

        // 2. 验证配置有效性
        validateCouponConfig(discountType, discountValue);

        // 3. 扣减库存（原子操作）
        if (activityMapper.decrementStock(activity.getId()) <= 0) {
            throw new RuntimeException("优惠券已领完");
        }

        // 4. 保存记录
        ActivityRecordPO record = buildCouponRecord(userId, activity, discountType, discountValue);
        activityRecordMapper.insert(record);
    }

    private void validateCouponConfig(String discountType, BigDecimal discountValue) {
        if (discountType == null || (!"PERCENT".equals(discountType) && !"FIXED".equals(discountType))) {
            throw new RuntimeException("无效的优惠券类型");
        }
        if (discountValue == null || discountValue.compareTo(BigDecimal.ZERO) <= 0) {
            throw new RuntimeException("折扣值必须大于零");
        }
        if ("PERCENT".equals(discountType) && discountValue.compareTo(BigDecimal.ONE) > 0) {
            throw new RuntimeException("折扣百分比不能超过100%");
        }
    }

    private ActivityRecordPO buildCouponRecord(Long userId, ActivityPO activity,
                                               String discountType, BigDecimal discountValue) {
        JSONObject detail = new JSONObject();
        detail.put("discountType", discountType);
        detail.put("discountValue", discountValue);

        return ActivityRecordPO.builder()
                .userId(userId)
                .activityId(activity.getId())
                .activityType(activity.getActivityType())
                .actionType("1")
                .detail(detail.toString())
                .build();
    }

    private void handleGiftActivity(Long userId, ActivityPO activity) {
        // 1. 解析配置
        JSONObject config = JSON.parseObject(activity.getConfig());
        String giftType = config.getString("giftType");
        String describe = config.getString("describe");

        // 2. 验证配置有效性
        validateGiftConfig(giftType, describe);

        // 3. 扣减库存（原子操作）
        if (activityMapper.decrementStock(activity.getId()) <= 0) {
            throw new RuntimeException("礼品已领完");
        }

        // 4. 保存记录
        ActivityRecordPO record = buildGiftRecord(userId, activity, giftType, describe);
        activityRecordMapper.insert(record);
    }

    private void validateGiftConfig(String giftType, String describe) {
        if (giftType == null || (!"PHYSICAL".equals(giftType) && !"VIRTUAL".equals(giftType))) {
            throw new RuntimeException("无效的礼品类型");
        }
        if (StringUtils.isBlank(describe)) {
            throw new RuntimeException("礼品描述不能为空");
        }
    }

    private ActivityRecordPO buildGiftRecord(Long userId, ActivityPO activity,
                                             String giftType, String describe) {
        JSONObject detail = new JSONObject();
        detail.put("giftType", giftType);
        detail.put("describe", describe);

        return ActivityRecordPO.builder()
                .userId(userId)
                .activityId(activity.getId())
                .activityType(activity.getActivityType())
                .actionType("1") // 领取动作
                .detail(detail.toString())
                .build();
    }

    private void handlePointsActivity(Long userId, ActivityPO activity) {
        // 1. 解析配置
        JSONObject config = JSON.parseObject(activity.getConfig());
        Integer points = config.getInteger("points");
        String triggerType = config.getString("triggerType");

        // 2. 验证配置有效性
        validatePointsConfig(points, triggerType);

        // 3. 触发限制校验
        //checkTriggerLimit(userId, activity.getId(), triggerType);

        // 4. 保存记录（无库存扣减）
        ActivityRecordPO record = buildPointsRecord(userId, activity, points, triggerType);
        activityRecordMapper.insert(record);
    }

    private void validatePointsConfig(Integer points, String triggerType) {
        if (points == null || points <= 0) {
            throw new RuntimeException("积分值必须为正整数");
        }
    }

    private ActivityRecordPO buildPointsRecord(Long userId, ActivityPO activity,
                                               Integer points, String triggerType) {
        JSONObject detail = new JSONObject();
        detail.put("points", points);
        detail.put("triggerType", triggerType);

        return ActivityRecordPO.builder()
                .userId(userId)
                .activityId(activity.getId())
                .activityType(activity.getActivityType())
                .actionType("1")
                .detail(detail.toString())
                .build();
    }

    private void handleQuizRedPacketActivity(Long userId, ActivityPO activity, List<Integer> userAnswers) {
        // 1. 校验答题记录
        if (activityRecordMapper.existsQuizRecord(userId, activity.getId())) {
            throw new RuntimeException("请勿重复答题");
        }

        // 2. 验证答案
        validateQuizAnswers(activity, userAnswers);

        // 3. 处理红包
        handleRedPacketActivity(userId, activity);

        // 4. 保存答题记录
        saveQuizRecord(userId, activity, userAnswers);
    }

    private void validateQuizAnswers(ActivityPO activity, List<Integer> userAnswers) {
        // 解析正确答案
        List<Integer> correctAnswers = JSON.parseArray(activity.getCorrectAnswer(), Integer.class);

        // 验证答案格式
        List<JSONObject> options = JSON.parseArray(activity.getAnswerOptions(), JSONObject.class);
        List<Integer> validOptionIds = options.stream()
                .map(o -> o.getInteger("id"))
                .collect(Collectors.toList());

        userAnswers.forEach(id -> {
            if (!validOptionIds.contains(id)) {
                throw new RuntimeException("包含无效选项ID: " + id);
            }
        });

        // 验证答案正确性
        boolean isCorrect;
        String questionType = activity.getQuestionType();
        switch (questionType) {
            case "1":
                isCorrect = validateSingleChoice(userAnswers, correctAnswers);
                break;
            case "2":
                isCorrect = validateMultipleChoice(userAnswers, correctAnswers);
                break;
            default:
                throw new RuntimeException("无效的题目类型");
        }

        if (!isCorrect) {
            throw new RuntimeException("答案错误");
        }
    }

    private boolean validateSingleChoice(List<Integer> userAnswers, List<Integer> correctAnswers) {
        return userAnswers.size() == 1
                && correctAnswers.size() == 1
                && userAnswers.get(0).equals(correctAnswers.get(0));
    }

    private boolean validateMultipleChoice(List<Integer> userAnswers, List<Integer> correctAnswers) {
        Set<Integer> userSet = new HashSet<>(userAnswers);
        Set<Integer> correctSet = new HashSet<>(correctAnswers);
        return userSet.equals(correctSet);
    }

    private void saveQuizRecord(Long userId, ActivityPO activity, List<Integer> userAnswers) {
        JSONObject detail = new JSONObject();
        detail.put("question", activity.getQuestionText());
        detail.put("userAnswers", userAnswers);
        detail.put("correctAnswers", JSON.parseArray(activity.getCorrectAnswer()));
        detail.put("options", JSON.parseArray(activity.getAnswerOptions()));

        ActivityRecordPO record = ActivityRecordPO.builder()
                .userId(userId)
                .activityId(activity.getId())
                .activityType(activity.getActivityType())
                .actionType("2") // 2=答题动作
                .detail(detail.toString())
                .build();

        activityRecordMapper.insert(record);
    }

}
