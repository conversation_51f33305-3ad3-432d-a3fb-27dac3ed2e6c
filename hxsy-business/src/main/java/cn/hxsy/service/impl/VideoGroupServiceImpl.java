package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.VideoGroupMapper;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.datasource.model.entity.VideoGroupPO;
import cn.hxsy.service.VideoGroupService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static cn.hxsy.cache.constant.user.CacheConstant.SYS_USER_LOGIN_TOKEN;

@Service
@Slf4j
public class VideoGroupServiceImpl extends ServiceImpl<VideoGroupMapper, VideoGroupPO> implements VideoGroupService {

    @Autowired
    private VideoGroupMapper videoGroupMapper;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Override
    public boolean save(VideoGroupPO videoGroup) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        videoGroup.setCreatedAt(LocalDateTime.now());
        //videoGroup.setCreatedBy(systemUserPO.getAccountId());

        return super.save(videoGroup); // 使用 ServiceImpl 提供的 save 方法
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id); // 使用 ServiceImpl 提供的 removeById 方法
    }


    @Override
    public boolean updateById(VideoGroupPO videoGroup) {

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        videoGroup.setUpdatedAt(LocalDateTime.now());
        //videoGroup.setUpdatedBy(systemUserPO.getAccountId());

        return super.updateById(videoGroup); // 使用 ServiceImpl 提供的 updateById 方法
    }

    @Override
    public VideoGroupPO getById(Long id) {
        return super.getById(id); // 使用 ServiceImpl 提供的 getById 方法
    }

    @Override
    public Page<VideoGroupPO> page(Page<VideoGroupPO> page, LambdaQueryWrapper<VideoGroupPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }
}
