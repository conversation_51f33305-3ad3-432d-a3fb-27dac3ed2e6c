package cn.hxsy.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import cn.hxsy.api.user.model.request.CustomerCourseVideoRequest;
import cn.hxsy.api.user.model.response.SystemUserResponse;
import cn.hxsy.api.user.service.UserInfoRpcService;
import cn.hxsy.base.response.Result;
import cn.hxsy.cache.config.RedisJsonUtils;
import cn.hxsy.dao.CourseGroupMapper;
import cn.hxsy.dao.CourseVideoMapper;
import cn.hxsy.datasource.model.entity.CampCoursePO;
import cn.hxsy.datasource.model.entity.CourseGroupPO;
import cn.hxsy.datasource.model.entity.CourseVideoPO;
import cn.hxsy.datasource.model.entity.SystemUserPO;
import cn.hxsy.model.request.CampCourseRequest;
import cn.hxsy.service.CampCourseService;
import cn.hxsy.service.CourseGroupService;
import cn.hxsy.service.CourseVideoService;
import cn.hxsy.utils.UserCacheUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static cn.hxsy.base.constant.ResponseType.Success;
import static cn.hxsy.cache.constant.user.CacheConstant.SYS_USER_LOGIN_TOKEN;

@Service
@Slf4j
public class CourseVideoServiceImpl extends ServiceImpl<CourseVideoMapper, CourseVideoPO> implements CourseVideoService  {

    @Autowired
    private CourseVideoMapper courseVideoMapper;

    @Autowired
    private CourseGroupService courseGroupService;

    @Autowired
    private CampCourseService campCourseService;

    @DubboReference(version = "1.0.0")
    private UserInfoRpcService userInfoRpcService;

    @Autowired
    private RedisJsonUtils redisJsonUtils;

    @Autowired
    private UserCacheUtil userCacheUtil;

    @Override
    public boolean save(CourseVideoPO courseVideo) {

        //根据courseVideo.getGroupId()确认course_group是否存在
        CourseGroupPO courseGroupInfo = courseGroupService.getById(courseVideo.getGroupId());

        if (courseGroupInfo == null) {
            return false;
        }

        // 获取对应业务人员缓存信息
        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
        if (systemUserPO == null) {
            throw new RuntimeException("获取业务人员信息失败");
        }

        courseVideo.setCreatedAt(LocalDateTime.now());
        courseVideo.setCreatedBy(systemUserPO.getAccountId());

        return super.save(courseVideo); // 使用 ServiceImpl 提供的 save 方法
    }

    @Override
    public boolean removeById(Long id) {
        return super.removeById(id); // 使用 ServiceImpl 提供的 removeById 方法
    }

    @Override
    public boolean updateById(CourseVideoPO courseVideo) {
        // 获取对应业务人员缓存信息
        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
        if (systemUserPO == null) {
            throw new RuntimeException("获取业务人员信息失败");
        }
        courseVideo.setUpdatedAt(LocalDateTime.now());
        courseVideo.setUpdatedBy(systemUserPO.getAccountId());
        // 还需要更新已经关联了选择了此小节的课程的营期下，该课程的数据
        // 1. 根据此小节课程id，与归属课程分组，获取此课程已经被哪些营期选择
        CampCourseRequest campCourseRequest = new CampCourseRequest();
        campCourseRequest.setCourseId(courseVideo.getId());
        campCourseRequest.setGroupId(courseVideo.getGroupId());
        campCourseRequest.setCourseVideo(courseVideo);
        campCourseRequest.setUpdateFlag(true);
        campCourseRequest.setUpdatedBy(systemUserPO.getAccountId());
        campCourseService.getCampByCourse(campCourseRequest);
        // 2. 更新这部分数据
        return super.updateById(courseVideo); // 使用 ServiceImpl 提供的 updateById 方法
    }

    @Override
    public boolean removeShellById(Long id) {

        CourseVideoPO courseVideo = courseVideoMapper.selectById(id);
        if (courseVideo == null) {
            throw new RuntimeException("课程视频不存在");
        }

        // 获取对应业务人员缓存信息
//        SystemUserPO systemUserPO = redisJsonUtils.get(SYS_USER_LOGIN_TOKEN + StpUtil.getTokenValue(), SystemUserPO.class);
//        if (systemUserPO == null) {
//            throw new RuntimeException("获取业务人员信息失败");
//        }

        // 构建更新条件
        UpdateWrapper<CourseVideoPO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", id);

        // 设置需要更新的字段
        courseVideo.setCourseStatus("0");
        //courseVideo.setUpdatedAt(LocalDateTime.now());
        //courseVideo.setUpdatedBy(systemUserPO.getAccountId());

        return courseVideoMapper.update(courseVideo, updateWrapper) > 0;
    }


    @Override
    public CourseVideoPO getById(Long id) {
        return super.getById(id); // 使用 ServiceImpl 提供的 getById 方法
    }

    @Override
    public Page<CourseVideoPO> page(Page<CourseVideoPO> page, LambdaQueryWrapper<CourseVideoPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchCourseVideos(List<CourseVideoPO> entities) {
        SystemUserResponse systemUserSelfInfo = userCacheUtil.getSystemUserSelfInfo();
        LocalDateTime now = LocalDateTime.now();

        // 设置公共字段
        entities.forEach(entity -> {
            entity.setCreatedBy(systemUserSelfInfo.getUsername());
            entity.setCreatedAt(now);
            entity.setUpdatedBy(systemUserSelfInfo.getUsername());
            entity.setUpdatedAt(now);
        });

        // 分批次插入（每批500条）
        int batchSize = 500;
        for (int i = 0; i < entities.size(); i += batchSize) {
            List<CourseVideoPO> batchList = entities.subList(i, Math.min(i + batchSize, entities.size()));
            if (!saveBatch(batchList)) {
                return false;
            }
        }
        return true;
    }
//    public Page<CourseVideoPO> page(Page<CourseVideoPO> page, LambdaQueryWrapper<CourseVideoPO> wrapper) {
//        return super.page(page, wrapper); // 使用 ServiceImpl 提供的 page 方法
//    }

}
