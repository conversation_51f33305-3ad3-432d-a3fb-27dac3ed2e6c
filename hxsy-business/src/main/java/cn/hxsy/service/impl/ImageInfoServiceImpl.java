package cn.hxsy.service.impl;

import cn.hxsy.dao.ImageInfoMapper;
import cn.hxsy.dao.VideoUploadMapper;
import cn.hxsy.datasource.model.entity.ImageInfoPO;
import cn.hxsy.datasource.model.entity.VideoUploadPO;
import cn.hxsy.service.ImageInfoService;
import cn.hxsy.service.VideoUploadService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ImageInfoServiceImpl extends ServiceImpl<ImageInfoMapper, ImageInfoPO> implements ImageInfoService {

    @Override
    public Page<ImageInfoPO> page(Page<ImageInfoPO> page, LambdaQueryWrapper<ImageInfoPO> wrapper) {
        // 调用 MyBatis Plus 提供的分页查询方法
        return super.page(page, wrapper);
    }
}
