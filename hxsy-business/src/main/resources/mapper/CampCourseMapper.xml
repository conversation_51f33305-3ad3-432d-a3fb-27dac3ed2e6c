<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CampCourseMapper">

    <insert id="batchInsert">
        INSERT INTO camp_course (
        id, camp_id, group_id, created_at, created_by, updated_at, updated_by
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.campId}, #{item.courseId}, #{item.orderNumber}, #{item.createdAt},
            #{item.createdBy}, #{item.updatedAt}, #{item.updatedBy})
        </foreach>
    </insert>

    <!-- 插入数据 -->
    <insert id="insertCampCourse" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO camp_course (
            camp_id,
            course_id,
            external_name,
            order_number,
            start_time,
            remark,
            status,
            field1,
            field2,
            field3,
            field4,
            field5
        ) VALUES (
                     #{campId},
                     #{courseId},
                     #{externalName},
                     #{orderNumber},
                     #{startTime},
                     #{remark},
                     #{status},
                     #{field1},
                     #{field2},
                     #{field3},
                     #{field4},
                     #{field5}
                 )
    </insert>

    <!-- 根据ID删除数据 -->
    <delete id="deleteCampCourseById" parameterType="Long">
        DELETE FROM camp_course WHERE id = #{id}
    </delete>

    <!-- 更新数据 -->
    <update id="updateCampCourse" parameterType="cn.hxsy.datasource.model.entity.CampCoursePO">
        UPDATE camp_course
        SET
            camp_id = #{campId},
            course_id = #{courseId},
            external_name = #{externalName},
            order_number = #{orderNumber},
            start_time = #{startTime},
            remark = #{remark},
            status = #{status},
            field1 = #{field1},
            field2 = #{field2},
            field3 = #{field3},
            field4 = #{field4},
            field5 = #{field5}
        WHERE id = #{id}
    </update>

    <!-- 查询所有营期课程 -->
    <select id="selectAllCampCourses" resultType="cn.hxsy.datasource.model.entity.CampCoursePO">
        SELECT
            id,
            camp_id,
            course_id,
            external_name,
            order_number,
            start_time,
            remark,
            status,
            field1,
            field2,
            field3,
            field4,
            field5
        FROM camp_course
    </select>

    <!-- 根据ID查询营期课程 -->
    <select id="selectCampCourseById" parameterType="Long" resultType="cn.hxsy.datasource.model.entity.CampCoursePO">
        SELECT
            id,
            camp_id,
            course_id,
            external_name,
            order_number,
            start_time,
            remark,
            status,
            field1,
            field2,
            field3,
            field4,
            field5
        FROM camp_course WHERE id = #{id}
    </select>

    <!-- 根据条件查询 -->
    <select id="selectCampCoursesByCondition" parameterType="map" resultType="cn.hxsy.datasource.model.entity.CampCoursePO">
        SELECT
        id,
        camp_id,
        course_id,
        external_name,
        order_number,
        start_time,
        remark,
        status,
        field1,
        field2,
        field3,
        field4,
        field5
        FROM camp_course
        <where>
            <if test="campId != null">
                AND camp_id = #{campId}
            </if>
        </where>
    </select>


    <select id="selectFirstCourseByCampIds" resultType="cn.hxsy.datasource.model.entity.CampCoursePO">
        SELECT
        camp_id as campId,
        course_id as courseId
        FROM camp_course
        WHERE camp_id IN
        <foreach collection="campIds" item="campId" open="(" separator="," close=")">
            #{campId}
        </foreach>
        ORDER BY order_number
    </select>
    <select id="selectByCourseIds" resultType="cn.hxsy.datasource.model.entity.CampCoursePO">
        SELECT
        id,
        camp_id as campId,
        course_id as courseId,
        start_time as startTime
        FROM camp_course
        WHERE course_id IN
        <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </select>

</mapper>