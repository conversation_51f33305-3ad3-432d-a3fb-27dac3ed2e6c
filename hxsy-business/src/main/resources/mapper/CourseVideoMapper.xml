<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CourseVideoMapper">

    <select id="selectByGroupIds" resultType="cn.hxsy.datasource.model.entity.CourseVideoPO">
        SELECT * FROM course_video
        WHERE group_id IN
        <foreach collection="groupIds" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </select>

    <!-- 插入数据 -->
    <insert id="insertCourseVideo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO course_video (
            course_name,
            group_id,
            cover_path,
            cover_id,
            course_introduction,
            course_description,
            course_content,
            start_time,
            patch_path,
            opening_setting,
            fullscreen_setting,
            video_path,
            video_id,
            course_pool,
            course_project,
            course_source,
            course_tag,
            course_salesmethod,
            course_price,
            underlined_price,
            course_status,
            field1,
            field2,
            field3,
            field4,
            field5,
            course_exam,
            reward_form,
            cash_form,
            reward_amount,
            rewards_points,
            error_limit
        ) VALUES (
                     #{courseName},
                     #{groupId},
                     #{coverPath},
                     #{coverId},
                     #{courseIntroduction},
                     #{courseDescription},
                     #{courseContent},
                     #{startTime},
                     #{patchPath},
                     #{openingSetting},
                     #{fullscreenSetting},
                     #{videoPath},
                     #{videoId},
                     #{coursePool, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{courseProject},
                     #{courseSource},
                     #{courseTag, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{courseSalesmethod},
                     #{coursePrice},
                     #{underlinedPrice},
                     #{courseStatus},
                     #{field1},
                     #{field2},
                     #{field3},
                     #{field4},
                     #{field5},
                     #{courseExam},
                     #{rewardForm},
                     #{cashForm},
                     #{rewardAmount},
                     #{rewardsPoints},
                     #{errorLimit}
                 )
    </insert>

    <!-- 根据ID删除数据 -->
    <delete id="deleteCourseVideoById" parameterType="Long">
        DELETE FROM course_video WHERE id = #{id}
    </delete>

    <!-- 更新数据 -->
    <update id="updateCourseVideo" parameterType="cn.hxsy.datasource.model.entity.CourseVideoPO">
        UPDATE course_video
        SET
            course_name = #{courseName},
            group_id = #{groupId},
            cover_path = #{coverPath},
            cover_id = #{coverId},
            course_introduction = #{courseIntroduction},
            course_description = #{courseDescription},
            course_content = #{courseContent},
            start_time = #{startTime},
            patch_path = #{patchPath},
            opening_setting = #{openingSetting},
            fullscreen_setting = #{fullscreenSetting},
            video_path = #{videoPath},
            video_id = #{videoId},
            course_pool = #{coursePool, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            course_project = #{courseProject},
            course_source = #{courseSource},
            course_tag = #{courseTag, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            course_salesmethod = #{courseSalesmethod},
            course_price = #{coursePrice},
            underlined_price = #{underlinedPrice},
            course_status = #{courseStatus},
            field1 = #{field1},
            field2 = #{field2},
            field3 = #{field3},
            field4 = #{field4},
            field5 = #{field5},
            course_exam = #{courseExam},
            reward_form = #{rewardForm},
            cash_form = #{cashForm},
            reward_amount = #{rewardAmount},
            rewards_points = #{rewardsPoints},
            error_limit = #{errorLimit}
        WHERE id = #{id}
    </update>

    <!-- 查询所有课程视频 -->
    <select id="selectAllCourseVideos" resultType="cn.hxsy.datasource.model.entity.CourseVideoPO">
        SELECT
            id,
            course_name,
            group_id,
            cover_path,
            cover_id,
            course_introduction,
            course_description,
            course_content,
            start_time,
            patch_path,
            opening_setting,
            fullscreen_setting,
            video_path,
            video_id,
            course_pool,
            course_project,
            course_source,
            course_tag,
            course_salesmethod,
            course_price,
            underlined_price,
            course_status,
            field1,
            field2,
            field3,
            field4,
            field5,
            course_exam,
            reward_form,
            cash_form,
            reward_amount,
            rewards_points,
            error_limit
        FROM course_video
    </select>

    <!-- 根据ID查询课程视频 -->
    <select id="selectCourseVideoById" parameterType="Long" resultType="cn.hxsy.datasource.model.entity.CourseVideoPO">
        SELECT
            id,
            course_name,
            group_id,
            cover_path,
            cover_id,
            course_introduction,
            course_description,
            course_content,
            start_time,
            patch_path,
            opening_setting,
            fullscreen_setting,
            video_path,
            video_id,
            course_pool,
            course_project,
            course_source,
            course_tag,
            course_salesmethod,
            course_price,
            underlined_price,
            course_status,
            field1,
            field2,
            field3,
            field4,
            field5,
            course_exam,
            reward_form,
            cash_form,
            reward_amount,
            rewards_points,
            error_limit
        FROM course_video WHERE id = #{id}
    </select>

    <!-- 查询根据条件 -->
    <select id="selectCourseVideosByCondition" parameterType="map" resultType="cn.hxsy.datasource.model.entity.CourseVideoPO">
        SELECT
            id,
            course_name,
            group_id,
            cover_path,
            cover_id,
            course_introduction,
            course_description,
            course_content,
            start_time,
            patch_path,
            opening_setting,
            fullscreen_setting,
            video_path,
            video_id,
            course_pool,
            course_project,
            course_source,
            course_tag,
            course_salesmethod,
            course_price,
            underlined_price,
            course_status,
            field1,
            field2,
            field3,
            field4,
            field5,
            course_exam,
            reward_form,
            cash_form,
            reward_amount,
            rewards_points,
            error_limit
        FROM course_video
        WHERE course_name LIKE CONCAT('%', #{courseName}, '%')
          AND group_id = #{groupId}
    </select>

    <select id="selectByCourseIds" resultType="cn.hxsy.datasource.model.entity.CourseVideoPO">
        SELECT
        id,
        course_name as courseName,
        course_description as courseDescription,
        course_price as coursePrice,
        start_time as startTime,
        cover_path as coverPath,
        video_path as videoPath
        FROM course_video
        WHERE id IN
        <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </select>

    <select id="getByCampIdAndDate" resultType="cn.hxsy.datasource.model.entity.CourseVideoPO">
        SELECT
            cv.*
        FROM
            CAMP_COURSE cc
            LEFT JOIN COURSE_VIDEO cv ON
            cc.COURSE_ID = cv.ID
        WHERE
            cc.CAMP_ID = #{campPeriodId}
          AND DATE(cc.START_TIME) = #{date}
    </select>

</mapper>
