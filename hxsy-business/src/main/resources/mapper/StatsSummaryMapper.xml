<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.StatsSummaryMapper">
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.StatsSummaryDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="total_members" property="totalMembers" jdbcType="BIGINT"/>
        <result column="today_new_members" property="todayNewMembers" jdbcType="BIGINT"/>
        <result column="today_views" property="todayViews" jdbcType="BIGINT"/>
        <result column="today_red_packets" property="todayRedPackets" jdbcType="BIGINT"/>
        <result column="total_companies" property="totalCompanies" jdbcType="BIGINT"/>
        <result column="month_new_companies" property="monthNewCompanies" jdbcType="BIGINT"/>
        <result column="total_sales_accounts" property="totalSalesAccounts" jdbcType="BIGINT"/>
        <result column="month_new_sales_accounts" property="monthNewSalesAccounts" jdbcType="BIGINT"/>
        <result column="record_date" property="recordDate" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, total_members, today_new_members, today_views, today_complete, today_red_packets,
        total_companies, month_new_companies, total_sales_accounts,
        month_new_sales_accounts, record_date
    </sql>

    <select id="selectByDateRange" resultType="cn.hxsy.datasource.model.entity.StatsSummaryDO">
        SELECT record_date, today_views, today_complete,today_red_packets, today_new_members
        FROM stats_summary
        WHERE record_date BETWEEN #{startDate} AND #{endDate}
        ORDER BY record_date ASC
    </select>

</mapper>