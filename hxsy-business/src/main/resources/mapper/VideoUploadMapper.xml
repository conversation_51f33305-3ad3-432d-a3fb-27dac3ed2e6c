<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.VideoUploadMapper">

    <!-- 批量插入 -->
    <insert id="batchInsertVideoUpload" parameterType="java.util.List">
        INSERT INTO video_upload (
        id,
        video_groupid,
        video_name,
        video_url,
        trans_status,
        stream_status,
        video_source,
        video_duration,
        video_size,
        created_by,
        created_at,
        updated_by,
        updated_at,
        remark,
        status
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.videoGroupid},
            #{item.videoName},
            #{item.videoUrl},
            #{item.transStatus},
            #{item.streamStatus},
            #{item.videoSource},
            #{item.videoDuration},
            #{item.videoSize},
            #{item.createdBy},
            #{item.createdAt},
            #{item.updatedBy},
            #{item.updatedAt},
            #{item.remark},
            #{item.status}
            )
        </foreach>
    </insert>

    <!-- 插入数据 -->
    <insert id="insertVideoUpload" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO video_upload (
            video_groupid,
            video_name,
            video_url,
            trans_status,
            stream_status,
            video_source,
            video_duration,
            video_size,
            created_by,
            created_at,
            updated_by,
            updated_at,
            remark,
            status
        ) VALUES (
                     #{videoGroupid},
                     #{videoName},
                     #{videoUrl},
                     #{transStatus},
                     #{streamStatus},
                     #{videoSource},
                     #{videoDuration},
                     #{videoSize},
                     #{createdBy},
                     #{createdAt},
                     #{updatedBy},
                     #{updatedAt},
                     #{remark},
                     #{status}
                 )
    </insert>

    <!-- 根据ID删除数据 -->
    <delete id="deleteVideoUploadById" parameterType="Long">
        DELETE FROM video_upload WHERE id = #{id}
    </delete>

    <!-- 更新数据 -->
    <update id="updateVideoUpload" parameterType="cn.hxsy.datasource.model.entity.VideoUploadPO">
        UPDATE video_upload
        SET
            video_groupid = #{videoGroupid},
            video_name = #{videoName},
            video_url = #{videoUrl},
            trans_status = #{transStatus},
            stream_status = #{streamStatus},
            video_source = #{videoSource},
            video_duration = #{videoDuration},
            video_size = #{videoSize},
            created_by = #{createdBy},
            created_at = #{createdAt},
            updated_by = #{updatedBy},
            updated_at = #{updatedAt},
            remark = #{remark},
            status = #{status}
        WHERE id = #{id}
    </update>

    <update id="updateStatusById">
        UPDATE video_upload
        SET
            status = '0',
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 查询所有视频上传记录 -->
    <select id="selectAllVideoUploads" resultType="cn.hxsy.datasource.model.entity.VideoUploadPO">
        SELECT
            id,
            video_groupid,
            video_name,
            video_url,
            trans_status,
            stream_status,
            video_source,
            video_duration,
            video_size,
            created_by,
            created_at,
            updated_by,
            updated_at,
            remark,
            status
        FROM video_upload
    </select>

    <!-- 根据ID查询视频上传记录 -->
    <select id="selectVideoUploadById" parameterType="Long" resultType="cn.hxsy.datasource.model.entity.VideoUploadPO">
        SELECT
            id,
            video_groupid,
            video_name,
            video_url,
            trans_status,
            stream_status,
            video_source,
            video_duration,
            video_size,
            created_by,
            created_at,
            updated_by,
            updated_at,
            remark,
            status
        FROM video_upload WHERE id = #{id}
    </select>

    <!-- 根据条件查询视频上传记录 -->
    <select id="selectVideoUploadsByCondition" parameterType="map" resultType="cn.hxsy.datasource.model.entity.VideoUploadPO">
        SELECT
            id,
            video_groupid,
            video_name,
            video_url,
            trans_status,
            stream_status,
            video_source,
            video_duration,
            video_size,
            created_by,
            created_at,
            updated_by,
            updated_at,
            remark,
            status
        FROM video_upload
        WHERE
            video_name LIKE CONCAT('%', #{videoName}, '%')
          AND video_groupid = #{videoGroupid}
    </select>

</mapper>