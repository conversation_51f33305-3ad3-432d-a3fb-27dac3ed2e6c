<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CourseGroupMapper">

    <select id="selectExistingGroupIds" resultType="java.lang.Long">
        SELECT id FROM course_group
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 插入数据 -->
    <insert id="insertCourseGroup" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO course_group (
            group_name,
            created_by,
            created_at,
            updated_by,
            updated_at,
            field1,
            field2,
            field3,
            field4,
            field5,
            remark,
            status
        ) VALUES (
                     #{groupName},
                     #{createdBy},
                     #{createdAt},
                     #{updatedBy},
                     #{updatedAt},
                     #{field1},
                     #{field2},
                     #{field3},
                     #{field4},
                     #{field5},
                     #{remark},
                     #{status}
                 )
    </insert>

    <!-- 根据ID删除数据 -->
    <delete id="deleteCourseById" parameterType="Long">
        DELETE FROM course_group WHERE id = #{id}
    </delete>

    <!-- 更新数据 -->
    <update id="updateCourseGroup" parameterType="cn.hxsy.datasource.model.entity.CourseGroupPO">
        UPDATE course_group
        SET
            group_name = #{groupName},
            created_by = #{createdBy},
            created_at = #{createdAt},
            updated_by = #{updatedBy},
            updated_at = #{updatedAt},
            field1 = #{field1},
            field2 = #{field2},
            field3 = #{field3},
            field4 = #{field4},
            field5 = #{field5},
            remark = #{remark},
            status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 查询所有视频课分组 -->
    <select id="selectAllCourseGroups" resultType="cn.hxsy.datasource.model.entity.CourseGroupPO">
        SELECT
            id,
            group_name,
            created_by,
            created_at,
            updated_by,
            updated_at,
            field1,
            field2,
            field3,
            field4,
            field5,
            remark,
            status
        FROM course_group
    </select>

    <!-- 根据ID查询视频课分组 -->
    <select id="selectCourseGroupById" parameterType="Long" resultType="cn.hxsy.datasource.model.entity.CourseGroupPO">
        SELECT
            id,
            group_name,
            created_by,
            created_at,
            updated_by,
            updated_at,
            field1,
            field2,
            field3,
            field4,
            field5,
            remark,
            status
        FROM course_group WHERE id = #{id}
    </select>

    <!-- 根据条件查询 -->
    <select id="selectCourseGroupsByCondition" parameterType="map" resultType="cn.hxsy.datasource.model.entity.CourseGroupPO">
        SELECT
            id,
            group_name,
            created_by,
            created_at,
            updated_by,
            updated_at,
            field1,
            field2,
            field3,
            field4,
            field5,
            remark,
            status
        FROM course_group
        WHERE group_name LIKE CONCAT('%', #{groupName}, '%')
    </select>

</mapper>