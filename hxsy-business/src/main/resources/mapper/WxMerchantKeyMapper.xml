<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.WxMerchantKeyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.WxMerchantKeyPO">
        <id column="id" property="id"/>
        <result column="company_id" property="companyId"/>
        <result column="sales_group_id" property="salesGroupId"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="key_version" property="keyVersion"/>
        <result column="private_key" property="privateKey"/>
        <result column="public_key_id" property="publicKeyId"/>
        <result column="merchant_serial_number" property="merchantSerialNumber"/>
        <result column="platform_serial_number" property="platformSerialNumber"/>
        <result column="public_key_path" property="publicKeyPath"/>
        <result column="private_key_path" property="privateKeyPath"/>
        <result column="remark" property="remark"/>
        <result column="status" property="status"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, company_id, sales_group_id, merchant_id, key_version, private_key, public_key_id,
    merchant_serial_number, platform_serial_number, public_key_path, private_key_path, remark, status, created_by,
    created_at, updated_by, updated_at
    </sql>
</mapper>
