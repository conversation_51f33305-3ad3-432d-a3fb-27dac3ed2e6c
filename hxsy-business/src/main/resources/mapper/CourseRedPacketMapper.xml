<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.datasource.mapper.CourseRedPacketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CourseRedPacket">
        <id column="id" property="id" />
        <result column="column_id" property="columnId" />
        <result column="company_id" property="companyId" />
        <result column="camp_period_id" property="campPeriodId" />
        <result column="course_id" property="courseId" />
        <result column="sales_group_id" property="salesGroupId" />
        <result column="sales_id" property="salesId" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="course_name" property="courseName" />
        <result column="transfer_amount" property="transferAmount" />
        <result column="merchant_id" property="merchantId" />
        <result column="out_bill_no" property="outBillNo" />
        <result column="transfer_bill_no" property="transferBillNo" />
        <result column="state" property="state" />
        <result column="package_info" property="packageInfo" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 根据条件查询红包总金额 -->
    <select id="getTotalAmountByConditions" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(CAST(transfer_amount AS DECIMAL(10,2))), 0) FROM course_red_packet
        WHERE status = 1 AND state = 5
        <if test="columnId != null">
            AND column_id = #{columnId}
        </if>
        <if test="companyId != null">
            AND company_id = #{companyId}
        </if>
        <if test="campPeriodId != null">
            AND camp_period_id = #{campPeriodId}
        </if>
        <if test="salesGroupId != null and salesGroupId != ''">
            AND sales_group_id = #{salesGroupId}
        </if>
        <if test="salesId != null and salesId != ''">
            AND sales_id = #{salesId}
        </if>
        <if test="courseId != null">
            AND course_id = #{courseId}
        </if>
        <if test="customerId != null">
            AND customer_id = #{customerId}
        </if>
        <if test="state != null and state != ''">
            AND state = #{state}
        </if>
        <if test="customerName != null and customerName != ''">
            AND customer_name LIKE CONCAT('%', #{customerName}, '%')
        </if>
        <if test="courseName != null and courseName != ''">
            AND course_name LIKE CONCAT('%', #{courseName}, '%')
        </if>
        <if test="merchantId != null and merchantId != ''">
            AND merchant_id = #{merchantId}
        </if>
        <if test="createStartTime != null">
            AND created_at &gt;= #{createStartTime}
        </if>
        <if test="createEndTime != null">
            AND created_at &lt;= #{createEndTime}
        </if>
    </select>

</mapper>
