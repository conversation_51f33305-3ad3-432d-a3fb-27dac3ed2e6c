<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hxsy.dao.CampPeriodMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hxsy.datasource.model.entity.CampPeriodPO">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="campperiod_name" property="campperiodName" />
        <result column="campperiod_introduction" property="campperiodIntroduction" />
        <result column="campperiod_coverpath" property="campperiodCoverpath" />
        <result column="campperiod_content" property="campperiodContent" />
        <result column="campperiod_pool" property="campperiodPool" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="campperiod_project" property="campperiodProject" />
        <result column="campperiod_source" property="campperiodSource" />
        <result column="campperiod_tag" property="campperiodTag" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="sales_group_id" property="salesGroupId" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="visual_list" property="visualList" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="campperiod_salesmethod" property="campperiodSalesmethod" />
        <result column="campperiod_price" property="campperiodPrice" />
        <result column="underlined_price" property="underlinedPrice" />
        <result column="campperiod_status" property="campperiodStatus" />
        <result column="enrollment_time" property="enrollmentTime" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="starting_flag" property="startingFlag" />
        <result column="starting_time" property="startingTime" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="registered_flag" property="registeredFlag" />
        <result column="catalog_mode" property="catalogMode" />
        <result column="series_flag" property="seriesFlag" />
        <result column="series_courses" property="seriesCourses" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="catalog_unlock" property="catalogUnlock" />
        <result column="need_phone" property="needPhone" />
        <result column="auto_register" property="autoRegister" />
        <result column="campperiod_red_pack" property="campperiodRedPack" />
        <result column="campperiod_red_pack_amount" property="campperiodRedPackAmount" />
        <result column="red_pack_range" property="redPackRange" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="unlock_time" property="unlockTime" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="created_at" property="createdAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 插入数据 -->
    <insert id="insertCampPeriod" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO `camp_period` (
            company_id,
            campperiod_name,
            campperiod_introduction,
            campperiod_coverpath,
            campperiod_content,
            campperiod_pool,
            campperiod_project,
            campperiod_source,
            campperiod_tag,
            sales_group_id,
            visual_list,
            campperiod_salesmethod,
            campperiod_price,
            underlined_price,
            campperiod_status,
            enrollment_time,
            starting_flag,
            starting_time,
            registered_flag,
            catalog_mode,
            series_flag,
            series_courses,
            catalog_unlock,
            campperiod_red_pack_amount,
            campperiod_red_pack,
            need_phone,
            auto_register,
            red_pack_range,
            unlock_time,
            created_at,
            created_by,
            updated_by,
            updated_at,
            remark,
            status,
            field1,
            field2,
            field3,
            field4,
            field5
        ) VALUES (
                     #{companyId},
                     #{campperiodName},
                     #{campperiodIntroduction},
                     #{campperiodCoverpath},
                     #{campperiodContent},
                     #{campperiodPool, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{campperiodProject},
                     #{campperiodSource},
                     #{campperiodTag, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{salesGroupId, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{visualList, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{campperiodSalesmethod},
                     #{campperiodPrice},
                     #{underlinedPrice},
                     #{campperiodStatus},
                     #{enrollmentTime, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{startingFlag},
                     #{startingTime, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{registeredFlag},
                     #{catalogMode},
                     #{seriesFlag},
                     #{seriesCourses, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{catalogUnlock},
                     #{needPhone},
                     #{autoRegister},
                     #{campperiodRedPack},
                     #{campperiodRedPackAmount},
                     #{red_pack_range, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{unlockTime, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
                     #{createdAt},
                     #{createdBy},
                     #{updatedBy},
                     #{updatedAt},
                     #{remark},
                     #{status},
                     #{field1},
                     #{field2},
                     #{field3},
                     #{field4},
                     #{field5}
                 )
    </insert>

    <!-- 根据ID删除数据 -->
    <delete id="deleteCampPeriodById" parameterType="Long">
        DELETE FROM `camp_period` WHERE id = #{id}
    </delete>

    <!-- 更新数据 -->
    <update id="updateCampPeriod" parameterType="cn.hxsy.datasource.model.entity.CampPeriodPO">
        UPDATE `camp_period`
        SET
            company_id = #{companyId},
            campperiod_name = #{campperiodName},
            campperiod_introduction = #{campperiodIntroduction},
            campperiod_coverpath = #{campperiodCoverpath},
            campperiod_content = #{campperiodContent},
            campperiod_pool = #{campperiodPool, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            campperiod_project = #{campperiodProject},
            campperiod_source = #{campperiodSource},
            campperiod_tag = #{campperiodTag, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            sales_group_id = #{salesGroupId, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            visual_list = #{visualList, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            campperiod_salesmethod = #{campperiodSalesmethod},
            campperiod_price = #{campperiodPrice},
            underlined_price = #{underlinedPrice},
            campperiod_status = #{campperiodStatus},
            enrollment_time = #{enrollmentTime, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            starting_flag = #{startingFlag},
            starting_time = #{startingTime, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            registered_flag = #{registeredFlag},
            catalog_mode = #{catalogMode},
            series_flag = #{seriesFlag},
            series_courses = #{seriesCourses, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            catalog_unlock = #{catalogUnlock},
            need_phone = #{needPhone},
            auto_register = #{autoRegister},
            campperiod_red_pack = #{campperiodRedPack},
            campperiod_red_pack_amount = #{campperiodRedPackAmount},
            red_pack_range = #{redPackRange, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            unlock_time = #{unlockTime, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            created_at = #{createdAt},
            created_by = #{createdBy},
            updated_by = #{updatedBy},
            updated_at = #{updatedAt},
            remark = #{remark},
            status = #{status},
            field1 = #{field1},
            field2 = #{field2},
            field3 = #{field3},
            field4 = #{field4},
            field5 = #{field5}
        WHERE id = #{id}
    </update>

    <!-- 查询所有营期 -->
    <select id="selectAllCampPeriods" resultType="cn.hxsy.datasource.model.entity.CampPeriodPO">
        SELECT
            id,
            company_id,
            campperiod_name,
            campperiod_introduction,
            campperiod_coverpath,
            campperiod_content,
            campperiod_pool,
            campperiod_project,
            campperiod_source,
            campperiod_tag,
            sales_group_id,
            visual_list,
            campperiod_salesmethod,
            campperiod_price,
            underlined_price,
            campperiod_status,
            enrollment_time,
            starting_flag,
            starting_time,
            registered_flag,
            catalog_mode,
            series_flag,
            series_courses,
            catalog_unlock,
            unlock_time,
            campperiod_red_pack_amount,
            campperiod_red_pack,
            need_phone,
            auto_register,
            red_pack_range,
            created_at,
            created_by,
            updated_by,
            updated_at,
            remark,
            status,
            field1,
            field2,
            field3,
            field4,
            field5
        FROM `camp_period`
    </select>

    <!-- 根据ID查询营期 -->
    <select id="selectCampPeriodById" parameterType="Long" resultType="cn.hxsy.datasource.model.entity.CampPeriodPO">
        SELECT
            id,
            company_id,
            campperiod_name,
            campperiod_introduction,
            campperiod_coverpath,
            campperiod_content,
            campperiod_pool,
            campperiod_project,
            campperiod_source,
            campperiod_tag,
            sales_group_id,
            visual_list,
            campperiod_salesmethod,
            campperiod_price,
            underlined_price,
            campperiod_status,
            enrollment_time,
            starting_flag,
            starting_time,
            registered_flag,
            catalog_mode,
            series_flag,
            series_courses,
            catalog_unlock,
            unlock_time,
            campperiod_red_pack_amount,
            campperiod_red_pack,
            need_phone,
            auto_register,
            red_pack_range,
            created_at,
            created_by,
            updated_by,
            updated_at,
            remark,
            status,
            field1,
            field2,
            field3,
            field4,
            field5
        FROM `camp_period` WHERE id = #{id}
    </select>

    <!-- 根据条件查询营期 -->
    <select id="selectCampPeriodsByCondition" parameterType="map" resultType="cn.hxsy.datasource.model.entity.CampPeriodPO">
        SELECT
            id,
            company_id,
            campperiod_name,
            campperiod_introduction,
            campperiod_coverpath,
            campperiod_content,
            campperiod_pool,
            campperiod_project,
            campperiod_source,
            campperiod_tag,
            sales_group_id,
            visual_list,
            campperiod_salesmethod,
            campperiod_price,
            underlined_price,
            campperiod_status,
            enrollment_time,
            starting_flag,
            starting_time,
            registered_flag,
            catalog_mode,
            series_flag,
            series_courses,
            catalog_unlock,
            unlock_time,
            campperiod_red_pack_amount,
            campperiod_red_pack,
            need_phone,
            auto_register,
            red_pack_range,
            created_at,
            created_by,
            updated_by,
            updated_at,
            remark,
            status,
            field1,
            field2,
            field3,
            field4,
            field5
        FROM `camp_period`
        WHERE campperiod_name LIKE CONCAT('%', #{campperiodName}, '%')
          AND company_id = #{companyId}
    </select>


    <select id="selectByCompanyAndSales" resultType="cn.hxsy.datasource.model.entity.CampPeriodPO">
        SELECT
            id,
            campperiod_red_pack_amount,
            campperiod_red_pack,
            need_phone,
            auto_register,
            red_pack_range,
            campperiod_name AS campperiodName,
            company_id AS companyId,
            starting_flag AS startingFlag
        FROM
            camp_period
        WHERE
            company_id = #{companyId}
          <if test="salesId != null">
              AND JSON_CONTAINS(sales_group_id, CAST(CONCAT('"', #{salesId}, '"') AS JSON), '$');
          </if>
    </select>

</mapper>