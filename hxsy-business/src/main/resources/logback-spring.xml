<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <!--将mybatis-plus的sql记录的日志文件 TODO name改成自己的项目的mapper包路径-->
    <logger name="cn.hxsy.dao" level="debug" />
    <logger name="org.apache.shardingsphere" level="debug"/>
    <!-- 基于 converter -->
    <conversionRule conversionWord="sensitive" converterClass="com.github.houbb.sensitive.logback.converter.SensitiveLogbackConverter" />

    <springProperty scope="context" name="app.name" source="spring.application.name"/>

    <property name="APP_NAME" value="${app.name}"/>
    <property name="LOG_PATH" value="./logs"/>
    <property name="LOG_FILE" value="${LOG_PATH}/application.log"/>
    <property name="FILE_LOG_PATTERN" value="%d %-5level [%thread] %logger{36} - %F:%L - %sensitive%n"/>
    <property name="CONSOLE_LOG_PATTERN" value="%d [%thread] %-5level %logger{36} - %F:%L - %msg%n"/>

    <appender name="APPLICATION"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <root level="INFO">
        <!--   输出到日志文件    -->
        <appender-ref ref="APPLICATION"/>
        <!--   输出到控制台    -->
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>