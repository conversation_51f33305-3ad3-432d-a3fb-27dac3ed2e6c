spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: @application.name@
  cloud: # 使用线上配置
    nacos:
      username: nacos
      password: nacos
      config:
        server-addr: 43.137.62.211:8848
        namespace: 09d6456c-e5a0-4d68-8120-bf646f2e3929
        group: DEFAULT_GROUP
        name: ${spring.application.name}
        shared-configs:
          - data-id: base.yaml
          - data-id: datasource.yaml
          - data-id: datasource-sharding.yaml
          - data-id: cache.yaml
          - data-id: rpc.yaml
          - data-id: config.yaml