<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.hxsy</groupId>
        <artifactId>hxsy-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <artifactId>hxsy-business</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>hxsy-business</name>
    <description>hxsy-business</description>

    <properties>
        <application.name>hxsy-business</application.name>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <!-- 强制指定依赖版本 -->
    <dependencyManagement>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 微信支付SDK -->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-apache-httpclient</artifactId>
            <version>0.6.0</version>
        </dependency>
        <!-- 微信支付sdk  -->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>0.2.17</version>
        </dependency>
        <!-- 企微sdk -->
        <dependency>
            <groupId>cn.felord</groupId>
            <artifactId>wecom-sdk</artifactId>
            <version>1.3.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>logging-interceptor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version> <!-- 最新稳定版 -->
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>4.12.0</version>
        </dependency>

        <!--公共依赖-->
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-config</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-datasource</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-base</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-web</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hxsy</groupId>
            <artifactId>hxsy-sa-token</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>hxsy-business</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>cn.hxsy.HxsyBusinessApplication</mainClass>
<!--                    <skip>true</skip>-->
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <debug>true</debug>
                    <debuglevel>lines,vars,source</debuglevel>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
